{"ast": null, "code": "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule normalizeWheel\n * @typechecks\n */\n\n'use strict';\n\nvar UserAgent_DEPRECATED = require('./UserAgent_DEPRECATED');\nvar isEventSupported = require('./isEventSupported');\n\n// Reasonable defaults\nvar PIXEL_STEP = 10;\nvar LINE_HEIGHT = 40;\nvar PAGE_HEIGHT = 800;\n\n/**\n * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n * complicated, thus this doc is long and (hopefully) detailed enough to answer\n * your questions.\n *\n * If you need to react to the mouse wheel in a predictable way, this code is\n * like your bestest friend. * hugs *\n *\n * As of today, there are 4 DOM event types you can listen to:\n *\n *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n *\n * So what to do?  The is the best:\n *\n *   normalizeWheel.getEventType();\n *\n * In your event callback, use this code to get sane interpretation of the\n * deltas.  This code will return an object with properties:\n *\n *   spinX   -- normalized spin speed (use for zoom) - x plane\n *   spinY   -- \" - y plane\n *   pixelX  -- normalized distance (to pixels) - x plane\n *   pixelY  -- \" - y plane\n *\n * Wheel values are provided by the browser assuming you are using the wheel to\n * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n * significantly on different platforms and browsers, forgetting that you can\n * scroll at different speeds.  Some devices (like trackpads) emit more events\n * at smaller increments with fine granularity, and some emit massive jumps with\n * linear speed or acceleration.\n *\n * This code does its best to normalize the deltas for you:\n *\n *   - spin is trying to normalize how far the wheel was spun (or trackpad\n *     dragged).  This is super useful for zoom support where you want to\n *     throw away the chunky scroll steps on the PC and make those equal to\n *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n *     resolve a single slow step on a wheel to 1.\n *\n *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n *     get the crazy differences between browsers, but at least it'll be in\n *     pixels!\n *\n *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n *     should translate to positive value zooming IN, negative zooming OUT.\n *     This matches the newer 'wheel' event.\n *\n * Why are there spinX, spinY (or pixels)?\n *\n *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n *     with a mouse.  It results in side-scrolling in the browser by default.\n *\n *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n *\n *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n *     probably is by browsers in conjunction with fancy 3D controllers .. but\n *     you know.\n *\n * Implementation info:\n *\n * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n * average mouse:\n *\n *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n *\n * On the trackpad:\n *\n *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n *\n * On other/older browsers.. it's more complicated as there can be multiple and\n * also missing delta values.\n *\n * The 'wheel' event is more standard:\n *\n * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n *\n * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n * backward compatibility with older events.  Those other values help us\n * better normalize spin speed.  Example of what the browsers provide:\n *\n *                          | event.wheelDelta | event.detail\n *        ------------------+------------------+--------------\n *          Safari v5/OS X  |       -120       |       0\n *          Safari v5/Win7  |       -120       |       0\n *         Chrome v17/OS X  |       -120       |       0\n *         Chrome v17/Win7  |       -120       |       0\n *                IE9/Win7  |       -120       |   undefined\n *         Firefox v4/OS X  |     undefined    |       1\n *         Firefox v4/Win7  |     undefined    |       3\n *\n */\nfunction normalizeWheel(/*object*/event) /*object*/{\n  var sX = 0,\n    sY = 0,\n    // spinX, spinY\n    pX = 0,\n    pY = 0; // pixelX, pixelY\n\n  // Legacy\n  if ('detail' in event) {\n    sY = event.detail;\n  }\n  if ('wheelDelta' in event) {\n    sY = -event.wheelDelta / 120;\n  }\n  if ('wheelDeltaY' in event) {\n    sY = -event.wheelDeltaY / 120;\n  }\n  if ('wheelDeltaX' in event) {\n    sX = -event.wheelDeltaX / 120;\n  }\n\n  // side scrolling on FF with DOMMouseScroll\n  if ('axis' in event && event.axis === event.HORIZONTAL_AXIS) {\n    sX = sY;\n    sY = 0;\n  }\n  pX = sX * PIXEL_STEP;\n  pY = sY * PIXEL_STEP;\n  if ('deltaY' in event) {\n    pY = event.deltaY;\n  }\n  if ('deltaX' in event) {\n    pX = event.deltaX;\n  }\n  if ((pX || pY) && event.deltaMode) {\n    if (event.deltaMode == 1) {\n      // delta in LINE units\n      pX *= LINE_HEIGHT;\n      pY *= LINE_HEIGHT;\n    } else {\n      // delta in PAGE units\n      pX *= PAGE_HEIGHT;\n      pY *= PAGE_HEIGHT;\n    }\n  }\n\n  // Fall-back if spin cannot be determined\n  if (pX && !sX) {\n    sX = pX < 1 ? -1 : 1;\n  }\n  if (pY && !sY) {\n    sY = pY < 1 ? -1 : 1;\n  }\n  return {\n    spinX: sX,\n    spinY: sY,\n    pixelX: pX,\n    pixelY: pY\n  };\n}\n\n/**\n * The best combination if you prefer spinX + spinY normalization.  It favors\n * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n * 'wheel' event, making spin speed determination impossible.\n */\nnormalizeWheel.getEventType = function () /*string*/{\n  return UserAgent_DEPRECATED.firefox() ? 'DOMMouseScroll' : isEventSupported('wheel') ? 'wheel' : 'mousewheel';\n};\nmodule.exports = normalizeWheel;", "map": {"version": 3, "names": ["UserAgent_DEPRECATED", "require", "isEventSupported", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "normalizeWheel", "event", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "axis", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "getEventType", "firefox", "module", "exports"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/normalize-wheel/src/normalizeWheel.js"], "sourcesContent": ["/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule normalizeWheel\n * @typechecks\n */\n\n'use strict';\n\nvar UserAgent_DEPRECATED = require('./UserAgent_DEPRECATED');\n\nvar isEventSupported = require('./isEventSupported');\n\n\n// Reasonable defaults\nvar PIXEL_STEP  = 10;\nvar LINE_HEIGHT = 40;\nvar PAGE_HEIGHT = 800;\n\n/**\n * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n * complicated, thus this doc is long and (hopefully) detailed enough to answer\n * your questions.\n *\n * If you need to react to the mouse wheel in a predictable way, this code is\n * like your bestest friend. * hugs *\n *\n * As of today, there are 4 DOM event types you can listen to:\n *\n *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n *\n * So what to do?  The is the best:\n *\n *   normalizeWheel.getEventType();\n *\n * In your event callback, use this code to get sane interpretation of the\n * deltas.  This code will return an object with properties:\n *\n *   spinX   -- normalized spin speed (use for zoom) - x plane\n *   spinY   -- \" - y plane\n *   pixelX  -- normalized distance (to pixels) - x plane\n *   pixelY  -- \" - y plane\n *\n * Wheel values are provided by the browser assuming you are using the wheel to\n * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n * significantly on different platforms and browsers, forgetting that you can\n * scroll at different speeds.  Some devices (like trackpads) emit more events\n * at smaller increments with fine granularity, and some emit massive jumps with\n * linear speed or acceleration.\n *\n * This code does its best to normalize the deltas for you:\n *\n *   - spin is trying to normalize how far the wheel was spun (or trackpad\n *     dragged).  This is super useful for zoom support where you want to\n *     throw away the chunky scroll steps on the PC and make those equal to\n *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n *     resolve a single slow step on a wheel to 1.\n *\n *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n *     get the crazy differences between browsers, but at least it'll be in\n *     pixels!\n *\n *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n *     should translate to positive value zooming IN, negative zooming OUT.\n *     This matches the newer 'wheel' event.\n *\n * Why are there spinX, spinY (or pixels)?\n *\n *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n *     with a mouse.  It results in side-scrolling in the browser by default.\n *\n *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n *\n *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n *     probably is by browsers in conjunction with fancy 3D controllers .. but\n *     you know.\n *\n * Implementation info:\n *\n * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n * average mouse:\n *\n *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n *\n * On the trackpad:\n *\n *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n *\n * On other/older browsers.. it's more complicated as there can be multiple and\n * also missing delta values.\n *\n * The 'wheel' event is more standard:\n *\n * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n *\n * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n * backward compatibility with older events.  Those other values help us\n * better normalize spin speed.  Example of what the browsers provide:\n *\n *                          | event.wheelDelta | event.detail\n *        ------------------+------------------+--------------\n *          Safari v5/OS X  |       -120       |       0\n *          Safari v5/Win7  |       -120       |       0\n *         Chrome v17/OS X  |       -120       |       0\n *         Chrome v17/Win7  |       -120       |       0\n *                IE9/Win7  |       -120       |   undefined\n *         Firefox v4/OS X  |     undefined    |       1\n *         Firefox v4/Win7  |     undefined    |       3\n *\n */\nfunction normalizeWheel(/*object*/ event) /*object*/ {\n  var sX = 0, sY = 0,       // spinX, spinY\n      pX = 0, pY = 0;       // pixelX, pixelY\n\n  // Legacy\n  if ('detail'      in event) { sY = event.detail; }\n  if ('wheelDelta'  in event) { sY = -event.wheelDelta / 120; }\n  if ('wheelDeltaY' in event) { sY = -event.wheelDeltaY / 120; }\n  if ('wheelDeltaX' in event) { sX = -event.wheelDeltaX / 120; }\n\n  // side scrolling on FF with DOMMouseScroll\n  if ( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {\n    sX = sY;\n    sY = 0;\n  }\n\n  pX = sX * PIXEL_STEP;\n  pY = sY * PIXEL_STEP;\n\n  if ('deltaY' in event) { pY = event.deltaY; }\n  if ('deltaX' in event) { pX = event.deltaX; }\n\n  if ((pX || pY) && event.deltaMode) {\n    if (event.deltaMode == 1) {          // delta in LINE units\n      pX *= LINE_HEIGHT;\n      pY *= LINE_HEIGHT;\n    } else {                             // delta in PAGE units\n      pX *= PAGE_HEIGHT;\n      pY *= PAGE_HEIGHT;\n    }\n  }\n\n  // Fall-back if spin cannot be determined\n  if (pX && !sX) { sX = (pX < 1) ? -1 : 1; }\n  if (pY && !sY) { sY = (pY < 1) ? -1 : 1; }\n\n  return { spinX  : sX,\n           spinY  : sY,\n           pixelX : pX,\n           pixelY : pY };\n}\n\n\n/**\n * The best combination if you prefer spinX + spinY normalization.  It favors\n * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n * 'wheel' event, making spin speed determination impossible.\n */\nnormalizeWheel.getEventType = function() /*string*/ {\n  return (UserAgent_DEPRECATED.firefox())\n           ? 'DOMMouseScroll'\n           : (isEventSupported('wheel'))\n               ? 'wheel'\n               : 'mousewheel';\n};\n\nmodule.exports = normalizeWheel;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE5D,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,oBAAoB,CAAC;;AAGpD;AACA,IAAIE,UAAU,GAAI,EAAE;AACpB,IAAIC,WAAW,GAAG,EAAE;AACpB,IAAIC,WAAW,GAAG,GAAG;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAC,UAAWC,KAAK,EAAE,UAAW;EACnD,IAAIC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC;IAAQ;IACtBC,EAAE,GAAG,CAAC;IAAEC,EAAE,GAAG,CAAC,CAAC,CAAO;;EAE1B;EACA,IAAI,QAAQ,IAASJ,KAAK,EAAE;IAAEE,EAAE,GAAGF,KAAK,CAACK,MAAM;EAAE;EACjD,IAAI,YAAY,IAAKL,KAAK,EAAE;IAAEE,EAAE,GAAG,CAACF,KAAK,CAACM,UAAU,GAAG,GAAG;EAAE;EAC5D,IAAI,aAAa,IAAIN,KAAK,EAAE;IAAEE,EAAE,GAAG,CAACF,KAAK,CAACO,WAAW,GAAG,GAAG;EAAE;EAC7D,IAAI,aAAa,IAAIP,KAAK,EAAE;IAAEC,EAAE,GAAG,CAACD,KAAK,CAACQ,WAAW,GAAG,GAAG;EAAE;;EAE7D;EACA,IAAK,MAAM,IAAIR,KAAK,IAAIA,KAAK,CAACS,IAAI,KAAKT,KAAK,CAACU,eAAe,EAAG;IAC7DT,EAAE,GAAGC,EAAE;IACPA,EAAE,GAAG,CAAC;EACR;EAEAC,EAAE,GAAGF,EAAE,GAAGL,UAAU;EACpBQ,EAAE,GAAGF,EAAE,GAAGN,UAAU;EAEpB,IAAI,QAAQ,IAAII,KAAK,EAAE;IAAEI,EAAE,GAAGJ,KAAK,CAACW,MAAM;EAAE;EAC5C,IAAI,QAAQ,IAAIX,KAAK,EAAE;IAAEG,EAAE,GAAGH,KAAK,CAACY,MAAM;EAAE;EAE5C,IAAI,CAACT,EAAE,IAAIC,EAAE,KAAKJ,KAAK,CAACa,SAAS,EAAE;IACjC,IAAIb,KAAK,CAACa,SAAS,IAAI,CAAC,EAAE;MAAW;MACnCV,EAAE,IAAIN,WAAW;MACjBO,EAAE,IAAIP,WAAW;IACnB,CAAC,MAAM;MAA8B;MACnCM,EAAE,IAAIL,WAAW;MACjBM,EAAE,IAAIN,WAAW;IACnB;EACF;;EAEA;EACA,IAAIK,EAAE,IAAI,CAACF,EAAE,EAAE;IAAEA,EAAE,GAAIE,EAAE,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG,CAAC;EAAE;EACzC,IAAIC,EAAE,IAAI,CAACF,EAAE,EAAE;IAAEA,EAAE,GAAIE,EAAE,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG,CAAC;EAAE;EAEzC,OAAO;IAAEU,KAAK,EAAIb,EAAE;IACXc,KAAK,EAAIb,EAAE;IACXc,MAAM,EAAGb,EAAE;IACXc,MAAM,EAAGb;EAAG,CAAC;AACxB;;AAGA;AACA;AACA;AACA;AACA;AACAL,cAAc,CAACmB,YAAY,GAAG,YAAW,UAAW;EAClD,OAAQzB,oBAAoB,CAAC0B,OAAO,CAAC,CAAC,GAC3B,gBAAgB,GACfxB,gBAAgB,CAAC,OAAO,CAAC,GACtB,OAAO,GACP,YAAY;AAC7B,CAAC;AAEDyB,MAAM,CAACC,OAAO,GAAGtB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}