{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取订单列表\nexport function getOrderList(params) {\n  return request({\n    url: '/finance/order/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 获取订单统计信息\nexport function getOrderStatistics() {\n  return request({\n    url: '/finance/order/statistics',\n    method: 'get'\n  });\n}\n\n// 获取订单详情\nexport function getOrderDetail(id) {\n  return request({\n    url: \"/finance/order/detail/\".concat(id),\n    method: 'get'\n  });\n}\n\n// 删除订单\nexport function deleteOrder(id) {\n  return request({\n    url: \"/finance/order/\".concat(id),\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["request", "getOrderList", "params", "url", "method", "getOrderStatistics", "getOrderDetail", "id", "concat", "deleteOrder"], "sources": ["E:/新项目/adminweb/src/api/finance/order.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取订单列表\r\nexport function getOrderList(params) {\r\n  return request({\r\n    url: '/finance/order/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取订单统计信息\r\nexport function getOrderStatistics() {\r\n  return request({\r\n    url: '/finance/order/statistics',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取订单详情\r\nexport function getOrderDetail(id) {\r\n  return request({\r\n    url: `/finance/order/detail/${id}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 删除订单\r\nexport function deleteOrder(id) {\r\n  return request({\r\n    url: `/finance/order/${id}`,\r\n    method: 'delete'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACnC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,kBAAkBA,CAAA,EAAG;EACnC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,cAAcA,CAACC,EAAE,EAAE;EACjC,OAAOP,OAAO,CAAC;IACbG,GAAG,2BAAAK,MAAA,CAA2BD,EAAE,CAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,WAAWA,CAACF,EAAE,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,oBAAAK,MAAA,CAAoBD,EAAE,CAAE;IAC3BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}