{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取协议列表\nexport function getAgreementList(params) {\n  return request({\n    url: '/agreement/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 新增协议\nexport function addAgreement(data) {\n  return request({\n    url: '/agreement/add',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改协议\nexport function updateAgreement(data) {\n  return request({\n    url: '/agreement/update',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除协议\nexport function deleteAgreement(id) {\n  return request({\n    url: \"/agreement/delete/\".concat(id),\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["request", "getAgreementList", "params", "url", "method", "addAgreement", "data", "updateAgreement", "deleteAgreement", "id", "concat"], "sources": ["E:/新项目/adminweb/src/api/system/agreement.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取协议列表\r\nexport function getAgreementList(params) {\r\n  return request({\r\n    url: '/agreement/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 新增协议\r\nexport function addAgreement(data) {\r\n  return request({\r\n    url: '/agreement/add',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 修改协议\r\nexport function updateAgreement(data) {\r\n  return request({\r\n    url: '/agreement/update',\r\n    method: 'put',\r\n    data\r\n  })\r\n}\r\n\r\n// 删除协议\r\nexport function deleteAgreement(id) {\r\n  return request({\r\n    url: `/agreement/delete/${id}`,\r\n    method: 'delete'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAOT,OAAO,CAAC;IACbG,GAAG,uBAAAO,MAAA,CAAuBD,EAAE,CAAE;IAC9BL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}