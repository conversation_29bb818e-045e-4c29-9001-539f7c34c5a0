{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport platform from '../platform/index.js';\nexport default platform.hasStandardBrowserEnv ? function (origin, isMSIE) {\n  return function (url) {\n    url = new URL(url, platform.origin);\n    return origin.protocol === url.protocol && origin.host === url.host && (isMSIE || origin.port === url.port);\n  };\n}(new URL(platform.origin), platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)) : function () {\n  return true;\n};", "map": {"version": 3, "names": ["platform", "hasStandardBrowserEnv", "origin", "isMSIE", "url", "URL", "protocol", "host", "port", "navigator", "test", "userAgent"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/axios/lib/helpers/isURLSameOrigin.js"], "sourcesContent": ["import platform from '../platform/index.js';\n\nexport default platform.hasStandardBrowserEnv ? ((origin, isMSIE) => (url) => {\n  url = new URL(url, platform.origin);\n\n  return (\n    origin.protocol === url.protocol &&\n    origin.host === url.host &&\n    (isMSIE || origin.port === url.port)\n  );\n})(\n  new URL(platform.origin),\n  platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)\n) : () => true;\n"], "mappings": ";;;;;;;;;;;AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAE3C,eAAeA,QAAQ,CAACC,qBAAqB,GAAI,UAACC,MAAM,EAAEC,MAAM;EAAA,OAAK,UAACC,GAAG,EAAK;IAC5EA,GAAG,GAAG,IAAIC,GAAG,CAACD,GAAG,EAAEJ,QAAQ,CAACE,MAAM,CAAC;IAEnC,OACEA,MAAM,CAACI,QAAQ,KAAKF,GAAG,CAACE,QAAQ,IAChCJ,MAAM,CAACK,IAAI,KAAKH,GAAG,CAACG,IAAI,KACvBJ,MAAM,IAAID,MAAM,CAACM,IAAI,KAAKJ,GAAG,CAACI,IAAI,CAAC;EAExC,CAAC;AAAA,EACC,IAAIH,GAAG,CAACL,QAAQ,CAACE,MAAM,CAAC,EACxBF,QAAQ,CAACS,SAAS,IAAI,iBAAiB,CAACC,IAAI,CAACV,QAAQ,CAACS,SAAS,CAACE,SAAS,CAC3E,CAAC,GAAG;EAAA,OAAM,IAAI;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}