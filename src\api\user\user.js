import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  // 转换日期范围
  const query = { ...params }
  if (query.dateRange && query.dateRange.length === 2) {
    query.startDate = query.dateRange[0]
    query.endDate = query.dateRange[1]
  }
  delete query.dateRange

  return request({
    url: '/user/list',
    method: 'get',
    params: query
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/user/${id}`,
    method: 'get'
  })
}

// 更新用户状态
export function updateUserStatus(id, status) {
  return request({
    url: `/user/${id}/status/${status}`,
    method: 'put'
  })
}

// 更新用户激活状态
export function updateUserActivatedStatus(id, isActivated) {
  return request({
    url: `/user/${id}/activated/${isActivated}`,
    method: 'put'
  })
}

// 重置用户密码
export function resetUserPassword(id) {
  return request({
    url: `/user/${id}/reset`,
    method: 'put'
  })
}

// 用户充值
export function rechargeUser(id, data) {
  return request({
    url: `/user/${id}/recharge`,
    method: 'post',
    data
  })
}

// 获取用户银行卡列表
export function getUserBankCards(userId) {
  
  return request({
    url: `/user/${userId}/bank-cards`,
    method: 'get'
  })
}

// 获取用户团队拓扑结构
export function getTeamTopology() {
  return request({
    url: '/user/topology/tree',
    method: 'get'
  })
}

// 获取指定用户的团队拓扑结构
export function getTeamTopologyByPhone(phone) {
  return request({
    url: `/user/topology/tree/${phone}`,
    method: 'get'
  })
}

// 获取代理等级列表
export function getAgentLevels() {
  return request({
    url: '/agent/levels',
    method: 'get'
  })
}

// 更新用户代理等级
export function updateUserLevel(userId, isManager) {
  return request({
    url: `/user/${userId}/level`,
    method: 'put',
    data: { isManager }
  })
}

// 修改用户余额
export function updateUserBalance(userId, newBalance) {
  return request({
    url: `/user/${userId}/balance`,
    method: 'put',
    data: { 
      newBalance: Number(newBalance) // 确保转换为数字
    }
  })
}

// 更新用户GB分红状态
export function updateUserGbDividend(userId, isGbDividend) {
  return request({
    url: '/user/updateGbDividend',
    method: 'put',
    data: {
      userId,
      isGbDividend
    }
  })
}

// 删除用户
export function deleteUser(userId) {
  return request({
    url: `/user/delete/${userId}`,
    method: 'delete'
  })
} 

// 根据邮箱查询团队拓扑
export function getTeamTopologyByEmail(email) {
  return request({
    url: `/user/topology/tree/${email}`,
    method: 'get'
  })
}

// 更新用户利润划转状态
export function updateUserProfitTransferStatus(userId, profitTransferEnabled) {
  return request({
    url: `/user/${userId}/profit-transfer/${profitTransferEnabled}`,
    method: 'put'
  })
}