{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport { getUserList, getUserDetail, updateUserStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels, updateUserLevel, updateUserBalance, updateUserGbDividend, deleteUser } from '@/api/user/user';\nimport { listUserWallet } from '@/api/user/wallet';\nimport { parseTime, formatDate } from '@/utils/date';\nimport axios from 'axios';\nexport default {\n  name: 'UserList',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        // 用户名/手机号\n        status: '',\n        // 状态\n        shareCode: '',\n        // 邀请码\n        referrerPhone: '',\n        // 邀请人手机号\n        email: '',\n        // 邮箱\n        dateRange: [],\n        startDate: '',\n        endDate: ''\n      },\n      loading: false,\n      total: 0,\n      tableData: [],\n      // 充值相关\n      rechargeVisible: false,\n      rechargeUser: {},\n      rechargeForm: {\n        amount: 100,\n        remark: ''\n      },\n      rechargeRules: {\n        amount: [{\n          required: true,\n          message: '请输入充值金额',\n          trigger: 'blur'\n        }]\n      },\n      // 详情相关\n      detailVisible: false,\n      detailUser: {\n        username: '',\n        phone: '',\n        realName: '',\n        teamCount: 0,\n        teamPerformance: 0,\n        createTime: '',\n        lastLoginTime: '',\n        balance: 0,\n        status: '1',\n        referrer: '',\n        inviteCode: '',\n        totalRecharge: 0,\n        totalWithdraw: 0,\n        commission: 0\n      },\n      // 银行卡相关\n      bankCardsVisible: false,\n      bankCardsLoading: false,\n      bankCards: [],\n      // 修改等级相关\n      changeLevelVisible: false,\n      currentUser: {},\n      levelForm: {\n        isManager: ''\n      },\n      // 修改余额相关\n      modifyBalanceVisible: false,\n      modifyBalanceForm: {\n        newBalance: 0\n      },\n      modifyBalanceRules: {\n        newBalance: [{\n          required: true,\n          message: '请输入新余额',\n          trigger: 'blur'\n        }]\n      },\n      walletDialogVisible: false,\n      walletList: [],\n      walletLoading: false\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  watch: {\n    // 监听日期范围变化\n    'listQuery.dateRange': function listQueryDateRange(val) {\n      if (val && val.length === 2) {\n        this.listQuery.startDate = formatDate(val[0]);\n        this.listQuery.endDate = formatDate(val[1]);\n      } else {\n        this.listQuery.startDate = '';\n        this.listQuery.endDate = '';\n      }\n    }\n  },\n  methods: {\n    // 获取列表数据\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              _context.next = 4;\n              return getUserList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0 || res.code === 200) {\n                // 确保数据存在\n                if (res.data) {\n                  _this.tableData = res.data.records || [];\n                  _this.total = res.data.total || 0;\n                } else {\n                  _this.tableData = [];\n                  _this.total = 0;\n                }\n              } else {\n                _this.$message.error(res.msg || '获取用户列表失败');\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取用户列表失败:', _context.t0);\n              _this.$message.error('获取用户列表失败');\n            case 12:\n              _context.prev = 12;\n              _this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 8, 12, 15]]);\n      }))();\n    },\n    // 搜索\n    handleSearch: function handleSearch() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    // 重置查询\n    resetQuery: function resetQuery() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        status: '',\n        isManager: '',\n        // 重置用户等级\n        shareCode: '',\n        // 重置邀请码\n        referrerPhone: '',\n        // 重置邀请人手机号\n        email: '',\n        // 重置邮箱\n        dateRange: [],\n        startDate: '',\n        endDate: '',\n        contractAgreement: '',\n        // 重置合同状态\n        isGbDividend: '' // 重置GB分红状态\n      };\n      this.getList();\n    },\n    // 格式化数字\n    formatNumber: function formatNumber(num) {\n      return num ? num.toLocaleString() : '0';\n    },\n    // 获取等级名称\n    getLevelName: function getLevelName(level) {\n      var levelMap = {\n        0: '普通会员',\n        1: '合伙人',\n        2: '联创'\n      };\n      return levelMap[level] || '未知等级';\n    },\n    // 获取等级标签类型\n    getLevelType: function getLevelType(level) {\n      var typeMap = {\n        0: 'info',\n        1: 'success',\n        2: 'warning'\n      };\n      return typeMap[level] || 'info';\n    },\n    // 处理状态变更\n    handleStatusChange: function handleStatusChange(row) {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return updateUserStatus(row.id, row.status);\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this2.$message.success(\"\".concat(row.status === 1 ? '启用' : '禁用', \"\\u6210\\u529F\"));\n              } else {\n                row.status = row.status === 1 ? 0 : 1;\n                _this2.$message.error(res.msg || '操作失败');\n              }\n              _context2.next = 11;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              row.status = row.status === 1 ? 0 : 1;\n              _this2.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    // 查看详情\n    handleDetail: function handleDetail(row) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return getUserDetail(row.id);\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this3.detailUser = res.data;\n                _this3.detailVisible = true;\n              } else {\n                _this3.$message.error(res.msg || '获取详情失败');\n              }\n              _context3.next = 10;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              _this3.$message.error('获取详情失败');\n            case 10:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    // 打开充值对话框\n    handleRecharge: function handleRecharge(row) {\n      this.rechargeUser = row;\n      this.rechargeForm = {\n        amount: 100,\n        remark: ''\n      };\n      this.rechargeVisible = true;\n    },\n    // 提交充值\n    submitRecharge: function submitRecharge() {\n      var _this4 = this;\n      this.$refs.rechargeForm.validate(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n            while (1) switch (_context4.prev = _context4.next) {\n              case 0:\n                if (!valid) {\n                  _context4.next = 11;\n                  break;\n                }\n                _context4.prev = 1;\n                _context4.next = 4;\n                return rechargeUser(_this4.rechargeUser.id, _this4.rechargeForm);\n              case 4:\n                res = _context4.sent;\n                if (res.code === 0 || res.code === 200) {\n                  _this4.$message.success('充值成功');\n                  _this4.rechargeVisible = false;\n                  _this4.getList(); // 刷新列表\n                } else {\n                  _this4.$message.error(res.msg || '充值失败');\n                }\n                _context4.next = 11;\n                break;\n              case 8:\n                _context4.prev = 8;\n                _context4.t0 = _context4[\"catch\"](1);\n                _this4.$message.error('充值失败');\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }, _callee4, null, [[1, 8]]);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    // 分页相关\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');\n    },\n    // 格式化日期\n    formatDate: function formatDate(time) {\n      if (!time) return '';\n      return parseTime(time, 'yyyy-MM-dd');\n    },\n    // 重置密码\n    handleReset: function handleReset(row) {\n      var _this5 = this;\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return resetUserPassword(row.id);\n            case 3:\n              res = _context5.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this5.$message.success('密码重置成功');\n              } else {\n                _this5.$message.error(res.msg || '密码重置失败');\n              }\n              _context5.next = 10;\n              break;\n            case 7:\n              _context5.prev = 7;\n              _context5.t0 = _context5[\"catch\"](0);\n              _this5.$message.error('密码重置失败');\n            case 10:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消重置，不做任何操作\n      });\n    },\n    // 查看银行\n    handleBankCards: function handleBankCards(row) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _this6.bankCardsVisible = true;\n              _this6.bankCardsLoading = true;\n              _context6.prev = 2;\n              _context6.next = 5;\n              return getUserBankCards(row.id);\n            case 5:\n              res = _context6.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this6.bankCards = res.data || [];\n              } else {\n                _this6.$message.error(res.msg || '获取银行卡列表失败');\n              }\n              _context6.next = 13;\n              break;\n            case 9:\n              _context6.prev = 9;\n              _context6.t0 = _context6[\"catch\"](2);\n              console.error('获取银行卡失败:', _context6.t0); // 添加错误日志\n              _this6.$message.error('获取银行卡列表失败');\n            case 13:\n              _context6.prev = 13;\n              _this6.bankCardsLoading = false;\n              return _context6.finish(13);\n            case 16:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6, null, [[2, 9, 13, 16]]);\n      }))();\n    },\n    // 打开修改等级对话框\n    handleChangeLevel: function handleChangeLevel(row) {\n      this.currentUser = row;\n      this.levelForm.isManager = row.isManager;\n      this.changeLevelVisible = true;\n    },\n    // 提交修改等级\n    submitChangeLevel: function submitChangeLevel() {\n      var _this7 = this;\n      this.$refs.levelForm.validate(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n            while (1) switch (_context7.prev = _context7.next) {\n              case 0:\n                if (!valid) {\n                  _context7.next = 12;\n                  break;\n                }\n                _context7.prev = 1;\n                _context7.next = 4;\n                return updateUserLevel(_this7.currentUser.id, _this7.levelForm.isManager);\n              case 4:\n                res = _context7.sent;\n                if (res.code === 0) {\n                  _this7.$message.success('修改等级成功');\n                  _this7.changeLevelVisible = false;\n                  _this7.getList(); // 刷新列表\n                } else {\n                  _this7.$message.error(res.msg || '修改等级失败');\n                }\n                _context7.next = 12;\n                break;\n              case 8:\n                _context7.prev = 8;\n                _context7.t0 = _context7[\"catch\"](1);\n                console.error('修改等级失败:', _context7.t0);\n                _this7.$message.error('修改等级失败');\n              case 12:\n              case \"end\":\n                return _context7.stop();\n            }\n          }, _callee7, null, [[1, 8]]);\n        }));\n        return function (_x2) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    },\n    // 打开修改余额对话框\n    handleModifyBalance: function handleModifyBalance(row) {\n      this.currentUser = row;\n      this.modifyBalanceForm.newBalance = row.availableBalance;\n      this.modifyBalanceVisible = true;\n    },\n    // 提交修改余额\n    submitModifyBalance: function submitModifyBalance() {\n      var _this8 = this;\n      this.$refs.modifyBalanceForm.validate(/*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n            while (1) switch (_context8.prev = _context8.next) {\n              case 0:\n                if (!valid) {\n                  _context8.next = 14;\n                  break;\n                }\n                _context8.prev = 1;\n                console.log('准备修改余额:', {\n                  userId: _this8.currentUser.id,\n                  newBalance: _this8.modifyBalanceForm.newBalance\n                });\n                _context8.next = 5;\n                return updateUserBalance(_this8.currentUser.id, _this8.modifyBalanceForm.newBalance);\n              case 5:\n                res = _context8.sent;\n                console.log('修改余额响应:', res);\n                if (res.code === 0 || res.code === 200) {\n                  _this8.$message.success('修改余额成功');\n                  _this8.modifyBalanceVisible = false;\n                  _this8.getList(); // 刷新列表\n                } else {\n                  _this8.$message.error(res.msg || '修改余额失败');\n                }\n                _context8.next = 14;\n                break;\n              case 10:\n                _context8.prev = 10;\n                _context8.t0 = _context8[\"catch\"](1);\n                console.error('修改余额失败:', _context8.t0);\n                _this8.$message.error('修改余额失败');\n              case 14:\n              case \"end\":\n                return _context8.stop();\n            }\n          }, _callee8, null, [[1, 10]]);\n        }));\n        return function (_x3) {\n          return _ref4.apply(this, arguments);\n        };\n      }());\n    },\n    // 处理GB分红状态变更\n    handleGbDividendChange: function handleGbDividendChange(row) {\n      var _this9 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee9() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee9$(_context9) {\n          while (1) switch (_context9.prev = _context9.next) {\n            case 0:\n              _context9.prev = 0;\n              _context9.next = 3;\n              return updateUserGbDividend(row.id, row.isGbDividend);\n            case 3:\n              res = _context9.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this9.$message.success(\"\".concat(row.isGbDividend === 1 ? '开启' : '关闭', \"GB\\u5206\\u7EA2\\u6210\\u529F\"));\n              } else {\n                row.isGbDividend = row.isGbDividend === 1 ? 0 : 1;\n                _this9.$message.error(res.msg || '操作失败');\n              }\n              _context9.next = 11;\n              break;\n            case 7:\n              _context9.prev = 7;\n              _context9.t0 = _context9[\"catch\"](0);\n              row.isGbDividend = row.isGbDividend === 1 ? 0 : 1;\n              _this9.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context9.stop();\n          }\n        }, _callee9, null, [[0, 7]]);\n      }))();\n    },\n    // 处理删除用户\n    handleDelete: function handleDelete(row) {\n      var _this10 = this;\n      this.$confirm('确认要删除该用户吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee10() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee10$(_context10) {\n          while (1) switch (_context10.prev = _context10.next) {\n            case 0:\n              _context10.prev = 0;\n              _context10.next = 3;\n              return deleteUser(row.id);\n            case 3:\n              res = _context10.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this10.$message.success('删除成功');\n                _this10.getList(); // 刷新列表\n              } else {\n                _this10.$message.error(res.msg || '删除失败');\n              }\n              _context10.next = 10;\n              break;\n            case 7:\n              _context10.prev = 7;\n              _context10.t0 = _context10[\"catch\"](0);\n              _this10.$message.error('删除失败');\n            case 10:\n            case \"end\":\n              return _context10.stop();\n          }\n        }, _callee10, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消删除，不做任何操作\n      });\n    },\n    handleWalletList: function handleWalletList(row) {\n      var _this11 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee11() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee11$(_context11) {\n          while (1) switch (_context11.prev = _context11.next) {\n            case 0:\n              _this11.walletDialogVisible = true;\n              _this11.walletLoading = true;\n              _context11.prev = 2;\n              _context11.next = 5;\n              return listUserWallet({\n                userId: row.id,\n                pageNum: 1,\n                pageSize: 100\n              });\n            case 5:\n              res = _context11.sent;\n              if (res.code === 0 && res.data && res.data.records) {\n                _this11.walletList = res.data.records;\n              } else {\n                _this11.walletList = [];\n              }\n              _context11.next = 13;\n              break;\n            case 9:\n              _context11.prev = 9;\n              _context11.t0 = _context11[\"catch\"](2);\n              _this11.walletList = [];\n              _this11.$message.error('获取钱包列表失败');\n            case 13:\n              _this11.walletLoading = false;\n            case 14:\n            case \"end\":\n              return _context11.stop();\n          }\n        }, _callee11, null, [[2, 9]]);\n      }))();\n    },\n    formatAddress: function formatAddress(addr) {\n      if (!addr) return '';\n      if (addr.length <= 16) return addr;\n      return addr.slice(0, 6) + '...' + addr.slice(-6);\n    }\n  }\n};", "map": {"version": 3, "names": ["getUserList", "getUserDetail", "updateUserStatus", "resetUserPassword", "rechargeUser", "getUserBankCards", "getAgentLevels", "updateUserLevel", "updateUserBalance", "updateUserGbDividend", "deleteUser", "listUserWallet", "parseTime", "formatDate", "axios", "name", "data", "list<PERSON>uery", "page", "limit", "username", "status", "shareCode", "referrerPhone", "email", "date<PERSON><PERSON><PERSON>", "startDate", "endDate", "loading", "total", "tableData", "rechargeVisible", "rechargeForm", "amount", "remark", "rechargeRules", "required", "message", "trigger", "detailVisible", "detailUser", "phone", "realName", "teamCount", "teamPerformance", "createTime", "lastLoginTime", "balance", "referrer", "inviteCode", "totalRecharge", "totalWithdraw", "commission", "bankCardsVisible", "bankCardsLoading", "bankCards", "changeLevelVisible", "currentUser", "levelForm", "is<PERSON>anager", "modifyBalanceVisible", "modifyBalanceForm", "newBalance", "modifyBalanceRules", "walletDialogVisible", "walletList", "walletLoading", "created", "getList", "watch", "listQueryDateRange", "val", "length", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "records", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleSearch", "reset<PERSON><PERSON>y", "contractAgreement", "isGbDividend", "formatNumber", "num", "toLocaleString", "getLevelName", "level", "levelMap", "getLevelType", "typeMap", "handleStatusChange", "row", "_this2", "_callee2", "_callee2$", "_context2", "id", "success", "concat", "handleDetail", "_this3", "_callee3", "_callee3$", "_context3", "handleRecharge", "submit<PERSON>echarge", "_this4", "$refs", "validate", "_ref", "_callee4", "valid", "_callee4$", "_context4", "_x", "apply", "arguments", "handleSizeChange", "handleCurrentChange", "formatDateTime", "time", "handleReset", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_callee5", "_callee5$", "_context5", "handleBankCards", "_this6", "_callee6", "_callee6$", "_context6", "handleChangeLevel", "submitChangeLevel", "_this7", "_ref3", "_callee7", "_callee7$", "_context7", "_x2", "handleModifyBalance", "availableBalance", "submitModifyBalance", "_this8", "_ref4", "_callee8", "_callee8$", "_context8", "log", "userId", "_x3", "handleGbDividendChange", "_this9", "_callee9", "_callee9$", "_context9", "handleDelete", "_this10", "_callee10", "_callee10$", "_context10", "handleWalletList", "_this11", "_callee11", "_callee11$", "_context11", "pageNum", "pageSize", "formatAddress", "addr", "slice"], "sources": ["src/views/user/list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.username\"\r\n              placeholder=\"用户名/手机号\"\r\n              clearable\r\n              class=\"filter-item\"\r\n              style=\"width: 180px\"\r\n            />\r\n          </el-col>  \r\n          <el-col :span=\"3\">\r\n            <el-select\r\n              v-model=\"listQuery.status\"\r\n              placeholder=\"账户状态\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            >\r\n              <el-option label=\"正常\" value=\"1\" />\r\n              <el-option label=\"禁用\" value=\"0\" />\r\n            </el-select>\r\n          </el-col>\r\n        \r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.shareCode\"\r\n              placeholder=\"邀请码\"\r\n              clearable\r\n              class=\"filter-item\"\r\n               style=\"width: 180px\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.referrerPhone\"\r\n              placeholder=\"邀请人手机号\"\r\n              clearable\r\n              class=\"filter-item\"\r\n               style=\"width: 180px\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-input\r\n              v-model.trim=\"listQuery.email\"\r\n              placeholder=\"邮箱\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n           <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"listQuery.dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              class=\"filter-item date-range-picker\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" class=\"filter-row\">\r\n         \r\n          <el-col :span=\"18\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n            <!-- <el-button type=\"warning\" icon=\"el-icon-download\">导出</el-button> -->\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"UUID\" prop=\"userNo\" width=\"130\" align=\"center\" />\r\n        <el-table-column label=\"用户名称\" prop=\"username\" align=\"center\"  width=\"120\"/>\r\n        <el-table-column label=\"手机号码\" prop=\"phone\" align=\"center\" width=\"120\" />\r\n        <el-table-column label=\"邮箱\" prop=\"email\" align=\"center\" width=\"180\" />\r\n        <el-table-column label=\"推荐人\" align=\"center\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.referrerPhone\">\r\n              {{ scope.row.referrerPhone }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ scope.row.referrerShareCode }}</el-tag>\r\n            </div>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"分享码\" prop=\"shareCode\" align=\"center\" width=\"120\" />\r\n        \r\n        <el-table-column label=\"账户状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              :active-value=\"0\"\r\n              :inactive-value=\"1\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"资金账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.availableBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"跟单账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.copyTradeBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"佣金账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.commissionBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"跟单冻结账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.usageFrozenBlance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"提现冻结余额\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">{{ formatNumber(scope.row.frozenBalance) }}USDT</span>\r\n          </template>\r\n        </el-table-column>\r\n      \r\n      \r\n        <el-table-column label=\"操作\" align=\"center\" width=\"400\" fixed=\"right\">\r\n          <template v-slot=\"{ row }\">\r\n            <el-button type=\"text\" @click=\"handleDetail(row)\">详情</el-button>\r\n            <el-button type=\"text\" @click=\"handleRecharge(row)\">充值</el-button>\r\n            <el-button type=\"text\" @click=\"handleWalletList(row)\">充值地址列表</el-button>\r\n            <el-button type=\"text\" @click=\"handleBankCards(row)\">提现地址列表</el-button>\r\n            <el-button type=\"text\" style=\"color: #f56c6c\" @click=\"handleReset(row)\">重置密码</el-button>\r\n           \r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        />\r\n      </div>\r\n\r\n      <!-- 用户详情对话框 -->\r\n      <el-dialog\r\n        title=\"用户详情\"\r\n        :visible.sync=\"detailVisible\"\r\n        width=\"800px\"\r\n        :close-on-click-modal=\"false\"\r\n        custom-class=\"user-detail-dialog\"\r\n      >\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"UUID\">{{ detailUser.userNo }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">{{ detailUser.phone }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"邮箱\">{{ detailUser.email }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户名称\">{{ detailUser.username }}</el-descriptions-item> \r\n          <el-descriptions-item label=\"佣金率\">{{ detailUser.commissionRate || '0' }}%</el-descriptions-item>\r\n          <el-descriptions-item label=\"累积总充值\">{{ formatNumber(detailUser.totalRecharge) || '0' }}USDT</el-descriptions-item>\r\n          <el-descriptions-item label=\"团队总人数\">{{ formatNumber(detailUser.teamTotalCount) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"团队新增人数\">{{ formatNumber(detailUser.teamTodayCount) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"注册时间\">{{ formatDateTime(detailUser.createTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"最后登录\">{{ formatDateTime(detailUser.updateTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"资金账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.availableBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"跟单账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.copyTradeBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n            <el-descriptions-item label=\"佣金账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.commissionBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"跟单冻结账户\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.usageFrozenBlance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"提现冻结余额\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(detailUser.frozenBalance) || '0' }}USDT</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"账户状态\">\r\n            <el-tag :type=\"detailUser.status === 1 ? 'success' : 'danger'\">\r\n              {{ detailUser.status === 1 ? '正常' : '禁用' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n         \r\n          <el-descriptions-item label=\"推荐人\">\r\n            <template v-if=\"detailUser.referrerPhone\">\r\n              {{ detailUser.referrerPhone }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ detailUser.referrerShareCode }}</el-tag>\r\n            </template>\r\n            <span v-else>-</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"邀请码\">{{ detailUser.shareCode || '-' }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </el-dialog>\r\n\r\n      <!-- 用户充值对话框 -->\r\n      <el-dialog\r\n        title=\"用户充值\"\r\n        :visible.sync=\"rechargeVisible\"\r\n        width=\"500px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"rechargeForm\"\r\n          :model=\"rechargeForm\"\r\n          :rules=\"rechargeRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"用户手机号\">\r\n            <span>{{ rechargeUser.phone }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"账户资金余额\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(rechargeUser.availableBalance) || '0' }}USDT</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"充值金额\" prop=\"amount\">\r\n            <el-input-number\r\n              v-model=\"rechargeForm.amount\"\r\n              :precision=\"2\"\r\n              :step=\"100\"\r\n              style=\"width: 200px\"\r\n            />\r\n            <div style=\"color:#999;font-size:12px;\">可输入负数进行扣款</div>\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"remark\">\r\n            <el-input\r\n              v-model=\"rechargeForm.remark\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"请输入充值备注\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"rechargeVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitRecharge\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 银行卡列表对话框 -->\r\n      <el-dialog\r\n        title=\"提现钱包地址列表\"\r\n        :visible.sync=\"bankCardsVisible\"\r\n        width=\"1000px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-table\r\n          :data=\"bankCards\"\r\n          border\r\n          style=\"width: 100%\"\r\n          v-loading=\"bankCardsLoading\"\r\n        >\r\n          <el-table-column label=\"钱包地址\" prop=\"chainAddress\" align=\"center\" width=\"360\" />   \r\n          <el-table-column label=\"链名称\" prop=\"chainName\" align=\"center\" width=\"150\" />   \r\n          <el-table-column label=\"是否默认\" prop=\"isDefault\" align=\"center\" width=\"120\" />\r\n          <el-table-column label=\"创建时间\" prop=\"createTime\" align=\"center\" width=\"160\" />\r\n          <el-table-column label=\"更新时间\" prop=\"updateTime\" align=\"center\" width=\"160\" />\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"bankCardsVisible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n     \r\n\r\n      <!-- 修改余额对话框 -->\r\n      <el-dialog\r\n        title=\"修改账户余额\"\r\n        :visible.sync=\"modifyBalanceVisible\"\r\n        width=\"400px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"modifyBalanceForm\"\r\n          :model=\"modifyBalanceForm\"\r\n          :rules=\"modifyBalanceRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"用户名\">\r\n            <span>{{ currentUser.username }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号\">\r\n            <span>{{ currentUser.phone }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"当前余额\">\r\n            <span style=\"color: #67C23A\">¥{{ formatNumber(currentUser.availableBalance) || '0' }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"新余额\" prop=\"newBalance\">\r\n            <el-input-number\r\n              v-model=\"modifyBalanceForm.newBalance\"\r\n              :precision=\"2\"\r\n              :step=\"100\"\r\n              :controls-position=\"'right'\"\r\n              :min=\"-999999999\"\r\n              style=\"width: 200px\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"modifyBalanceVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitModifyBalance\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 钱包地址列表对话框 -->\r\n      <el-dialog\r\n        title=\"充值钱包地址列表\"\r\n        :visible.sync=\"walletDialogVisible\"\r\n        width=\"1000px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-table :data=\"walletList\" v-loading=\"walletLoading\" border>\r\n          <el-table-column label=\"链名称\" prop=\"chainName\" align=\"center\" />\r\n          <el-table-column label=\"链地址\" prop=\"chainAddress\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip class=\"item\" effect=\"dark\" :content=\"scope.row.chainAddress\" placement=\"top\">\r\n                <span style=\"cursor:pointer;\">\r\n                  {{ formatAddress(scope.row.chainAddress) }}\r\n                </span>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"BNB余额\" prop=\"bnbBalance\" align=\"center\" />\r\n          <el-table-column label=\"USDT余额\" prop=\"usdtBalance\" align=\"center\" />\r\n          <el-table-column label=\"创建时间\" prop=\"createTime\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.createTime) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"更新时间\" prop=\"updateTime\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatDateTime(scope.row.updateTime) }}\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"walletDialogVisible = false\">关闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getUserList, getUserDetail, updateUserStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels, updateUserLevel, updateUserBalance, updateUserGbDividend, deleteUser } from '@/api/user/user'\r\nimport { listUserWallet } from '@/api/user/wallet'\r\nimport { parseTime, formatDate } from '@/utils/date'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'UserList',\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',      // 用户名/手机号\r\n        status: '',        // 状态\r\n        shareCode: '',     // 邀请码\r\n        referrerPhone: '', // 邀请人手机号\r\n        email: '',         // 邮箱\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: '',\r\n        \r\n      },\r\n \r\n      loading: false,\r\n      total: 0,\r\n      tableData: [],\r\n      // 充值相关\r\n      rechargeVisible: false,\r\n      rechargeUser: {},\r\n      rechargeForm: {\r\n        amount: 100,\r\n        remark: ''\r\n      },\r\n      rechargeRules: {\r\n        amount: [\r\n          { required: true, message: '请输入充值金额', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 详情相关\r\n      detailVisible: false,\r\n      detailUser: {\r\n        username: '',\r\n        phone: '',\r\n        realName: '',\r\n      \r\n        teamCount: 0,\r\n        teamPerformance: 0,\r\n        createTime: '',\r\n        lastLoginTime: '',\r\n        balance: 0,\r\n        status: '1',\r\n        referrer: '',\r\n        inviteCode: '',\r\n        totalRecharge: 0,\r\n        totalWithdraw: 0,\r\n        commission: 0\r\n      },\r\n      // 银行卡相关\r\n      bankCardsVisible: false,\r\n      bankCardsLoading: false,\r\n      bankCards: [],\r\n      // 修改等级相关\r\n      changeLevelVisible: false,\r\n      currentUser: {},\r\n      levelForm: {\r\n        isManager: ''\r\n      },\r\n   \r\n    \r\n      // 修改余额相关\r\n      modifyBalanceVisible: false,\r\n      modifyBalanceForm: {\r\n        newBalance: 0\r\n      },\r\n      modifyBalanceRules: {\r\n        newBalance: [\r\n          { required: true, message: '请输入新余额', trigger: 'blur' }\r\n        ]\r\n      },\r\n      walletDialogVisible: false,\r\n      walletList: [],\r\n      walletLoading: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n   \r\n  },\r\n  watch: {\r\n    // 监听日期范围变化\r\n    'listQuery.dateRange'(val) {\r\n      if (val && val.length === 2) {\r\n        this.listQuery.startDate = formatDate(val[0])\r\n        this.listQuery.endDate = formatDate(val[1])\r\n      } else {\r\n        this.listQuery.startDate = ''\r\n        this.listQuery.endDate = ''\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取列表数据\r\n    async getList() {\r\n      this.loading = true\r\n      try { \r\n        const res = await getUserList(this.listQuery)  \r\n        if (res.code === 0 || res.code === 200) {\r\n          // 确保数据存在\r\n          if (res.data) {\r\n            this.tableData = res.data.records || []\r\n            this.total = res.data.total || 0 \r\n          } else {\r\n            this.tableData = []\r\n            this.total = 0\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '获取用户列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取用户列表失败:', error)\r\n        this.$message.error('获取用户列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch() {\r\n      this.listQuery.page = 1\r\n      this.getList()\r\n    },\r\n\r\n    // 重置查询\r\n    resetQuery() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        status: '',\r\n        isManager: '',     // 重置用户等级\r\n        shareCode: '',     // 重置邀请码\r\n        referrerPhone: '', // 重置邀请人手机号\r\n        email: '',         // 重置邮箱\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: '',\r\n        contractAgreement: '', // 重置合同状态\r\n        isGbDividend: ''      // 重置GB分红状态\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化数字\r\n    formatNumber(num) {\r\n      return num ? num.toLocaleString() : '0'\r\n    },\r\n\r\n    // 获取等级名称\r\n    getLevelName(level) {\r\n      const levelMap = {\r\n        0: '普通会员',\r\n        1: '合伙人',\r\n        2: '联创'\r\n      }\r\n      return levelMap[level] || '未知等级'\r\n    },\r\n\r\n    // 获取等级标签类型\r\n    getLevelType(level) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'warning'\r\n      }\r\n      return typeMap[level] || 'info'\r\n    },\r\n\r\n    // 处理状态变更\r\n    async handleStatusChange(row) {\r\n      try {\r\n        const res = await updateUserStatus(row.id, row.status)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.status === 1 ? '启用' : '禁用'}成功`)\r\n        } else {\r\n          row.status = row.status === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.status = row.status === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 查看详情\r\n    async handleDetail(row) {\r\n      try {\r\n        const res = await getUserDetail(row.id)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.detailUser = res.data\r\n          this.detailVisible = true\r\n        } else {\r\n          this.$message.error(res.msg || '获取详情失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取详情失败')\r\n      }\r\n    },\r\n\r\n    // 打开充值对话框\r\n    handleRecharge(row) {\r\n      this.rechargeUser = row\r\n      this.rechargeForm = {\r\n        amount: 100,\r\n        remark: ''\r\n      }\r\n      this.rechargeVisible = true\r\n    },\r\n\r\n    // 提交充值\r\n    submitRecharge() {\r\n      this.$refs.rechargeForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await rechargeUser(this.rechargeUser.id, this.rechargeForm)\r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('充值成功')\r\n              this.rechargeVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '充值失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('充值失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 分页相关\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(time) {\r\n      if (!time) return ''\r\n      return parseTime(time, 'yyyy-MM-dd')\r\n    },\r\n\r\n    // 重置密码\r\n    handleReset(row) {\r\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await resetUserPassword(row.id)\r\n          if (res.code === 0 || res.code === 200) {\r\n            this.$message.success('密码重置成功')\r\n          } else {\r\n            this.$message.error(res.msg || '密码重置失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('密码重置失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消重置，不做任何操作\r\n      })\r\n    },\r\n\r\n    // 查看银行\r\n    async handleBankCards(row) {\r\n      \r\n      this.bankCardsVisible = true\r\n      this.bankCardsLoading = true\r\n      try {\r\n        const res = await getUserBankCards(row.id)\r\n       \r\n        if (res.code === 0 || res.code === 200) {\r\n          this.bankCards = res.data || []\r\n        } else {\r\n          this.$message.error(res.msg || '获取银行卡列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取银行卡失败:', error)  // 添加错误日志\r\n        this.$message.error('获取银行卡列表失败')\r\n      } finally {\r\n        this.bankCardsLoading = false\r\n      }\r\n    },\r\n \r\n    // 打开修改等级对话框\r\n    handleChangeLevel(row) {\r\n      this.currentUser = row\r\n      this.levelForm.isManager = row.isManager\r\n      this.changeLevelVisible = true\r\n    },\r\n    \r\n    // 提交修改等级\r\n    submitChangeLevel() {\r\n      this.$refs.levelForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await updateUserLevel(\r\n              this.currentUser.id,\r\n              this.levelForm.isManager\r\n            )\r\n            if (res.code === 0) {\r\n              this.$message.success('修改等级成功')\r\n              this.changeLevelVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '修改等级失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改等级失败:', error)\r\n            this.$message.error('修改等级失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 打开修改余额对话框\r\n    handleModifyBalance(row) {\r\n      this.currentUser = row\r\n      this.modifyBalanceForm.newBalance = row.availableBalance\r\n      this.modifyBalanceVisible = true\r\n    },\r\n\r\n    // 提交修改余额\r\n    submitModifyBalance() {\r\n      this.$refs.modifyBalanceForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            console.log('准备修改余额:', {\r\n              userId: this.currentUser.id,\r\n              newBalance: this.modifyBalanceForm.newBalance\r\n            })\r\n            \r\n            const res = await updateUserBalance(\r\n              this.currentUser.id, \r\n              this.modifyBalanceForm.newBalance\r\n            )\r\n            \r\n            console.log('修改余额响应:', res)\r\n            \r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('修改余额成功')\r\n              this.modifyBalanceVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '修改余额失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改余额失败:', error)\r\n            this.$message.error('修改余额失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理GB分红状态变更\r\n    async handleGbDividendChange(row) {\r\n      try {\r\n        const res = await updateUserGbDividend(row.id, row.isGbDividend)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.isGbDividend === 1 ? '开启' : '关闭'}GB分红成功`)\r\n        } else {\r\n          row.isGbDividend = row.isGbDividend === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.isGbDividend = row.isGbDividend === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 处理删除用户\r\n    handleDelete(row) {\r\n      this.$confirm('确认要删除该用户吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await deleteUser(row.id)\r\n          if (res.code === 0 || res.code === 200) {\r\n            this.$message.success('删除成功')\r\n            this.getList() // 刷新列表\r\n          } else {\r\n            this.$message.error(res.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('删除失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消删除，不做任何操作\r\n      })\r\n    },\r\n\r\n    async handleWalletList(row) {\r\n      this.walletDialogVisible = true;\r\n      this.walletLoading = true;\r\n      try {\r\n        const res = await listUserWallet({ userId: row.id, pageNum: 1, pageSize: 100 });\r\n        if (res.code === 0 && res.data && res.data.records) {\r\n          this.walletList = res.data.records;\r\n        } else {\r\n          this.walletList = [];\r\n        }\r\n      } catch (e) {\r\n        this.walletList = [];\r\n        this.$message.error('获取钱包列表失败');\r\n      }\r\n      this.walletLoading = false;\r\n    },\r\n\r\n    formatAddress(addr) {\r\n      if (!addr) return '';\r\n      if (addr.length <= 16) return addr;\r\n      return addr.slice(0, 6) + '...' + addr.slice(-6);\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 黑金主题整体覆盖 */\r\n.app-container {\r\n  background: #181818;\r\n  min-height: 100vh;\r\n}\r\n.el-card.box-card {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  border-radius: 12px;\r\n  color: #fff;\r\n  box-shadow: 0 4px 24px #000a;\r\n}\r\n.filter-container {\r\n  background: transparent;\r\n  .el-input__inner, .el-select .el-input__inner {\r\n    background: #232323 !important;\r\n    border: 1.5px solid #FFD700 !important;\r\n    color: #fff !important;\r\n    &::placeholder {\r\n      color: #b3b3b3 !important;\r\n    }\r\n  }\r\n  .el-select-dropdown {\r\n    background: #232323 !important;\r\n    color: #fff !important;\r\n    .el-select-dropdown__item {\r\n      color: #fff !important;\r\n      &:hover, &.selected {\r\n        color: #FFD700 !important;\r\n        background: #181818 !important;\r\n      }\r\n    }\r\n  }\r\n  .el-date-editor {\r\n    background: #232323 !important;\r\n    border: 1.5px solid #FFD700 !important;\r\n    color: #fff !important;\r\n    .el-input__inner {\r\n      background: #232323 !important;\r\n      color: #fff !important;\r\n      border: none !important;\r\n    }\r\n    .el-input__prefix, .el-input__suffix {\r\n      color: #FFD700 !important;\r\n    }\r\n  }\r\n  .el-date-editor .el-range-separator {\r\n    color: #FFD700 !important;\r\n  }\r\n  .el-button {\r\n    background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;\r\n    color: #181818 !important;\r\n    border: none !important;\r\n    font-weight: bold;\r\n    border-radius: 8px;\r\n    &:hover {\r\n      background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%) !important;\r\n      color: #000 !important;\r\n    }\r\n  }\r\n}\r\n.el-table {\r\n  background: #181818 !important;\r\n  color: #fff !important;\r\n  th, td {\r\n    background: #232323 !important;\r\n    color: #fff !important;\r\n    border-color: #FFD70033 !important;\r\n  }\r\n  td {\r\n    color: #fff !important;\r\n  }\r\n  td .is-disabled, td .is-disabled * {\r\n    color: #b3b3b3 !important;\r\n  }\r\n  .el-table__body tr:hover > td {\r\n    background: #292929 !important;\r\n  }\r\n  .el-table__header th {\r\n    background: #181818 !important;\r\n    color: #FFD700 !important;\r\n    font-weight: bold;\r\n  }\r\n}\r\n.el-table .el-button[type=\"text\"] {\r\n  color: #FFD700 !important;\r\n  font-weight: bold;\r\n}\r\n.el-tag.el-tag--info {\r\n  background: #232323 !important;\r\n  color: #FFD700 !important;\r\n  border: 1px solid #FFD700 !important;\r\n}\r\n.el-switch {\r\n  .el-switch__core {\r\n    background: #232323 !important;\r\n    border: 1.5px solid #FFD700 !important;\r\n  }\r\n  .el-switch__button {\r\n    background: #FFD700 !important;\r\n  }\r\n}\r\n.el-pagination {\r\n  background: transparent;\r\n  .el-pager li {\r\n    color: #fff !important;\r\n    background: #232323 !important;\r\n    border: 1px solid #FFD700 !important;\r\n    &.active {\r\n      color: #181818 !important;\r\n      background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;\r\n      border: 1px solid #FFD700 !important;\r\n    }\r\n  }\r\n  .el-pagination__total, .el-pagination__jump, .el-pagination__sizes {\r\n    color: #fff !important;\r\n  }\r\n}\r\n// 滚动条黑金化\r\n::-webkit-scrollbar {\r\n  height: 8px;\r\n  width: 8px;\r\n  background: #232323;\r\n}\r\n::-webkit-scrollbar-thumb {\r\n  background: #FFD70033;\r\n  border-radius: 4px;\r\n}\r\n// 弹窗黑金主题\r\n::v-deep .el-dialog {\r\n  background: #181818 !important;\r\n  border-radius: 12px;\r\n  .el-dialog__header {\r\n    color: #fff !important;\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    opacity: 1 !important;\r\n    border-bottom: 1px solid #FFD70033;\r\n  }\r\n  .el-dialog__title {\r\n    color: #fff !important;\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    opacity: 1 !important;\r\n  }\r\n  .el-dialog__footer {\r\n    .el-button--primary {\r\n      background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%);\r\n      color: #181818;\r\n      border: none;\r\n      font-weight: bold;\r\n      &:hover {\r\n        background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%);\r\n        color: #000;\r\n      }\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    color: #FFD700;\r\n  }\r\n  .el-input__inner {\r\n    background: #232323;\r\n    border: 1.5px solid #FFD700;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n  \r\n  .filter-row {\r\n    margin-bottom: 20px;\r\n    \r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  \r\n  .filter-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .date-range-picker {\r\n    width: 100%;\r\n  }\r\n  \r\n  .el-button {\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .el-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n// 修改对话框样式\r\n.user-detail-dialog {\r\n  ::v-deep .el-dialog__body {\r\n    padding: 10px 20px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__header {\r\n    padding: 15px 20px 10px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__footer {\r\n    padding: 10px 20px 15px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  margin: 0;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding-top: 0;\r\n}\r\n\r\n// 日期选择器弹窗黑金化\r\n::v-deep .el-picker-panel {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  .el-date-table th {\r\n    color: #FFD700 !important;\r\n  }\r\n  .el-date-table td {\r\n    color: #fff !important;\r\n    &.in-range {\r\n      background: #FFD70022 !important;\r\n    }\r\n    &.current, &.today {\r\n      color: #FFD700 !important;\r\n      font-weight: bold;\r\n    }\r\n    &.available:hover {\r\n      background: #FFD70033 !important;\r\n      color: #FFD700 !important;\r\n    }\r\n  }\r\n  .el-picker-panel__footer {\r\n    background: #232323 !important;\r\n    border-top: 1px solid #FFD70033 !important;\r\n    .el-button--text, .el-button--default {\r\n      color: #FFD700 !important;\r\n    }\r\n  }\r\n}\r\n\r\n// 强化表格内容字体色\r\n::v-deep .el-table td, ::v-deep .el-table td * {\r\n  color: #fff !important;\r\n}\r\n\r\n.el-table__fixed-body-wrapper td,\r\n.el-table__fixed-body-wrapper td *,\r\n.el-table__fixed .cell,\r\n.el-table__fixed .cell * {\r\n  color: #fff !important;\r\n  -webkit-text-fill-color: #fff !important;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table__body td,\r\n.el-table__body td *,\r\n.el-table__body .cell,\r\n.el-table__body .cell * {\r\n  color: #fff !important;\r\n  -webkit-text-fill-color: #fff !important;\r\n}\r\n.el-table__body td .is-disabled,\r\n.el-table__body td .is-disabled *,\r\n.el-table__body td[style*='color: #ccc'],\r\n.el-table__body td[style*='color: #e0e0e0'] {\r\n  color: #b3b3b3 !important;\r\n  -webkit-text-fill-color: #b3b3b3 !important;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n/* 黑金主题 el-descriptions-item label 全覆盖 */\r\n.el-descriptions__label,\r\n.el-descriptions-item__label,\r\n.el-descriptions-item__cell.is-label {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n  font-weight: bold !important;\r\n  border-right: 1px solid #FFD70033 !important;\r\n}\r\n</style>"], "mappings": ";;;AAwWA,SAAAA,WAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,oBAAA,EAAAC,UAAA;AACA,SAAAC,cAAA;AACA,SAAAC,SAAA,EAAAC,UAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QAAA;QACAC,MAAA;QAAA;QACAC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;MAEA;MAEAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACA3B,YAAA;MACA4B,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,aAAA;QACAF,MAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,aAAA;MACAC,UAAA;QACApB,QAAA;QACAqB,KAAA;QACAC,QAAA;QAEAC,SAAA;QACAC,eAAA;QACAC,UAAA;QACAC,aAAA;QACAC,OAAA;QACA1B,MAAA;QACA2B,QAAA;QACAC,UAAA;QACAC,aAAA;QACAC,aAAA;QACAC,UAAA;MACA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,kBAAA;MACAC,WAAA;MACAC,SAAA;QACAC,SAAA;MACA;MAGA;MACAC,oBAAA;MACAC,iBAAA;QACAC,UAAA;MACA;MACAC,kBAAA;QACAD,UAAA,GACA;UAAA1B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA0B,mBAAA;MACAC,UAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EAEA;EACAC,KAAA;IACA;IACA,gCAAAC,mBAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA,CAAAC,MAAA;QACA,KAAAvD,SAAA,CAAAS,SAAA,GAAAb,UAAA,CAAA0D,GAAA;QACA,KAAAtD,SAAA,CAAAU,OAAA,GAAAd,UAAA,CAAA0D,GAAA;MACA;QACA,KAAAtD,SAAA,CAAAS,SAAA;QACA,KAAAT,SAAA,CAAAU,OAAA;MACA;IACA;EACA;EACA8C,OAAA;IACA;IACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAA9C,OAAA;cAAAsD,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEApF,WAAA,CAAA0E,KAAA,CAAAzD,SAAA;YAAA;cAAA8D,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA;gBACA,IAAAP,GAAA,CAAA/D,IAAA;kBACA0D,KAAA,CAAA5C,SAAA,GAAAiD,GAAA,CAAA/D,IAAA,CAAAuE,OAAA;kBACAb,KAAA,CAAA7C,KAAA,GAAAkD,GAAA,CAAA/D,IAAA,CAAAa,KAAA;gBACA;kBACA6C,KAAA,CAAA5C,SAAA;kBACA4C,KAAA,CAAA7C,KAAA;gBACA;cACA;gBACA6C,KAAA,CAAAc,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,cAAAP,QAAA,CAAAS,EAAA;cACAjB,KAAA,CAAAc,QAAA,CAAAC,KAAA;YAAA;cAAAP,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAA9C,OAAA;cAAA,OAAAsD,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IAEA;IAEA;IACAiB,YAAA,WAAAA,aAAA;MACA,KAAA9E,SAAA,CAAAC,IAAA;MACA,KAAAkD,OAAA;IACA;IAEA;IACA4B,UAAA,WAAAA,WAAA;MACA,KAAA/E,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAsC,SAAA;QAAA;QACArC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAsE,iBAAA;QAAA;QACAC,YAAA;MACA;MACA,KAAA9B,OAAA;IACA;IAEA;IACA+B,YAAA,WAAAA,aAAAC,GAAA;MACA,OAAAA,GAAA,GAAAA,GAAA,CAAAC,cAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAC,KAAA;MACA,IAAAC,QAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,KAAA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAAF,KAAA;MACA,IAAAG,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAH,KAAA;IACA;IAEA;IACAI,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAAlC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiC,SAAA;QAAA,IAAA/B,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAA5B,IAAA;cAAA,OAEAlF,gBAAA,CAAA0G,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAAvF,MAAA;YAAA;cAAA0D,GAAA,GAAAiC,SAAA,CAAA3B,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAuB,MAAA,CAAArB,QAAA,CAAA0B,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAAvF,MAAA;cACA;gBACAuF,GAAA,CAAAvF,MAAA,GAAAuF,GAAA,CAAAvF,MAAA;gBACAwF,MAAA,CAAArB,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAsB,SAAA,CAAA5B,IAAA;cAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAArB,EAAA,GAAAqB,SAAA;cAEAJ,GAAA,CAAAvF,MAAA,GAAAuF,GAAA,CAAAvF,MAAA;cACAwF,MAAA,CAAArB,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAuB,SAAA,CAAAlB,IAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAEA;IAEA;IACAM,YAAA,WAAAA,aAAAR,GAAA;MAAA,IAAAS,MAAA;MAAA,OAAA1C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyC,SAAA;QAAA,IAAAvC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;YAAA;cAAAoC,SAAA,CAAArC,IAAA;cAAAqC,SAAA,CAAApC,IAAA;cAAA,OAEAnF,aAAA,CAAA2G,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAyC,SAAA,CAAAnC,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA+B,MAAA,CAAA7E,UAAA,GAAAuC,GAAA,CAAA/D,IAAA;gBACAqG,MAAA,CAAA9E,aAAA;cACA;gBACA8E,MAAA,CAAA7B,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA8B,SAAA,CAAApC,IAAA;cAAA;YAAA;cAAAoC,SAAA,CAAArC,IAAA;cAAAqC,SAAA,CAAA7B,EAAA,GAAA6B,SAAA;cAEAH,MAAA,CAAA7B,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA+B,SAAA,CAAA1B,IAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IAEA;IAEA;IACAG,cAAA,WAAAA,eAAAb,GAAA;MACA,KAAAxG,YAAA,GAAAwG,GAAA;MACA,KAAA5E,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,KAAAH,eAAA;IACA;IAEA;IACA2F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA5F,YAAA,CAAA6F,QAAA;QAAA,IAAAC,IAAA,GAAAnD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkD,SAAAC,KAAA;UAAA,IAAAjD,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiD,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA/C,IAAA,GAAA+C,SAAA,CAAA9C,IAAA;cAAA;gBAAA,KACA4C,KAAA;kBAAAE,SAAA,CAAA9C,IAAA;kBAAA;gBAAA;gBAAA8C,SAAA,CAAA/C,IAAA;gBAAA+C,SAAA,CAAA9C,IAAA;gBAAA,OAEAhF,YAAA,CAAAuH,MAAA,CAAAvH,YAAA,CAAA6G,EAAA,EAAAU,MAAA,CAAA3F,YAAA;cAAA;gBAAA+C,GAAA,GAAAmD,SAAA,CAAA7C,IAAA;gBACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACAqC,MAAA,CAAAnC,QAAA,CAAA0B,OAAA;kBACAS,MAAA,CAAA5F,eAAA;kBACA4F,MAAA,CAAAvD,OAAA;gBACA;kBACAuD,MAAA,CAAAnC,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAwC,SAAA,CAAA9C,IAAA;gBAAA;cAAA;gBAAA8C,SAAA,CAAA/C,IAAA;gBAAA+C,SAAA,CAAAvC,EAAA,GAAAuC,SAAA;gBAEAP,MAAA,CAAAnC,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAyC,SAAA,CAAApC,IAAA;YAAA;UAAA,GAAAiC,QAAA;QAAA,CAGA;QAAA,iBAAAI,EAAA;UAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA/D,GAAA;MACA,KAAAtD,SAAA,CAAAE,KAAA,GAAAoD,GAAA;MACA,KAAAH,OAAA;IACA;IACAmE,mBAAA,WAAAA,oBAAAhE,GAAA;MACA,KAAAtD,SAAA,CAAAC,IAAA,GAAAqD,GAAA;MACA,KAAAH,OAAA;IACA;IAEA;IACAoE,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,OAAA7H,SAAA,CAAA6H,IAAA;IACA;IAEA;IACA5H,UAAA,WAAAA,WAAA4H,IAAA;MACA,KAAAA,IAAA;MACA,OAAA7H,SAAA,CAAA6H,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAArE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoE,SAAA;QAAA,IAAAlE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAkE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhE,IAAA,GAAAgE,SAAA,CAAA/D,IAAA;YAAA;cAAA+D,SAAA,CAAAhE,IAAA;cAAAgE,SAAA,CAAA/D,IAAA;cAAA,OAEAjF,iBAAA,CAAAyG,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAoE,SAAA,CAAA9D,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAqD,MAAA,CAAAnD,QAAA,CAAA0B,OAAA;cACA;gBACAyB,MAAA,CAAAnD,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAyD,SAAA,CAAA/D,IAAA;cAAA;YAAA;cAAA+D,SAAA,CAAAhE,IAAA;cAAAgE,SAAA,CAAAxD,EAAA,GAAAwD,SAAA;cAEAR,MAAA,CAAAnD,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA0D,SAAA,CAAArD,IAAA;UAAA;QAAA,GAAAmD,QAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IAEA;IACAG,eAAA,WAAAA,gBAAAxC,GAAA;MAAA,IAAAyC,MAAA;MAAA,OAAA1E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyE,SAAA;QAAA,IAAAvE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAApE,IAAA;YAAA;cAEAiE,MAAA,CAAAhG,gBAAA;cACAgG,MAAA,CAAA/F,gBAAA;cAAAkG,SAAA,CAAArE,IAAA;cAAAqE,SAAA,CAAApE,IAAA;cAAA,OAEA/E,gBAAA,CAAAuG,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAyE,SAAA,CAAAnE,IAAA;cAEA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA+D,MAAA,CAAA9F,SAAA,GAAAwB,GAAA,CAAA/D,IAAA;cACA;gBACAqI,MAAA,CAAA7D,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA8D,SAAA,CAAApE,IAAA;cAAA;YAAA;cAAAoE,SAAA,CAAArE,IAAA;cAAAqE,SAAA,CAAA7D,EAAA,GAAA6D,SAAA;cAEA5D,OAAA,CAAAH,KAAA,aAAA+D,SAAA,CAAA7D,EAAA;cACA0D,MAAA,CAAA7D,QAAA,CAAAC,KAAA;YAAA;cAAA+D,SAAA,CAAArE,IAAA;cAEAkE,MAAA,CAAA/F,gBAAA;cAAA,OAAAkG,SAAA,CAAA3D,MAAA;YAAA;YAAA;cAAA,OAAA2D,SAAA,CAAA1D,IAAA;UAAA;QAAA,GAAAwD,QAAA;MAAA;IAEA;IAEA;IACAG,iBAAA,WAAAA,kBAAA7C,GAAA;MACA,KAAAnD,WAAA,GAAAmD,GAAA;MACA,KAAAlD,SAAA,CAAAC,SAAA,GAAAiD,GAAA,CAAAjD,SAAA;MACA,KAAAH,kBAAA;IACA;IAEA;IACAkG,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAA/B,KAAA,CAAAlE,SAAA,CAAAmE,QAAA;QAAA,IAAA+B,KAAA,GAAAjF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgF,SAAA7B,KAAA;UAAA,IAAAjD,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8E,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA5E,IAAA,GAAA4E,SAAA,CAAA3E,IAAA;cAAA;gBAAA,KACA4C,KAAA;kBAAA+B,SAAA,CAAA3E,IAAA;kBAAA;gBAAA;gBAAA2E,SAAA,CAAA5E,IAAA;gBAAA4E,SAAA,CAAA3E,IAAA;gBAAA,OAEA7E,eAAA,CACAoJ,MAAA,CAAAlG,WAAA,CAAAwD,EAAA,EACA0C,MAAA,CAAAjG,SAAA,CAAAC,SACA;cAAA;gBAHAoB,GAAA,GAAAgF,SAAA,CAAA1E,IAAA;gBAIA,IAAAN,GAAA,CAAAO,IAAA;kBACAqE,MAAA,CAAAnE,QAAA,CAAA0B,OAAA;kBACAyC,MAAA,CAAAnG,kBAAA;kBACAmG,MAAA,CAAAvF,OAAA;gBACA;kBACAuF,MAAA,CAAAnE,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAqE,SAAA,CAAA3E,IAAA;gBAAA;cAAA;gBAAA2E,SAAA,CAAA5E,IAAA;gBAAA4E,SAAA,CAAApE,EAAA,GAAAoE,SAAA;gBAEAnE,OAAA,CAAAH,KAAA,YAAAsE,SAAA,CAAApE,EAAA;gBACAgE,MAAA,CAAAnE,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAsE,SAAA,CAAAjE,IAAA;YAAA;UAAA,GAAA+D,QAAA;QAAA,CAGA;QAAA,iBAAAG,GAAA;UAAA,OAAAJ,KAAA,CAAAxB,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACA4B,mBAAA,WAAAA,oBAAArD,GAAA;MACA,KAAAnD,WAAA,GAAAmD,GAAA;MACA,KAAA/C,iBAAA,CAAAC,UAAA,GAAA8C,GAAA,CAAAsD,gBAAA;MACA,KAAAtG,oBAAA;IACA;IAEA;IACAuG,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAAxC,KAAA,CAAA/D,iBAAA,CAAAgE,QAAA;QAAA,IAAAwC,KAAA,GAAA1F,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyF,SAAAtC,KAAA;UAAA,IAAAjD,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuF,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAArF,IAAA,GAAAqF,SAAA,CAAApF,IAAA;cAAA;gBAAA,KACA4C,KAAA;kBAAAwC,SAAA,CAAApF,IAAA;kBAAA;gBAAA;gBAAAoF,SAAA,CAAArF,IAAA;gBAEAS,OAAA,CAAA6E,GAAA;kBACAC,MAAA,EAAAN,MAAA,CAAA3G,WAAA,CAAAwD,EAAA;kBACAnD,UAAA,EAAAsG,MAAA,CAAAvG,iBAAA,CAAAC;gBACA;gBAAA0G,SAAA,CAAApF,IAAA;gBAAA,OAEA5E,iBAAA,CACA4J,MAAA,CAAA3G,WAAA,CAAAwD,EAAA,EACAmD,MAAA,CAAAvG,iBAAA,CAAAC,UACA;cAAA;gBAHAiB,GAAA,GAAAyF,SAAA,CAAAnF,IAAA;gBAKAO,OAAA,CAAA6E,GAAA,YAAA1F,GAAA;gBAEA,IAAAA,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACA8E,MAAA,CAAA5E,QAAA,CAAA0B,OAAA;kBACAkD,MAAA,CAAAxG,oBAAA;kBACAwG,MAAA,CAAAhG,OAAA;gBACA;kBACAgG,MAAA,CAAA5E,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAA8E,SAAA,CAAApF,IAAA;gBAAA;cAAA;gBAAAoF,SAAA,CAAArF,IAAA;gBAAAqF,SAAA,CAAA7E,EAAA,GAAA6E,SAAA;gBAEA5E,OAAA,CAAAH,KAAA,YAAA+E,SAAA,CAAA7E,EAAA;gBACAyE,MAAA,CAAA5E,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAA+E,SAAA,CAAA1E,IAAA;YAAA;UAAA,GAAAwE,QAAA;QAAA,CAGA;QAAA,iBAAAK,GAAA;UAAA,OAAAN,KAAA,CAAAjC,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAuC,sBAAA,WAAAA,uBAAAhE,GAAA;MAAA,IAAAiE,MAAA;MAAA,OAAAlG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiG,SAAA;QAAA,IAAA/F,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7F,IAAA,GAAA6F,SAAA,CAAA5F,IAAA;YAAA;cAAA4F,SAAA,CAAA7F,IAAA;cAAA6F,SAAA,CAAA5F,IAAA;cAAA,OAEA3E,oBAAA,CAAAmG,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAAV,YAAA;YAAA;cAAAnB,GAAA,GAAAiG,SAAA,CAAA3F,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAuF,MAAA,CAAArF,QAAA,CAAA0B,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAAV,YAAA;cACA;gBACAU,GAAA,CAAAV,YAAA,GAAAU,GAAA,CAAAV,YAAA;gBACA2E,MAAA,CAAArF,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAsF,SAAA,CAAA5F,IAAA;cAAA;YAAA;cAAA4F,SAAA,CAAA7F,IAAA;cAAA6F,SAAA,CAAArF,EAAA,GAAAqF,SAAA;cAEApE,GAAA,CAAAV,YAAA,GAAAU,GAAA,CAAAV,YAAA;cACA2E,MAAA,CAAArF,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAuF,SAAA,CAAAlF,IAAA;UAAA;QAAA,GAAAgF,QAAA;MAAA;IAEA;IAEA;IACAG,YAAA,WAAAA,aAAArE,GAAA;MAAA,IAAAsE,OAAA;MACA,KAAAtC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAArE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsG,UAAA;QAAA,IAAApG,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAoG,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlG,IAAA,GAAAkG,UAAA,CAAAjG,IAAA;YAAA;cAAAiG,UAAA,CAAAlG,IAAA;cAAAkG,UAAA,CAAAjG,IAAA;cAAA,OAEA1E,UAAA,CAAAkG,GAAA,CAAAK,EAAA;YAAA;cAAAlC,GAAA,GAAAsG,UAAA,CAAAhG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA4F,OAAA,CAAA1F,QAAA,CAAA0B,OAAA;gBACAgE,OAAA,CAAA9G,OAAA;cACA;gBACA8G,OAAA,CAAA1F,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA2F,UAAA,CAAAjG,IAAA;cAAA;YAAA;cAAAiG,UAAA,CAAAlG,IAAA;cAAAkG,UAAA,CAAA1F,EAAA,GAAA0F,UAAA;cAEAH,OAAA,CAAA1F,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA4F,UAAA,CAAAvF,IAAA;UAAA;QAAA,GAAAqF,SAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IAEAG,gBAAA,WAAAA,iBAAA1E,GAAA;MAAA,IAAA2E,OAAA;MAAA,OAAA5G,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2G,UAAA;QAAA,IAAAzG,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAyG,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvG,IAAA,GAAAuG,UAAA,CAAAtG,IAAA;YAAA;cACAmG,OAAA,CAAAvH,mBAAA;cACAuH,OAAA,CAAArH,aAAA;cAAAwH,UAAA,CAAAvG,IAAA;cAAAuG,UAAA,CAAAtG,IAAA;cAAA,OAEAzE,cAAA;gBAAA+J,MAAA,EAAA9D,GAAA,CAAAK,EAAA;gBAAA0E,OAAA;gBAAAC,QAAA;cAAA;YAAA;cAAA7G,GAAA,GAAA2G,UAAA,CAAArG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAA/D,IAAA,IAAA+D,GAAA,CAAA/D,IAAA,CAAAuE,OAAA;gBACAgG,OAAA,CAAAtH,UAAA,GAAAc,GAAA,CAAA/D,IAAA,CAAAuE,OAAA;cACA;gBACAgG,OAAA,CAAAtH,UAAA;cACA;cAAAyH,UAAA,CAAAtG,IAAA;cAAA;YAAA;cAAAsG,UAAA,CAAAvG,IAAA;cAAAuG,UAAA,CAAA/F,EAAA,GAAA+F,UAAA;cAEAH,OAAA,CAAAtH,UAAA;cACAsH,OAAA,CAAA/F,QAAA,CAAAC,KAAA;YAAA;cAEA8F,OAAA,CAAArH,aAAA;YAAA;YAAA;cAAA,OAAAwH,UAAA,CAAA5F,IAAA;UAAA;QAAA,GAAA0F,SAAA;MAAA;IACA;IAEAK,aAAA,WAAAA,cAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAA,IAAA,CAAAtH,MAAA,eAAAsH,IAAA;MACA,OAAAA,IAAA,CAAAC,KAAA,iBAAAD,IAAA,CAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}