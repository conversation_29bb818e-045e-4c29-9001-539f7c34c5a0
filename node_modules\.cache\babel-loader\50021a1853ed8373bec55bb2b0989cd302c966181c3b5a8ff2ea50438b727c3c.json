{"ast": null, "code": "var _typeof = require(\"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\n//! moment.js locale configuration\n//! locale : Uzbek Latin [uz-latn]\n//! author : <PERSON><PERSON><PERSON> : github.com/Rasulbeeek\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var uzLatn = moment.defineLocale('uz-latn', {\n    months: 'Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr'.split('_'),\n    monthsShort: 'Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek'.split('_'),\n    weekdays: 'Yakshan<PERSON>_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba'.split('_'),\n    weekdaysShort: 'Yak_Dush_Sesh_Chor_Pay_Jum_Shan'.split('_'),\n    weekdaysMin: 'Ya_Du_Se_Cho_Pa_Ju_Sha'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'D MMMM YYYY, dddd HH:mm'\n    },\n    calendar: {\n      sameDay: '[Bugun soat] LT [da]',\n      nextDay: '[Ertaga] LT [da]',\n      nextWeek: 'dddd [kuni soat] LT [da]',\n      lastDay: '[Kecha soat] LT [da]',\n      lastWeek: \"[O'tgan] dddd [kuni soat] LT [da]\",\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'Yaqin %s ichida',\n      past: 'Bir necha %s oldin',\n      s: 'soniya',\n      ss: '%d soniya',\n      m: 'bir daqiqa',\n      mm: '%d daqiqa',\n      h: 'bir soat',\n      hh: '%d soat',\n      d: 'bir kun',\n      dd: '%d kun',\n      M: 'bir oy',\n      MM: '%d oy',\n      y: 'bir yil',\n      yy: '%d yil'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return uzLatn;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "uzLatn", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["G:/备份9/adminweb/node_modules/moment/locale/uz-latn.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Uzbek Latin [uz-latn]\n//! author : <PERSON><PERSON><PERSON> : github.com/Rasulbeeek\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var uzLatn = moment.defineLocale('uz-latn', {\n        months: 'Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr'.split(\n            '_'\n        ),\n        monthsShort: 'Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek'.split('_'),\n        weekdays:\n            'Yakshan<PERSON>_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shan<PERSON>'.split(\n                '_'\n            ),\n        weekdaysShort: 'Ya<PERSON>_Du<PERSON>_<PERSON><PERSON>_Chor_Pay_Jum_Shan'.split('_'),\n        weekdaysMin: 'Ya_Du_Se_Cho_Pa_Ju_Sha'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'D MMMM YYYY, dddd HH:mm',\n        },\n        calendar: {\n            sameDay: '[Bugun soat] LT [da]',\n            nextDay: '[Ertaga] LT [da]',\n            nextWeek: 'dddd [kuni soat] LT [da]',\n            lastDay: '[Kecha soat] LT [da]',\n            lastWeek: \"[O'tgan] dddd [kuni soat] LT [da]\",\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'Yaqin %s ichida',\n            past: 'Bir necha %s oldin',\n            s: 'soniya',\n            ss: '%d soniya',\n            m: 'bir daqiqa',\n            mm: '%d daqiqa',\n            h: 'bir soat',\n            hh: '%d soat',\n            d: 'bir kun',\n            dd: '%d kun',\n            M: 'bir oy',\n            MM: '%d oy',\n            y: 'bir yil',\n            yy: '%d yil',\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return uzLatn;\n\n})));\n"], "mappings": ";AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,MAAM,GAAGD,MAAM,CAACE,YAAY,CAAC,SAAS,EAAE;IACxCC,MAAM,EAAE,4EAA4E,CAACC,KAAK,CACtF,GACJ,CAAC;IACDC,WAAW,EAAE,mDAAmD,CAACD,KAAK,CAAC,GAAG,CAAC;IAC3EE,QAAQ,EACJ,8DAA8D,CAACF,KAAK,CAChE,GACJ,CAAC;IACLG,aAAa,EAAE,iCAAiC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC3DI,WAAW,EAAE,wBAAwB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAChDK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,sBAAsB;MAC/BC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,0BAA0B;MACpCC,OAAO,EAAE,sBAAsB;MAC/BC,QAAQ,EAAE,mCAAmC;MAC7CC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,iBAAiB;MACzBC,IAAI,EAAE,oBAAoB;MAC1BC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOvC,MAAM;AAEjB,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}