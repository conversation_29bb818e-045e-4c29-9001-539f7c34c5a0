{"ast": null, "code": "import \"core-js/modules/es.array.fill.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport { DEFAULT_PATH_STYLE } from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineDash } from '../canvas/dashStyle.js';\nimport { map } from '../core/util.js';\nimport { normalizeColor } from './helper.js';\nvar NONE = 'none';\nvar mathRound = Math.round;\nfunction pathHasFill(style) {\n  var fill = style.fill;\n  return fill != null && fill !== NONE;\n}\nfunction pathHasStroke(style) {\n  var stroke = style.stroke;\n  return stroke != null && stroke !== NONE;\n}\nvar strokeProps = ['lineCap', 'miterLimit', 'lineJoin'];\nvar svgStrokeProps = map(strokeProps, function (prop) {\n  return \"stroke-\" + prop.toLowerCase();\n});\nexport default function mapStyleToAttrs(updateAttr, style, el, forceUpdate) {\n  var opacity = style.opacity == null ? 1 : style.opacity;\n  if (el instanceof ZRImage) {\n    updateAttr('opacity', opacity);\n    return;\n  }\n  if (pathHasFill(style)) {\n    var fill = normalizeColor(style.fill);\n    updateAttr('fill', fill.color);\n    var fillOpacity = style.fillOpacity != null ? style.fillOpacity * fill.opacity * opacity : fill.opacity * opacity;\n    if (forceUpdate || fillOpacity < 1) {\n      updateAttr('fill-opacity', fillOpacity);\n    }\n  } else {\n    updateAttr('fill', NONE);\n  }\n  if (pathHasStroke(style)) {\n    var stroke = normalizeColor(style.stroke);\n    updateAttr('stroke', stroke.color);\n    var strokeScale = style.strokeNoScale ? el.getLineScale() : 1;\n    var strokeWidth = strokeScale ? (style.lineWidth || 0) / strokeScale : 0;\n    var strokeOpacity = style.strokeOpacity != null ? style.strokeOpacity * stroke.opacity * opacity : stroke.opacity * opacity;\n    var strokeFirst = style.strokeFirst;\n    if (forceUpdate || strokeWidth !== 1) {\n      updateAttr('stroke-width', strokeWidth);\n    }\n    if (forceUpdate || strokeFirst) {\n      updateAttr('paint-order', strokeFirst ? 'stroke' : 'fill');\n    }\n    if (forceUpdate || strokeOpacity < 1) {\n      updateAttr('stroke-opacity', strokeOpacity);\n    }\n    if (style.lineDash) {\n      var _a = getLineDash(el),\n        lineDash = _a[0],\n        lineDashOffset = _a[1];\n      if (lineDash) {\n        lineDashOffset = mathRound(lineDashOffset || 0);\n        updateAttr('stroke-dasharray', lineDash.join(','));\n        if (lineDashOffset || forceUpdate) {\n          updateAttr('stroke-dashoffset', lineDashOffset);\n        }\n      }\n    } else if (forceUpdate) {\n      updateAttr('stroke-dasharray', NONE);\n    }\n    for (var i = 0; i < strokeProps.length; i++) {\n      var propName = strokeProps[i];\n      if (forceUpdate || style[propName] !== DEFAULT_PATH_STYLE[propName]) {\n        var val = style[propName] || DEFAULT_PATH_STYLE[propName];\n        val && updateAttr(svgStrokeProps[i], val);\n      }\n    }\n  } else if (forceUpdate) {\n    updateAttr('stroke', NONE);\n  }\n}", "map": {"version": 3, "names": ["DEFAULT_PATH_STYLE", "ZRImage", "getLineDash", "map", "normalizeColor", "NONE", "mathRound", "Math", "round", "pathHasFill", "style", "fill", "pathHasStroke", "stroke", "strokeProps", "svgStrokeProps", "prop", "toLowerCase", "mapStyleToAttrs", "updateAttr", "el", "forceUpdate", "opacity", "color", "fillOpacity", "strokeScale", "strokeNoScale", "getLineScale", "strokeWidth", "lineWidth", "strokeOpacity", "<PERSON><PERSON><PERSON><PERSON>", "lineDash", "_a", "lineDashOffset", "join", "i", "length", "propName", "val"], "sources": ["E:/最新的代码/adminweb/node_modules/zrender/lib/svg/mapStyleToAttrs.js"], "sourcesContent": ["import { DEFAULT_PATH_STYLE } from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineDash } from '../canvas/dashStyle.js';\nimport { map } from '../core/util.js';\nimport { normalizeColor } from './helper.js';\nvar NONE = 'none';\nvar mathRound = Math.round;\nfunction pathHasFill(style) {\n    var fill = style.fill;\n    return fill != null && fill !== NONE;\n}\nfunction pathHasStroke(style) {\n    var stroke = style.stroke;\n    return stroke != null && stroke !== NONE;\n}\nvar strokeProps = ['lineCap', 'miterLimit', 'lineJoin'];\nvar svgStrokeProps = map(strokeProps, function (prop) { return \"stroke-\" + prop.toLowerCase(); });\nexport default function mapStyleToAttrs(updateAttr, style, el, forceUpdate) {\n    var opacity = style.opacity == null ? 1 : style.opacity;\n    if (el instanceof ZRImage) {\n        updateAttr('opacity', opacity);\n        return;\n    }\n    if (pathHasFill(style)) {\n        var fill = normalizeColor(style.fill);\n        updateAttr('fill', fill.color);\n        var fillOpacity = style.fillOpacity != null\n            ? style.fillOpacity * fill.opacity * opacity\n            : fill.opacity * opacity;\n        if (forceUpdate || fillOpacity < 1) {\n            updateAttr('fill-opacity', fillOpacity);\n        }\n    }\n    else {\n        updateAttr('fill', NONE);\n    }\n    if (pathHasStroke(style)) {\n        var stroke = normalizeColor(style.stroke);\n        updateAttr('stroke', stroke.color);\n        var strokeScale = style.strokeNoScale\n            ? el.getLineScale()\n            : 1;\n        var strokeWidth = (strokeScale ? (style.lineWidth || 0) / strokeScale : 0);\n        var strokeOpacity = style.strokeOpacity != null\n            ? style.strokeOpacity * stroke.opacity * opacity\n            : stroke.opacity * opacity;\n        var strokeFirst = style.strokeFirst;\n        if (forceUpdate || strokeWidth !== 1) {\n            updateAttr('stroke-width', strokeWidth);\n        }\n        if (forceUpdate || strokeFirst) {\n            updateAttr('paint-order', strokeFirst ? 'stroke' : 'fill');\n        }\n        if (forceUpdate || strokeOpacity < 1) {\n            updateAttr('stroke-opacity', strokeOpacity);\n        }\n        if (style.lineDash) {\n            var _a = getLineDash(el), lineDash = _a[0], lineDashOffset = _a[1];\n            if (lineDash) {\n                lineDashOffset = mathRound(lineDashOffset || 0);\n                updateAttr('stroke-dasharray', lineDash.join(','));\n                if (lineDashOffset || forceUpdate) {\n                    updateAttr('stroke-dashoffset', lineDashOffset);\n                }\n            }\n        }\n        else if (forceUpdate) {\n            updateAttr('stroke-dasharray', NONE);\n        }\n        for (var i = 0; i < strokeProps.length; i++) {\n            var propName = strokeProps[i];\n            if (forceUpdate || style[propName] !== DEFAULT_PATH_STYLE[propName]) {\n                var val = style[propName] || DEFAULT_PATH_STYLE[propName];\n                val && updateAttr(svgStrokeProps[i], val);\n            }\n        }\n    }\n    else if (forceUpdate) {\n        updateAttr('stroke', NONE);\n    }\n}\n"], "mappings": ";;AAAA,SAASA,kBAAkB,QAAQ,oBAAoB;AACvD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,cAAc,QAAQ,aAAa;AAC5C,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK;AAC1B,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;EACrB,OAAOA,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAKN,IAAI;AACxC;AACA,SAASO,aAAaA,CAACF,KAAK,EAAE;EAC1B,IAAIG,MAAM,GAAGH,KAAK,CAACG,MAAM;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAKR,IAAI;AAC5C;AACA,IAAIS,WAAW,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC;AACvD,IAAIC,cAAc,GAAGZ,GAAG,CAACW,WAAW,EAAE,UAAUE,IAAI,EAAE;EAAE,OAAO,SAAS,GAAGA,IAAI,CAACC,WAAW,CAAC,CAAC;AAAE,CAAC,CAAC;AACjG,eAAe,SAASC,eAAeA,CAACC,UAAU,EAAET,KAAK,EAAEU,EAAE,EAAEC,WAAW,EAAE;EACxE,IAAIC,OAAO,GAAGZ,KAAK,CAACY,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGZ,KAAK,CAACY,OAAO;EACvD,IAAIF,EAAE,YAAYnB,OAAO,EAAE;IACvBkB,UAAU,CAAC,SAAS,EAAEG,OAAO,CAAC;IAC9B;EACJ;EACA,IAAIb,WAAW,CAACC,KAAK,CAAC,EAAE;IACpB,IAAIC,IAAI,GAAGP,cAAc,CAACM,KAAK,CAACC,IAAI,CAAC;IACrCQ,UAAU,CAAC,MAAM,EAAER,IAAI,CAACY,KAAK,CAAC;IAC9B,IAAIC,WAAW,GAAGd,KAAK,CAACc,WAAW,IAAI,IAAI,GACrCd,KAAK,CAACc,WAAW,GAAGb,IAAI,CAACW,OAAO,GAAGA,OAAO,GAC1CX,IAAI,CAACW,OAAO,GAAGA,OAAO;IAC5B,IAAID,WAAW,IAAIG,WAAW,GAAG,CAAC,EAAE;MAChCL,UAAU,CAAC,cAAc,EAAEK,WAAW,CAAC;IAC3C;EACJ,CAAC,MACI;IACDL,UAAU,CAAC,MAAM,EAAEd,IAAI,CAAC;EAC5B;EACA,IAAIO,aAAa,CAACF,KAAK,CAAC,EAAE;IACtB,IAAIG,MAAM,GAAGT,cAAc,CAACM,KAAK,CAACG,MAAM,CAAC;IACzCM,UAAU,CAAC,QAAQ,EAAEN,MAAM,CAACU,KAAK,CAAC;IAClC,IAAIE,WAAW,GAAGf,KAAK,CAACgB,aAAa,GAC/BN,EAAE,CAACO,YAAY,CAAC,CAAC,GACjB,CAAC;IACP,IAAIC,WAAW,GAAIH,WAAW,GAAG,CAACf,KAAK,CAACmB,SAAS,IAAI,CAAC,IAAIJ,WAAW,GAAG,CAAE;IAC1E,IAAIK,aAAa,GAAGpB,KAAK,CAACoB,aAAa,IAAI,IAAI,GACzCpB,KAAK,CAACoB,aAAa,GAAGjB,MAAM,CAACS,OAAO,GAAGA,OAAO,GAC9CT,MAAM,CAACS,OAAO,GAAGA,OAAO;IAC9B,IAAIS,WAAW,GAAGrB,KAAK,CAACqB,WAAW;IACnC,IAAIV,WAAW,IAAIO,WAAW,KAAK,CAAC,EAAE;MAClCT,UAAU,CAAC,cAAc,EAAES,WAAW,CAAC;IAC3C;IACA,IAAIP,WAAW,IAAIU,WAAW,EAAE;MAC5BZ,UAAU,CAAC,aAAa,EAAEY,WAAW,GAAG,QAAQ,GAAG,MAAM,CAAC;IAC9D;IACA,IAAIV,WAAW,IAAIS,aAAa,GAAG,CAAC,EAAE;MAClCX,UAAU,CAAC,gBAAgB,EAAEW,aAAa,CAAC;IAC/C;IACA,IAAIpB,KAAK,CAACsB,QAAQ,EAAE;MAChB,IAAIC,EAAE,GAAG/B,WAAW,CAACkB,EAAE,CAAC;QAAEY,QAAQ,GAAGC,EAAE,CAAC,CAAC,CAAC;QAAEC,cAAc,GAAGD,EAAE,CAAC,CAAC,CAAC;MAClE,IAAID,QAAQ,EAAE;QACVE,cAAc,GAAG5B,SAAS,CAAC4B,cAAc,IAAI,CAAC,CAAC;QAC/Cf,UAAU,CAAC,kBAAkB,EAAEa,QAAQ,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,IAAID,cAAc,IAAIb,WAAW,EAAE;UAC/BF,UAAU,CAAC,mBAAmB,EAAEe,cAAc,CAAC;QACnD;MACJ;IACJ,CAAC,MACI,IAAIb,WAAW,EAAE;MAClBF,UAAU,CAAC,kBAAkB,EAAEd,IAAI,CAAC;IACxC;IACA,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,WAAW,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAIE,QAAQ,GAAGxB,WAAW,CAACsB,CAAC,CAAC;MAC7B,IAAIf,WAAW,IAAIX,KAAK,CAAC4B,QAAQ,CAAC,KAAKtC,kBAAkB,CAACsC,QAAQ,CAAC,EAAE;QACjE,IAAIC,GAAG,GAAG7B,KAAK,CAAC4B,QAAQ,CAAC,IAAItC,kBAAkB,CAACsC,QAAQ,CAAC;QACzDC,GAAG,IAAIpB,UAAU,CAACJ,cAAc,CAACqB,CAAC,CAAC,EAAEG,GAAG,CAAC;MAC7C;IACJ;EACJ,CAAC,MACI,IAAIlB,WAAW,EAAE;IAClBF,UAAU,CAAC,QAAQ,EAAEd,IAAI,CAAC;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}