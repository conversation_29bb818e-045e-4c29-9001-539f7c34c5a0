{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.index-of.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.match.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, isObject, isArray, createHashMap, map, assert, isString, indexOf, isStringSafe, isNumber } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { isNumeric, getRandomIdBase, getPrecision, round } from './number.js';\nimport { warn } from './log.js';\nfunction interpolateNumber(p0, p1, percent) {\n  return (p1 - p0) * percent + p0;\n}\n/**\n * Make the name displayable. But we should\n * make sure it is not duplicated with user\n * specified name, so use '\\0';\n */\nvar DUMMY_COMPONENT_NAME_PREFIX = 'series\\0';\nvar INTERNAL_COMPONENT_ID_PREFIX = '\\0_ec_\\0';\n/**\n * If value is not array, then translate it to array.\n * @param  {*} value\n * @return {Array} [value] or value\n */\nexport function normalizeToArray(value) {\n  return value instanceof Array ? value : value == null ? [] : [value];\n}\n/**\n * Sync default option between normal and emphasis like `position` and `show`\n * In case some one will write code like\n *     label: {\n *          show: false,\n *          position: 'outside',\n *          fontSize: 18\n *     },\n *     emphasis: {\n *          label: { show: true }\n *     }\n */\nexport function defaultEmphasis(opt, key, subOpts) {\n  // Caution: performance sensitive.\n  if (opt) {\n    opt[key] = opt[key] || {};\n    opt.emphasis = opt.emphasis || {};\n    opt.emphasis[key] = opt.emphasis[key] || {};\n    // Default emphasis option from normal\n    for (var i = 0, len = subOpts.length; i < len; i++) {\n      var subOptName = subOpts[i];\n      if (!opt.emphasis[key].hasOwnProperty(subOptName) && opt[key].hasOwnProperty(subOptName)) {\n        opt.emphasis[key][subOptName] = opt[key][subOptName];\n      }\n    }\n  }\n}\nexport var TEXT_STYLE_OPTIONS = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'rich', 'tag', 'color', 'textBorderColor', 'textBorderWidth', 'width', 'height', 'lineHeight', 'align', 'verticalAlign', 'baseline', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY', 'textShadowColor', 'textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY', 'backgroundColor', 'borderColor', 'borderWidth', 'borderRadius', 'padding'];\n// modelUtil.LABEL_OPTIONS = modelUtil.TEXT_STYLE_OPTIONS.concat([\n//     'position', 'offset', 'rotate', 'origin', 'show', 'distance', 'formatter',\n//     'fontStyle', 'fontWeight', 'fontSize', 'fontFamily',\n//     // FIXME: deprecated, check and remove it.\n//     'textStyle'\n// ]);\n/**\n * The method does not ensure performance.\n * data could be [12, 2323, {value: 223}, [1221, 23], {value: [2, 23]}]\n * This helper method retrieves value from data.\n */\nexport function getDataItemValue(dataItem) {\n  return isObject(dataItem) && !isArray(dataItem) && !(dataItem instanceof Date) ? dataItem.value : dataItem;\n}\n/**\n * data could be [12, 2323, {value: 223}, [1221, 23], {value: [2, 23]}]\n * This helper method determine if dataItem has extra option besides value\n */\nexport function isDataItemOption(dataItem) {\n  return isObject(dataItem) && !(dataItem instanceof Array);\n  // // markLine data can be array\n  // && !(dataItem[0] && isObject(dataItem[0]) && !(dataItem[0] instanceof Array));\n}\n;\n/**\n * Mapping to existings for merge.\n *\n * Mode \"normalMege\":\n *     The mapping result (merge result) will keep the order of the existing\n *     component, rather than the order of new option. Because we should ensure\n *     some specified index reference (like xAxisIndex) keep work.\n *     And in most cases, \"merge option\" is used to update partial option but not\n *     be expected to change the order.\n *\n * Mode \"replaceMege\":\n *     (1) Only the id mapped components will be merged.\n *     (2) Other existing components (except internal components) will be removed.\n *     (3) Other new options will be used to create new component.\n *     (4) The index of the existing components will not be modified.\n *     That means their might be \"hole\" after the removal.\n *     The new components are created first at those available index.\n *\n * Mode \"replaceAll\":\n *     This mode try to support that reproduce an echarts instance from another\n *     echarts instance (via `getOption`) in some simple cases.\n *     In this scenario, the `result` index are exactly the consistent with the `newCmptOptions`,\n *     which ensures the component index referring (like `xAxisIndex: ?`) corrent. That is,\n *     the \"hole\" in `newCmptOptions` will also be kept.\n *     On the contrary, other modes try best to eliminate holes.\n *     PENDING: This is an experimental mode yet.\n *\n * @return See the comment of <MappingResult>.\n */\nexport function mappingToExists(existings, newCmptOptions, mode) {\n  var isNormalMergeMode = mode === 'normalMerge';\n  var isReplaceMergeMode = mode === 'replaceMerge';\n  var isReplaceAllMode = mode === 'replaceAll';\n  existings = existings || [];\n  newCmptOptions = (newCmptOptions || []).slice();\n  var existingIdIdxMap = createHashMap();\n  // Validate id and name on user input option.\n  each(newCmptOptions, function (cmptOption, index) {\n    if (!isObject(cmptOption)) {\n      newCmptOptions[index] = null;\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      // There is some legacy case that name is set as `false`.\n      // But should work normally rather than throw error.\n      if (cmptOption.id != null && !isValidIdOrName(cmptOption.id)) {\n        warnInvalidateIdOrName(cmptOption.id);\n      }\n      if (cmptOption.name != null && !isValidIdOrName(cmptOption.name)) {\n        warnInvalidateIdOrName(cmptOption.name);\n      }\n    }\n  });\n  var result = prepareResult(existings, existingIdIdxMap, mode);\n  if (isNormalMergeMode || isReplaceMergeMode) {\n    mappingById(result, existings, existingIdIdxMap, newCmptOptions);\n  }\n  if (isNormalMergeMode) {\n    mappingByName(result, newCmptOptions);\n  }\n  if (isNormalMergeMode || isReplaceMergeMode) {\n    mappingByIndex(result, newCmptOptions, isReplaceMergeMode);\n  } else if (isReplaceAllMode) {\n    mappingInReplaceAllMode(result, newCmptOptions);\n  }\n  makeIdAndName(result);\n  // The array `result` MUST NOT contain elided items, otherwise the\n  // forEach will omit those items and result in incorrect result.\n  return result;\n}\nfunction prepareResult(existings, existingIdIdxMap, mode) {\n  var result = [];\n  if (mode === 'replaceAll') {\n    return result;\n  }\n  // Do not use native `map` to in case that the array `existings`\n  // contains elided items, which will be omitted.\n  for (var index = 0; index < existings.length; index++) {\n    var existing = existings[index];\n    // Because of replaceMerge, `existing` may be null/undefined.\n    if (existing && existing.id != null) {\n      existingIdIdxMap.set(existing.id, index);\n    }\n    // For non-internal-componnets:\n    //     Mode \"normalMerge\": all existings kept.\n    //     Mode \"replaceMerge\": all existing removed unless mapped by id.\n    // For internal-components:\n    //     go with \"replaceMerge\" approach in both mode.\n    result.push({\n      existing: mode === 'replaceMerge' || isComponentIdInternal(existing) ? null : existing,\n      newOption: null,\n      keyInfo: null,\n      brandNew: null\n    });\n  }\n  return result;\n}\nfunction mappingById(result, existings, existingIdIdxMap, newCmptOptions) {\n  // Mapping by id if specified.\n  each(newCmptOptions, function (cmptOption, index) {\n    if (!cmptOption || cmptOption.id == null) {\n      return;\n    }\n    var optionId = makeComparableKey(cmptOption.id);\n    var existingIdx = existingIdIdxMap.get(optionId);\n    if (existingIdx != null) {\n      var resultItem = result[existingIdx];\n      assert(!resultItem.newOption, 'Duplicated option on id \"' + optionId + '\".');\n      resultItem.newOption = cmptOption;\n      // In both mode, if id matched, new option will be merged to\n      // the existings rather than creating new component model.\n      resultItem.existing = existings[existingIdx];\n      newCmptOptions[index] = null;\n    }\n  });\n}\nfunction mappingByName(result, newCmptOptions) {\n  // Mapping by name if specified.\n  each(newCmptOptions, function (cmptOption, index) {\n    if (!cmptOption || cmptOption.name == null) {\n      return;\n    }\n    for (var i = 0; i < result.length; i++) {\n      var existing = result[i].existing;\n      if (!result[i].newOption // Consider name: two map to one.\n      // Can not match when both ids existing but different.\n      && existing && (existing.id == null || cmptOption.id == null) && !isComponentIdInternal(cmptOption) && !isComponentIdInternal(existing) && keyExistAndEqual('name', existing, cmptOption)) {\n        result[i].newOption = cmptOption;\n        newCmptOptions[index] = null;\n        return;\n      }\n    }\n  });\n}\nfunction mappingByIndex(result, newCmptOptions, brandNew) {\n  each(newCmptOptions, function (cmptOption) {\n    if (!cmptOption) {\n      return;\n    }\n    // Find the first place that not mapped by id and not internal component (consider the \"hole\").\n    var resultItem;\n    var nextIdx = 0;\n    while (\n    // Be `!resultItem` only when `nextIdx >= result.length`.\n    (resultItem = result[nextIdx]\n    // (1) Existing models that already have id should be able to mapped to. Because\n    // after mapping performed, model will always be assigned with an id if user not given.\n    // After that all models have id.\n    // (2) If new option has id, it can only set to a hole or append to the last. It should\n    // not be merged to the existings with different id. Because id should not be overwritten.\n    // (3) Name can be overwritten, because axis use name as 'show label text'.\n    ) && (resultItem.newOption || isComponentIdInternal(resultItem.existing) ||\n    // In mode \"replaceMerge\", here no not-mapped-non-internal-existing.\n    resultItem.existing && cmptOption.id != null && !keyExistAndEqual('id', cmptOption, resultItem.existing))) {\n      nextIdx++;\n    }\n    if (resultItem) {\n      resultItem.newOption = cmptOption;\n      resultItem.brandNew = brandNew;\n    } else {\n      result.push({\n        newOption: cmptOption,\n        brandNew: brandNew,\n        existing: null,\n        keyInfo: null\n      });\n    }\n    nextIdx++;\n  });\n}\nfunction mappingInReplaceAllMode(result, newCmptOptions) {\n  each(newCmptOptions, function (cmptOption) {\n    // The feature \"reproduce\" requires \"hole\" will also reproduced\n    // in case that component index referring are broken.\n    result.push({\n      newOption: cmptOption,\n      brandNew: true,\n      existing: null,\n      keyInfo: null\n    });\n  });\n}\n/**\n * Make id and name for mapping result (result of mappingToExists)\n * into `keyInfo` field.\n */\nfunction makeIdAndName(mapResult) {\n  // We use this id to hash component models and view instances\n  // in echarts. id can be specified by user, or auto generated.\n  // The id generation rule ensures new view instance are able\n  // to mapped to old instance when setOption are called in\n  // no-merge mode. So we generate model id by name and plus\n  // type in view id.\n  // name can be duplicated among components, which is convenient\n  // to specify multi components (like series) by one name.\n  // Ensure that each id is distinct.\n  var idMap = createHashMap();\n  each(mapResult, function (item) {\n    var existing = item.existing;\n    existing && idMap.set(existing.id, item);\n  });\n  each(mapResult, function (item) {\n    var opt = item.newOption;\n    // Force ensure id not duplicated.\n    assert(!opt || opt.id == null || !idMap.get(opt.id) || idMap.get(opt.id) === item, 'id duplicates: ' + (opt && opt.id));\n    opt && opt.id != null && idMap.set(opt.id, item);\n    !item.keyInfo && (item.keyInfo = {});\n  });\n  // Make name and id.\n  each(mapResult, function (item, index) {\n    var existing = item.existing;\n    var opt = item.newOption;\n    var keyInfo = item.keyInfo;\n    if (!isObject(opt)) {\n      return;\n    }\n    // Name can be overwritten. Consider case: axis.name = '20km'.\n    // But id generated by name will not be changed, which affect\n    // only in that case: setOption with 'not merge mode' and view\n    // instance will be recreated, which can be accepted.\n    keyInfo.name = opt.name != null ? makeComparableKey(opt.name) : existing ? existing.name\n    // Avoid that different series has the same name,\n    // because name may be used like in color pallet.\n    : DUMMY_COMPONENT_NAME_PREFIX + index;\n    if (existing) {\n      keyInfo.id = makeComparableKey(existing.id);\n    } else if (opt.id != null) {\n      keyInfo.id = makeComparableKey(opt.id);\n    } else {\n      // Consider this situatoin:\n      //  optionA: [{name: 'a'}, {name: 'a'}, {..}]\n      //  optionB [{..}, {name: 'a'}, {name: 'a'}]\n      // Series with the same name between optionA and optionB\n      // should be mapped.\n      var idNum = 0;\n      do {\n        keyInfo.id = '\\0' + keyInfo.name + '\\0' + idNum++;\n      } while (idMap.get(keyInfo.id));\n    }\n    idMap.set(keyInfo.id, item);\n  });\n}\nfunction keyExistAndEqual(attr, obj1, obj2) {\n  var key1 = convertOptionIdName(obj1[attr], null);\n  var key2 = convertOptionIdName(obj2[attr], null);\n  // See `MappingExistingItem`. `id` and `name` trade string equals to number.\n  return key1 != null && key2 != null && key1 === key2;\n}\n/**\n * @return return null if not exist.\n */\nfunction makeComparableKey(val) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (val == null) {\n      throw new Error();\n    }\n  }\n  return convertOptionIdName(val, '');\n}\nexport function convertOptionIdName(idOrName, defaultValue) {\n  if (idOrName == null) {\n    return defaultValue;\n  }\n  return isString(idOrName) ? idOrName : isNumber(idOrName) || isStringSafe(idOrName) ? idOrName + '' : defaultValue;\n}\nfunction warnInvalidateIdOrName(idOrName) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn('`' + idOrName + '` is invalid id or name. Must be a string or number.');\n  }\n}\nfunction isValidIdOrName(idOrName) {\n  return isStringSafe(idOrName) || isNumeric(idOrName);\n}\nexport function isNameSpecified(componentModel) {\n  var name = componentModel.name;\n  // Is specified when `indexOf` get -1 or > 0.\n  return !!(name && name.indexOf(DUMMY_COMPONENT_NAME_PREFIX));\n}\n/**\n * @public\n * @param {Object} cmptOption\n * @return {boolean}\n */\nexport function isComponentIdInternal(cmptOption) {\n  return cmptOption && cmptOption.id != null && makeComparableKey(cmptOption.id).indexOf(INTERNAL_COMPONENT_ID_PREFIX) === 0;\n}\nexport function makeInternalComponentId(idSuffix) {\n  return INTERNAL_COMPONENT_ID_PREFIX + idSuffix;\n}\nexport function setComponentTypeToKeyInfo(mappingResult, mainType, componentModelCtor) {\n  // Set mainType and complete subType.\n  each(mappingResult, function (item) {\n    var newOption = item.newOption;\n    if (isObject(newOption)) {\n      item.keyInfo.mainType = mainType;\n      item.keyInfo.subType = determineSubType(mainType, newOption, item.existing, componentModelCtor);\n    }\n  });\n}\nfunction determineSubType(mainType, newCmptOption, existComponent, componentModelCtor) {\n  var subType = newCmptOption.type ? newCmptOption.type : existComponent ? existComponent.subType\n  // Use determineSubType only when there is no existComponent.\n  : componentModelCtor.determineSubType(mainType, newCmptOption);\n  // tooltip, markline, markpoint may always has no subType\n  return subType;\n}\n/**\n * A helper for removing duplicate items between batchA and batchB,\n * and in themselves, and categorize by series.\n *\n * @param batchA Like: [{seriesId: 2, dataIndex: [32, 4, 5]}, ...]\n * @param batchB Like: [{seriesId: 2, dataIndex: [32, 4, 5]}, ...]\n * @return result: [resultBatchA, resultBatchB]\n */\nexport function compressBatches(batchA, batchB) {\n  var mapA = {};\n  var mapB = {};\n  makeMap(batchA || [], mapA);\n  makeMap(batchB || [], mapB, mapA);\n  return [mapToArray(mapA), mapToArray(mapB)];\n  function makeMap(sourceBatch, map, otherMap) {\n    for (var i = 0, len = sourceBatch.length; i < len; i++) {\n      var seriesId = convertOptionIdName(sourceBatch[i].seriesId, null);\n      if (seriesId == null) {\n        return;\n      }\n      var dataIndices = normalizeToArray(sourceBatch[i].dataIndex);\n      var otherDataIndices = otherMap && otherMap[seriesId];\n      for (var j = 0, lenj = dataIndices.length; j < lenj; j++) {\n        var dataIndex = dataIndices[j];\n        if (otherDataIndices && otherDataIndices[dataIndex]) {\n          otherDataIndices[dataIndex] = null;\n        } else {\n          (map[seriesId] || (map[seriesId] = {}))[dataIndex] = 1;\n        }\n      }\n    }\n  }\n  function mapToArray(map, isData) {\n    var result = [];\n    for (var i in map) {\n      if (map.hasOwnProperty(i) && map[i] != null) {\n        if (isData) {\n          result.push(+i);\n        } else {\n          var dataIndices = mapToArray(map[i], true);\n          dataIndices.length && result.push({\n            seriesId: i,\n            dataIndex: dataIndices\n          });\n        }\n      }\n    }\n    return result;\n  }\n}\n/**\n * @param payload Contains dataIndex (means rawIndex) / dataIndexInside / name\n *                         each of which can be Array or primary type.\n * @return dataIndex If not found, return undefined/null.\n */\nexport function queryDataIndex(data, payload) {\n  if (payload.dataIndexInside != null) {\n    return payload.dataIndexInside;\n  } else if (payload.dataIndex != null) {\n    return isArray(payload.dataIndex) ? map(payload.dataIndex, function (value) {\n      return data.indexOfRawIndex(value);\n    }) : data.indexOfRawIndex(payload.dataIndex);\n  } else if (payload.name != null) {\n    return isArray(payload.name) ? map(payload.name, function (value) {\n      return data.indexOfName(value);\n    }) : data.indexOfName(payload.name);\n  }\n}\n/**\n * Enable property storage to any host object.\n * Notice: Serialization is not supported.\n *\n * For example:\n * let inner = zrUitl.makeInner();\n *\n * function some1(hostObj) {\n *      inner(hostObj).someProperty = 1212;\n *      ...\n * }\n * function some2() {\n *      let fields = inner(this);\n *      fields.someProperty1 = 1212;\n *      fields.someProperty2 = 'xx';\n *      ...\n * }\n *\n * @return {Function}\n */\nexport function makeInner() {\n  var key = '__ec_inner_' + innerUniqueIndex++;\n  return function (hostObj) {\n    return hostObj[key] || (hostObj[key] = {});\n  };\n}\nvar innerUniqueIndex = getRandomIdBase();\n/**\n * The same behavior as `component.getReferringComponents`.\n */\nexport function parseFinder(ecModel, finderInput, opt) {\n  var _a = preParseFinder(finderInput, opt),\n    mainTypeSpecified = _a.mainTypeSpecified,\n    queryOptionMap = _a.queryOptionMap,\n    others = _a.others;\n  var result = others;\n  var defaultMainType = opt ? opt.defaultMainType : null;\n  if (!mainTypeSpecified && defaultMainType) {\n    queryOptionMap.set(defaultMainType, {});\n  }\n  queryOptionMap.each(function (queryOption, mainType) {\n    var queryResult = queryReferringComponents(ecModel, mainType, queryOption, {\n      useDefault: defaultMainType === mainType,\n      enableAll: opt && opt.enableAll != null ? opt.enableAll : true,\n      enableNone: opt && opt.enableNone != null ? opt.enableNone : true\n    });\n    result[mainType + 'Models'] = queryResult.models;\n    result[mainType + 'Model'] = queryResult.models[0];\n  });\n  return result;\n}\nexport function preParseFinder(finderInput, opt) {\n  var finder;\n  if (isString(finderInput)) {\n    var obj = {};\n    obj[finderInput + 'Index'] = 0;\n    finder = obj;\n  } else {\n    finder = finderInput;\n  }\n  var queryOptionMap = createHashMap();\n  var others = {};\n  var mainTypeSpecified = false;\n  each(finder, function (value, key) {\n    // Exclude 'dataIndex' and other illgal keys.\n    if (key === 'dataIndex' || key === 'dataIndexInside') {\n      others[key] = value;\n      return;\n    }\n    var parsedKey = key.match(/^(\\w+)(Index|Id|Name)$/) || [];\n    var mainType = parsedKey[1];\n    var queryType = (parsedKey[2] || '').toLowerCase();\n    if (!mainType || !queryType || opt && opt.includeMainTypes && indexOf(opt.includeMainTypes, mainType) < 0) {\n      return;\n    }\n    mainTypeSpecified = mainTypeSpecified || !!mainType;\n    var queryOption = queryOptionMap.get(mainType) || queryOptionMap.set(mainType, {});\n    queryOption[queryType] = value;\n  });\n  return {\n    mainTypeSpecified: mainTypeSpecified,\n    queryOptionMap: queryOptionMap,\n    others: others\n  };\n}\nexport var SINGLE_REFERRING = {\n  useDefault: true,\n  enableAll: false,\n  enableNone: false\n};\nexport var MULTIPLE_REFERRING = {\n  useDefault: false,\n  enableAll: true,\n  enableNone: true\n};\nexport function queryReferringComponents(ecModel, mainType, userOption, opt) {\n  opt = opt || SINGLE_REFERRING;\n  var indexOption = userOption.index;\n  var idOption = userOption.id;\n  var nameOption = userOption.name;\n  var result = {\n    models: null,\n    specified: indexOption != null || idOption != null || nameOption != null\n  };\n  if (!result.specified) {\n    // Use the first as default if `useDefault`.\n    var firstCmpt = void 0;\n    result.models = opt.useDefault && (firstCmpt = ecModel.getComponent(mainType)) ? [firstCmpt] : [];\n    return result;\n  }\n  if (indexOption === 'none' || indexOption === false) {\n    assert(opt.enableNone, '`\"none\"` or `false` is not a valid value on index option.');\n    result.models = [];\n    return result;\n  }\n  // `queryComponents` will return all components if\n  // both all of index/id/name are null/undefined.\n  if (indexOption === 'all') {\n    assert(opt.enableAll, '`\"all\"` is not a valid value on index option.');\n    indexOption = idOption = nameOption = null;\n  }\n  result.models = ecModel.queryComponents({\n    mainType: mainType,\n    index: indexOption,\n    id: idOption,\n    name: nameOption\n  });\n  return result;\n}\nexport function setAttribute(dom, key, value) {\n  dom.setAttribute ? dom.setAttribute(key, value) : dom[key] = value;\n}\nexport function getAttribute(dom, key) {\n  return dom.getAttribute ? dom.getAttribute(key) : dom[key];\n}\nexport function getTooltipRenderMode(renderModeOption) {\n  if (renderModeOption === 'auto') {\n    // Using html when `document` exists, use richText otherwise\n    return env.domSupported ? 'html' : 'richText';\n  } else {\n    return renderModeOption || 'html';\n  }\n}\n/**\n * Group a list by key.\n */\nexport function groupData(array, getKey // return key\n) {\n  var buckets = createHashMap();\n  var keys = [];\n  each(array, function (item) {\n    var key = getKey(item);\n    (buckets.get(key) || (keys.push(key), buckets.set(key, []))).push(item);\n  });\n  return {\n    keys: keys,\n    buckets: buckets\n  };\n}\n/**\n * Interpolate raw values of a series with percent\n *\n * @param data         data\n * @param labelModel   label model of the text element\n * @param sourceValue  start value. May be null/undefined when init.\n * @param targetValue  end value\n * @param percent      0~1 percentage; 0 uses start value while 1 uses end value\n * @return             interpolated values\n *                     If `sourceValue` and `targetValue` are `number`, return `number`.\n *                     If `sourceValue` and `targetValue` are `string`, return `string`.\n *                     If `sourceValue` and `targetValue` are `(string | number)[]`, return `(string | number)[]`.\n *                     Other cases do not supported.\n */\nexport function interpolateRawValues(data, precision, sourceValue, targetValue, percent) {\n  var isAutoPrecision = precision == null || precision === 'auto';\n  if (targetValue == null) {\n    return targetValue;\n  }\n  if (isNumber(targetValue)) {\n    var value = interpolateNumber(sourceValue || 0, targetValue, percent);\n    return round(value, isAutoPrecision ? Math.max(getPrecision(sourceValue || 0), getPrecision(targetValue)) : precision);\n  } else if (isString(targetValue)) {\n    return percent < 1 ? sourceValue : targetValue;\n  } else {\n    var interpolated = [];\n    var leftArr = sourceValue;\n    var rightArr = targetValue;\n    var length_1 = Math.max(leftArr ? leftArr.length : 0, rightArr.length);\n    for (var i = 0; i < length_1; ++i) {\n      var info = data.getDimensionInfo(i);\n      // Don't interpolate ordinal dims\n      if (info && info.type === 'ordinal') {\n        // In init, there is no `sourceValue`, but should better not to get undefined result.\n        interpolated[i] = (percent < 1 && leftArr ? leftArr : rightArr)[i];\n      } else {\n        var leftVal = leftArr && leftArr[i] ? leftArr[i] : 0;\n        var rightVal = rightArr[i];\n        var value = interpolateNumber(leftVal, rightVal, percent);\n        interpolated[i] = round(value, isAutoPrecision ? Math.max(getPrecision(leftVal), getPrecision(rightVal)) : precision);\n      }\n    }\n    return interpolated;\n  }\n}", "map": {"version": 3, "names": ["each", "isObject", "isArray", "createHashMap", "map", "assert", "isString", "indexOf", "isStringSafe", "isNumber", "env", "isNumeric", "getRandomIdBase", "getPrecision", "round", "warn", "interpolateNumber", "p0", "p1", "percent", "DUMMY_COMPONENT_NAME_PREFIX", "INTERNAL_COMPONENT_ID_PREFIX", "normalizeToArray", "value", "Array", "defaultEmphasis", "opt", "key", "subOpts", "emphasis", "i", "len", "length", "subOptName", "hasOwnProperty", "TEXT_STYLE_OPTIONS", "getDataItemValue", "dataItem", "Date", "isDataItemOption", "mappingToExists", "existings", "newCmptOptions", "mode", "isNormalMergeMode", "isReplaceMergeMode", "isReplaceAllMode", "slice", "existingIdIdxMap", "cmptOption", "index", "process", "NODE_ENV", "id", "isValidIdOrName", "warnInvalidateIdOrName", "name", "result", "prepareResult", "mappingById", "mappingByName", "mappingByIndex", "mappingInReplaceAllMode", "makeIdAndName", "existing", "set", "push", "isComponentIdInternal", "newOption", "keyInfo", "brandNew", "optionId", "makeComparableKey", "existingIdx", "get", "resultItem", "keyExistAndEqual", "nextIdx", "mapResult", "idMap", "item", "idNum", "attr", "obj1", "obj2", "key1", "convertOptionIdName", "key2", "val", "Error", "idOrName", "defaultValue", "isNameSpecified", "componentModel", "makeInternalComponentId", "idSuffix", "setComponentTypeToKeyInfo", "mappingResult", "mainType", "componentModelCtor", "subType", "determineSubType", "newCmptOption", "existComponent", "type", "compressBatches", "batchA", "batchB", "mapA", "mapB", "makeMap", "mapToArray", "sourceBatch", "otherMap", "seriesId", "dataIndices", "dataIndex", "otherDataIndices", "j", "lenj", "isData", "queryDataIndex", "data", "payload", "dataIndexInside", "indexOfRawIndex", "indexOfName", "makeInner", "innerUniqueIndex", "hostObj", "parseFinder", "ecModel", "finderInput", "_a", "preParseFinder", "mainTypeSpecified", "queryOptionMap", "others", "defaultMainType", "queryOption", "query<PERSON><PERSON>ult", "queryReferringComponents", "useDefault", "enableAll", "enableNone", "models", "finder", "obj", "parsed<PERSON><PERSON>", "match", "queryType", "toLowerCase", "includeMainTypes", "SINGLE_REFERRING", "MULTIPLE_REFERRING", "userOption", "indexOption", "idOption", "nameOption", "specified", "firstCmpt", "getComponent", "queryComponents", "setAttribute", "dom", "getAttribute", "getTooltipRenderMode", "renderModeOption", "domSupported", "groupData", "array", "<PERSON><PERSON><PERSON>", "buckets", "keys", "interpolateRawValues", "precision", "sourceValue", "targetValue", "isAutoPrecision", "Math", "max", "interpolated", "leftArr", "rightArr", "length_1", "info", "getDimensionInfo", "leftVal", "rightVal"], "sources": ["E:/新项目/整理6/adminweb/node_modules/echarts/lib/util/model.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { each, isObject, isArray, createHashMap, map, assert, isString, indexOf, isStringSafe, isNumber } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { isNumeric, getRandomIdBase, getPrecision, round } from './number.js';\nimport { warn } from './log.js';\nfunction interpolateNumber(p0, p1, percent) {\n  return (p1 - p0) * percent + p0;\n}\n/**\n * Make the name displayable. But we should\n * make sure it is not duplicated with user\n * specified name, so use '\\0';\n */\nvar DUMMY_COMPONENT_NAME_PREFIX = 'series\\0';\nvar INTERNAL_COMPONENT_ID_PREFIX = '\\0_ec_\\0';\n/**\n * If value is not array, then translate it to array.\n * @param  {*} value\n * @return {Array} [value] or value\n */\nexport function normalizeToArray(value) {\n  return value instanceof Array ? value : value == null ? [] : [value];\n}\n/**\n * Sync default option between normal and emphasis like `position` and `show`\n * In case some one will write code like\n *     label: {\n *          show: false,\n *          position: 'outside',\n *          fontSize: 18\n *     },\n *     emphasis: {\n *          label: { show: true }\n *     }\n */\nexport function defaultEmphasis(opt, key, subOpts) {\n  // Caution: performance sensitive.\n  if (opt) {\n    opt[key] = opt[key] || {};\n    opt.emphasis = opt.emphasis || {};\n    opt.emphasis[key] = opt.emphasis[key] || {};\n    // Default emphasis option from normal\n    for (var i = 0, len = subOpts.length; i < len; i++) {\n      var subOptName = subOpts[i];\n      if (!opt.emphasis[key].hasOwnProperty(subOptName) && opt[key].hasOwnProperty(subOptName)) {\n        opt.emphasis[key][subOptName] = opt[key][subOptName];\n      }\n    }\n  }\n}\nexport var TEXT_STYLE_OPTIONS = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'rich', 'tag', 'color', 'textBorderColor', 'textBorderWidth', 'width', 'height', 'lineHeight', 'align', 'verticalAlign', 'baseline', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY', 'textShadowColor', 'textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY', 'backgroundColor', 'borderColor', 'borderWidth', 'borderRadius', 'padding'];\n// modelUtil.LABEL_OPTIONS = modelUtil.TEXT_STYLE_OPTIONS.concat([\n//     'position', 'offset', 'rotate', 'origin', 'show', 'distance', 'formatter',\n//     'fontStyle', 'fontWeight', 'fontSize', 'fontFamily',\n//     // FIXME: deprecated, check and remove it.\n//     'textStyle'\n// ]);\n/**\n * The method does not ensure performance.\n * data could be [12, 2323, {value: 223}, [1221, 23], {value: [2, 23]}]\n * This helper method retrieves value from data.\n */\nexport function getDataItemValue(dataItem) {\n  return isObject(dataItem) && !isArray(dataItem) && !(dataItem instanceof Date) ? dataItem.value : dataItem;\n}\n/**\n * data could be [12, 2323, {value: 223}, [1221, 23], {value: [2, 23]}]\n * This helper method determine if dataItem has extra option besides value\n */\nexport function isDataItemOption(dataItem) {\n  return isObject(dataItem) && !(dataItem instanceof Array);\n  // // markLine data can be array\n  // && !(dataItem[0] && isObject(dataItem[0]) && !(dataItem[0] instanceof Array));\n}\n\n;\n/**\n * Mapping to existings for merge.\n *\n * Mode \"normalMege\":\n *     The mapping result (merge result) will keep the order of the existing\n *     component, rather than the order of new option. Because we should ensure\n *     some specified index reference (like xAxisIndex) keep work.\n *     And in most cases, \"merge option\" is used to update partial option but not\n *     be expected to change the order.\n *\n * Mode \"replaceMege\":\n *     (1) Only the id mapped components will be merged.\n *     (2) Other existing components (except internal components) will be removed.\n *     (3) Other new options will be used to create new component.\n *     (4) The index of the existing components will not be modified.\n *     That means their might be \"hole\" after the removal.\n *     The new components are created first at those available index.\n *\n * Mode \"replaceAll\":\n *     This mode try to support that reproduce an echarts instance from another\n *     echarts instance (via `getOption`) in some simple cases.\n *     In this scenario, the `result` index are exactly the consistent with the `newCmptOptions`,\n *     which ensures the component index referring (like `xAxisIndex: ?`) corrent. That is,\n *     the \"hole\" in `newCmptOptions` will also be kept.\n *     On the contrary, other modes try best to eliminate holes.\n *     PENDING: This is an experimental mode yet.\n *\n * @return See the comment of <MappingResult>.\n */\nexport function mappingToExists(existings, newCmptOptions, mode) {\n  var isNormalMergeMode = mode === 'normalMerge';\n  var isReplaceMergeMode = mode === 'replaceMerge';\n  var isReplaceAllMode = mode === 'replaceAll';\n  existings = existings || [];\n  newCmptOptions = (newCmptOptions || []).slice();\n  var existingIdIdxMap = createHashMap();\n  // Validate id and name on user input option.\n  each(newCmptOptions, function (cmptOption, index) {\n    if (!isObject(cmptOption)) {\n      newCmptOptions[index] = null;\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      // There is some legacy case that name is set as `false`.\n      // But should work normally rather than throw error.\n      if (cmptOption.id != null && !isValidIdOrName(cmptOption.id)) {\n        warnInvalidateIdOrName(cmptOption.id);\n      }\n      if (cmptOption.name != null && !isValidIdOrName(cmptOption.name)) {\n        warnInvalidateIdOrName(cmptOption.name);\n      }\n    }\n  });\n  var result = prepareResult(existings, existingIdIdxMap, mode);\n  if (isNormalMergeMode || isReplaceMergeMode) {\n    mappingById(result, existings, existingIdIdxMap, newCmptOptions);\n  }\n  if (isNormalMergeMode) {\n    mappingByName(result, newCmptOptions);\n  }\n  if (isNormalMergeMode || isReplaceMergeMode) {\n    mappingByIndex(result, newCmptOptions, isReplaceMergeMode);\n  } else if (isReplaceAllMode) {\n    mappingInReplaceAllMode(result, newCmptOptions);\n  }\n  makeIdAndName(result);\n  // The array `result` MUST NOT contain elided items, otherwise the\n  // forEach will omit those items and result in incorrect result.\n  return result;\n}\nfunction prepareResult(existings, existingIdIdxMap, mode) {\n  var result = [];\n  if (mode === 'replaceAll') {\n    return result;\n  }\n  // Do not use native `map` to in case that the array `existings`\n  // contains elided items, which will be omitted.\n  for (var index = 0; index < existings.length; index++) {\n    var existing = existings[index];\n    // Because of replaceMerge, `existing` may be null/undefined.\n    if (existing && existing.id != null) {\n      existingIdIdxMap.set(existing.id, index);\n    }\n    // For non-internal-componnets:\n    //     Mode \"normalMerge\": all existings kept.\n    //     Mode \"replaceMerge\": all existing removed unless mapped by id.\n    // For internal-components:\n    //     go with \"replaceMerge\" approach in both mode.\n    result.push({\n      existing: mode === 'replaceMerge' || isComponentIdInternal(existing) ? null : existing,\n      newOption: null,\n      keyInfo: null,\n      brandNew: null\n    });\n  }\n  return result;\n}\nfunction mappingById(result, existings, existingIdIdxMap, newCmptOptions) {\n  // Mapping by id if specified.\n  each(newCmptOptions, function (cmptOption, index) {\n    if (!cmptOption || cmptOption.id == null) {\n      return;\n    }\n    var optionId = makeComparableKey(cmptOption.id);\n    var existingIdx = existingIdIdxMap.get(optionId);\n    if (existingIdx != null) {\n      var resultItem = result[existingIdx];\n      assert(!resultItem.newOption, 'Duplicated option on id \"' + optionId + '\".');\n      resultItem.newOption = cmptOption;\n      // In both mode, if id matched, new option will be merged to\n      // the existings rather than creating new component model.\n      resultItem.existing = existings[existingIdx];\n      newCmptOptions[index] = null;\n    }\n  });\n}\nfunction mappingByName(result, newCmptOptions) {\n  // Mapping by name if specified.\n  each(newCmptOptions, function (cmptOption, index) {\n    if (!cmptOption || cmptOption.name == null) {\n      return;\n    }\n    for (var i = 0; i < result.length; i++) {\n      var existing = result[i].existing;\n      if (!result[i].newOption // Consider name: two map to one.\n      // Can not match when both ids existing but different.\n      && existing && (existing.id == null || cmptOption.id == null) && !isComponentIdInternal(cmptOption) && !isComponentIdInternal(existing) && keyExistAndEqual('name', existing, cmptOption)) {\n        result[i].newOption = cmptOption;\n        newCmptOptions[index] = null;\n        return;\n      }\n    }\n  });\n}\nfunction mappingByIndex(result, newCmptOptions, brandNew) {\n  each(newCmptOptions, function (cmptOption) {\n    if (!cmptOption) {\n      return;\n    }\n    // Find the first place that not mapped by id and not internal component (consider the \"hole\").\n    var resultItem;\n    var nextIdx = 0;\n    while (\n    // Be `!resultItem` only when `nextIdx >= result.length`.\n    (resultItem = result[nextIdx]\n    // (1) Existing models that already have id should be able to mapped to. Because\n    // after mapping performed, model will always be assigned with an id if user not given.\n    // After that all models have id.\n    // (2) If new option has id, it can only set to a hole or append to the last. It should\n    // not be merged to the existings with different id. Because id should not be overwritten.\n    // (3) Name can be overwritten, because axis use name as 'show label text'.\n    ) && (resultItem.newOption || isComponentIdInternal(resultItem.existing) ||\n    // In mode \"replaceMerge\", here no not-mapped-non-internal-existing.\n    resultItem.existing && cmptOption.id != null && !keyExistAndEqual('id', cmptOption, resultItem.existing))) {\n      nextIdx++;\n    }\n    if (resultItem) {\n      resultItem.newOption = cmptOption;\n      resultItem.brandNew = brandNew;\n    } else {\n      result.push({\n        newOption: cmptOption,\n        brandNew: brandNew,\n        existing: null,\n        keyInfo: null\n      });\n    }\n    nextIdx++;\n  });\n}\nfunction mappingInReplaceAllMode(result, newCmptOptions) {\n  each(newCmptOptions, function (cmptOption) {\n    // The feature \"reproduce\" requires \"hole\" will also reproduced\n    // in case that component index referring are broken.\n    result.push({\n      newOption: cmptOption,\n      brandNew: true,\n      existing: null,\n      keyInfo: null\n    });\n  });\n}\n/**\n * Make id and name for mapping result (result of mappingToExists)\n * into `keyInfo` field.\n */\nfunction makeIdAndName(mapResult) {\n  // We use this id to hash component models and view instances\n  // in echarts. id can be specified by user, or auto generated.\n  // The id generation rule ensures new view instance are able\n  // to mapped to old instance when setOption are called in\n  // no-merge mode. So we generate model id by name and plus\n  // type in view id.\n  // name can be duplicated among components, which is convenient\n  // to specify multi components (like series) by one name.\n  // Ensure that each id is distinct.\n  var idMap = createHashMap();\n  each(mapResult, function (item) {\n    var existing = item.existing;\n    existing && idMap.set(existing.id, item);\n  });\n  each(mapResult, function (item) {\n    var opt = item.newOption;\n    // Force ensure id not duplicated.\n    assert(!opt || opt.id == null || !idMap.get(opt.id) || idMap.get(opt.id) === item, 'id duplicates: ' + (opt && opt.id));\n    opt && opt.id != null && idMap.set(opt.id, item);\n    !item.keyInfo && (item.keyInfo = {});\n  });\n  // Make name and id.\n  each(mapResult, function (item, index) {\n    var existing = item.existing;\n    var opt = item.newOption;\n    var keyInfo = item.keyInfo;\n    if (!isObject(opt)) {\n      return;\n    }\n    // Name can be overwritten. Consider case: axis.name = '20km'.\n    // But id generated by name will not be changed, which affect\n    // only in that case: setOption with 'not merge mode' and view\n    // instance will be recreated, which can be accepted.\n    keyInfo.name = opt.name != null ? makeComparableKey(opt.name) : existing ? existing.name\n    // Avoid that different series has the same name,\n    // because name may be used like in color pallet.\n    : DUMMY_COMPONENT_NAME_PREFIX + index;\n    if (existing) {\n      keyInfo.id = makeComparableKey(existing.id);\n    } else if (opt.id != null) {\n      keyInfo.id = makeComparableKey(opt.id);\n    } else {\n      // Consider this situatoin:\n      //  optionA: [{name: 'a'}, {name: 'a'}, {..}]\n      //  optionB [{..}, {name: 'a'}, {name: 'a'}]\n      // Series with the same name between optionA and optionB\n      // should be mapped.\n      var idNum = 0;\n      do {\n        keyInfo.id = '\\0' + keyInfo.name + '\\0' + idNum++;\n      } while (idMap.get(keyInfo.id));\n    }\n    idMap.set(keyInfo.id, item);\n  });\n}\nfunction keyExistAndEqual(attr, obj1, obj2) {\n  var key1 = convertOptionIdName(obj1[attr], null);\n  var key2 = convertOptionIdName(obj2[attr], null);\n  // See `MappingExistingItem`. `id` and `name` trade string equals to number.\n  return key1 != null && key2 != null && key1 === key2;\n}\n/**\n * @return return null if not exist.\n */\nfunction makeComparableKey(val) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (val == null) {\n      throw new Error();\n    }\n  }\n  return convertOptionIdName(val, '');\n}\nexport function convertOptionIdName(idOrName, defaultValue) {\n  if (idOrName == null) {\n    return defaultValue;\n  }\n  return isString(idOrName) ? idOrName : isNumber(idOrName) || isStringSafe(idOrName) ? idOrName + '' : defaultValue;\n}\nfunction warnInvalidateIdOrName(idOrName) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn('`' + idOrName + '` is invalid id or name. Must be a string or number.');\n  }\n}\nfunction isValidIdOrName(idOrName) {\n  return isStringSafe(idOrName) || isNumeric(idOrName);\n}\nexport function isNameSpecified(componentModel) {\n  var name = componentModel.name;\n  // Is specified when `indexOf` get -1 or > 0.\n  return !!(name && name.indexOf(DUMMY_COMPONENT_NAME_PREFIX));\n}\n/**\n * @public\n * @param {Object} cmptOption\n * @return {boolean}\n */\nexport function isComponentIdInternal(cmptOption) {\n  return cmptOption && cmptOption.id != null && makeComparableKey(cmptOption.id).indexOf(INTERNAL_COMPONENT_ID_PREFIX) === 0;\n}\nexport function makeInternalComponentId(idSuffix) {\n  return INTERNAL_COMPONENT_ID_PREFIX + idSuffix;\n}\nexport function setComponentTypeToKeyInfo(mappingResult, mainType, componentModelCtor) {\n  // Set mainType and complete subType.\n  each(mappingResult, function (item) {\n    var newOption = item.newOption;\n    if (isObject(newOption)) {\n      item.keyInfo.mainType = mainType;\n      item.keyInfo.subType = determineSubType(mainType, newOption, item.existing, componentModelCtor);\n    }\n  });\n}\nfunction determineSubType(mainType, newCmptOption, existComponent, componentModelCtor) {\n  var subType = newCmptOption.type ? newCmptOption.type : existComponent ? existComponent.subType\n  // Use determineSubType only when there is no existComponent.\n  : componentModelCtor.determineSubType(mainType, newCmptOption);\n  // tooltip, markline, markpoint may always has no subType\n  return subType;\n}\n/**\n * A helper for removing duplicate items between batchA and batchB,\n * and in themselves, and categorize by series.\n *\n * @param batchA Like: [{seriesId: 2, dataIndex: [32, 4, 5]}, ...]\n * @param batchB Like: [{seriesId: 2, dataIndex: [32, 4, 5]}, ...]\n * @return result: [resultBatchA, resultBatchB]\n */\nexport function compressBatches(batchA, batchB) {\n  var mapA = {};\n  var mapB = {};\n  makeMap(batchA || [], mapA);\n  makeMap(batchB || [], mapB, mapA);\n  return [mapToArray(mapA), mapToArray(mapB)];\n  function makeMap(sourceBatch, map, otherMap) {\n    for (var i = 0, len = sourceBatch.length; i < len; i++) {\n      var seriesId = convertOptionIdName(sourceBatch[i].seriesId, null);\n      if (seriesId == null) {\n        return;\n      }\n      var dataIndices = normalizeToArray(sourceBatch[i].dataIndex);\n      var otherDataIndices = otherMap && otherMap[seriesId];\n      for (var j = 0, lenj = dataIndices.length; j < lenj; j++) {\n        var dataIndex = dataIndices[j];\n        if (otherDataIndices && otherDataIndices[dataIndex]) {\n          otherDataIndices[dataIndex] = null;\n        } else {\n          (map[seriesId] || (map[seriesId] = {}))[dataIndex] = 1;\n        }\n      }\n    }\n  }\n  function mapToArray(map, isData) {\n    var result = [];\n    for (var i in map) {\n      if (map.hasOwnProperty(i) && map[i] != null) {\n        if (isData) {\n          result.push(+i);\n        } else {\n          var dataIndices = mapToArray(map[i], true);\n          dataIndices.length && result.push({\n            seriesId: i,\n            dataIndex: dataIndices\n          });\n        }\n      }\n    }\n    return result;\n  }\n}\n/**\n * @param payload Contains dataIndex (means rawIndex) / dataIndexInside / name\n *                         each of which can be Array or primary type.\n * @return dataIndex If not found, return undefined/null.\n */\nexport function queryDataIndex(data, payload) {\n  if (payload.dataIndexInside != null) {\n    return payload.dataIndexInside;\n  } else if (payload.dataIndex != null) {\n    return isArray(payload.dataIndex) ? map(payload.dataIndex, function (value) {\n      return data.indexOfRawIndex(value);\n    }) : data.indexOfRawIndex(payload.dataIndex);\n  } else if (payload.name != null) {\n    return isArray(payload.name) ? map(payload.name, function (value) {\n      return data.indexOfName(value);\n    }) : data.indexOfName(payload.name);\n  }\n}\n/**\n * Enable property storage to any host object.\n * Notice: Serialization is not supported.\n *\n * For example:\n * let inner = zrUitl.makeInner();\n *\n * function some1(hostObj) {\n *      inner(hostObj).someProperty = 1212;\n *      ...\n * }\n * function some2() {\n *      let fields = inner(this);\n *      fields.someProperty1 = 1212;\n *      fields.someProperty2 = 'xx';\n *      ...\n * }\n *\n * @return {Function}\n */\nexport function makeInner() {\n  var key = '__ec_inner_' + innerUniqueIndex++;\n  return function (hostObj) {\n    return hostObj[key] || (hostObj[key] = {});\n  };\n}\nvar innerUniqueIndex = getRandomIdBase();\n/**\n * The same behavior as `component.getReferringComponents`.\n */\nexport function parseFinder(ecModel, finderInput, opt) {\n  var _a = preParseFinder(finderInput, opt),\n    mainTypeSpecified = _a.mainTypeSpecified,\n    queryOptionMap = _a.queryOptionMap,\n    others = _a.others;\n  var result = others;\n  var defaultMainType = opt ? opt.defaultMainType : null;\n  if (!mainTypeSpecified && defaultMainType) {\n    queryOptionMap.set(defaultMainType, {});\n  }\n  queryOptionMap.each(function (queryOption, mainType) {\n    var queryResult = queryReferringComponents(ecModel, mainType, queryOption, {\n      useDefault: defaultMainType === mainType,\n      enableAll: opt && opt.enableAll != null ? opt.enableAll : true,\n      enableNone: opt && opt.enableNone != null ? opt.enableNone : true\n    });\n    result[mainType + 'Models'] = queryResult.models;\n    result[mainType + 'Model'] = queryResult.models[0];\n  });\n  return result;\n}\nexport function preParseFinder(finderInput, opt) {\n  var finder;\n  if (isString(finderInput)) {\n    var obj = {};\n    obj[finderInput + 'Index'] = 0;\n    finder = obj;\n  } else {\n    finder = finderInput;\n  }\n  var queryOptionMap = createHashMap();\n  var others = {};\n  var mainTypeSpecified = false;\n  each(finder, function (value, key) {\n    // Exclude 'dataIndex' and other illgal keys.\n    if (key === 'dataIndex' || key === 'dataIndexInside') {\n      others[key] = value;\n      return;\n    }\n    var parsedKey = key.match(/^(\\w+)(Index|Id|Name)$/) || [];\n    var mainType = parsedKey[1];\n    var queryType = (parsedKey[2] || '').toLowerCase();\n    if (!mainType || !queryType || opt && opt.includeMainTypes && indexOf(opt.includeMainTypes, mainType) < 0) {\n      return;\n    }\n    mainTypeSpecified = mainTypeSpecified || !!mainType;\n    var queryOption = queryOptionMap.get(mainType) || queryOptionMap.set(mainType, {});\n    queryOption[queryType] = value;\n  });\n  return {\n    mainTypeSpecified: mainTypeSpecified,\n    queryOptionMap: queryOptionMap,\n    others: others\n  };\n}\nexport var SINGLE_REFERRING = {\n  useDefault: true,\n  enableAll: false,\n  enableNone: false\n};\nexport var MULTIPLE_REFERRING = {\n  useDefault: false,\n  enableAll: true,\n  enableNone: true\n};\nexport function queryReferringComponents(ecModel, mainType, userOption, opt) {\n  opt = opt || SINGLE_REFERRING;\n  var indexOption = userOption.index;\n  var idOption = userOption.id;\n  var nameOption = userOption.name;\n  var result = {\n    models: null,\n    specified: indexOption != null || idOption != null || nameOption != null\n  };\n  if (!result.specified) {\n    // Use the first as default if `useDefault`.\n    var firstCmpt = void 0;\n    result.models = opt.useDefault && (firstCmpt = ecModel.getComponent(mainType)) ? [firstCmpt] : [];\n    return result;\n  }\n  if (indexOption === 'none' || indexOption === false) {\n    assert(opt.enableNone, '`\"none\"` or `false` is not a valid value on index option.');\n    result.models = [];\n    return result;\n  }\n  // `queryComponents` will return all components if\n  // both all of index/id/name are null/undefined.\n  if (indexOption === 'all') {\n    assert(opt.enableAll, '`\"all\"` is not a valid value on index option.');\n    indexOption = idOption = nameOption = null;\n  }\n  result.models = ecModel.queryComponents({\n    mainType: mainType,\n    index: indexOption,\n    id: idOption,\n    name: nameOption\n  });\n  return result;\n}\nexport function setAttribute(dom, key, value) {\n  dom.setAttribute ? dom.setAttribute(key, value) : dom[key] = value;\n}\nexport function getAttribute(dom, key) {\n  return dom.getAttribute ? dom.getAttribute(key) : dom[key];\n}\nexport function getTooltipRenderMode(renderModeOption) {\n  if (renderModeOption === 'auto') {\n    // Using html when `document` exists, use richText otherwise\n    return env.domSupported ? 'html' : 'richText';\n  } else {\n    return renderModeOption || 'html';\n  }\n}\n/**\n * Group a list by key.\n */\nexport function groupData(array, getKey // return key\n) {\n  var buckets = createHashMap();\n  var keys = [];\n  each(array, function (item) {\n    var key = getKey(item);\n    (buckets.get(key) || (keys.push(key), buckets.set(key, []))).push(item);\n  });\n  return {\n    keys: keys,\n    buckets: buckets\n  };\n}\n/**\n * Interpolate raw values of a series with percent\n *\n * @param data         data\n * @param labelModel   label model of the text element\n * @param sourceValue  start value. May be null/undefined when init.\n * @param targetValue  end value\n * @param percent      0~1 percentage; 0 uses start value while 1 uses end value\n * @return             interpolated values\n *                     If `sourceValue` and `targetValue` are `number`, return `number`.\n *                     If `sourceValue` and `targetValue` are `string`, return `string`.\n *                     If `sourceValue` and `targetValue` are `(string | number)[]`, return `(string | number)[]`.\n *                     Other cases do not supported.\n */\nexport function interpolateRawValues(data, precision, sourceValue, targetValue, percent) {\n  var isAutoPrecision = precision == null || precision === 'auto';\n  if (targetValue == null) {\n    return targetValue;\n  }\n  if (isNumber(targetValue)) {\n    var value = interpolateNumber(sourceValue || 0, targetValue, percent);\n    return round(value, isAutoPrecision ? Math.max(getPrecision(sourceValue || 0), getPrecision(targetValue)) : precision);\n  } else if (isString(targetValue)) {\n    return percent < 1 ? sourceValue : targetValue;\n  } else {\n    var interpolated = [];\n    var leftArr = sourceValue;\n    var rightArr = targetValue;\n    var length_1 = Math.max(leftArr ? leftArr.length : 0, rightArr.length);\n    for (var i = 0; i < length_1; ++i) {\n      var info = data.getDimensionInfo(i);\n      // Don't interpolate ordinal dims\n      if (info && info.type === 'ordinal') {\n        // In init, there is no `sourceValue`, but should better not to get undefined result.\n        interpolated[i] = (percent < 1 && leftArr ? leftArr : rightArr)[i];\n      } else {\n        var leftVal = leftArr && leftArr[i] ? leftArr[i] : 0;\n        var rightVal = rightArr[i];\n        var value = interpolateNumber(leftVal, rightVal, percent);\n        interpolated[i] = round(value, isAutoPrecision ? Math.max(getPrecision(leftVal), getPrecision(rightVal)) : precision);\n      }\n    }\n    return interpolated;\n  }\n}"], "mappings": ";;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,0BAA0B;AACzI,OAAOC,GAAG,MAAM,yBAAyB;AACzC,SAASC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,KAAK,QAAQ,aAAa;AAC7E,SAASC,IAAI,QAAQ,UAAU;AAC/B,SAASC,iBAAiBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAE;EAC1C,OAAO,CAACD,EAAE,GAAGD,EAAE,IAAIE,OAAO,GAAGF,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,2BAA2B,GAAG,UAAU;AAC5C,IAAIC,4BAA4B,GAAG,UAAU;AAC7C;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAOA,KAAK,YAAYC,KAAK,GAAGD,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,KAAK,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACjD;EACA,IAAIF,GAAG,EAAE;IACPA,GAAG,CAACC,GAAG,CAAC,GAAGD,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IACzBD,GAAG,CAACG,QAAQ,GAAGH,GAAG,CAACG,QAAQ,IAAI,CAAC,CAAC;IACjCH,GAAG,CAACG,QAAQ,CAACF,GAAG,CAAC,GAAGD,GAAG,CAACG,QAAQ,CAACF,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,OAAO,CAACI,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAClD,IAAIG,UAAU,GAAGL,OAAO,CAACE,CAAC,CAAC;MAC3B,IAAI,CAACJ,GAAG,CAACG,QAAQ,CAACF,GAAG,CAAC,CAACO,cAAc,CAACD,UAAU,CAAC,IAAIP,GAAG,CAACC,GAAG,CAAC,CAACO,cAAc,CAACD,UAAU,CAAC,EAAE;QACxFP,GAAG,CAACG,QAAQ,CAACF,GAAG,CAAC,CAACM,UAAU,CAAC,GAAGP,GAAG,CAACC,GAAG,CAAC,CAACM,UAAU,CAAC;MACtD;IACF;EACF;AACF;AACA,OAAO,IAAIE,kBAAkB,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,SAAS,CAAC;AACpb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EACzC,OAAOpC,QAAQ,CAACoC,QAAQ,CAAC,IAAI,CAACnC,OAAO,CAACmC,QAAQ,CAAC,IAAI,EAAEA,QAAQ,YAAYC,IAAI,CAAC,GAAGD,QAAQ,CAACd,KAAK,GAAGc,QAAQ;AAC5G;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAACF,QAAQ,EAAE;EACzC,OAAOpC,QAAQ,CAACoC,QAAQ,CAAC,IAAI,EAAEA,QAAQ,YAAYb,KAAK,CAAC;EACzD;EACA;AACF;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgB,eAAeA,CAACC,SAAS,EAAEC,cAAc,EAAEC,IAAI,EAAE;EAC/D,IAAIC,iBAAiB,GAAGD,IAAI,KAAK,aAAa;EAC9C,IAAIE,kBAAkB,GAAGF,IAAI,KAAK,cAAc;EAChD,IAAIG,gBAAgB,GAAGH,IAAI,KAAK,YAAY;EAC5CF,SAAS,GAAGA,SAAS,IAAI,EAAE;EAC3BC,cAAc,GAAG,CAACA,cAAc,IAAI,EAAE,EAAEK,KAAK,CAAC,CAAC;EAC/C,IAAIC,gBAAgB,GAAG7C,aAAa,CAAC,CAAC;EACtC;EACAH,IAAI,CAAC0C,cAAc,EAAE,UAAUO,UAAU,EAAEC,KAAK,EAAE;IAChD,IAAI,CAACjD,QAAQ,CAACgD,UAAU,CAAC,EAAE;MACzBP,cAAc,CAACQ,KAAK,CAAC,GAAG,IAAI;MAC5B;IACF;IACA,IAAIC,OAAO,CAACzC,GAAG,CAAC0C,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA;MACA,IAAIH,UAAU,CAACI,EAAE,IAAI,IAAI,IAAI,CAACC,eAAe,CAACL,UAAU,CAACI,EAAE,CAAC,EAAE;QAC5DE,sBAAsB,CAACN,UAAU,CAACI,EAAE,CAAC;MACvC;MACA,IAAIJ,UAAU,CAACO,IAAI,IAAI,IAAI,IAAI,CAACF,eAAe,CAACL,UAAU,CAACO,IAAI,CAAC,EAAE;QAChED,sBAAsB,CAACN,UAAU,CAACO,IAAI,CAAC;MACzC;IACF;EACF,CAAC,CAAC;EACF,IAAIC,MAAM,GAAGC,aAAa,CAACjB,SAAS,EAAEO,gBAAgB,EAAEL,IAAI,CAAC;EAC7D,IAAIC,iBAAiB,IAAIC,kBAAkB,EAAE;IAC3Cc,WAAW,CAACF,MAAM,EAAEhB,SAAS,EAAEO,gBAAgB,EAAEN,cAAc,CAAC;EAClE;EACA,IAAIE,iBAAiB,EAAE;IACrBgB,aAAa,CAACH,MAAM,EAAEf,cAAc,CAAC;EACvC;EACA,IAAIE,iBAAiB,IAAIC,kBAAkB,EAAE;IAC3CgB,cAAc,CAACJ,MAAM,EAAEf,cAAc,EAAEG,kBAAkB,CAAC;EAC5D,CAAC,MAAM,IAAIC,gBAAgB,EAAE;IAC3BgB,uBAAuB,CAACL,MAAM,EAAEf,cAAc,CAAC;EACjD;EACAqB,aAAa,CAACN,MAAM,CAAC;EACrB;EACA;EACA,OAAOA,MAAM;AACf;AACA,SAASC,aAAaA,CAACjB,SAAS,EAAEO,gBAAgB,EAAEL,IAAI,EAAE;EACxD,IAAIc,MAAM,GAAG,EAAE;EACf,IAAId,IAAI,KAAK,YAAY,EAAE;IACzB,OAAOc,MAAM;EACf;EACA;EACA;EACA,KAAK,IAAIP,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGT,SAAS,CAACT,MAAM,EAAEkB,KAAK,EAAE,EAAE;IACrD,IAAIc,QAAQ,GAAGvB,SAAS,CAACS,KAAK,CAAC;IAC/B;IACA,IAAIc,QAAQ,IAAIA,QAAQ,CAACX,EAAE,IAAI,IAAI,EAAE;MACnCL,gBAAgB,CAACiB,GAAG,CAACD,QAAQ,CAACX,EAAE,EAAEH,KAAK,CAAC;IAC1C;IACA;IACA;IACA;IACA;IACA;IACAO,MAAM,CAACS,IAAI,CAAC;MACVF,QAAQ,EAAErB,IAAI,KAAK,cAAc,IAAIwB,qBAAqB,CAACH,QAAQ,CAAC,GAAG,IAAI,GAAGA,QAAQ;MACtFI,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EACA,OAAOb,MAAM;AACf;AACA,SAASE,WAAWA,CAACF,MAAM,EAAEhB,SAAS,EAAEO,gBAAgB,EAAEN,cAAc,EAAE;EACxE;EACA1C,IAAI,CAAC0C,cAAc,EAAE,UAAUO,UAAU,EAAEC,KAAK,EAAE;IAChD,IAAI,CAACD,UAAU,IAAIA,UAAU,CAACI,EAAE,IAAI,IAAI,EAAE;MACxC;IACF;IACA,IAAIkB,QAAQ,GAAGC,iBAAiB,CAACvB,UAAU,CAACI,EAAE,CAAC;IAC/C,IAAIoB,WAAW,GAAGzB,gBAAgB,CAAC0B,GAAG,CAACH,QAAQ,CAAC;IAChD,IAAIE,WAAW,IAAI,IAAI,EAAE;MACvB,IAAIE,UAAU,GAAGlB,MAAM,CAACgB,WAAW,CAAC;MACpCpE,MAAM,CAAC,CAACsE,UAAU,CAACP,SAAS,EAAE,2BAA2B,GAAGG,QAAQ,GAAG,IAAI,CAAC;MAC5EI,UAAU,CAACP,SAAS,GAAGnB,UAAU;MACjC;MACA;MACA0B,UAAU,CAACX,QAAQ,GAAGvB,SAAS,CAACgC,WAAW,CAAC;MAC5C/B,cAAc,CAACQ,KAAK,CAAC,GAAG,IAAI;IAC9B;EACF,CAAC,CAAC;AACJ;AACA,SAASU,aAAaA,CAACH,MAAM,EAAEf,cAAc,EAAE;EAC7C;EACA1C,IAAI,CAAC0C,cAAc,EAAE,UAAUO,UAAU,EAAEC,KAAK,EAAE;IAChD,IAAI,CAACD,UAAU,IAAIA,UAAU,CAACO,IAAI,IAAI,IAAI,EAAE;MAC1C;IACF;IACA,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,MAAM,CAACzB,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtC,IAAIkC,QAAQ,GAAGP,MAAM,CAAC3B,CAAC,CAAC,CAACkC,QAAQ;MACjC,IAAI,CAACP,MAAM,CAAC3B,CAAC,CAAC,CAACsC,SAAS,CAAC;MACzB;MAAA,GACGJ,QAAQ,KAAKA,QAAQ,CAACX,EAAE,IAAI,IAAI,IAAIJ,UAAU,CAACI,EAAE,IAAI,IAAI,CAAC,IAAI,CAACc,qBAAqB,CAAClB,UAAU,CAAC,IAAI,CAACkB,qBAAqB,CAACH,QAAQ,CAAC,IAAIY,gBAAgB,CAAC,MAAM,EAAEZ,QAAQ,EAAEf,UAAU,CAAC,EAAE;QACzLQ,MAAM,CAAC3B,CAAC,CAAC,CAACsC,SAAS,GAAGnB,UAAU;QAChCP,cAAc,CAACQ,KAAK,CAAC,GAAG,IAAI;QAC5B;MACF;IACF;EACF,CAAC,CAAC;AACJ;AACA,SAASW,cAAcA,CAACJ,MAAM,EAAEf,cAAc,EAAE4B,QAAQ,EAAE;EACxDtE,IAAI,CAAC0C,cAAc,EAAE,UAAUO,UAAU,EAAE;IACzC,IAAI,CAACA,UAAU,EAAE;MACf;IACF;IACA;IACA,IAAI0B,UAAU;IACd,IAAIE,OAAO,GAAG,CAAC;IACf;IACA;IACA,CAACF,UAAU,GAAGlB,MAAM,CAACoB,OAAO;IAC5B;IACA;IACA;IACA;IACA;IACA;IAAA,MACMF,UAAU,CAACP,SAAS,IAAID,qBAAqB,CAACQ,UAAU,CAACX,QAAQ,CAAC;IACxE;IACAW,UAAU,CAACX,QAAQ,IAAIf,UAAU,CAACI,EAAE,IAAI,IAAI,IAAI,CAACuB,gBAAgB,CAAC,IAAI,EAAE3B,UAAU,EAAE0B,UAAU,CAACX,QAAQ,CAAC,CAAC,EAAE;MACzGa,OAAO,EAAE;IACX;IACA,IAAIF,UAAU,EAAE;MACdA,UAAU,CAACP,SAAS,GAAGnB,UAAU;MACjC0B,UAAU,CAACL,QAAQ,GAAGA,QAAQ;IAChC,CAAC,MAAM;MACLb,MAAM,CAACS,IAAI,CAAC;QACVE,SAAS,EAAEnB,UAAU;QACrBqB,QAAQ,EAAEA,QAAQ;QAClBN,QAAQ,EAAE,IAAI;QACdK,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IACAQ,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AACA,SAASf,uBAAuBA,CAACL,MAAM,EAAEf,cAAc,EAAE;EACvD1C,IAAI,CAAC0C,cAAc,EAAE,UAAUO,UAAU,EAAE;IACzC;IACA;IACAQ,MAAM,CAACS,IAAI,CAAC;MACVE,SAAS,EAAEnB,UAAU;MACrBqB,QAAQ,EAAE,IAAI;MACdN,QAAQ,EAAE,IAAI;MACdK,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA,SAASN,aAAaA,CAACe,SAAS,EAAE;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,KAAK,GAAG5E,aAAa,CAAC,CAAC;EAC3BH,IAAI,CAAC8E,SAAS,EAAE,UAAUE,IAAI,EAAE;IAC9B,IAAIhB,QAAQ,GAAGgB,IAAI,CAAChB,QAAQ;IAC5BA,QAAQ,IAAIe,KAAK,CAACd,GAAG,CAACD,QAAQ,CAACX,EAAE,EAAE2B,IAAI,CAAC;EAC1C,CAAC,CAAC;EACFhF,IAAI,CAAC8E,SAAS,EAAE,UAAUE,IAAI,EAAE;IAC9B,IAAItD,GAAG,GAAGsD,IAAI,CAACZ,SAAS;IACxB;IACA/D,MAAM,CAAC,CAACqB,GAAG,IAAIA,GAAG,CAAC2B,EAAE,IAAI,IAAI,IAAI,CAAC0B,KAAK,CAACL,GAAG,CAAChD,GAAG,CAAC2B,EAAE,CAAC,IAAI0B,KAAK,CAACL,GAAG,CAAChD,GAAG,CAAC2B,EAAE,CAAC,KAAK2B,IAAI,EAAE,iBAAiB,IAAItD,GAAG,IAAIA,GAAG,CAAC2B,EAAE,CAAC,CAAC;IACvH3B,GAAG,IAAIA,GAAG,CAAC2B,EAAE,IAAI,IAAI,IAAI0B,KAAK,CAACd,GAAG,CAACvC,GAAG,CAAC2B,EAAE,EAAE2B,IAAI,CAAC;IAChD,CAACA,IAAI,CAACX,OAAO,KAAKW,IAAI,CAACX,OAAO,GAAG,CAAC,CAAC,CAAC;EACtC,CAAC,CAAC;EACF;EACArE,IAAI,CAAC8E,SAAS,EAAE,UAAUE,IAAI,EAAE9B,KAAK,EAAE;IACrC,IAAIc,QAAQ,GAAGgB,IAAI,CAAChB,QAAQ;IAC5B,IAAItC,GAAG,GAAGsD,IAAI,CAACZ,SAAS;IACxB,IAAIC,OAAO,GAAGW,IAAI,CAACX,OAAO;IAC1B,IAAI,CAACpE,QAAQ,CAACyB,GAAG,CAAC,EAAE;MAClB;IACF;IACA;IACA;IACA;IACA;IACA2C,OAAO,CAACb,IAAI,GAAG9B,GAAG,CAAC8B,IAAI,IAAI,IAAI,GAAGgB,iBAAiB,CAAC9C,GAAG,CAAC8B,IAAI,CAAC,GAAGQ,QAAQ,GAAGA,QAAQ,CAACR;IACpF;IACA;IAAA,EACEpC,2BAA2B,GAAG8B,KAAK;IACrC,IAAIc,QAAQ,EAAE;MACZK,OAAO,CAAChB,EAAE,GAAGmB,iBAAiB,CAACR,QAAQ,CAACX,EAAE,CAAC;IAC7C,CAAC,MAAM,IAAI3B,GAAG,CAAC2B,EAAE,IAAI,IAAI,EAAE;MACzBgB,OAAO,CAAChB,EAAE,GAAGmB,iBAAiB,CAAC9C,GAAG,CAAC2B,EAAE,CAAC;IACxC,CAAC,MAAM;MACL;MACA;MACA;MACA;MACA;MACA,IAAI4B,KAAK,GAAG,CAAC;MACb,GAAG;QACDZ,OAAO,CAAChB,EAAE,GAAG,IAAI,GAAGgB,OAAO,CAACb,IAAI,GAAG,IAAI,GAAGyB,KAAK,EAAE;MACnD,CAAC,QAAQF,KAAK,CAACL,GAAG,CAACL,OAAO,CAAChB,EAAE,CAAC;IAChC;IACA0B,KAAK,CAACd,GAAG,CAACI,OAAO,CAAChB,EAAE,EAAE2B,IAAI,CAAC;EAC7B,CAAC,CAAC;AACJ;AACA,SAASJ,gBAAgBA,CAACM,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC1C,IAAIC,IAAI,GAAGC,mBAAmB,CAACH,IAAI,CAACD,IAAI,CAAC,EAAE,IAAI,CAAC;EAChD,IAAIK,IAAI,GAAGD,mBAAmB,CAACF,IAAI,CAACF,IAAI,CAAC,EAAE,IAAI,CAAC;EAChD;EACA,OAAOG,IAAI,IAAI,IAAI,IAAIE,IAAI,IAAI,IAAI,IAAIF,IAAI,KAAKE,IAAI;AACtD;AACA;AACA;AACA;AACA,SAASf,iBAAiBA,CAACgB,GAAG,EAAE;EAC9B,IAAIrC,OAAO,CAACzC,GAAG,CAAC0C,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIoC,GAAG,IAAI,IAAI,EAAE;MACf,MAAM,IAAIC,KAAK,CAAC,CAAC;IACnB;EACF;EACA,OAAOH,mBAAmB,CAACE,GAAG,EAAE,EAAE,CAAC;AACrC;AACA,OAAO,SAASF,mBAAmBA,CAACI,QAAQ,EAAEC,YAAY,EAAE;EAC1D,IAAID,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAOC,YAAY;EACrB;EACA,OAAOrF,QAAQ,CAACoF,QAAQ,CAAC,GAAGA,QAAQ,GAAGjF,QAAQ,CAACiF,QAAQ,CAAC,IAAIlF,YAAY,CAACkF,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,GAAGC,YAAY;AACpH;AACA,SAASpC,sBAAsBA,CAACmC,QAAQ,EAAE;EACxC,IAAIvC,OAAO,CAACzC,GAAG,CAAC0C,QAAQ,KAAK,YAAY,EAAE;IACzCrC,IAAI,CAAC,GAAG,GAAG2E,QAAQ,GAAG,sDAAsD,CAAC;EAC/E;AACF;AACA,SAASpC,eAAeA,CAACoC,QAAQ,EAAE;EACjC,OAAOlF,YAAY,CAACkF,QAAQ,CAAC,IAAI/E,SAAS,CAAC+E,QAAQ,CAAC;AACtD;AACA,OAAO,SAASE,eAAeA,CAACC,cAAc,EAAE;EAC9C,IAAIrC,IAAI,GAAGqC,cAAc,CAACrC,IAAI;EAC9B;EACA,OAAO,CAAC,EAAEA,IAAI,IAAIA,IAAI,CAACjD,OAAO,CAACa,2BAA2B,CAAC,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+C,qBAAqBA,CAAClB,UAAU,EAAE;EAChD,OAAOA,UAAU,IAAIA,UAAU,CAACI,EAAE,IAAI,IAAI,IAAImB,iBAAiB,CAACvB,UAAU,CAACI,EAAE,CAAC,CAAC9C,OAAO,CAACc,4BAA4B,CAAC,KAAK,CAAC;AAC5H;AACA,OAAO,SAASyE,uBAAuBA,CAACC,QAAQ,EAAE;EAChD,OAAO1E,4BAA4B,GAAG0E,QAAQ;AAChD;AACA,OAAO,SAASC,yBAAyBA,CAACC,aAAa,EAAEC,QAAQ,EAAEC,kBAAkB,EAAE;EACrF;EACAnG,IAAI,CAACiG,aAAa,EAAE,UAAUjB,IAAI,EAAE;IAClC,IAAIZ,SAAS,GAAGY,IAAI,CAACZ,SAAS;IAC9B,IAAInE,QAAQ,CAACmE,SAAS,CAAC,EAAE;MACvBY,IAAI,CAACX,OAAO,CAAC6B,QAAQ,GAAGA,QAAQ;MAChClB,IAAI,CAACX,OAAO,CAAC+B,OAAO,GAAGC,gBAAgB,CAACH,QAAQ,EAAE9B,SAAS,EAAEY,IAAI,CAAChB,QAAQ,EAAEmC,kBAAkB,CAAC;IACjG;EACF,CAAC,CAAC;AACJ;AACA,SAASE,gBAAgBA,CAACH,QAAQ,EAAEI,aAAa,EAAEC,cAAc,EAAEJ,kBAAkB,EAAE;EACrF,IAAIC,OAAO,GAAGE,aAAa,CAACE,IAAI,GAAGF,aAAa,CAACE,IAAI,GAAGD,cAAc,GAAGA,cAAc,CAACH;EACxF;EAAA,EACED,kBAAkB,CAACE,gBAAgB,CAACH,QAAQ,EAAEI,aAAa,CAAC;EAC9D;EACA,OAAOF,OAAO;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,eAAeA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC9C,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,IAAIC,IAAI,GAAG,CAAC,CAAC;EACbC,OAAO,CAACJ,MAAM,IAAI,EAAE,EAAEE,IAAI,CAAC;EAC3BE,OAAO,CAACH,MAAM,IAAI,EAAE,EAAEE,IAAI,EAAED,IAAI,CAAC;EACjC,OAAO,CAACG,UAAU,CAACH,IAAI,CAAC,EAAEG,UAAU,CAACF,IAAI,CAAC,CAAC;EAC3C,SAASC,OAAOA,CAACE,WAAW,EAAE5G,GAAG,EAAE6G,QAAQ,EAAE;IAC3C,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGiF,WAAW,CAAChF,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACtD,IAAIoF,QAAQ,GAAG5B,mBAAmB,CAAC0B,WAAW,CAAClF,CAAC,CAAC,CAACoF,QAAQ,EAAE,IAAI,CAAC;MACjE,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACpB;MACF;MACA,IAAIC,WAAW,GAAG7F,gBAAgB,CAAC0F,WAAW,CAAClF,CAAC,CAAC,CAACsF,SAAS,CAAC;MAC5D,IAAIC,gBAAgB,GAAGJ,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC;MACrD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGJ,WAAW,CAACnF,MAAM,EAAEsF,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;QACxD,IAAIF,SAAS,GAAGD,WAAW,CAACG,CAAC,CAAC;QAC9B,IAAID,gBAAgB,IAAIA,gBAAgB,CAACD,SAAS,CAAC,EAAE;UACnDC,gBAAgB,CAACD,SAAS,CAAC,GAAG,IAAI;QACpC,CAAC,MAAM;UACL,CAAChH,GAAG,CAAC8G,QAAQ,CAAC,KAAK9G,GAAG,CAAC8G,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC,GAAG,CAAC;QACxD;MACF;IACF;EACF;EACA,SAASL,UAAUA,CAAC3G,GAAG,EAAEoH,MAAM,EAAE;IAC/B,IAAI/D,MAAM,GAAG,EAAE;IACf,KAAK,IAAI3B,CAAC,IAAI1B,GAAG,EAAE;MACjB,IAAIA,GAAG,CAAC8B,cAAc,CAACJ,CAAC,CAAC,IAAI1B,GAAG,CAAC0B,CAAC,CAAC,IAAI,IAAI,EAAE;QAC3C,IAAI0F,MAAM,EAAE;UACV/D,MAAM,CAACS,IAAI,CAAC,CAACpC,CAAC,CAAC;QACjB,CAAC,MAAM;UACL,IAAIqF,WAAW,GAAGJ,UAAU,CAAC3G,GAAG,CAAC0B,CAAC,CAAC,EAAE,IAAI,CAAC;UAC1CqF,WAAW,CAACnF,MAAM,IAAIyB,MAAM,CAACS,IAAI,CAAC;YAChCgD,QAAQ,EAAEpF,CAAC;YACXsF,SAAS,EAAED;UACb,CAAC,CAAC;QACJ;MACF;IACF;IACA,OAAO1D,MAAM;EACf;AACF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgE,cAAcA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC5C,IAAIA,OAAO,CAACC,eAAe,IAAI,IAAI,EAAE;IACnC,OAAOD,OAAO,CAACC,eAAe;EAChC,CAAC,MAAM,IAAID,OAAO,CAACP,SAAS,IAAI,IAAI,EAAE;IACpC,OAAOlH,OAAO,CAACyH,OAAO,CAACP,SAAS,CAAC,GAAGhH,GAAG,CAACuH,OAAO,CAACP,SAAS,EAAE,UAAU7F,KAAK,EAAE;MAC1E,OAAOmG,IAAI,CAACG,eAAe,CAACtG,KAAK,CAAC;IACpC,CAAC,CAAC,GAAGmG,IAAI,CAACG,eAAe,CAACF,OAAO,CAACP,SAAS,CAAC;EAC9C,CAAC,MAAM,IAAIO,OAAO,CAACnE,IAAI,IAAI,IAAI,EAAE;IAC/B,OAAOtD,OAAO,CAACyH,OAAO,CAACnE,IAAI,CAAC,GAAGpD,GAAG,CAACuH,OAAO,CAACnE,IAAI,EAAE,UAAUjC,KAAK,EAAE;MAChE,OAAOmG,IAAI,CAACI,WAAW,CAACvG,KAAK,CAAC;IAChC,CAAC,CAAC,GAAGmG,IAAI,CAACI,WAAW,CAACH,OAAO,CAACnE,IAAI,CAAC;EACrC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuE,SAASA,CAAA,EAAG;EAC1B,IAAIpG,GAAG,GAAG,aAAa,GAAGqG,gBAAgB,EAAE;EAC5C,OAAO,UAAUC,OAAO,EAAE;IACxB,OAAOA,OAAO,CAACtG,GAAG,CAAC,KAAKsG,OAAO,CAACtG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C,CAAC;AACH;AACA,IAAIqG,gBAAgB,GAAGpH,eAAe,CAAC,CAAC;AACxC;AACA;AACA;AACA,OAAO,SAASsH,WAAWA,CAACC,OAAO,EAAEC,WAAW,EAAE1G,GAAG,EAAE;EACrD,IAAI2G,EAAE,GAAGC,cAAc,CAACF,WAAW,EAAE1G,GAAG,CAAC;IACvC6G,iBAAiB,GAAGF,EAAE,CAACE,iBAAiB;IACxCC,cAAc,GAAGH,EAAE,CAACG,cAAc;IAClCC,MAAM,GAAGJ,EAAE,CAACI,MAAM;EACpB,IAAIhF,MAAM,GAAGgF,MAAM;EACnB,IAAIC,eAAe,GAAGhH,GAAG,GAAGA,GAAG,CAACgH,eAAe,GAAG,IAAI;EACtD,IAAI,CAACH,iBAAiB,IAAIG,eAAe,EAAE;IACzCF,cAAc,CAACvE,GAAG,CAACyE,eAAe,EAAE,CAAC,CAAC,CAAC;EACzC;EACAF,cAAc,CAACxI,IAAI,CAAC,UAAU2I,WAAW,EAAEzC,QAAQ,EAAE;IACnD,IAAI0C,WAAW,GAAGC,wBAAwB,CAACV,OAAO,EAAEjC,QAAQ,EAAEyC,WAAW,EAAE;MACzEG,UAAU,EAAEJ,eAAe,KAAKxC,QAAQ;MACxC6C,SAAS,EAAErH,GAAG,IAAIA,GAAG,CAACqH,SAAS,IAAI,IAAI,GAAGrH,GAAG,CAACqH,SAAS,GAAG,IAAI;MAC9DC,UAAU,EAAEtH,GAAG,IAAIA,GAAG,CAACsH,UAAU,IAAI,IAAI,GAAGtH,GAAG,CAACsH,UAAU,GAAG;IAC/D,CAAC,CAAC;IACFvF,MAAM,CAACyC,QAAQ,GAAG,QAAQ,CAAC,GAAG0C,WAAW,CAACK,MAAM;IAChDxF,MAAM,CAACyC,QAAQ,GAAG,OAAO,CAAC,GAAG0C,WAAW,CAACK,MAAM,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC;EACF,OAAOxF,MAAM;AACf;AACA,OAAO,SAAS6E,cAAcA,CAACF,WAAW,EAAE1G,GAAG,EAAE;EAC/C,IAAIwH,MAAM;EACV,IAAI5I,QAAQ,CAAC8H,WAAW,CAAC,EAAE;IACzB,IAAIe,GAAG,GAAG,CAAC,CAAC;IACZA,GAAG,CAACf,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC;IAC9Bc,MAAM,GAAGC,GAAG;EACd,CAAC,MAAM;IACLD,MAAM,GAAGd,WAAW;EACtB;EACA,IAAII,cAAc,GAAGrI,aAAa,CAAC,CAAC;EACpC,IAAIsI,MAAM,GAAG,CAAC,CAAC;EACf,IAAIF,iBAAiB,GAAG,KAAK;EAC7BvI,IAAI,CAACkJ,MAAM,EAAE,UAAU3H,KAAK,EAAEI,GAAG,EAAE;IACjC;IACA,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,iBAAiB,EAAE;MACpD8G,MAAM,CAAC9G,GAAG,CAAC,GAAGJ,KAAK;MACnB;IACF;IACA,IAAI6H,SAAS,GAAGzH,GAAG,CAAC0H,KAAK,CAAC,wBAAwB,CAAC,IAAI,EAAE;IACzD,IAAInD,QAAQ,GAAGkD,SAAS,CAAC,CAAC,CAAC;IAC3B,IAAIE,SAAS,GAAG,CAACF,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEG,WAAW,CAAC,CAAC;IAClD,IAAI,CAACrD,QAAQ,IAAI,CAACoD,SAAS,IAAI5H,GAAG,IAAIA,GAAG,CAAC8H,gBAAgB,IAAIjJ,OAAO,CAACmB,GAAG,CAAC8H,gBAAgB,EAAEtD,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzG;IACF;IACAqC,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC,CAACrC,QAAQ;IACnD,IAAIyC,WAAW,GAAGH,cAAc,CAAC9D,GAAG,CAACwB,QAAQ,CAAC,IAAIsC,cAAc,CAACvE,GAAG,CAACiC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAClFyC,WAAW,CAACW,SAAS,CAAC,GAAG/H,KAAK;EAChC,CAAC,CAAC;EACF,OAAO;IACLgH,iBAAiB,EAAEA,iBAAiB;IACpCC,cAAc,EAAEA,cAAc;IAC9BC,MAAM,EAAEA;EACV,CAAC;AACH;AACA,OAAO,IAAIgB,gBAAgB,GAAG;EAC5BX,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,KAAK;EAChBC,UAAU,EAAE;AACd,CAAC;AACD,OAAO,IAAIU,kBAAkB,GAAG;EAC9BZ,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE;AACd,CAAC;AACD,OAAO,SAASH,wBAAwBA,CAACV,OAAO,EAAEjC,QAAQ,EAAEyD,UAAU,EAAEjI,GAAG,EAAE;EAC3EA,GAAG,GAAGA,GAAG,IAAI+H,gBAAgB;EAC7B,IAAIG,WAAW,GAAGD,UAAU,CAACzG,KAAK;EAClC,IAAI2G,QAAQ,GAAGF,UAAU,CAACtG,EAAE;EAC5B,IAAIyG,UAAU,GAAGH,UAAU,CAACnG,IAAI;EAChC,IAAIC,MAAM,GAAG;IACXwF,MAAM,EAAE,IAAI;IACZc,SAAS,EAAEH,WAAW,IAAI,IAAI,IAAIC,QAAQ,IAAI,IAAI,IAAIC,UAAU,IAAI;EACtE,CAAC;EACD,IAAI,CAACrG,MAAM,CAACsG,SAAS,EAAE;IACrB;IACA,IAAIC,SAAS,GAAG,KAAK,CAAC;IACtBvG,MAAM,CAACwF,MAAM,GAAGvH,GAAG,CAACoH,UAAU,KAAKkB,SAAS,GAAG7B,OAAO,CAAC8B,YAAY,CAAC/D,QAAQ,CAAC,CAAC,GAAG,CAAC8D,SAAS,CAAC,GAAG,EAAE;IACjG,OAAOvG,MAAM;EACf;EACA,IAAImG,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,KAAK,EAAE;IACnDvJ,MAAM,CAACqB,GAAG,CAACsH,UAAU,EAAE,2DAA2D,CAAC;IACnFvF,MAAM,CAACwF,MAAM,GAAG,EAAE;IAClB,OAAOxF,MAAM;EACf;EACA;EACA;EACA,IAAImG,WAAW,KAAK,KAAK,EAAE;IACzBvJ,MAAM,CAACqB,GAAG,CAACqH,SAAS,EAAE,+CAA+C,CAAC;IACtEa,WAAW,GAAGC,QAAQ,GAAGC,UAAU,GAAG,IAAI;EAC5C;EACArG,MAAM,CAACwF,MAAM,GAAGd,OAAO,CAAC+B,eAAe,CAAC;IACtChE,QAAQ,EAAEA,QAAQ;IAClBhD,KAAK,EAAE0G,WAAW;IAClBvG,EAAE,EAAEwG,QAAQ;IACZrG,IAAI,EAAEsG;EACR,CAAC,CAAC;EACF,OAAOrG,MAAM;AACf;AACA,OAAO,SAAS0G,YAAYA,CAACC,GAAG,EAAEzI,GAAG,EAAEJ,KAAK,EAAE;EAC5C6I,GAAG,CAACD,YAAY,GAAGC,GAAG,CAACD,YAAY,CAACxI,GAAG,EAAEJ,KAAK,CAAC,GAAG6I,GAAG,CAACzI,GAAG,CAAC,GAAGJ,KAAK;AACpE;AACA,OAAO,SAAS8I,YAAYA,CAACD,GAAG,EAAEzI,GAAG,EAAE;EACrC,OAAOyI,GAAG,CAACC,YAAY,GAAGD,GAAG,CAACC,YAAY,CAAC1I,GAAG,CAAC,GAAGyI,GAAG,CAACzI,GAAG,CAAC;AAC5D;AACA,OAAO,SAAS2I,oBAAoBA,CAACC,gBAAgB,EAAE;EACrD,IAAIA,gBAAgB,KAAK,MAAM,EAAE;IAC/B;IACA,OAAO7J,GAAG,CAAC8J,YAAY,GAAG,MAAM,GAAG,UAAU;EAC/C,CAAC,MAAM;IACL,OAAOD,gBAAgB,IAAI,MAAM;EACnC;AACF;AACA;AACA;AACA;AACA,OAAO,SAASE,SAASA,CAACC,KAAK,EAAEC,MAAM,CAAC;AAAA,EACtC;EACA,IAAIC,OAAO,GAAGzK,aAAa,CAAC,CAAC;EAC7B,IAAI0K,IAAI,GAAG,EAAE;EACb7K,IAAI,CAAC0K,KAAK,EAAE,UAAU1F,IAAI,EAAE;IAC1B,IAAIrD,GAAG,GAAGgJ,MAAM,CAAC3F,IAAI,CAAC;IACtB,CAAC4F,OAAO,CAAClG,GAAG,CAAC/C,GAAG,CAAC,KAAKkJ,IAAI,CAAC3G,IAAI,CAACvC,GAAG,CAAC,EAAEiJ,OAAO,CAAC3G,GAAG,CAACtC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAEuC,IAAI,CAACc,IAAI,CAAC;EACzE,CAAC,CAAC;EACF,OAAO;IACL6F,IAAI,EAAEA,IAAI;IACVD,OAAO,EAAEA;EACX,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,oBAAoBA,CAACpD,IAAI,EAAEqD,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAE9J,OAAO,EAAE;EACvF,IAAI+J,eAAe,GAAGH,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,MAAM;EAC/D,IAAIE,WAAW,IAAI,IAAI,EAAE;IACvB,OAAOA,WAAW;EACpB;EACA,IAAIxK,QAAQ,CAACwK,WAAW,CAAC,EAAE;IACzB,IAAI1J,KAAK,GAAGP,iBAAiB,CAACgK,WAAW,IAAI,CAAC,EAAEC,WAAW,EAAE9J,OAAO,CAAC;IACrE,OAAOL,KAAK,CAACS,KAAK,EAAE2J,eAAe,GAAGC,IAAI,CAACC,GAAG,CAACvK,YAAY,CAACmK,WAAW,IAAI,CAAC,CAAC,EAAEnK,YAAY,CAACoK,WAAW,CAAC,CAAC,GAAGF,SAAS,CAAC;EACxH,CAAC,MAAM,IAAIzK,QAAQ,CAAC2K,WAAW,CAAC,EAAE;IAChC,OAAO9J,OAAO,GAAG,CAAC,GAAG6J,WAAW,GAAGC,WAAW;EAChD,CAAC,MAAM;IACL,IAAII,YAAY,GAAG,EAAE;IACrB,IAAIC,OAAO,GAAGN,WAAW;IACzB,IAAIO,QAAQ,GAAGN,WAAW;IAC1B,IAAIO,QAAQ,GAAGL,IAAI,CAACC,GAAG,CAACE,OAAO,GAAGA,OAAO,CAACtJ,MAAM,GAAG,CAAC,EAAEuJ,QAAQ,CAACvJ,MAAM,CAAC;IACtE,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0J,QAAQ,EAAE,EAAE1J,CAAC,EAAE;MACjC,IAAI2J,IAAI,GAAG/D,IAAI,CAACgE,gBAAgB,CAAC5J,CAAC,CAAC;MACnC;MACA,IAAI2J,IAAI,IAAIA,IAAI,CAACjF,IAAI,KAAK,SAAS,EAAE;QACnC;QACA6E,YAAY,CAACvJ,CAAC,CAAC,GAAG,CAACX,OAAO,GAAG,CAAC,IAAImK,OAAO,GAAGA,OAAO,GAAGC,QAAQ,EAAEzJ,CAAC,CAAC;MACpE,CAAC,MAAM;QACL,IAAI6J,OAAO,GAAGL,OAAO,IAAIA,OAAO,CAACxJ,CAAC,CAAC,GAAGwJ,OAAO,CAACxJ,CAAC,CAAC,GAAG,CAAC;QACpD,IAAI8J,QAAQ,GAAGL,QAAQ,CAACzJ,CAAC,CAAC;QAC1B,IAAIP,KAAK,GAAGP,iBAAiB,CAAC2K,OAAO,EAAEC,QAAQ,EAAEzK,OAAO,CAAC;QACzDkK,YAAY,CAACvJ,CAAC,CAAC,GAAGhB,KAAK,CAACS,KAAK,EAAE2J,eAAe,GAAGC,IAAI,CAACC,GAAG,CAACvK,YAAY,CAAC8K,OAAO,CAAC,EAAE9K,YAAY,CAAC+K,QAAQ,CAAC,CAAC,GAAGb,SAAS,CAAC;MACvH;IACF;IACA,OAAOM,YAAY;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}