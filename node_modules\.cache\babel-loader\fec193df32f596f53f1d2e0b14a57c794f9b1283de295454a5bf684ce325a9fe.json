{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as polyHelper from '../helper/poly.js';\nvar PolygonShape = function () {\n  function PolygonShape() {\n    this.points = null;\n    this.smooth = 0;\n    this.smoothConstraint = null;\n  }\n  return PolygonShape;\n}();\nexport { PolygonShape };\nvar Polygon = function (_super) {\n  __extends(Polygon, _super);\n  function Polygon(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Polygon.prototype.getDefaultShape = function () {\n    return new PolygonShape();\n  };\n  Polygon.prototype.buildPath = function (ctx, shape) {\n    polyHelper.buildPath(ctx, shape, true);\n  };\n  return Polygon;\n}(Path);\n;\nPolygon.prototype.type = 'polygon';\nexport default Polygon;", "map": {"version": 3, "names": ["__extends", "Path", "polyHelper", "PolygonShape", "points", "smooth", "smoothConstraint", "Polygon", "_super", "opts", "call", "prototype", "getDefaultShape", "buildPath", "ctx", "shape", "type"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/zrender/lib/graphic/shape/Polygon.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as polyHelper from '../helper/poly.js';\nvar PolygonShape = (function () {\n    function PolygonShape() {\n        this.points = null;\n        this.smooth = 0;\n        this.smoothConstraint = null;\n    }\n    return PolygonShape;\n}());\nexport { PolygonShape };\nvar Polygon = (function (_super) {\n    __extends(Polygon, _super);\n    function Polygon(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Polygon.prototype.getDefaultShape = function () {\n        return new PolygonShape();\n    };\n    Polygon.prototype.buildPath = function (ctx, shape) {\n        polyHelper.buildPath(ctx, shape, true);\n    };\n    return Polygon;\n}(Path));\n;\nPolygon.prototype.type = 'polygon';\nexport default Polygon;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,IAAIC,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAAA,EAAG;IACpB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAChC;EACA,OAAOH,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY;AACrB,IAAII,OAAO,GAAI,UAAUC,MAAM,EAAE;EAC7BR,SAAS,CAACO,OAAO,EAAEC,MAAM,CAAC;EAC1B,SAASD,OAAOA,CAACE,IAAI,EAAE;IACnB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EAC1C;EACAF,OAAO,CAACI,SAAS,CAACC,eAAe,GAAG,YAAY;IAC5C,OAAO,IAAIT,YAAY,CAAC,CAAC;EAC7B,CAAC;EACDI,OAAO,CAACI,SAAS,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAChDb,UAAU,CAACW,SAAS,CAACC,GAAG,EAAEC,KAAK,EAAE,IAAI,CAAC;EAC1C,CAAC;EACD,OAAOR,OAAO;AAClB,CAAC,CAACN,IAAI,CAAE;AACR;AACAM,OAAO,CAACI,SAAS,CAACK,IAAI,GAAG,SAAS;AAClC,eAAeT,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}