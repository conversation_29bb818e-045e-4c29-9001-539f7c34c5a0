{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取菜单树\nexport function getMenuTree() {\n  return request({\n    url: '/menu/tree',\n    method: 'get'\n  });\n}\n\n// 获取角色菜单权限\nexport function getRoleMenus(roleId) {\n  return request({\n    url: \"/menu/role/\".concat(roleId),\n    method: 'get'\n  });\n}\n\n// 保存角色菜单权限\nexport function assignRoleMenus(roleId, menuIds) {\n  return request({\n    url: \"/menu/role/\".concat(roleId),\n    method: 'post',\n    data: menuIds\n  });\n}", "map": {"version": 3, "names": ["request", "getMenuTree", "url", "method", "getRoleMenus", "roleId", "concat", "assignRoleMenus", "menuIds", "data"], "sources": ["E:/新项目/整理6/adminweb/src/api/system/menu.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取菜单树\r\nexport function getMenuTree() {\r\n  return request({\r\n    url: '/menu/tree',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取角色菜单权限\r\nexport function getRoleMenus(roleId) {\r\n  return request({\r\n    url: `/menu/role/${roleId}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 保存角色菜单权限\r\nexport function assignRoleMenus(roleId, menuIds) {\r\n  return request({\r\n    url: `/menu/role/${roleId}`,\r\n    method: 'post',\r\n    data: menuIds\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5B,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACnC,OAAOL,OAAO,CAAC;IACbE,GAAG,gBAAAI,MAAA,CAAgBD,MAAM,CAAE;IAC3BF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,eAAeA,CAACF,MAAM,EAAEG,OAAO,EAAE;EAC/C,OAAOR,OAAO,CAAC;IACbE,GAAG,gBAAAI,MAAA,CAAgBD,MAAM,CAAE;IAC3BF,MAAM,EAAE,MAAM;IACdM,IAAI,EAAED;EACR,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}