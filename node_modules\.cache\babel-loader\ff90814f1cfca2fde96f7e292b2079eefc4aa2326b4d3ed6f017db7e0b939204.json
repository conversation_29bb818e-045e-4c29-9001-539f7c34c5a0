{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport EffectSymbol from '../helper/EffectSymbol.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport pointsLayout from '../../layout/points.js';\nimport ChartView from '../../view/Chart.js';\nvar EffectScatterView = /** @class */function (_super) {\n  __extends(EffectScatterView, _super);\n  function EffectScatterView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = EffectScatterView.type;\n    return _this;\n  }\n  EffectScatterView.prototype.init = function () {\n    this._symbolDraw = new SymbolDraw(EffectSymbol);\n  };\n  EffectScatterView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var effectSymbolDraw = this._symbolDraw;\n    effectSymbolDraw.updateData(data, {\n      clipShape: this._getClipShape(seriesModel)\n    });\n    this.group.add(effectSymbolDraw.group);\n  };\n  EffectScatterView.prototype._getClipShape = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    var clipArea = coordSys && coordSys.getArea && coordSys.getArea();\n    return seriesModel.get('clip', true) ? clipArea : null;\n  };\n  EffectScatterView.prototype.updateTransform = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    this.group.dirty();\n    var res = pointsLayout('').reset(seriesModel, ecModel, api);\n    if (res.progress) {\n      res.progress({\n        start: 0,\n        end: data.count(),\n        count: data.count()\n      }, data);\n    }\n    this._symbolDraw.updateLayout();\n  };\n  EffectScatterView.prototype._updateGroupTransform = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.getRoamTransform) {\n      this.group.transform = matrix.clone(coordSys.getRoamTransform());\n      this.group.decomposeTransform();\n    }\n  };\n  EffectScatterView.prototype.remove = function (ecModel, api) {\n    this._symbolDraw && this._symbolDraw.remove(true);\n  };\n  EffectScatterView.type = 'effectScatter';\n  return EffectScatterView;\n}(ChartView);\nexport default EffectScatterView;", "map": {"version": 3, "names": ["__extends", "SymbolDraw", "EffectSymbol", "matrix", "pointsLayout", "ChartView", "EffectScatterView", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "_symbolDraw", "render", "seriesModel", "ecModel", "api", "data", "getData", "effectSymbolDraw", "updateData", "clipShape", "_getClipShape", "group", "add", "coordSys", "coordinateSystem", "clipArea", "getArea", "get", "updateTransform", "dirty", "res", "reset", "progress", "start", "end", "count", "updateLayout", "_updateGroupTransform", "getRoamTransform", "transform", "clone", "decomposeTransform", "remove"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/echarts/lib/chart/effectScatter/EffectScatterView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport EffectSymbol from '../helper/EffectSymbol.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport pointsLayout from '../../layout/points.js';\nimport ChartView from '../../view/Chart.js';\nvar EffectScatterView = /** @class */function (_super) {\n  __extends(EffectScatterView, _super);\n  function EffectScatterView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = EffectScatterView.type;\n    return _this;\n  }\n  EffectScatterView.prototype.init = function () {\n    this._symbolDraw = new SymbolDraw(EffectSymbol);\n  };\n  EffectScatterView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var effectSymbolDraw = this._symbolDraw;\n    effectSymbolDraw.updateData(data, {\n      clipShape: this._getClipShape(seriesModel)\n    });\n    this.group.add(effectSymbolDraw.group);\n  };\n  EffectScatterView.prototype._getClipShape = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    var clipArea = coordSys && coordSys.getArea && coordSys.getArea();\n    return seriesModel.get('clip', true) ? clipArea : null;\n  };\n  EffectScatterView.prototype.updateTransform = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    this.group.dirty();\n    var res = pointsLayout('').reset(seriesModel, ecModel, api);\n    if (res.progress) {\n      res.progress({\n        start: 0,\n        end: data.count(),\n        count: data.count()\n      }, data);\n    }\n    this._symbolDraw.updateLayout();\n  };\n  EffectScatterView.prototype._updateGroupTransform = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.getRoamTransform) {\n      this.group.transform = matrix.clone(coordSys.getRoamTransform());\n      this.group.decomposeTransform();\n    }\n  };\n  EffectScatterView.prototype.remove = function (ecModel, api) {\n    this._symbolDraw && this._symbolDraw.remove(true);\n  };\n  EffectScatterView.type = 'effectScatter';\n  return EffectScatterView;\n}(ChartView);\nexport default EffectScatterView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,IAAIC,iBAAiB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACrDP,SAAS,CAACM,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,iBAAiB,CAACK,IAAI;IACnC,OAAOH,KAAK;EACd;EACAF,iBAAiB,CAACM,SAAS,CAACC,IAAI,GAAG,YAAY;IAC7C,IAAI,CAACC,WAAW,GAAG,IAAIb,UAAU,CAACC,YAAY,CAAC;EACjD,CAAC;EACDI,iBAAiB,CAACM,SAAS,CAACG,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACxE,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,gBAAgB,GAAG,IAAI,CAACP,WAAW;IACvCO,gBAAgB,CAACC,UAAU,CAACH,IAAI,EAAE;MAChCI,SAAS,EAAE,IAAI,CAACC,aAAa,CAACR,WAAW;IAC3C,CAAC,CAAC;IACF,IAAI,CAACS,KAAK,CAACC,GAAG,CAACL,gBAAgB,CAACI,KAAK,CAAC;EACxC,CAAC;EACDnB,iBAAiB,CAACM,SAAS,CAACY,aAAa,GAAG,UAAUR,WAAW,EAAE;IACjE,IAAIW,QAAQ,GAAGX,WAAW,CAACY,gBAAgB;IAC3C,IAAIC,QAAQ,GAAGF,QAAQ,IAAIA,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACG,OAAO,CAAC,CAAC;IACjE,OAAOd,WAAW,CAACe,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAGF,QAAQ,GAAG,IAAI;EACxD,CAAC;EACDvB,iBAAiB,CAACM,SAAS,CAACoB,eAAe,GAAG,UAAUhB,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACjF,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAI,CAACK,KAAK,CAACQ,KAAK,CAAC,CAAC;IAClB,IAAIC,GAAG,GAAG9B,YAAY,CAAC,EAAE,CAAC,CAAC+B,KAAK,CAACnB,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;IAC3D,IAAIgB,GAAG,CAACE,QAAQ,EAAE;MAChBF,GAAG,CAACE,QAAQ,CAAC;QACXC,KAAK,EAAE,CAAC;QACRC,GAAG,EAAEnB,IAAI,CAACoB,KAAK,CAAC,CAAC;QACjBA,KAAK,EAAEpB,IAAI,CAACoB,KAAK,CAAC;MACpB,CAAC,EAAEpB,IAAI,CAAC;IACV;IACA,IAAI,CAACL,WAAW,CAAC0B,YAAY,CAAC,CAAC;EACjC,CAAC;EACDlC,iBAAiB,CAACM,SAAS,CAAC6B,qBAAqB,GAAG,UAAUzB,WAAW,EAAE;IACzE,IAAIW,QAAQ,GAAGX,WAAW,CAACY,gBAAgB;IAC3C,IAAID,QAAQ,IAAIA,QAAQ,CAACe,gBAAgB,EAAE;MACzC,IAAI,CAACjB,KAAK,CAACkB,SAAS,GAAGxC,MAAM,CAACyC,KAAK,CAACjB,QAAQ,CAACe,gBAAgB,CAAC,CAAC,CAAC;MAChE,IAAI,CAACjB,KAAK,CAACoB,kBAAkB,CAAC,CAAC;IACjC;EACF,CAAC;EACDvC,iBAAiB,CAACM,SAAS,CAACkC,MAAM,GAAG,UAAU7B,OAAO,EAAEC,GAAG,EAAE;IAC3D,IAAI,CAACJ,WAAW,IAAI,IAAI,CAACA,WAAW,CAACgC,MAAM,CAAC,IAAI,CAAC;EACnD,CAAC;EACDxC,iBAAiB,CAACK,IAAI,GAAG,eAAe;EACxC,OAAOL,iBAAiB;AAC1B,CAAC,CAACD,SAAS,CAAC;AACZ,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}