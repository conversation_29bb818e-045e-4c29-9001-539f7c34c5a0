{"ast": null, "code": "import _regeneratorRuntime from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _objectSpread from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { getCommissionList, getCommissionStats, exportCommissionList } from '@/api/reward/commission';\nimport moment from 'moment';\nexport default {\n  name: 'RewardList',\n  data: function data() {\n    return {\n      loading: false,\n      listQuery: {\n        page: 1,\n        limit: 10,\n        keyword: '',\n        commissionType: '',\n        dateRange: []\n      },\n      total: 0,\n      tableData: [],\n      detailVisible: false,\n      currentRecord: {},\n      todayStats: {\n        totalAmount: 0,\n        count: 0\n      },\n      weekStats: {\n        totalAmount: 0,\n        count: 0\n      },\n      monthStats: {\n        totalAmount: 0,\n        count: 0\n      },\n      totalStats: {\n        totalAmount: 0,\n        count: 0\n      },\n      typeMap: {\n        '购买赠送': 'success',\n        '推广赠送': 'primary',\n        '培育赠送': 'warning',\n        '管理赠送': 'info'\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getStats();\n  },\n  methods: {\n    // 重置查询\n    handleReset: function handleReset() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        keyword: '',\n        commissionType: '',\n        dateRange: []\n      };\n      this.getList();\n    },\n    // 导出数据\n    handleExport: function handleExport() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var params, res, blob, fileName, link;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _this.loading = true;\n              params = _objectSpread({}, _this.listQuery);\n              if (params.dateRange && params.dateRange.length === 2) {\n                params.startDate = params.dateRange[0];\n                params.endDate = params.dateRange[1];\n              }\n              delete params.dateRange;\n              _context.next = 7;\n              return exportCommissionList(params);\n            case 7:\n              res = _context.sent;\n              blob = new Blob([res], {\n                type: 'application/vnd.ms-excel'\n              });\n              fileName = \"\\u8D60\\u9001\\u8BB0\\u5F55\".concat(moment().format('YYYY-MM-DD'), \".xlsx\");\n              if ('download' in document.createElement('a')) {\n                link = document.createElement('a');\n                link.download = fileName;\n                link.style.display = 'none';\n                link.href = URL.createObjectURL(blob);\n                document.body.appendChild(link);\n                link.click();\n                URL.revokeObjectURL(link.href);\n                document.body.removeChild(link);\n              } else {\n                navigator.msSaveBlob(blob, fileName);\n              }\n              _this.$message.success('导出成功');\n              _context.next = 18;\n              break;\n            case 14:\n              _context.prev = 14;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('导出失败:', _context.t0);\n              _this.$message.error('导出失败');\n            case 18:\n              _context.prev = 18;\n              _this.loading = false;\n              return _context.finish(18);\n            case 21:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 14, 18, 21]]);\n      }))();\n    },\n    getList: function getList() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var params, res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _this2.loading = true;\n              params = _objectSpread({}, _this2.listQuery);\n              if (params.dateRange && params.dateRange.length === 2) {\n                params.startDate = params.dateRange[0];\n                params.endDate = params.dateRange[1];\n              }\n              delete params.dateRange;\n              _context2.next = 7;\n              return getCommissionList(params);\n            case 7:\n              res = _context2.sent;\n              if (res.code === 0) {\n                _this2.tableData = res.data;\n                _this2.total = res.total;\n              } else {\n                _this2.$message.error(res.msg || '获取赠送记录失败');\n              }\n              _context2.next = 15;\n              break;\n            case 11:\n              _context2.prev = 11;\n              _context2.t0 = _context2[\"catch\"](0);\n              console.error('获取赠送记录失败:', _context2.t0);\n              _this2.$message.error('获取赠送记录失败');\n            case 15:\n              _context2.prev = 15;\n              _this2.loading = false;\n              return _context2.finish(15);\n            case 18:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 11, 15, 18]]);\n      }))();\n    },\n    getStats: function getStats() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return getCommissionStats();\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0) {\n                _this3.todayStats = _this3.formatStatistics(res.today);\n                _this3.weekStats = _this3.formatStatistics(res.week);\n                _this3.monthStats = _this3.formatStatistics(res.month);\n                _this3.totalStats = _this3.formatStatistics(res.total);\n              }\n              _context3.next = 10;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              console.error('获取统计数据失败:', _context3.t0);\n            case 10:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    handleDetail: function handleDetail(row) {\n      this.currentRecord = _objectSpread({}, row);\n      this.detailVisible = true;\n    },\n    getCommissionTypeName: function getCommissionTypeName(type) {\n      var typeMap = {\n        1: '购买赠送',\n        2: '推广赠送',\n        3: '培育赠送',\n        4: '管理赠送'\n      };\n      return typeMap[type] || '未知类型';\n    },\n    getTypeTag: function getTypeTag(type) {\n      var typeTagMap = {\n        1: 'success',\n        2: 'primary',\n        3: 'warning',\n        4: 'info'\n      };\n      return typeTagMap[type] || '';\n    },\n    formatNumber: function formatNumber(num) {\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    },\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '-';\n      return moment(time).format('YYYY-MM-DD HH:mm:ss');\n    },\n    formatStatistics: function formatStatistics(stats) {\n      return {\n        totalAmount: stats.totalAmount || 0,\n        count: stats.count || 0\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["getCommissionList", "getCommissionStats", "exportCommissionList", "moment", "name", "data", "loading", "list<PERSON>uery", "page", "limit", "keyword", "commissionType", "date<PERSON><PERSON><PERSON>", "total", "tableData", "detailVisible", "currentRecord", "todayStats", "totalAmount", "count", "weekStats", "monthStats", "totalStats", "typeMap", "created", "getList", "getStats", "methods", "handleReset", "handleExport", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "params", "res", "blob", "fileName", "link", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "length", "startDate", "endDate", "sent", "Blob", "type", "concat", "format", "document", "createElement", "download", "style", "display", "href", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msSaveBlob", "$message", "success", "t0", "console", "error", "finish", "stop", "_this2", "_callee2", "_callee2$", "_context2", "code", "msg", "_this3", "_callee3", "_callee3$", "_context3", "formatStatistics", "today", "week", "month", "handleSizeChange", "val", "handleCurrentChange", "handleDetail", "row", "getCommissionTypeName", "getTypeTag", "typeTagMap", "formatNumber", "num", "toString", "replace", "formatDateTime", "time", "stats"], "sources": ["src/views/reward/list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 数据统计分析区域 -->\r\n      <el-row :gutter=\"20\" class=\"statistics-container\" style=\"margin-bottom: 20px;\">\r\n        <el-col :span=\"6\">\r\n          <el-card class=\"box-card total-statistics\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>总赠送统计</span>\r\n            </div>\r\n            <div class=\"statistics-content\">\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放总额</div>\r\n                <div class=\"value highlight\">¥ {{ formatNumber(totalStats.totalAmount) }}</div>\r\n              </div>\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放笔数</div>\r\n                <div class=\"value highlight\">{{ totalStats.count }} 笔</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"6\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>今日赠送统计</span>\r\n            </div>\r\n            <div class=\"statistics-content\">\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放总额</div>\r\n                <div class=\"value\">¥ {{ formatNumber(todayStats.totalAmount) }}</div>\r\n              </div>\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放笔数</div>\r\n                <div class=\"value\">{{ todayStats.count }} 笔</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>本周赠送统计</span>\r\n            </div>\r\n            <div class=\"statistics-content\">\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放总额</div>\r\n                <div class=\"value\">¥ {{ formatNumber(weekStats.totalAmount) }}</div>\r\n              </div>\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放笔数</div>\r\n                <div class=\"value\">{{ weekStats.count }} 笔</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>本月赠送统计</span>\r\n            </div>\r\n            <div class=\"statistics-content\">\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放总额</div>\r\n                <div class=\"value\">¥ {{ formatNumber(monthStats.totalAmount) }}</div>\r\n              </div>\r\n              <div class=\"statistics-item\">\r\n                <div class=\"label\">发放笔数</div>\r\n                <div class=\"value\">{{ monthStats.count }} 笔</div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <div class=\"filter-line\">\r\n          <el-input\r\n            v-model=\"listQuery.keyword\"\r\n            placeholder=\"用户名/手机号\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 180px\"\r\n            class=\"filter-item\"\r\n          />\r\n          <el-select\r\n            v-model=\"listQuery.commissionType\"\r\n            placeholder=\"赠送类型\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 130px\"\r\n            class=\"filter-item\"\r\n          >\r\n            <el-option label=\"购买赠送\" :value=\"1\" />\r\n            <el-option label=\"推广赠送\" :value=\"2\" />\r\n            <el-option label=\"培育赠送\" :value=\"3\" />\r\n            <el-option label=\"管理赠送\" :value=\"4\" />\r\n          </el-select>\r\n          <el-date-picker\r\n            v-model=\"listQuery.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"YYYY-MM-DD\"\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            class=\"filter-item\"\r\n          />\r\n          <div class=\"filter-buttons\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"getList\" :loading=\"loading\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" size=\"small\" @click=\"handleReset\">重置</el-button>\r\n            <el-button type=\"warning\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExport\">导出</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n        <el-table-column label=\"用户名称\" prop=\"username\" align=\"center\" min-width=\"100\" />\r\n        <el-table-column label=\"手机号码\" prop=\"phone\" align=\"center\" min-width=\"120\" />\r\n        <el-table-column label=\"赠送类型\" prop=\"commissionType\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getTypeTag(scope.row.commissionType)\">\r\n              {{ getCommissionTypeName(scope.row.commissionType) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"赠送流量\" align=\"center\" min-width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">¥ {{ scope.row.commissionAmount }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"赠送状态\" align=\"center\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.releaseStatus === 1 ? 'success' : 'info'\">\r\n              {{ scope.row.releaseStatus === 1 ? '已赠送' : '待赠送' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"赠送时间\" align=\"center\" min-width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.releaseTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" prop=\"remark\" align=\"center\" min-width=\"120\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"80\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog title=\"赠送详情\" :visible.sync=\"detailVisible\" width=\"600px\">\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"用户名称\">{{ currentRecord.username }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号码\">{{ currentRecord.phone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赠送类型\">\r\n          <el-tag :type=\"getTypeTag(currentRecord.commissionType)\">\r\n            {{ getCommissionTypeName(currentRecord.commissionType) }}\r\n          </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"赠送流量\">¥ {{ currentRecord.commissionAmount }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"赠送状态\">\r\n          <el-tag :type=\"currentRecord.releaseStatus === 1 ? 'success' : 'info'\">\r\n            {{ currentRecord.releaseStatus === 1 ? '已赠送' : '待赠送' }}\r\n          </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"赠送时间\">{{ formatDateTime(currentRecord.releaseTime) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"创建时间\">{{ formatDateTime(currentRecord.createTime) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"备注\">{{ currentRecord.remark || '-' }}</el-descriptions-item>\r\n      </el-descriptions>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCommissionList, getCommissionStats, exportCommissionList } from '@/api/reward/commission'\r\nimport moment from 'moment'\r\n\r\nexport default {\r\n  name: 'RewardList',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        keyword: '',\r\n        commissionType: '',\r\n        dateRange: []\r\n      },\r\n      total: 0,\r\n      tableData: [],\r\n      detailVisible: false,\r\n      currentRecord: {},\r\n      todayStats: {\r\n        totalAmount: 0,\r\n        count: 0\r\n      },\r\n      weekStats: {\r\n        totalAmount: 0,\r\n        count: 0\r\n      },\r\n      monthStats: {\r\n        totalAmount: 0,\r\n        count: 0\r\n      },\r\n      totalStats: {\r\n        totalAmount: 0,\r\n        count: 0\r\n      },\r\n      typeMap: {\r\n        '购买赠送': 'success',\r\n        '推广赠送': 'primary',\r\n        '培育赠送': 'warning',\r\n        '管理赠送': 'info'\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getStats()\r\n  },\r\n  methods: {\r\n    // 重置查询\r\n    handleReset() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        keyword: '',\r\n        commissionType: '',\r\n        dateRange: []\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    // 导出数据\r\n    async handleExport() {\r\n      try {\r\n        this.loading = true\r\n        const params = { ...this.listQuery }\r\n        if (params.dateRange && params.dateRange.length === 2) {\r\n          params.startDate = params.dateRange[0]\r\n          params.endDate = params.dateRange[1]\r\n        }\r\n        delete params.dateRange\r\n\r\n        const res = await exportCommissionList(params)\r\n        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })\r\n        const fileName = `赠送记录${moment().format('YYYY-MM-DD')}.xlsx`\r\n        \r\n        if ('download' in document.createElement('a')) {\r\n          const link = document.createElement('a')\r\n          link.download = fileName\r\n          link.style.display = 'none'\r\n          link.href = URL.createObjectURL(blob)\r\n          document.body.appendChild(link)\r\n          link.click()\r\n          URL.revokeObjectURL(link.href)\r\n          document.body.removeChild(link)\r\n        } else {\r\n          navigator.msSaveBlob(blob, fileName)\r\n        }\r\n        this.$message.success('导出成功')\r\n      } catch (error) {\r\n        console.error('导出失败:', error)\r\n        this.$message.error('导出失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    async getList() {\r\n      try {\r\n        this.loading = true\r\n        const params = { ...this.listQuery }\r\n        if (params.dateRange && params.dateRange.length === 2) {\r\n          params.startDate = params.dateRange[0]\r\n          params.endDate = params.dateRange[1]\r\n        }\r\n        delete params.dateRange\r\n\r\n        const res = await getCommissionList(params)\r\n        if (res.code === 0) {\r\n          this.tableData = res.data\r\n          this.total = res.total\r\n        } else {\r\n          this.$message.error(res.msg || '获取赠送记录失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取赠送记录失败:', error)\r\n        this.$message.error('获取赠送记录失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    async getStats() {\r\n      try {\r\n        const res = await getCommissionStats()\r\n        if (res.code === 0) {\r\n          this.todayStats = this.formatStatistics(res.today)\r\n          this.weekStats = this.formatStatistics(res.week)\r\n          this.monthStats = this.formatStatistics(res.month)\r\n          this.totalStats = this.formatStatistics(res.total)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取统计数据失败:', error)\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n    handleDetail(row) {\r\n      this.currentRecord = { ...row }\r\n      this.detailVisible = true\r\n    },\r\n    getCommissionTypeName(type) {\r\n      const typeMap = {\r\n        1: '购买赠送',\r\n        2: '推广赠送',\r\n        3: '培育赠送',\r\n        4: '管理赠送'\r\n      }\r\n      return typeMap[type] || '未知类型'\r\n    },\r\n    getTypeTag(type) {\r\n      const typeTagMap = {\r\n        1: 'success',\r\n        2: 'primary', \r\n        3: 'warning',\r\n        4: 'info'\r\n      }\r\n      return typeTagMap[type] || ''\r\n    },\r\n    formatNumber(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    },\r\n    formatDateTime(time) {\r\n      if (!time) return '-'\r\n      return moment(time).format('YYYY-MM-DD HH:mm:ss')\r\n    },\r\n    formatStatistics(stats) {\r\n      return {\r\n        totalAmount: stats.totalAmount || 0,\r\n        count: stats.count || 0\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    .filter-item {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n\r\n  .statistics-container {\r\n    margin-top: 20px;\r\n    \r\n    .box-card {\r\n      .statistics-content {\r\n        padding: 10px;\r\n        \r\n        .statistics-item {\r\n          margin-bottom: 10px;\r\n          \r\n          .label {\r\n            font-size: 14px;\r\n            color: #909399;\r\n            margin-bottom: 3px;\r\n          }\r\n          \r\n          .value {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #303133;\r\n            \r\n            &.highlight {\r\n              color: #409EFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.filter-container {\r\n  padding-bottom: 20px;\r\n  \r\n  .filter-line {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 15px 10px;\r\n    align-items: center;\r\n  }\r\n\r\n  .filter-item {\r\n    margin: 0;\r\n  }\r\n\r\n  .filter-buttons {\r\n    white-space: nowrap;\r\n    \r\n    .el-button {\r\n      margin-left: 0;\r\n      margin-right: 10px;\r\n      \r\n      &:last-child {\r\n        margin-right: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;AA2MA,SAAAA,iBAAA,EAAAC,kBAAA,EAAAC,oBAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,cAAA;QACAC,SAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;MACAC,aAAA;MACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA;MACAC,SAAA;QACAF,WAAA;QACAC,KAAA;MACA;MACAE,UAAA;QACAH,WAAA;QACAC,KAAA;MACA;MACAG,UAAA;QACAJ,WAAA;QACAC,KAAA;MACA;MACAI,OAAA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAArB,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,cAAA;QACAC,SAAA;MACA;MACA,KAAAa,OAAA;IACA;IAEA;IACAI,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,IAAA;QAAA,OAAAP,mBAAA,GAAAQ,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAb,KAAA,CAAAxB,OAAA;cACA6B,MAAA,GAAAU,aAAA,KAAAf,KAAA,CAAAvB,SAAA;cACA,IAAA4B,MAAA,CAAAvB,SAAA,IAAAuB,MAAA,CAAAvB,SAAA,CAAAkC,MAAA;gBACAX,MAAA,CAAAY,SAAA,GAAAZ,MAAA,CAAAvB,SAAA;gBACAuB,MAAA,CAAAa,OAAA,GAAAb,MAAA,CAAAvB,SAAA;cACA;cACA,OAAAuB,MAAA,CAAAvB,SAAA;cAAA8B,QAAA,CAAAE,IAAA;cAAA,OAEA1C,oBAAA,CAAAiC,MAAA;YAAA;cAAAC,GAAA,GAAAM,QAAA,CAAAO,IAAA;cACAZ,IAAA,OAAAa,IAAA,EAAAd,GAAA;gBAAAe,IAAA;cAAA;cACAb,QAAA,8BAAAc,MAAA,CAAAjD,MAAA,GAAAkD,MAAA;cAEA,kBAAAC,QAAA,CAAAC,aAAA;gBACAhB,IAAA,GAAAe,QAAA,CAAAC,aAAA;gBACAhB,IAAA,CAAAiB,QAAA,GAAAlB,QAAA;gBACAC,IAAA,CAAAkB,KAAA,CAAAC,OAAA;gBACAnB,IAAA,CAAAoB,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAxB,IAAA;gBACAiB,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAxB,IAAA;gBACAA,IAAA,CAAAyB,KAAA;gBACAJ,GAAA,CAAAK,eAAA,CAAA1B,IAAA,CAAAoB,IAAA;gBACAL,QAAA,CAAAQ,IAAA,CAAAI,WAAA,CAAA3B,IAAA;cACA;gBACA4B,SAAA,CAAAC,UAAA,CAAA/B,IAAA,EAAAC,QAAA;cACA;cACAR,KAAA,CAAAuC,QAAA,CAAAC,OAAA;cAAA5B,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAA6B,EAAA,GAAA7B,QAAA;cAEA8B,OAAA,CAAAC,KAAA,UAAA/B,QAAA,CAAA6B,EAAA;cACAzC,KAAA,CAAAuC,QAAA,CAAAI,KAAA;YAAA;cAAA/B,QAAA,CAAAC,IAAA;cAEAb,KAAA,CAAAxB,OAAA;cAAA,OAAAoC,QAAA,CAAAgC,MAAA;YAAA;YAAA;cAAA,OAAAhC,QAAA,CAAAiC,IAAA;UAAA;QAAA,GAAAzC,OAAA;MAAA;IAEA;IAEAT,OAAA,WAAAA,QAAA;MAAA,IAAAmD,MAAA;MAAA,OAAA7C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4C,SAAA;QAAA,IAAA1C,MAAA,EAAAC,GAAA;QAAA,OAAAJ,mBAAA,GAAAQ,IAAA,UAAAsC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApC,IAAA,GAAAoC,SAAA,CAAAnC,IAAA;YAAA;cAAAmC,SAAA,CAAApC,IAAA;cAEAiC,MAAA,CAAAtE,OAAA;cACA6B,MAAA,GAAAU,aAAA,KAAA+B,MAAA,CAAArE,SAAA;cACA,IAAA4B,MAAA,CAAAvB,SAAA,IAAAuB,MAAA,CAAAvB,SAAA,CAAAkC,MAAA;gBACAX,MAAA,CAAAY,SAAA,GAAAZ,MAAA,CAAAvB,SAAA;gBACAuB,MAAA,CAAAa,OAAA,GAAAb,MAAA,CAAAvB,SAAA;cACA;cACA,OAAAuB,MAAA,CAAAvB,SAAA;cAAAmE,SAAA,CAAAnC,IAAA;cAAA,OAEA5C,iBAAA,CAAAmC,MAAA;YAAA;cAAAC,GAAA,GAAA2C,SAAA,CAAA9B,IAAA;cACA,IAAAb,GAAA,CAAA4C,IAAA;gBACAJ,MAAA,CAAA9D,SAAA,GAAAsB,GAAA,CAAA/B,IAAA;gBACAuE,MAAA,CAAA/D,KAAA,GAAAuB,GAAA,CAAAvB,KAAA;cACA;gBACA+D,MAAA,CAAAP,QAAA,CAAAI,KAAA,CAAArC,GAAA,CAAA6C,GAAA;cACA;cAAAF,SAAA,CAAAnC,IAAA;cAAA;YAAA;cAAAmC,SAAA,CAAApC,IAAA;cAAAoC,SAAA,CAAAR,EAAA,GAAAQ,SAAA;cAEAP,OAAA,CAAAC,KAAA,cAAAM,SAAA,CAAAR,EAAA;cACAK,MAAA,CAAAP,QAAA,CAAAI,KAAA;YAAA;cAAAM,SAAA,CAAApC,IAAA;cAEAiC,MAAA,CAAAtE,OAAA;cAAA,OAAAyE,SAAA,CAAAL,MAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEAnD,QAAA,WAAAA,SAAA;MAAA,IAAAwD,MAAA;MAAA,OAAAnD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkD,SAAA;QAAA,IAAA/C,GAAA;QAAA,OAAAJ,mBAAA,GAAAQ,IAAA,UAAA4C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1C,IAAA,GAAA0C,SAAA,CAAAzC,IAAA;YAAA;cAAAyC,SAAA,CAAA1C,IAAA;cAAA0C,SAAA,CAAAzC,IAAA;cAAA,OAEA3C,kBAAA;YAAA;cAAAmC,GAAA,GAAAiD,SAAA,CAAApC,IAAA;cACA,IAAAb,GAAA,CAAA4C,IAAA;gBACAE,MAAA,CAAAjE,UAAA,GAAAiE,MAAA,CAAAI,gBAAA,CAAAlD,GAAA,CAAAmD,KAAA;gBACAL,MAAA,CAAA9D,SAAA,GAAA8D,MAAA,CAAAI,gBAAA,CAAAlD,GAAA,CAAAoD,IAAA;gBACAN,MAAA,CAAA7D,UAAA,GAAA6D,MAAA,CAAAI,gBAAA,CAAAlD,GAAA,CAAAqD,KAAA;gBACAP,MAAA,CAAA5D,UAAA,GAAA4D,MAAA,CAAAI,gBAAA,CAAAlD,GAAA,CAAAvB,KAAA;cACA;cAAAwE,SAAA,CAAAzC,IAAA;cAAA;YAAA;cAAAyC,SAAA,CAAA1C,IAAA;cAAA0C,SAAA,CAAAd,EAAA,GAAAc,SAAA;cAEAb,OAAA,CAAAC,KAAA,cAAAY,SAAA,CAAAd,EAAA;YAAA;YAAA;cAAA,OAAAc,SAAA,CAAAV,IAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IAEA;IACAO,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAApF,SAAA,CAAAE,KAAA,GAAAkF,GAAA;MACA,KAAAlE,OAAA;IACA;IACAmE,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAApF,SAAA,CAAAC,IAAA,GAAAmF,GAAA;MACA,KAAAlE,OAAA;IACA;IACAoE,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAA9E,aAAA,GAAA6B,aAAA,KAAAiD,GAAA;MACA,KAAA/E,aAAA;IACA;IACAgF,qBAAA,WAAAA,sBAAA5C,IAAA;MACA,IAAA5B,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA4B,IAAA;IACA;IACA6C,UAAA,WAAAA,WAAA7C,IAAA;MACA,IAAA8C,UAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,UAAA,CAAA9C,IAAA;IACA;IACA+C,YAAA,WAAAA,aAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,QAAA,GAAAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,OAAApG,MAAA,CAAAoG,IAAA,EAAAlD,MAAA;IACA;IACAiC,gBAAA,WAAAA,iBAAAkB,KAAA;MACA;QACAtF,WAAA,EAAAsF,KAAA,CAAAtF,WAAA;QACAC,KAAA,EAAAqF,KAAA,CAAArF,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}