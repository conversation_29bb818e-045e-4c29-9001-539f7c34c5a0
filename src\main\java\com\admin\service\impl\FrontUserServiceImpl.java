package com.admin.service.impl;

import com.admin.entity.FrontUser;
import com.admin.entity.RechargeRecord;
import com.admin.mapper.FrontUserMapper;
import com.admin.mapper.RechargeRecordMapper;
import com.admin.model.query.FrontUserQuery;
import com.admin.service.FrontUserService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FrontUserServiceImpl extends ServiceImpl<FrontUserMapper, FrontUser> implements FrontUserService {
    
    @Autowired
    private FrontUserMapper frontUserMapper;
    
    @Autowired
    private RechargeRecordMapper rechargeRecordMapper;
    

    
    @Override
    @Transactional(readOnly = true)
    public IPage<FrontUser> getUserList(Page<FrontUser> page, FrontUserQuery query) {
        return frontUserMapper.getUserList(page, query);
    }
    
    @Override
    @Transactional(readOnly = true)
    public FrontUser getUserDetail(Long id) {
        return frontUserMapper.getUserDetail(id);
    }
    
    @Override
    @Transactional
    public boolean updateUserStatus(Long id, Integer status) {
        FrontUser user = new FrontUser();
        user.setId(id);
        user.setStatus(status);
        return frontUserMapper.updateById(user) > 0;
    }

    @Override
    @Transactional
    public boolean updateUserActivatedStatus(Long id, Integer isActivated) {
        return frontUserMapper.updateActivatedStatus(id, isActivated) > 0;
    }
    
    @Override
    @Transactional
    public boolean resetPassword(Long id) {
        String encodedPwd = new org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder().encode("123456");
        return baseMapper.updatePasswordById(id, encodedPwd) > 0;
    }
    
    @Override
    @Transactional
    public boolean recharge(Long id, BigDecimal amount, String remark) {
        try {
            // 1. 获取用户信息
            FrontUser user = frontUserMapper.selectById(id);
            if (user == null) {
                log.error("充值失败：用户不存在，userId={}", id);
                return false;
            }
            
            // 2. 更新用户余额
            if (frontUserMapper.updateAvailableBalance(id, amount) > 0) {
                // 3. 添加充值记录
                RechargeRecord record = new RechargeRecord();
                record.setUserId(id);
                record.setUsername(user.getUsername());
                record.setEmail(user.getEmail());
                record.setAmount(amount);
                record.setRechargeType(2);          // 2:后台充值
                record.setAuditStatus(1);           // 1:已通过
               
                record.setCreateTime(LocalDateTime.now());
                record.setUpdateTime(LocalDateTime.now());
                record.setRemark(remark.trim()!=""?remark:(amount.compareTo(BigDecimal.ZERO)>0?"后台人工充值":"后台人工扣款"));
                rechargeRecordMapper.insert(record);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("用户充值失败: {}", e.getMessage(), e);
            throw new RuntimeException("充值失败");
        }
    }
    
    @Override
    public List<Map<String, Object>> getTeamTopology() {
        // 获取顶级用户（没有推荐人的），并关联代理级别表
        List<FrontUser> topUsers = baseMapper.getTopUsers();
        return topUsers.stream().map(this::buildUserNode).collect(Collectors.toList());
    }
    
    @Override
    public List<Map<String, Object>> getTeamTopologyByEmail(String email) {
        // TODO: 将原有基于手机号的查询逻辑改为基于邮箱的查询
        // 例如：调用mapper.getUserByEmail(email) 及相关递归/组装逻辑
        // 其余逻辑保持不变
        // 你可以将原有getTeamTopologyByPhone方法体复制过来，手机号相关的地方全部替换为邮箱
        // 根据邮箱查找用户，并关联代理级别表
        FrontUser user = baseMapper.getUserByEmail(email);
        
        if (user == null) {
            return Collections.emptyList();
        }
        
        // 构建该用户的团队树
        return Collections.singletonList(buildUserNode(user));
    }
    
    private Map<String, Object> buildUserNode(FrontUser user) {
        Map<String, Object> node = new HashMap<>();
        node.put("id", user.getId());
        node.put("userNo", user.getUserNo());
        node.put("userName", user.getUsername());
        node.put("email", user.getEmail());
   
        node.put("createTime", user.getCreateTime());
        
        // 获取直接下级用户
        List<FrontUser> children = baseMapper.getSubordinateUsers(user.getShareCode());
        
        // 设置设备数量
        node.put("teamTotalCount", user.getTeamTotalCount());       // 团队人数
        node.put("teamTodayCount", user.getTeamTodayCount()); // 今日新增设备数量
        
        
        if (!children.isEmpty()) {
            node.put("children", children.stream()
                .map(this::buildUserNode)
                .collect(Collectors.toList()));
        }
        
        return node;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resetTeamTodayCount() {
        return baseMapper.resetTeamTodayCount();
    }
   

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAvailableBalance(Long userId, BigDecimal amount) {
        try {
            // 获取用户当前信息
            FrontUser user = this.getById(userId);
            if (user == null) {
                log.error("更新用户余额失败：用户不存在，userId={}", userId);
                return false;
            }

            // 计算新的余额
            BigDecimal currentBalance = user.getAvailableBalance() != null ? user.getAvailableBalance() : BigDecimal.ZERO;
            BigDecimal newBalance = currentBalance.add(amount);

            // 更新用户余额
            user.setAvailableBalance(newBalance);
            return this.updateById(user);
        } catch (Exception e) {
            log.error("更新用户余额失败: userId={}, amount={}", userId, amount, e);
            throw new RuntimeException("更新用户余额失败", e);
        }
    }


    @Override
    public boolean hasReferrals(String shareCode) {
        return this.baseMapper.exists(new QueryWrapper<FrontUser>()
            .eq("referrer_code", shareCode));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long userId) {
        try {
            // 1. 检查用户是否存在
            FrontUser user = this.getById(userId);
            if (user == null) {
                log.error("删除用户失败：用户不存在，userId={}", userId);
                return false;
            }
            // 3. 检查用户是否有推荐的下级用户
            if (hasReferrals(user.getShareCode())) {
                log.error("删除用户失败：用户有推荐的下级用户，userId={}", userId);
                return false;
            }

            // 4. 检查用户账户余额
            if (user.getAvailableBalance() != null &&
                user.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0) {
                log.error("删除用户失败：用户账户余额大于0，userId={}", userId);
                return false;
            }

            // 5. 执行删除操作
            return this.removeById(userId);
        } catch (Exception e) {
            log.error("删除用户异常: userId={}", userId, e);
            throw new RuntimeException("删除用户失败", e);
        }
    }

    @Override
    @Transactional
    public boolean updateProfitTransferStatus(Long userId, Integer profitTransferEnabled) {
        try {
            FrontUser user = new FrontUser();
            user.setId(userId);
            user.setProfitTransferEnabled(profitTransferEnabled);
            return frontUserMapper.updateById(user) > 0;
        } catch (Exception e) {
            log.error("更新用户利润划转状态失败: userId={}, profitTransferEnabled={}", userId, profitTransferEnabled, e);
            return false;
        }
    }
}