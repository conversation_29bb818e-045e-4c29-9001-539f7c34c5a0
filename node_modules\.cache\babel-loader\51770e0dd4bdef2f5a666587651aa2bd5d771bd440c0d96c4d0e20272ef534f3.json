{"ast": null, "code": "import \"core-js/modules/es.array.fill.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { enterEmphasis, leaveEmphasis, toggleHoverEmphasis } from '../../util/states.js';\nimport { getDefaultLabel } from './labelHelper.js';\nimport { extend } from 'zrender/lib/core/util.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar _Symbol = /** @class */function (_super) {\n  __extends(_Symbol2, _super);\n  function _Symbol2(data, idx, seriesScope, opts) {\n    var _this = _super.call(this) || this;\n    _this.updateData(data, idx, seriesScope, opts);\n    return _this;\n  }\n  _Symbol2.prototype._createSymbol = function (symbolType, data, idx, symbolSize, keepAspect) {\n    // Remove paths created before\n    this.removeAll();\n    // let symbolPath = createSymbol(\n    //     symbolType, -0.5, -0.5, 1, 1, color\n    // );\n    // If width/height are set too small (e.g., set to 1) on ios10\n    // and macOS Sierra, a circle stroke become a rect, no matter what\n    // the scale is set. So we set width/height as 2. See #4150.\n    var symbolPath = createSymbol(symbolType, -1, -1, 2, 2, null, keepAspect);\n    symbolPath.attr({\n      z2: 100,\n      culling: true,\n      scaleX: symbolSize[0] / 2,\n      scaleY: symbolSize[1] / 2\n    });\n    // Rewrite drift method\n    symbolPath.drift = driftSymbol;\n    this._symbolType = symbolType;\n    this.add(symbolPath);\n  };\n  /**\n   * Stop animation\n   * @param {boolean} toLastFrame\n   */\n  _Symbol2.prototype.stopSymbolAnimation = function (toLastFrame) {\n    this.childAt(0).stopAnimation(null, toLastFrame);\n  };\n  _Symbol2.prototype.getSymbolType = function () {\n    return this._symbolType;\n  };\n  /**\n   * FIXME:\n   * Caution: This method breaks the encapsulation of this module,\n   * but it indeed brings convenience. So do not use the method\n   * unless you detailedly know all the implements of `Symbol`,\n   * especially animation.\n   *\n   * Get symbol path element.\n   */\n  _Symbol2.prototype.getSymbolPath = function () {\n    return this.childAt(0);\n  };\n  /**\n   * Highlight symbol\n   */\n  _Symbol2.prototype.highlight = function () {\n    enterEmphasis(this.childAt(0));\n  };\n  /**\n   * Downplay symbol\n   */\n  _Symbol2.prototype.downplay = function () {\n    leaveEmphasis(this.childAt(0));\n  };\n  /**\n   * @param {number} zlevel\n   * @param {number} z\n   */\n  _Symbol2.prototype.setZ = function (zlevel, z) {\n    var symbolPath = this.childAt(0);\n    symbolPath.zlevel = zlevel;\n    symbolPath.z = z;\n  };\n  _Symbol2.prototype.setDraggable = function (draggable, hasCursorOption) {\n    var symbolPath = this.childAt(0);\n    symbolPath.draggable = draggable;\n    symbolPath.cursor = !hasCursorOption && draggable ? 'move' : symbolPath.cursor;\n  };\n  /**\n   * Update symbol properties\n   */\n  _Symbol2.prototype.updateData = function (data, idx, seriesScope, opts) {\n    this.silent = false;\n    var symbolType = data.getItemVisual(idx, 'symbol') || 'circle';\n    var seriesModel = data.hostModel;\n    var symbolSize = _Symbol2.getSymbolSize(data, idx);\n    var isInit = symbolType !== this._symbolType;\n    var disableAnimation = opts && opts.disableAnimation;\n    if (isInit) {\n      var keepAspect = data.getItemVisual(idx, 'symbolKeepAspect');\n      this._createSymbol(symbolType, data, idx, symbolSize, keepAspect);\n    } else {\n      var symbolPath = this.childAt(0);\n      symbolPath.silent = false;\n      var target = {\n        scaleX: symbolSize[0] / 2,\n        scaleY: symbolSize[1] / 2\n      };\n      disableAnimation ? symbolPath.attr(target) : graphic.updateProps(symbolPath, target, seriesModel, idx);\n      saveOldStyle(symbolPath);\n    }\n    this._updateCommon(data, idx, symbolSize, seriesScope, opts);\n    if (isInit) {\n      var symbolPath = this.childAt(0);\n      if (!disableAnimation) {\n        var target = {\n          scaleX: this._sizeX,\n          scaleY: this._sizeY,\n          style: {\n            // Always fadeIn. Because it has fadeOut animation when symbol is removed..\n            opacity: symbolPath.style.opacity\n          }\n        };\n        symbolPath.scaleX = symbolPath.scaleY = 0;\n        symbolPath.style.opacity = 0;\n        graphic.initProps(symbolPath, target, seriesModel, idx);\n      }\n    }\n    if (disableAnimation) {\n      // Must stop leave transition manually if don't call initProps or updateProps.\n      this.childAt(0).stopAnimation('leave');\n    }\n  };\n  _Symbol2.prototype._updateCommon = function (data, idx, symbolSize, seriesScope, opts) {\n    var symbolPath = this.childAt(0);\n    var seriesModel = data.hostModel;\n    var emphasisItemStyle;\n    var blurItemStyle;\n    var selectItemStyle;\n    var focus;\n    var blurScope;\n    var emphasisDisabled;\n    var labelStatesModels;\n    var hoverScale;\n    var cursorStyle;\n    if (seriesScope) {\n      emphasisItemStyle = seriesScope.emphasisItemStyle;\n      blurItemStyle = seriesScope.blurItemStyle;\n      selectItemStyle = seriesScope.selectItemStyle;\n      focus = seriesScope.focus;\n      blurScope = seriesScope.blurScope;\n      labelStatesModels = seriesScope.labelStatesModels;\n      hoverScale = seriesScope.hoverScale;\n      cursorStyle = seriesScope.cursorStyle;\n      emphasisDisabled = seriesScope.emphasisDisabled;\n    }\n    if (!seriesScope || data.hasItemOption) {\n      var itemModel = seriesScope && seriesScope.itemModel ? seriesScope.itemModel : data.getItemModel(idx);\n      var emphasisModel = itemModel.getModel('emphasis');\n      emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n      selectItemStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n      blurItemStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n      focus = emphasisModel.get('focus');\n      blurScope = emphasisModel.get('blurScope');\n      emphasisDisabled = emphasisModel.get('disabled');\n      labelStatesModels = getLabelStatesModels(itemModel);\n      hoverScale = emphasisModel.getShallow('scale');\n      cursorStyle = itemModel.getShallow('cursor');\n    }\n    var symbolRotate = data.getItemVisual(idx, 'symbolRotate');\n    symbolPath.attr('rotation', (symbolRotate || 0) * Math.PI / 180 || 0);\n    var symbolOffset = normalizeSymbolOffset(data.getItemVisual(idx, 'symbolOffset'), symbolSize);\n    if (symbolOffset) {\n      symbolPath.x = symbolOffset[0];\n      symbolPath.y = symbolOffset[1];\n    }\n    cursorStyle && symbolPath.attr('cursor', cursorStyle);\n    var symbolStyle = data.getItemVisual(idx, 'style');\n    var visualColor = symbolStyle.fill;\n    if (symbolPath instanceof ZRImage) {\n      var pathStyle = symbolPath.style;\n      symbolPath.useStyle(extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolStyle));\n    } else {\n      if (symbolPath.__isEmptyBrush) {\n        // fill and stroke will be swapped if it's empty.\n        // So we cloned a new style to avoid it affecting the original style in visual storage.\n        // TODO Better implementation. No empty logic!\n        symbolPath.useStyle(extend({}, symbolStyle));\n      } else {\n        symbolPath.useStyle(symbolStyle);\n      }\n      // Disable decal because symbol scale will been applied on the decal.\n      symbolPath.style.decal = null;\n      symbolPath.setColor(visualColor, opts && opts.symbolInnerColor);\n      symbolPath.style.strokeNoScale = true;\n    }\n    var liftZ = data.getItemVisual(idx, 'liftZ');\n    var z2Origin = this._z2;\n    if (liftZ != null) {\n      if (z2Origin == null) {\n        this._z2 = symbolPath.z2;\n        symbolPath.z2 += liftZ;\n      }\n    } else if (z2Origin != null) {\n      symbolPath.z2 = z2Origin;\n      this._z2 = null;\n    }\n    var useNameLabel = opts && opts.useNameLabel;\n    setLabelStyle(symbolPath, labelStatesModels, {\n      labelFetcher: seriesModel,\n      labelDataIndex: idx,\n      defaultText: getLabelDefaultText,\n      inheritColor: visualColor,\n      defaultOpacity: symbolStyle.opacity\n    });\n    // Do not execute util needed.\n    function getLabelDefaultText(idx) {\n      return useNameLabel ? data.getName(idx) : getDefaultLabel(data, idx);\n    }\n    this._sizeX = symbolSize[0] / 2;\n    this._sizeY = symbolSize[1] / 2;\n    var emphasisState = symbolPath.ensureState('emphasis');\n    emphasisState.style = emphasisItemStyle;\n    symbolPath.ensureState('select').style = selectItemStyle;\n    symbolPath.ensureState('blur').style = blurItemStyle;\n    // null / undefined / true means to use default strategy.\n    // 0 / false / negative number / NaN / Infinity means no scale.\n    var scaleRatio = hoverScale == null || hoverScale === true ? Math.max(1.1, 3 / this._sizeY)\n    // PENDING: restrict hoverScale > 1? It seems unreasonable to scale down\n    : isFinite(hoverScale) && hoverScale > 0 ? +hoverScale : 1;\n    // always set scale to allow resetting\n    emphasisState.scaleX = this._sizeX * scaleRatio;\n    emphasisState.scaleY = this._sizeY * scaleRatio;\n    this.setSymbolScale(1);\n    toggleHoverEmphasis(this, focus, blurScope, emphasisDisabled);\n  };\n  _Symbol2.prototype.setSymbolScale = function (scale) {\n    this.scaleX = this.scaleY = scale;\n  };\n  _Symbol2.prototype.fadeOut = function (cb, seriesModel, opt) {\n    var symbolPath = this.childAt(0);\n    var dataIndex = getECData(this).dataIndex;\n    var animationOpt = opt && opt.animation;\n    // Avoid mistaken hover when fading out\n    this.silent = symbolPath.silent = true;\n    // Not show text when animating\n    if (opt && opt.fadeLabel) {\n      var textContent = symbolPath.getTextContent();\n      if (textContent) {\n        graphic.removeElement(textContent, {\n          style: {\n            opacity: 0\n          }\n        }, seriesModel, {\n          dataIndex: dataIndex,\n          removeOpt: animationOpt,\n          cb: function cb() {\n            symbolPath.removeTextContent();\n          }\n        });\n      }\n    } else {\n      symbolPath.removeTextContent();\n    }\n    graphic.removeElement(symbolPath, {\n      style: {\n        opacity: 0\n      },\n      scaleX: 0,\n      scaleY: 0\n    }, seriesModel, {\n      dataIndex: dataIndex,\n      cb: cb,\n      removeOpt: animationOpt\n    });\n  };\n  _Symbol2.getSymbolSize = function (data, idx) {\n    return normalizeSymbolSize(data.getItemVisual(idx, 'symbolSize'));\n  };\n  return _Symbol2;\n}(graphic.Group);\nfunction driftSymbol(dx, dy) {\n  this.parent.drift(dx, dy);\n}\nexport default _Symbol;", "map": {"version": 3, "names": ["__extends", "createSymbol", "normalizeSymbolOffset", "normalizeSymbolSize", "graphic", "getECData", "enterEmphasis", "leaveEmphasis", "toggleHoverEmphasis", "getDefaultLabel", "extend", "setLabelStyle", "getLabelStatesModels", "ZRImage", "saveOldStyle", "Symbol", "_super", "data", "idx", "seriesScope", "opts", "_this", "call", "updateData", "prototype", "_createSymbol", "symbolType", "symbolSize", "keepAspect", "removeAll", "symbolPath", "attr", "z2", "culling", "scaleX", "scaleY", "drift", "driftSymbol", "_symbolType", "add", "stopSymbolAnimation", "toLastFrame", "childAt", "stopAnimation", "getSymbolType", "getSymbolPath", "highlight", "downplay", "setZ", "zlevel", "z", "setDraggable", "draggable", "hasCursorOption", "cursor", "silent", "getItemVisual", "seriesModel", "hostModel", "getSymbolSize", "isInit", "disableAnimation", "target", "updateProps", "_update<PERSON><PERSON>mon", "_sizeX", "_sizeY", "style", "opacity", "initProps", "emphasisItemStyle", "blurItemStyle", "selectItemStyle", "focus", "blurScope", "emphasisDisabled", "labelStatesModels", "hoverScale", "cursorStyle", "hasItemOption", "itemModel", "getItemModel", "emphasisModel", "getModel", "getItemStyle", "get", "getShallow", "symbolRotate", "Math", "PI", "symbolOffset", "x", "y", "symbolStyle", "visualColor", "fill", "pathStyle", "useStyle", "image", "width", "height", "__isEmptyBrush", "decal", "setColor", "symbolInnerColor", "strokeNoScale", "liftZ", "z2Origin", "_z2", "useNameLabel", "labelFetcher", "labelDataIndex", "defaultText", "getLabelDefaultText", "inheritColor", "defaultOpacity", "getName", "emphasisState", "ensureState", "scaleRatio", "max", "isFinite", "setSymbolScale", "scale", "fadeOut", "cb", "opt", "dataIndex", "animationOpt", "animation", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "getTextContent", "removeElement", "removeOpt", "removeTextContent", "Group", "dx", "dy", "parent"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/echarts/lib/chart/helper/Symbol.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { enterEmphasis, leaveEmphasis, toggleHoverEmphasis } from '../../util/states.js';\nimport { getDefaultLabel } from './labelHelper.js';\nimport { extend } from 'zrender/lib/core/util.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar Symbol = /** @class */function (_super) {\n  __extends(Symbol, _super);\n  function Symbol(data, idx, seriesScope, opts) {\n    var _this = _super.call(this) || this;\n    _this.updateData(data, idx, seriesScope, opts);\n    return _this;\n  }\n  Symbol.prototype._createSymbol = function (symbolType, data, idx, symbolSize, keepAspect) {\n    // Remove paths created before\n    this.removeAll();\n    // let symbolPath = createSymbol(\n    //     symbolType, -0.5, -0.5, 1, 1, color\n    // );\n    // If width/height are set too small (e.g., set to 1) on ios10\n    // and macOS Sierra, a circle stroke become a rect, no matter what\n    // the scale is set. So we set width/height as 2. See #4150.\n    var symbolPath = createSymbol(symbolType, -1, -1, 2, 2, null, keepAspect);\n    symbolPath.attr({\n      z2: 100,\n      culling: true,\n      scaleX: symbolSize[0] / 2,\n      scaleY: symbolSize[1] / 2\n    });\n    // Rewrite drift method\n    symbolPath.drift = driftSymbol;\n    this._symbolType = symbolType;\n    this.add(symbolPath);\n  };\n  /**\n   * Stop animation\n   * @param {boolean} toLastFrame\n   */\n  Symbol.prototype.stopSymbolAnimation = function (toLastFrame) {\n    this.childAt(0).stopAnimation(null, toLastFrame);\n  };\n  Symbol.prototype.getSymbolType = function () {\n    return this._symbolType;\n  };\n  /**\n   * FIXME:\n   * Caution: This method breaks the encapsulation of this module,\n   * but it indeed brings convenience. So do not use the method\n   * unless you detailedly know all the implements of `Symbol`,\n   * especially animation.\n   *\n   * Get symbol path element.\n   */\n  Symbol.prototype.getSymbolPath = function () {\n    return this.childAt(0);\n  };\n  /**\n   * Highlight symbol\n   */\n  Symbol.prototype.highlight = function () {\n    enterEmphasis(this.childAt(0));\n  };\n  /**\n   * Downplay symbol\n   */\n  Symbol.prototype.downplay = function () {\n    leaveEmphasis(this.childAt(0));\n  };\n  /**\n   * @param {number} zlevel\n   * @param {number} z\n   */\n  Symbol.prototype.setZ = function (zlevel, z) {\n    var symbolPath = this.childAt(0);\n    symbolPath.zlevel = zlevel;\n    symbolPath.z = z;\n  };\n  Symbol.prototype.setDraggable = function (draggable, hasCursorOption) {\n    var symbolPath = this.childAt(0);\n    symbolPath.draggable = draggable;\n    symbolPath.cursor = !hasCursorOption && draggable ? 'move' : symbolPath.cursor;\n  };\n  /**\n   * Update symbol properties\n   */\n  Symbol.prototype.updateData = function (data, idx, seriesScope, opts) {\n    this.silent = false;\n    var symbolType = data.getItemVisual(idx, 'symbol') || 'circle';\n    var seriesModel = data.hostModel;\n    var symbolSize = Symbol.getSymbolSize(data, idx);\n    var isInit = symbolType !== this._symbolType;\n    var disableAnimation = opts && opts.disableAnimation;\n    if (isInit) {\n      var keepAspect = data.getItemVisual(idx, 'symbolKeepAspect');\n      this._createSymbol(symbolType, data, idx, symbolSize, keepAspect);\n    } else {\n      var symbolPath = this.childAt(0);\n      symbolPath.silent = false;\n      var target = {\n        scaleX: symbolSize[0] / 2,\n        scaleY: symbolSize[1] / 2\n      };\n      disableAnimation ? symbolPath.attr(target) : graphic.updateProps(symbolPath, target, seriesModel, idx);\n      saveOldStyle(symbolPath);\n    }\n    this._updateCommon(data, idx, symbolSize, seriesScope, opts);\n    if (isInit) {\n      var symbolPath = this.childAt(0);\n      if (!disableAnimation) {\n        var target = {\n          scaleX: this._sizeX,\n          scaleY: this._sizeY,\n          style: {\n            // Always fadeIn. Because it has fadeOut animation when symbol is removed..\n            opacity: symbolPath.style.opacity\n          }\n        };\n        symbolPath.scaleX = symbolPath.scaleY = 0;\n        symbolPath.style.opacity = 0;\n        graphic.initProps(symbolPath, target, seriesModel, idx);\n      }\n    }\n    if (disableAnimation) {\n      // Must stop leave transition manually if don't call initProps or updateProps.\n      this.childAt(0).stopAnimation('leave');\n    }\n  };\n  Symbol.prototype._updateCommon = function (data, idx, symbolSize, seriesScope, opts) {\n    var symbolPath = this.childAt(0);\n    var seriesModel = data.hostModel;\n    var emphasisItemStyle;\n    var blurItemStyle;\n    var selectItemStyle;\n    var focus;\n    var blurScope;\n    var emphasisDisabled;\n    var labelStatesModels;\n    var hoverScale;\n    var cursorStyle;\n    if (seriesScope) {\n      emphasisItemStyle = seriesScope.emphasisItemStyle;\n      blurItemStyle = seriesScope.blurItemStyle;\n      selectItemStyle = seriesScope.selectItemStyle;\n      focus = seriesScope.focus;\n      blurScope = seriesScope.blurScope;\n      labelStatesModels = seriesScope.labelStatesModels;\n      hoverScale = seriesScope.hoverScale;\n      cursorStyle = seriesScope.cursorStyle;\n      emphasisDisabled = seriesScope.emphasisDisabled;\n    }\n    if (!seriesScope || data.hasItemOption) {\n      var itemModel = seriesScope && seriesScope.itemModel ? seriesScope.itemModel : data.getItemModel(idx);\n      var emphasisModel = itemModel.getModel('emphasis');\n      emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n      selectItemStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n      blurItemStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n      focus = emphasisModel.get('focus');\n      blurScope = emphasisModel.get('blurScope');\n      emphasisDisabled = emphasisModel.get('disabled');\n      labelStatesModels = getLabelStatesModels(itemModel);\n      hoverScale = emphasisModel.getShallow('scale');\n      cursorStyle = itemModel.getShallow('cursor');\n    }\n    var symbolRotate = data.getItemVisual(idx, 'symbolRotate');\n    symbolPath.attr('rotation', (symbolRotate || 0) * Math.PI / 180 || 0);\n    var symbolOffset = normalizeSymbolOffset(data.getItemVisual(idx, 'symbolOffset'), symbolSize);\n    if (symbolOffset) {\n      symbolPath.x = symbolOffset[0];\n      symbolPath.y = symbolOffset[1];\n    }\n    cursorStyle && symbolPath.attr('cursor', cursorStyle);\n    var symbolStyle = data.getItemVisual(idx, 'style');\n    var visualColor = symbolStyle.fill;\n    if (symbolPath instanceof ZRImage) {\n      var pathStyle = symbolPath.style;\n      symbolPath.useStyle(extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolStyle));\n    } else {\n      if (symbolPath.__isEmptyBrush) {\n        // fill and stroke will be swapped if it's empty.\n        // So we cloned a new style to avoid it affecting the original style in visual storage.\n        // TODO Better implementation. No empty logic!\n        symbolPath.useStyle(extend({}, symbolStyle));\n      } else {\n        symbolPath.useStyle(symbolStyle);\n      }\n      // Disable decal because symbol scale will been applied on the decal.\n      symbolPath.style.decal = null;\n      symbolPath.setColor(visualColor, opts && opts.symbolInnerColor);\n      symbolPath.style.strokeNoScale = true;\n    }\n    var liftZ = data.getItemVisual(idx, 'liftZ');\n    var z2Origin = this._z2;\n    if (liftZ != null) {\n      if (z2Origin == null) {\n        this._z2 = symbolPath.z2;\n        symbolPath.z2 += liftZ;\n      }\n    } else if (z2Origin != null) {\n      symbolPath.z2 = z2Origin;\n      this._z2 = null;\n    }\n    var useNameLabel = opts && opts.useNameLabel;\n    setLabelStyle(symbolPath, labelStatesModels, {\n      labelFetcher: seriesModel,\n      labelDataIndex: idx,\n      defaultText: getLabelDefaultText,\n      inheritColor: visualColor,\n      defaultOpacity: symbolStyle.opacity\n    });\n    // Do not execute util needed.\n    function getLabelDefaultText(idx) {\n      return useNameLabel ? data.getName(idx) : getDefaultLabel(data, idx);\n    }\n    this._sizeX = symbolSize[0] / 2;\n    this._sizeY = symbolSize[1] / 2;\n    var emphasisState = symbolPath.ensureState('emphasis');\n    emphasisState.style = emphasisItemStyle;\n    symbolPath.ensureState('select').style = selectItemStyle;\n    symbolPath.ensureState('blur').style = blurItemStyle;\n    // null / undefined / true means to use default strategy.\n    // 0 / false / negative number / NaN / Infinity means no scale.\n    var scaleRatio = hoverScale == null || hoverScale === true ? Math.max(1.1, 3 / this._sizeY)\n    // PENDING: restrict hoverScale > 1? It seems unreasonable to scale down\n    : isFinite(hoverScale) && hoverScale > 0 ? +hoverScale : 1;\n    // always set scale to allow resetting\n    emphasisState.scaleX = this._sizeX * scaleRatio;\n    emphasisState.scaleY = this._sizeY * scaleRatio;\n    this.setSymbolScale(1);\n    toggleHoverEmphasis(this, focus, blurScope, emphasisDisabled);\n  };\n  Symbol.prototype.setSymbolScale = function (scale) {\n    this.scaleX = this.scaleY = scale;\n  };\n  Symbol.prototype.fadeOut = function (cb, seriesModel, opt) {\n    var symbolPath = this.childAt(0);\n    var dataIndex = getECData(this).dataIndex;\n    var animationOpt = opt && opt.animation;\n    // Avoid mistaken hover when fading out\n    this.silent = symbolPath.silent = true;\n    // Not show text when animating\n    if (opt && opt.fadeLabel) {\n      var textContent = symbolPath.getTextContent();\n      if (textContent) {\n        graphic.removeElement(textContent, {\n          style: {\n            opacity: 0\n          }\n        }, seriesModel, {\n          dataIndex: dataIndex,\n          removeOpt: animationOpt,\n          cb: function () {\n            symbolPath.removeTextContent();\n          }\n        });\n      }\n    } else {\n      symbolPath.removeTextContent();\n    }\n    graphic.removeElement(symbolPath, {\n      style: {\n        opacity: 0\n      },\n      scaleX: 0,\n      scaleY: 0\n    }, seriesModel, {\n      dataIndex: dataIndex,\n      cb: cb,\n      removeOpt: animationOpt\n    });\n  };\n  Symbol.getSymbolSize = function (data, idx) {\n    return normalizeSymbolSize(data.getItemVisual(idx, 'symbolSize'));\n  };\n  return Symbol;\n}(graphic.Group);\nfunction driftSymbol(dx, dy) {\n  this.parent.drift(dx, dy);\n}\nexport default Symbol;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,YAAY,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC/F,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,aAAa,EAAEC,aAAa,EAAEC,mBAAmB,QAAQ,sBAAsB;AACxF,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,OAAOC,OAAO,MAAM,8BAA8B;AAClD,SAASC,YAAY,QAAQ,oCAAoC;AACjE,IAAIC,OAAM,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC1ChB,SAAS,CAACe,QAAM,EAAEC,MAAM,CAAC;EACzB,SAASD,QAAMA,CAACE,IAAI,EAAEC,GAAG,EAAEC,WAAW,EAAEC,IAAI,EAAE;IAC5C,IAAIC,KAAK,GAAGL,MAAM,CAACM,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,UAAU,CAACN,IAAI,EAAEC,GAAG,EAAEC,WAAW,EAAEC,IAAI,CAAC;IAC9C,OAAOC,KAAK;EACd;EACAN,QAAM,CAACS,SAAS,CAACC,aAAa,GAAG,UAAUC,UAAU,EAAET,IAAI,EAAEC,GAAG,EAAES,UAAU,EAAEC,UAAU,EAAE;IACxF;IACA,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,UAAU,GAAG7B,YAAY,CAACyB,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAEE,UAAU,CAAC;IACzEE,UAAU,CAACC,IAAI,CAAC;MACdC,EAAE,EAAE,GAAG;MACPC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAEP,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACzBQ,MAAM,EAAER,UAAU,CAAC,CAAC,CAAC,GAAG;IAC1B,CAAC,CAAC;IACF;IACAG,UAAU,CAACM,KAAK,GAAGC,WAAW;IAC9B,IAAI,CAACC,WAAW,GAAGZ,UAAU;IAC7B,IAAI,CAACa,GAAG,CAACT,UAAU,CAAC;EACtB,CAAC;EACD;AACF;AACA;AACA;EACEf,QAAM,CAACS,SAAS,CAACgB,mBAAmB,GAAG,UAAUC,WAAW,EAAE;IAC5D,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,aAAa,CAAC,IAAI,EAAEF,WAAW,CAAC;EAClD,CAAC;EACD1B,QAAM,CAACS,SAAS,CAACoB,aAAa,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACN,WAAW;EACzB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvB,QAAM,CAACS,SAAS,CAACqB,aAAa,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC;EACxB,CAAC;EACD;AACF;AACA;EACE3B,QAAM,CAACS,SAAS,CAACsB,SAAS,GAAG,YAAY;IACvCxC,aAAa,CAAC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EACD;AACF;AACA;EACE3B,QAAM,CAACS,SAAS,CAACuB,QAAQ,GAAG,YAAY;IACtCxC,aAAa,CAAC,IAAI,CAACmC,OAAO,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EACD;AACF;AACA;AACA;EACE3B,QAAM,CAACS,SAAS,CAACwB,IAAI,GAAG,UAAUC,MAAM,EAAEC,CAAC,EAAE;IAC3C,IAAIpB,UAAU,GAAG,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;IAChCZ,UAAU,CAACmB,MAAM,GAAGA,MAAM;IAC1BnB,UAAU,CAACoB,CAAC,GAAGA,CAAC;EAClB,CAAC;EACDnC,QAAM,CAACS,SAAS,CAAC2B,YAAY,GAAG,UAAUC,SAAS,EAAEC,eAAe,EAAE;IACpE,IAAIvB,UAAU,GAAG,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;IAChCZ,UAAU,CAACsB,SAAS,GAAGA,SAAS;IAChCtB,UAAU,CAACwB,MAAM,GAAG,CAACD,eAAe,IAAID,SAAS,GAAG,MAAM,GAAGtB,UAAU,CAACwB,MAAM;EAChF,CAAC;EACD;AACF;AACA;EACEvC,QAAM,CAACS,SAAS,CAACD,UAAU,GAAG,UAAUN,IAAI,EAAEC,GAAG,EAAEC,WAAW,EAAEC,IAAI,EAAE;IACpE,IAAI,CAACmC,MAAM,GAAG,KAAK;IACnB,IAAI7B,UAAU,GAAGT,IAAI,CAACuC,aAAa,CAACtC,GAAG,EAAE,QAAQ,CAAC,IAAI,QAAQ;IAC9D,IAAIuC,WAAW,GAAGxC,IAAI,CAACyC,SAAS;IAChC,IAAI/B,UAAU,GAAGZ,QAAM,CAAC4C,aAAa,CAAC1C,IAAI,EAAEC,GAAG,CAAC;IAChD,IAAI0C,MAAM,GAAGlC,UAAU,KAAK,IAAI,CAACY,WAAW;IAC5C,IAAIuB,gBAAgB,GAAGzC,IAAI,IAAIA,IAAI,CAACyC,gBAAgB;IACpD,IAAID,MAAM,EAAE;MACV,IAAIhC,UAAU,GAAGX,IAAI,CAACuC,aAAa,CAACtC,GAAG,EAAE,kBAAkB,CAAC;MAC5D,IAAI,CAACO,aAAa,CAACC,UAAU,EAAET,IAAI,EAAEC,GAAG,EAAES,UAAU,EAAEC,UAAU,CAAC;IACnE,CAAC,MAAM;MACL,IAAIE,UAAU,GAAG,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;MAChCZ,UAAU,CAACyB,MAAM,GAAG,KAAK;MACzB,IAAIO,MAAM,GAAG;QACX5B,MAAM,EAAEP,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;QACzBQ,MAAM,EAAER,UAAU,CAAC,CAAC,CAAC,GAAG;MAC1B,CAAC;MACDkC,gBAAgB,GAAG/B,UAAU,CAACC,IAAI,CAAC+B,MAAM,CAAC,GAAG1D,OAAO,CAAC2D,WAAW,CAACjC,UAAU,EAAEgC,MAAM,EAAEL,WAAW,EAAEvC,GAAG,CAAC;MACtGJ,YAAY,CAACgB,UAAU,CAAC;IAC1B;IACA,IAAI,CAACkC,aAAa,CAAC/C,IAAI,EAAEC,GAAG,EAAES,UAAU,EAAER,WAAW,EAAEC,IAAI,CAAC;IAC5D,IAAIwC,MAAM,EAAE;MACV,IAAI9B,UAAU,GAAG,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;MAChC,IAAI,CAACmB,gBAAgB,EAAE;QACrB,IAAIC,MAAM,GAAG;UACX5B,MAAM,EAAE,IAAI,CAAC+B,MAAM;UACnB9B,MAAM,EAAE,IAAI,CAAC+B,MAAM;UACnBC,KAAK,EAAE;YACL;YACAC,OAAO,EAAEtC,UAAU,CAACqC,KAAK,CAACC;UAC5B;QACF,CAAC;QACDtC,UAAU,CAACI,MAAM,GAAGJ,UAAU,CAACK,MAAM,GAAG,CAAC;QACzCL,UAAU,CAACqC,KAAK,CAACC,OAAO,GAAG,CAAC;QAC5BhE,OAAO,CAACiE,SAAS,CAACvC,UAAU,EAAEgC,MAAM,EAAEL,WAAW,EAAEvC,GAAG,CAAC;MACzD;IACF;IACA,IAAI2C,gBAAgB,EAAE;MACpB;MACA,IAAI,CAACnB,OAAO,CAAC,CAAC,CAAC,CAACC,aAAa,CAAC,OAAO,CAAC;IACxC;EACF,CAAC;EACD5B,QAAM,CAACS,SAAS,CAACwC,aAAa,GAAG,UAAU/C,IAAI,EAAEC,GAAG,EAAES,UAAU,EAAER,WAAW,EAAEC,IAAI,EAAE;IACnF,IAAIU,UAAU,GAAG,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;IAChC,IAAIe,WAAW,GAAGxC,IAAI,CAACyC,SAAS;IAChC,IAAIY,iBAAiB;IACrB,IAAIC,aAAa;IACjB,IAAIC,eAAe;IACnB,IAAIC,KAAK;IACT,IAAIC,SAAS;IACb,IAAIC,gBAAgB;IACpB,IAAIC,iBAAiB;IACrB,IAAIC,UAAU;IACd,IAAIC,WAAW;IACf,IAAI3D,WAAW,EAAE;MACfmD,iBAAiB,GAAGnD,WAAW,CAACmD,iBAAiB;MACjDC,aAAa,GAAGpD,WAAW,CAACoD,aAAa;MACzCC,eAAe,GAAGrD,WAAW,CAACqD,eAAe;MAC7CC,KAAK,GAAGtD,WAAW,CAACsD,KAAK;MACzBC,SAAS,GAAGvD,WAAW,CAACuD,SAAS;MACjCE,iBAAiB,GAAGzD,WAAW,CAACyD,iBAAiB;MACjDC,UAAU,GAAG1D,WAAW,CAAC0D,UAAU;MACnCC,WAAW,GAAG3D,WAAW,CAAC2D,WAAW;MACrCH,gBAAgB,GAAGxD,WAAW,CAACwD,gBAAgB;IACjD;IACA,IAAI,CAACxD,WAAW,IAAIF,IAAI,CAAC8D,aAAa,EAAE;MACtC,IAAIC,SAAS,GAAG7D,WAAW,IAAIA,WAAW,CAAC6D,SAAS,GAAG7D,WAAW,CAAC6D,SAAS,GAAG/D,IAAI,CAACgE,YAAY,CAAC/D,GAAG,CAAC;MACrG,IAAIgE,aAAa,GAAGF,SAAS,CAACG,QAAQ,CAAC,UAAU,CAAC;MAClDb,iBAAiB,GAAGY,aAAa,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MACtEZ,eAAe,GAAGQ,SAAS,CAACG,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;MAC5Eb,aAAa,GAAGS,SAAS,CAACG,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;MACxEX,KAAK,GAAGS,aAAa,CAACG,GAAG,CAAC,OAAO,CAAC;MAClCX,SAAS,GAAGQ,aAAa,CAACG,GAAG,CAAC,WAAW,CAAC;MAC1CV,gBAAgB,GAAGO,aAAa,CAACG,GAAG,CAAC,UAAU,CAAC;MAChDT,iBAAiB,GAAGhE,oBAAoB,CAACoE,SAAS,CAAC;MACnDH,UAAU,GAAGK,aAAa,CAACI,UAAU,CAAC,OAAO,CAAC;MAC9CR,WAAW,GAAGE,SAAS,CAACM,UAAU,CAAC,QAAQ,CAAC;IAC9C;IACA,IAAIC,YAAY,GAAGtE,IAAI,CAACuC,aAAa,CAACtC,GAAG,EAAE,cAAc,CAAC;IAC1DY,UAAU,CAACC,IAAI,CAAC,UAAU,EAAE,CAACwD,YAAY,IAAI,CAAC,IAAIC,IAAI,CAACC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;IACrE,IAAIC,YAAY,GAAGxF,qBAAqB,CAACe,IAAI,CAACuC,aAAa,CAACtC,GAAG,EAAE,cAAc,CAAC,EAAES,UAAU,CAAC;IAC7F,IAAI+D,YAAY,EAAE;MAChB5D,UAAU,CAAC6D,CAAC,GAAGD,YAAY,CAAC,CAAC,CAAC;MAC9B5D,UAAU,CAAC8D,CAAC,GAAGF,YAAY,CAAC,CAAC,CAAC;IAChC;IACAZ,WAAW,IAAIhD,UAAU,CAACC,IAAI,CAAC,QAAQ,EAAE+C,WAAW,CAAC;IACrD,IAAIe,WAAW,GAAG5E,IAAI,CAACuC,aAAa,CAACtC,GAAG,EAAE,OAAO,CAAC;IAClD,IAAI4E,WAAW,GAAGD,WAAW,CAACE,IAAI;IAClC,IAAIjE,UAAU,YAAYjB,OAAO,EAAE;MACjC,IAAImF,SAAS,GAAGlE,UAAU,CAACqC,KAAK;MAChCrC,UAAU,CAACmE,QAAQ,CAACvF,MAAM,CAAC;QACzB;QACAwF,KAAK,EAAEF,SAAS,CAACE,KAAK;QACtBP,CAAC,EAAEK,SAAS,CAACL,CAAC;QACdC,CAAC,EAAEI,SAAS,CAACJ,CAAC;QACdO,KAAK,EAAEH,SAAS,CAACG,KAAK;QACtBC,MAAM,EAAEJ,SAAS,CAACI;MACpB,CAAC,EAAEP,WAAW,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,IAAI/D,UAAU,CAACuE,cAAc,EAAE;QAC7B;QACA;QACA;QACAvE,UAAU,CAACmE,QAAQ,CAACvF,MAAM,CAAC,CAAC,CAAC,EAAEmF,WAAW,CAAC,CAAC;MAC9C,CAAC,MAAM;QACL/D,UAAU,CAACmE,QAAQ,CAACJ,WAAW,CAAC;MAClC;MACA;MACA/D,UAAU,CAACqC,KAAK,CAACmC,KAAK,GAAG,IAAI;MAC7BxE,UAAU,CAACyE,QAAQ,CAACT,WAAW,EAAE1E,IAAI,IAAIA,IAAI,CAACoF,gBAAgB,CAAC;MAC/D1E,UAAU,CAACqC,KAAK,CAACsC,aAAa,GAAG,IAAI;IACvC;IACA,IAAIC,KAAK,GAAGzF,IAAI,CAACuC,aAAa,CAACtC,GAAG,EAAE,OAAO,CAAC;IAC5C,IAAIyF,QAAQ,GAAG,IAAI,CAACC,GAAG;IACvB,IAAIF,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIC,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAI,CAACC,GAAG,GAAG9E,UAAU,CAACE,EAAE;QACxBF,UAAU,CAACE,EAAE,IAAI0E,KAAK;MACxB;IACF,CAAC,MAAM,IAAIC,QAAQ,IAAI,IAAI,EAAE;MAC3B7E,UAAU,CAACE,EAAE,GAAG2E,QAAQ;MACxB,IAAI,CAACC,GAAG,GAAG,IAAI;IACjB;IACA,IAAIC,YAAY,GAAGzF,IAAI,IAAIA,IAAI,CAACyF,YAAY;IAC5ClG,aAAa,CAACmB,UAAU,EAAE8C,iBAAiB,EAAE;MAC3CkC,YAAY,EAAErD,WAAW;MACzBsD,cAAc,EAAE7F,GAAG;MACnB8F,WAAW,EAAEC,mBAAmB;MAChCC,YAAY,EAAEpB,WAAW;MACzBqB,cAAc,EAAEtB,WAAW,CAACzB;IAC9B,CAAC,CAAC;IACF;IACA,SAAS6C,mBAAmBA,CAAC/F,GAAG,EAAE;MAChC,OAAO2F,YAAY,GAAG5F,IAAI,CAACmG,OAAO,CAAClG,GAAG,CAAC,GAAGT,eAAe,CAACQ,IAAI,EAAEC,GAAG,CAAC;IACtE;IACA,IAAI,CAAC+C,MAAM,GAAGtC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IAC/B,IAAI,CAACuC,MAAM,GAAGvC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IAC/B,IAAI0F,aAAa,GAAGvF,UAAU,CAACwF,WAAW,CAAC,UAAU,CAAC;IACtDD,aAAa,CAAClD,KAAK,GAAGG,iBAAiB;IACvCxC,UAAU,CAACwF,WAAW,CAAC,QAAQ,CAAC,CAACnD,KAAK,GAAGK,eAAe;IACxD1C,UAAU,CAACwF,WAAW,CAAC,MAAM,CAAC,CAACnD,KAAK,GAAGI,aAAa;IACpD;IACA;IACA,IAAIgD,UAAU,GAAG1C,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,IAAI,GAAGW,IAAI,CAACgC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAACtD,MAAM;IAC1F;IAAA,EACEuD,QAAQ,CAAC5C,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,GAAG,CAACA,UAAU,GAAG,CAAC;IAC1D;IACAwC,aAAa,CAACnF,MAAM,GAAG,IAAI,CAAC+B,MAAM,GAAGsD,UAAU;IAC/CF,aAAa,CAAClF,MAAM,GAAG,IAAI,CAAC+B,MAAM,GAAGqD,UAAU;IAC/C,IAAI,CAACG,cAAc,CAAC,CAAC,CAAC;IACtBlH,mBAAmB,CAAC,IAAI,EAAEiE,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;EAC/D,CAAC;EACD5D,QAAM,CAACS,SAAS,CAACkG,cAAc,GAAG,UAAUC,KAAK,EAAE;IACjD,IAAI,CAACzF,MAAM,GAAG,IAAI,CAACC,MAAM,GAAGwF,KAAK;EACnC,CAAC;EACD5G,QAAM,CAACS,SAAS,CAACoG,OAAO,GAAG,UAAUC,EAAE,EAAEpE,WAAW,EAAEqE,GAAG,EAAE;IACzD,IAAIhG,UAAU,GAAG,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;IAChC,IAAIqF,SAAS,GAAG1H,SAAS,CAAC,IAAI,CAAC,CAAC0H,SAAS;IACzC,IAAIC,YAAY,GAAGF,GAAG,IAAIA,GAAG,CAACG,SAAS;IACvC;IACA,IAAI,CAAC1E,MAAM,GAAGzB,UAAU,CAACyB,MAAM,GAAG,IAAI;IACtC;IACA,IAAIuE,GAAG,IAAIA,GAAG,CAACI,SAAS,EAAE;MACxB,IAAIC,WAAW,GAAGrG,UAAU,CAACsG,cAAc,CAAC,CAAC;MAC7C,IAAID,WAAW,EAAE;QACf/H,OAAO,CAACiI,aAAa,CAACF,WAAW,EAAE;UACjChE,KAAK,EAAE;YACLC,OAAO,EAAE;UACX;QACF,CAAC,EAAEX,WAAW,EAAE;UACdsE,SAAS,EAAEA,SAAS;UACpBO,SAAS,EAAEN,YAAY;UACvBH,EAAE,EAAE,SAAJA,EAAEA,CAAA,EAAc;YACd/F,UAAU,CAACyG,iBAAiB,CAAC,CAAC;UAChC;QACF,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLzG,UAAU,CAACyG,iBAAiB,CAAC,CAAC;IAChC;IACAnI,OAAO,CAACiI,aAAa,CAACvG,UAAU,EAAE;MAChCqC,KAAK,EAAE;QACLC,OAAO,EAAE;MACX,CAAC;MACDlC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACV,CAAC,EAAEsB,WAAW,EAAE;MACdsE,SAAS,EAAEA,SAAS;MACpBF,EAAE,EAAEA,EAAE;MACNS,SAAS,EAAEN;IACb,CAAC,CAAC;EACJ,CAAC;EACDjH,QAAM,CAAC4C,aAAa,GAAG,UAAU1C,IAAI,EAAEC,GAAG,EAAE;IAC1C,OAAOf,mBAAmB,CAACc,IAAI,CAACuC,aAAa,CAACtC,GAAG,EAAE,YAAY,CAAC,CAAC;EACnE,CAAC;EACD,OAAOH,QAAM;AACf,CAAC,CAACX,OAAO,CAACoI,KAAK,CAAC;AAChB,SAASnG,WAAWA,CAACoG,EAAE,EAAEC,EAAE,EAAE;EAC3B,IAAI,CAACC,MAAM,CAACvG,KAAK,CAACqG,EAAE,EAAEC,EAAE,CAAC;AAC3B;AACA,eAAe3H,OAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}