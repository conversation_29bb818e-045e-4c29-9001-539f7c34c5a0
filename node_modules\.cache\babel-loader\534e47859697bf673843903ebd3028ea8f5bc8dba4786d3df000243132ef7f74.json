{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取设备列表\nexport function getDeviceList(params) {\n  return request({\n    url: '/user/device/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 导出设备数据\nexport function exportDeviceData(params) {\n  return request({\n    url: '/user/device/export',\n    method: 'get',\n    params: params,\n    responseType: 'blob'\n  });\n}\n\n// 获取设备详情\nexport function getDeviceDetail(id) {\n  return request({\n    url: \"/user/device/detail/\".concat(id),\n    method: 'get'\n  });\n}", "map": {"version": 3, "names": ["request", "getDeviceList", "params", "url", "method", "exportDeviceData", "responseType", "getDeviceDetail", "id", "concat"], "sources": ["E:/新项目/adminweb/src/api/user/device.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取设备列表\r\nexport function getDeviceList(params) {\r\n  return request({\r\n    url: '/user/device/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 导出设备数据\r\nexport function exportDeviceData(params) {\r\n  return request({\r\n    url: '/user/device/export',\r\n    method: 'get',\r\n    params,\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n// 获取设备详情\r\nexport function getDeviceDetail(id) {\r\n  return request({\r\n    url: `/user/device/detail/${id}`,\r\n    method: 'get'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,gBAAgBA,CAACH,MAAM,EAAE;EACvC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA,MAAM;IACNI,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAOR,OAAO,CAAC;IACbG,GAAG,yBAAAM,MAAA,CAAyBD,EAAE,CAAE;IAChCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}