{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar SingleAxis = /** @class */function (_super) {\n  __extends(SingleAxis, _super);\n  function SingleAxis(dim, scale, coordExtent, axisType, position) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    _this.type = axisType || 'value';\n    _this.position = position || 'bottom';\n    return _this;\n  }\n  /**\n   * Judge the orient of the axis.\n   */\n  SingleAxis.prototype.isHorizontal = function () {\n    var position = this.position;\n    return position === 'top' || position === 'bottom';\n  };\n  SingleAxis.prototype.pointToData = function (point, clamp) {\n    return this.coordinateSystem.pointToData(point)[0];\n  };\n  return SingleAxis;\n}(Axis);\nexport default SingleAxis;", "map": {"version": 3, "names": ["__extends", "Axis", "SingleAxis", "_super", "dim", "scale", "coordExtent", "axisType", "position", "_this", "call", "type", "prototype", "isHorizontal", "pointToData", "point", "clamp", "coordinateSystem"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/echarts/lib/coord/single/SingleAxis.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar SingleAxis = /** @class */function (_super) {\n  __extends(SingleAxis, _super);\n  function SingleAxis(dim, scale, coordExtent, axisType, position) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    _this.type = axisType || 'value';\n    _this.position = position || 'bottom';\n    return _this;\n  }\n  /**\n   * Judge the orient of the axis.\n   */\n  SingleAxis.prototype.isHorizontal = function () {\n    var position = this.position;\n    return position === 'top' || position === 'bottom';\n  };\n  SingleAxis.prototype.pointToData = function (point, clamp) {\n    return this.coordinateSystem.pointToData(point)[0];\n  };\n  return SingleAxis;\n}(Axis);\nexport default SingleAxis;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CH,SAAS,CAACE,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAACE,GAAG,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAC/D,IAAIC,KAAK,GAAGN,MAAM,CAACO,IAAI,CAAC,IAAI,EAAEN,GAAG,EAAEC,KAAK,EAAEC,WAAW,CAAC,IAAI,IAAI;IAC9DG,KAAK,CAACE,IAAI,GAAGJ,QAAQ,IAAI,OAAO;IAChCE,KAAK,CAACD,QAAQ,GAAGA,QAAQ,IAAI,QAAQ;IACrC,OAAOC,KAAK;EACd;EACA;AACF;AACA;EACEP,UAAU,CAACU,SAAS,CAACC,YAAY,GAAG,YAAY;IAC9C,IAAIL,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,OAAOA,QAAQ,KAAK,KAAK,IAAIA,QAAQ,KAAK,QAAQ;EACpD,CAAC;EACDN,UAAU,CAACU,SAAS,CAACE,WAAW,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACzD,OAAO,IAAI,CAACC,gBAAgB,CAACH,WAAW,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC;EACD,OAAOb,UAAU;AACnB,CAAC,CAACD,IAAI,CAAC;AACP,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}