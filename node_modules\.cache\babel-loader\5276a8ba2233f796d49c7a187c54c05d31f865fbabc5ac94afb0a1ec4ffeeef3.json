{"ast": null, "code": "import \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.reverse.js\";\nimport \"core-js/modules/es.array.slice.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { curry, each, map, bind, merge, clone, defaults, assert } from 'zrender/lib/core/util.js';\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as interactionMutex from './interactionMutex.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nvar BRUSH_PANEL_GLOBAL = true;\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathPow = Math.pow;\nvar COVER_Z = 10000;\nvar UNSELECT_THRESHOLD = 6;\nvar MIN_RESIZE_LINE_WIDTH = 6;\nvar MUTEX_RESOURCE_KEY = 'globalPan';\nvar DIRECTION_MAP = {\n  w: [0, 0],\n  e: [0, 1],\n  n: [1, 0],\n  s: [1, 1]\n};\nvar CURSOR_MAP = {\n  w: 'ew',\n  e: 'ew',\n  n: 'ns',\n  s: 'ns',\n  ne: 'nesw',\n  sw: 'nesw',\n  nw: 'nwse',\n  se: 'nwse'\n};\nvar DEFAULT_BRUSH_OPT = {\n  brushStyle: {\n    lineWidth: 2,\n    stroke: 'rgba(210,219,238,0.3)',\n    fill: '#D2DBEE'\n  },\n  transformable: true,\n  brushMode: 'single',\n  removeOnClick: false\n};\nvar baseUID = 0;\n/**\n * params:\n *     areas: Array.<Array>, coord relates to container group,\n *                             If no container specified, to global.\n *     opt {\n *         isEnd: boolean,\n *         removeOnClick: boolean\n *     }\n */\nvar BrushController = /** @class */function (_super) {\n  __extends(BrushController, _super);\n  function BrushController(zr) {\n    var _this = _super.call(this) || this;\n    /**\n     * @internal\n     */\n    _this._track = [];\n    /**\n     * @internal\n     */\n    _this._covers = [];\n    _this._handlers = {};\n    if (process.env.NODE_ENV !== 'production') {\n      assert(zr);\n    }\n    _this._zr = zr;\n    _this.group = new graphic.Group();\n    _this._uid = 'brushController_' + baseUID++;\n    each(pointerHandlers, function (handler, eventName) {\n      this._handlers[eventName] = bind(handler, this);\n    }, _this);\n    return _this;\n  }\n  /**\n   * If set to `false`, select disabled.\n   */\n  BrushController.prototype.enableBrush = function (brushOption) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(this._mounted);\n    }\n    this._brushType && this._doDisableBrush();\n    brushOption.brushType && this._doEnableBrush(brushOption);\n    return this;\n  };\n  BrushController.prototype._doEnableBrush = function (brushOption) {\n    var zr = this._zr;\n    // Consider roam, which takes globalPan too.\n    if (!this._enableGlobalPan) {\n      interactionMutex.take(zr, MUTEX_RESOURCE_KEY, this._uid);\n    }\n    each(this._handlers, function (handler, eventName) {\n      zr.on(eventName, handler);\n    });\n    this._brushType = brushOption.brushType;\n    this._brushOption = merge(clone(DEFAULT_BRUSH_OPT), brushOption, true);\n  };\n  BrushController.prototype._doDisableBrush = function () {\n    var zr = this._zr;\n    interactionMutex.release(zr, MUTEX_RESOURCE_KEY, this._uid);\n    each(this._handlers, function (handler, eventName) {\n      zr.off(eventName, handler);\n    });\n    this._brushType = this._brushOption = null;\n  };\n  /**\n   * @param panelOpts If not pass, it is global brush.\n   */\n  BrushController.prototype.setPanels = function (panelOpts) {\n    if (panelOpts && panelOpts.length) {\n      var panels_1 = this._panels = {};\n      each(panelOpts, function (panelOpts) {\n        panels_1[panelOpts.panelId] = clone(panelOpts);\n      });\n    } else {\n      this._panels = null;\n    }\n    return this;\n  };\n  BrushController.prototype.mount = function (opt) {\n    opt = opt || {};\n    if (process.env.NODE_ENV !== 'production') {\n      this._mounted = true; // should be at first.\n    }\n    this._enableGlobalPan = opt.enableGlobalPan;\n    var thisGroup = this.group;\n    this._zr.add(thisGroup);\n    thisGroup.attr({\n      x: opt.x || 0,\n      y: opt.y || 0,\n      rotation: opt.rotation || 0,\n      scaleX: opt.scaleX || 1,\n      scaleY: opt.scaleY || 1\n    });\n    this._transform = thisGroup.getLocalTransform();\n    return this;\n  };\n  // eachCover(cb, context): void {\n  //     each(this._covers, cb, context);\n  // }\n  /**\n   * Update covers.\n   * @param coverConfigList\n   *        If coverConfigList is null/undefined, all covers removed.\n   */\n  BrushController.prototype.updateCovers = function (coverConfigList) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(this._mounted);\n    }\n    coverConfigList = map(coverConfigList, function (coverConfig) {\n      return merge(clone(DEFAULT_BRUSH_OPT), coverConfig, true);\n    });\n    var tmpIdPrefix = '\\0-brush-index-';\n    var oldCovers = this._covers;\n    var newCovers = this._covers = [];\n    var controller = this;\n    var creatingCover = this._creatingCover;\n    new DataDiffer(oldCovers, coverConfigList, oldGetKey, getKey).add(addOrUpdate).update(addOrUpdate).remove(remove).execute();\n    return this;\n    function getKey(brushOption, index) {\n      return (brushOption.id != null ? brushOption.id : tmpIdPrefix + index) + '-' + brushOption.brushType;\n    }\n    function oldGetKey(cover, index) {\n      return getKey(cover.__brushOption, index);\n    }\n    function addOrUpdate(newIndex, oldIndex) {\n      var newBrushInternal = coverConfigList[newIndex];\n      // Consider setOption in event listener of brushSelect,\n      // where updating cover when creating should be forbidden.\n      if (oldIndex != null && oldCovers[oldIndex] === creatingCover) {\n        newCovers[newIndex] = oldCovers[oldIndex];\n      } else {\n        var cover = newCovers[newIndex] = oldIndex != null ? (oldCovers[oldIndex].__brushOption = newBrushInternal, oldCovers[oldIndex]) : endCreating(controller, createCover(controller, newBrushInternal));\n        updateCoverAfterCreation(controller, cover);\n      }\n    }\n    function remove(oldIndex) {\n      if (oldCovers[oldIndex] !== creatingCover) {\n        controller.group.remove(oldCovers[oldIndex]);\n      }\n    }\n  };\n  BrushController.prototype.unmount = function () {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!this._mounted) {\n        return;\n      }\n    }\n    this.enableBrush(false);\n    // container may 'removeAll' outside.\n    clearCovers(this);\n    this._zr.remove(this.group);\n    if (process.env.NODE_ENV !== 'production') {\n      this._mounted = false; // should be at last.\n    }\n    return this;\n  };\n  BrushController.prototype.dispose = function () {\n    this.unmount();\n    this.off();\n  };\n  return BrushController;\n}(Eventful);\nfunction createCover(controller, brushOption) {\n  var cover = coverRenderers[brushOption.brushType].createCover(controller, brushOption);\n  cover.__brushOption = brushOption;\n  updateZ(cover, brushOption);\n  controller.group.add(cover);\n  return cover;\n}\nfunction endCreating(controller, creatingCover) {\n  var coverRenderer = getCoverRenderer(creatingCover);\n  if (coverRenderer.endCreating) {\n    coverRenderer.endCreating(controller, creatingCover);\n    updateZ(creatingCover, creatingCover.__brushOption);\n  }\n  return creatingCover;\n}\nfunction updateCoverShape(controller, cover) {\n  var brushOption = cover.__brushOption;\n  getCoverRenderer(cover).updateCoverShape(controller, cover, brushOption.range, brushOption);\n}\nfunction updateZ(cover, brushOption) {\n  var z = brushOption.z;\n  z == null && (z = COVER_Z);\n  cover.traverse(function (el) {\n    el.z = z;\n    el.z2 = z; // Consider in given container.\n  });\n}\nfunction updateCoverAfterCreation(controller, cover) {\n  getCoverRenderer(cover).updateCommon(controller, cover);\n  updateCoverShape(controller, cover);\n}\nfunction getCoverRenderer(cover) {\n  return coverRenderers[cover.__brushOption.brushType];\n}\n// return target panel or `true` (means global panel)\nfunction getPanelByPoint(controller, e, localCursorPoint) {\n  var panels = controller._panels;\n  if (!panels) {\n    return BRUSH_PANEL_GLOBAL; // Global panel\n  }\n  var panel;\n  var transform = controller._transform;\n  each(panels, function (pn) {\n    pn.isTargetByCursor(e, localCursorPoint, transform) && (panel = pn);\n  });\n  return panel;\n}\n// Return a panel or true\nfunction getPanelByCover(controller, cover) {\n  var panels = controller._panels;\n  if (!panels) {\n    return BRUSH_PANEL_GLOBAL; // Global panel\n  }\n  var panelId = cover.__brushOption.panelId;\n  // User may give cover without coord sys info,\n  // which is then treated as global panel.\n  return panelId != null ? panels[panelId] : BRUSH_PANEL_GLOBAL;\n}\nfunction clearCovers(controller) {\n  var covers = controller._covers;\n  var originalLength = covers.length;\n  each(covers, function (cover) {\n    controller.group.remove(cover);\n  }, controller);\n  covers.length = 0;\n  return !!originalLength;\n}\nfunction trigger(controller, opt) {\n  var areas = map(controller._covers, function (cover) {\n    var brushOption = cover.__brushOption;\n    var range = clone(brushOption.range);\n    return {\n      brushType: brushOption.brushType,\n      panelId: brushOption.panelId,\n      range: range\n    };\n  });\n  controller.trigger('brush', {\n    areas: areas,\n    isEnd: !!opt.isEnd,\n    removeOnClick: !!opt.removeOnClick\n  });\n}\nfunction shouldShowCover(controller) {\n  var track = controller._track;\n  if (!track.length) {\n    return false;\n  }\n  var p2 = track[track.length - 1];\n  var p1 = track[0];\n  var dx = p2[0] - p1[0];\n  var dy = p2[1] - p1[1];\n  var dist = mathPow(dx * dx + dy * dy, 0.5);\n  return dist > UNSELECT_THRESHOLD;\n}\nfunction getTrackEnds(track) {\n  var tail = track.length - 1;\n  tail < 0 && (tail = 0);\n  return [track[0], track[tail]];\n}\n;\nfunction createBaseRectCover(rectRangeConverter, controller, brushOption, edgeNameSequences) {\n  var cover = new graphic.Group();\n  cover.add(new graphic.Rect({\n    name: 'main',\n    style: makeStyle(brushOption),\n    silent: true,\n    draggable: true,\n    cursor: 'move',\n    drift: curry(driftRect, rectRangeConverter, controller, cover, ['n', 's', 'w', 'e']),\n    ondragend: curry(trigger, controller, {\n      isEnd: true\n    })\n  }));\n  each(edgeNameSequences, function (nameSequence) {\n    cover.add(new graphic.Rect({\n      name: nameSequence.join(''),\n      style: {\n        opacity: 0\n      },\n      draggable: true,\n      silent: true,\n      invisible: true,\n      drift: curry(driftRect, rectRangeConverter, controller, cover, nameSequence),\n      ondragend: curry(trigger, controller, {\n        isEnd: true\n      })\n    }));\n  });\n  return cover;\n}\nfunction updateBaseRect(controller, cover, localRange, brushOption) {\n  var lineWidth = brushOption.brushStyle.lineWidth || 0;\n  var handleSize = mathMax(lineWidth, MIN_RESIZE_LINE_WIDTH);\n  var x = localRange[0][0];\n  var y = localRange[1][0];\n  var xa = x - lineWidth / 2;\n  var ya = y - lineWidth / 2;\n  var x2 = localRange[0][1];\n  var y2 = localRange[1][1];\n  var x2a = x2 - handleSize + lineWidth / 2;\n  var y2a = y2 - handleSize + lineWidth / 2;\n  var width = x2 - x;\n  var height = y2 - y;\n  var widtha = width + lineWidth;\n  var heighta = height + lineWidth;\n  updateRectShape(controller, cover, 'main', x, y, width, height);\n  if (brushOption.transformable) {\n    updateRectShape(controller, cover, 'w', xa, ya, handleSize, heighta);\n    updateRectShape(controller, cover, 'e', x2a, ya, handleSize, heighta);\n    updateRectShape(controller, cover, 'n', xa, ya, widtha, handleSize);\n    updateRectShape(controller, cover, 's', xa, y2a, widtha, handleSize);\n    updateRectShape(controller, cover, 'nw', xa, ya, handleSize, handleSize);\n    updateRectShape(controller, cover, 'ne', x2a, ya, handleSize, handleSize);\n    updateRectShape(controller, cover, 'sw', xa, y2a, handleSize, handleSize);\n    updateRectShape(controller, cover, 'se', x2a, y2a, handleSize, handleSize);\n  }\n}\nfunction updateCommon(controller, cover) {\n  var brushOption = cover.__brushOption;\n  var transformable = brushOption.transformable;\n  var mainEl = cover.childAt(0);\n  mainEl.useStyle(makeStyle(brushOption));\n  mainEl.attr({\n    silent: !transformable,\n    cursor: transformable ? 'move' : 'default'\n  });\n  each([['w'], ['e'], ['n'], ['s'], ['s', 'e'], ['s', 'w'], ['n', 'e'], ['n', 'w']], function (nameSequence) {\n    var el = cover.childOfName(nameSequence.join(''));\n    var globalDir = nameSequence.length === 1 ? getGlobalDirection1(controller, nameSequence[0]) : getGlobalDirection2(controller, nameSequence);\n    el && el.attr({\n      silent: !transformable,\n      invisible: !transformable,\n      cursor: transformable ? CURSOR_MAP[globalDir] + '-resize' : null\n    });\n  });\n}\nfunction updateRectShape(controller, cover, name, x, y, w, h) {\n  var el = cover.childOfName(name);\n  el && el.setShape(pointsToRect(clipByPanel(controller, cover, [[x, y], [x + w, y + h]])));\n}\nfunction makeStyle(brushOption) {\n  return defaults({\n    strokeNoScale: true\n  }, brushOption.brushStyle);\n}\nfunction formatRectRange(x, y, x2, y2) {\n  var min = [mathMin(x, x2), mathMin(y, y2)];\n  var max = [mathMax(x, x2), mathMax(y, y2)];\n  return [[min[0], max[0]], [min[1], max[1]] // y range\n  ];\n}\nfunction getTransform(controller) {\n  return graphic.getTransform(controller.group);\n}\nfunction getGlobalDirection1(controller, localDirName) {\n  var map = {\n    w: 'left',\n    e: 'right',\n    n: 'top',\n    s: 'bottom'\n  };\n  var inverseMap = {\n    left: 'w',\n    right: 'e',\n    top: 'n',\n    bottom: 's'\n  };\n  var dir = graphic.transformDirection(map[localDirName], getTransform(controller));\n  return inverseMap[dir];\n}\nfunction getGlobalDirection2(controller, localDirNameSeq) {\n  var globalDir = [getGlobalDirection1(controller, localDirNameSeq[0]), getGlobalDirection1(controller, localDirNameSeq[1])];\n  (globalDir[0] === 'e' || globalDir[0] === 'w') && globalDir.reverse();\n  return globalDir.join('');\n}\nfunction driftRect(rectRangeConverter, controller, cover, dirNameSequence, dx, dy) {\n  var brushOption = cover.__brushOption;\n  var rectRange = rectRangeConverter.toRectRange(brushOption.range);\n  var localDelta = toLocalDelta(controller, dx, dy);\n  each(dirNameSequence, function (dirName) {\n    var ind = DIRECTION_MAP[dirName];\n    rectRange[ind[0]][ind[1]] += localDelta[ind[0]];\n  });\n  brushOption.range = rectRangeConverter.fromRectRange(formatRectRange(rectRange[0][0], rectRange[1][0], rectRange[0][1], rectRange[1][1]));\n  updateCoverAfterCreation(controller, cover);\n  trigger(controller, {\n    isEnd: false\n  });\n}\nfunction driftPolygon(controller, cover, dx, dy) {\n  var range = cover.__brushOption.range;\n  var localDelta = toLocalDelta(controller, dx, dy);\n  each(range, function (point) {\n    point[0] += localDelta[0];\n    point[1] += localDelta[1];\n  });\n  updateCoverAfterCreation(controller, cover);\n  trigger(controller, {\n    isEnd: false\n  });\n}\nfunction toLocalDelta(controller, dx, dy) {\n  var thisGroup = controller.group;\n  var localD = thisGroup.transformCoordToLocal(dx, dy);\n  var localZero = thisGroup.transformCoordToLocal(0, 0);\n  return [localD[0] - localZero[0], localD[1] - localZero[1]];\n}\nfunction clipByPanel(controller, cover, data) {\n  var panel = getPanelByCover(controller, cover);\n  return panel && panel !== BRUSH_PANEL_GLOBAL ? panel.clipPath(data, controller._transform) : clone(data);\n}\nfunction pointsToRect(points) {\n  var xmin = mathMin(points[0][0], points[1][0]);\n  var ymin = mathMin(points[0][1], points[1][1]);\n  var xmax = mathMax(points[0][0], points[1][0]);\n  var ymax = mathMax(points[0][1], points[1][1]);\n  return {\n    x: xmin,\n    y: ymin,\n    width: xmax - xmin,\n    height: ymax - ymin\n  };\n}\nfunction resetCursor(controller, e, localCursorPoint) {\n  if (\n  // Check active\n  !controller._brushType\n  // resetCursor should be always called when mouse is in zr area,\n  // but not called when mouse is out of zr area to avoid bad influence\n  // if `mousemove`, `mouseup` are triggered from `document` event.\n  || isOutsideZrArea(controller, e.offsetX, e.offsetY)) {\n    return;\n  }\n  var zr = controller._zr;\n  var covers = controller._covers;\n  var currPanel = getPanelByPoint(controller, e, localCursorPoint);\n  // Check whether in covers.\n  if (!controller._dragging) {\n    for (var i = 0; i < covers.length; i++) {\n      var brushOption = covers[i].__brushOption;\n      if (currPanel && (currPanel === BRUSH_PANEL_GLOBAL || brushOption.panelId === currPanel.panelId) && coverRenderers[brushOption.brushType].contain(covers[i], localCursorPoint[0], localCursorPoint[1])) {\n        // Use cursor style set on cover.\n        return;\n      }\n    }\n  }\n  currPanel && zr.setCursorStyle('crosshair');\n}\nfunction preventDefault(e) {\n  var rawE = e.event;\n  rawE.preventDefault && rawE.preventDefault();\n}\nfunction mainShapeContain(cover, x, y) {\n  return cover.childOfName('main').contain(x, y);\n}\nfunction updateCoverByMouse(controller, e, localCursorPoint, isEnd) {\n  var creatingCover = controller._creatingCover;\n  var panel = controller._creatingPanel;\n  var thisBrushOption = controller._brushOption;\n  var eventParams;\n  controller._track.push(localCursorPoint.slice());\n  if (shouldShowCover(controller) || creatingCover) {\n    if (panel && !creatingCover) {\n      thisBrushOption.brushMode === 'single' && clearCovers(controller);\n      var brushOption = clone(thisBrushOption);\n      brushOption.brushType = determineBrushType(brushOption.brushType, panel);\n      brushOption.panelId = panel === BRUSH_PANEL_GLOBAL ? null : panel.panelId;\n      creatingCover = controller._creatingCover = createCover(controller, brushOption);\n      controller._covers.push(creatingCover);\n    }\n    if (creatingCover) {\n      var coverRenderer = coverRenderers[determineBrushType(controller._brushType, panel)];\n      var coverBrushOption = creatingCover.__brushOption;\n      coverBrushOption.range = coverRenderer.getCreatingRange(clipByPanel(controller, creatingCover, controller._track));\n      if (isEnd) {\n        endCreating(controller, creatingCover);\n        coverRenderer.updateCommon(controller, creatingCover);\n      }\n      updateCoverShape(controller, creatingCover);\n      eventParams = {\n        isEnd: isEnd\n      };\n    }\n  } else if (isEnd && thisBrushOption.brushMode === 'single' && thisBrushOption.removeOnClick) {\n    // Help user to remove covers easily, only by a tiny drag, in 'single' mode.\n    // But a single click do not clear covers, because user may have casual\n    // clicks (for example, click on other component and do not expect covers\n    // disappear).\n    // Only some cover removed, trigger action, but not every click trigger action.\n    if (getPanelByPoint(controller, e, localCursorPoint) && clearCovers(controller)) {\n      eventParams = {\n        isEnd: isEnd,\n        removeOnClick: true\n      };\n    }\n  }\n  return eventParams;\n}\nfunction determineBrushType(brushType, panel) {\n  if (brushType === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(panel && panel.defaultBrushType, 'MUST have defaultBrushType when brushType is \"atuo\"');\n    }\n    return panel.defaultBrushType;\n  }\n  return brushType;\n}\nvar pointerHandlers = {\n  mousedown: function mousedown(e) {\n    if (this._dragging) {\n      // In case some browser do not support globalOut,\n      // and release mouse out side the browser.\n      handleDragEnd(this, e);\n    } else if (!e.target || !e.target.draggable) {\n      preventDefault(e);\n      var localCursorPoint = this.group.transformCoordToLocal(e.offsetX, e.offsetY);\n      this._creatingCover = null;\n      var panel = this._creatingPanel = getPanelByPoint(this, e, localCursorPoint);\n      if (panel) {\n        this._dragging = true;\n        this._track = [localCursorPoint.slice()];\n      }\n    }\n  },\n  mousemove: function mousemove(e) {\n    var x = e.offsetX;\n    var y = e.offsetY;\n    var localCursorPoint = this.group.transformCoordToLocal(x, y);\n    resetCursor(this, e, localCursorPoint);\n    if (this._dragging) {\n      preventDefault(e);\n      var eventParams = updateCoverByMouse(this, e, localCursorPoint, false);\n      eventParams && trigger(this, eventParams);\n    }\n  },\n  mouseup: function mouseup(e) {\n    handleDragEnd(this, e);\n  }\n};\nfunction handleDragEnd(controller, e) {\n  if (controller._dragging) {\n    preventDefault(e);\n    var x = e.offsetX;\n    var y = e.offsetY;\n    var localCursorPoint = controller.group.transformCoordToLocal(x, y);\n    var eventParams = updateCoverByMouse(controller, e, localCursorPoint, true);\n    controller._dragging = false;\n    controller._track = [];\n    controller._creatingCover = null;\n    // trigger event should be at final, after procedure will be nested.\n    eventParams && trigger(controller, eventParams);\n  }\n}\nfunction isOutsideZrArea(controller, x, y) {\n  var zr = controller._zr;\n  return x < 0 || x > zr.getWidth() || y < 0 || y > zr.getHeight();\n}\n/**\n * key: brushType\n */\nvar coverRenderers = {\n  lineX: getLineRenderer(0),\n  lineY: getLineRenderer(1),\n  rect: {\n    createCover: function createCover(controller, brushOption) {\n      function returnInput(range) {\n        return range;\n      }\n      return createBaseRectCover({\n        toRectRange: returnInput,\n        fromRectRange: returnInput\n      }, controller, brushOption, [['w'], ['e'], ['n'], ['s'], ['s', 'e'], ['s', 'w'], ['n', 'e'], ['n', 'w']]);\n    },\n    getCreatingRange: function getCreatingRange(localTrack) {\n      var ends = getTrackEnds(localTrack);\n      return formatRectRange(ends[1][0], ends[1][1], ends[0][0], ends[0][1]);\n    },\n    updateCoverShape: function updateCoverShape(controller, cover, localRange, brushOption) {\n      updateBaseRect(controller, cover, localRange, brushOption);\n    },\n    updateCommon: updateCommon,\n    contain: mainShapeContain\n  },\n  polygon: {\n    createCover: function createCover(controller, brushOption) {\n      var cover = new graphic.Group();\n      // Do not use graphic.Polygon because graphic.Polyline do not close the\n      // border of the shape when drawing, which is a better experience for user.\n      cover.add(new graphic.Polyline({\n        name: 'main',\n        style: makeStyle(brushOption),\n        silent: true\n      }));\n      return cover;\n    },\n    getCreatingRange: function getCreatingRange(localTrack) {\n      return localTrack;\n    },\n    endCreating: function endCreating(controller, cover) {\n      cover.remove(cover.childAt(0));\n      // Use graphic.Polygon close the shape.\n      cover.add(new graphic.Polygon({\n        name: 'main',\n        draggable: true,\n        drift: curry(driftPolygon, controller, cover),\n        ondragend: curry(trigger, controller, {\n          isEnd: true\n        })\n      }));\n    },\n    updateCoverShape: function updateCoverShape(controller, cover, localRange, brushOption) {\n      cover.childAt(0).setShape({\n        points: clipByPanel(controller, cover, localRange)\n      });\n    },\n    updateCommon: updateCommon,\n    contain: mainShapeContain\n  }\n};\nfunction getLineRenderer(xyIndex) {\n  return {\n    createCover: function createCover(controller, brushOption) {\n      return createBaseRectCover({\n        toRectRange: function toRectRange(range) {\n          var rectRange = [range, [0, 100]];\n          xyIndex && rectRange.reverse();\n          return rectRange;\n        },\n        fromRectRange: function fromRectRange(rectRange) {\n          return rectRange[xyIndex];\n        }\n      }, controller, brushOption, [[['w'], ['e']], [['n'], ['s']]][xyIndex]);\n    },\n    getCreatingRange: function getCreatingRange(localTrack) {\n      var ends = getTrackEnds(localTrack);\n      var min = mathMin(ends[0][xyIndex], ends[1][xyIndex]);\n      var max = mathMax(ends[0][xyIndex], ends[1][xyIndex]);\n      return [min, max];\n    },\n    updateCoverShape: function updateCoverShape(controller, cover, localRange, brushOption) {\n      var otherExtent;\n      // If brushWidth not specified, fit the panel.\n      var panel = getPanelByCover(controller, cover);\n      if (panel !== BRUSH_PANEL_GLOBAL && panel.getLinearBrushOtherExtent) {\n        otherExtent = panel.getLinearBrushOtherExtent(xyIndex);\n      } else {\n        var zr = controller._zr;\n        otherExtent = [0, [zr.getWidth(), zr.getHeight()][1 - xyIndex]];\n      }\n      var rectRange = [localRange, otherExtent];\n      xyIndex && rectRange.reverse();\n      updateBaseRect(controller, cover, rectRange, brushOption);\n    },\n    updateCommon: updateCommon,\n    contain: mainShapeContain\n  };\n}\nexport default BrushController;", "map": {"version": 3, "names": ["__extends", "curry", "each", "map", "bind", "merge", "clone", "defaults", "assert", "Eventful", "graphic", "interactionMutex", "<PERSON><PERSON><PERSON><PERSON>", "BRUSH_PANEL_GLOBAL", "mathMin", "Math", "min", "mathMax", "max", "mathPow", "pow", "COVER_Z", "UNSELECT_THRESHOLD", "MIN_RESIZE_LINE_WIDTH", "MUTEX_RESOURCE_KEY", "DIRECTION_MAP", "w", "e", "n", "s", "CURSOR_MAP", "ne", "sw", "nw", "se", "DEFAULT_BRUSH_OPT", "brushStyle", "lineWidth", "stroke", "fill", "transformable", "brushMode", "removeOnClick", "baseUID", "BrushController", "_super", "zr", "_this", "call", "_track", "_covers", "_handlers", "process", "env", "NODE_ENV", "_zr", "group", "Group", "_uid", "pointerHandlers", "handler", "eventName", "prototype", "enableBrush", "brushOption", "_mounted", "_brushType", "_doDisableBrush", "brushType", "_doEnableBrush", "_enableGlobalPan", "take", "on", "_brushOption", "release", "off", "set<PERSON><PERSON><PERSON>", "panelOpts", "length", "panels_1", "_panels", "panelId", "mount", "opt", "enableGlobalPan", "thisGroup", "add", "attr", "x", "y", "rotation", "scaleX", "scaleY", "_transform", "getLocalTransform", "updateCovers", "coverConfigList", "coverConfig", "tmpIdPrefix", "oldCovers", "newCovers", "controller", "creatingCover", "_creatingCover", "oldGetKey", "<PERSON><PERSON><PERSON>", "addOrUpdate", "update", "remove", "execute", "index", "id", "cover", "__brushOption", "newIndex", "oldIndex", "newBrushInternal", "endCreating", "createCover", "updateCoverAfterCreation", "unmount", "clearCovers", "dispose", "coverRenderers", "updateZ", "<PERSON><PERSON><PERSON><PERSON>", "get<PERSON>over<PERSON><PERSON><PERSON>", "updateCoverShape", "range", "z", "traverse", "el", "z2", "updateCommon", "getPanelByPoint", "localCursorPoint", "panels", "panel", "transform", "pn", "isTargetByCursor", "getPanelByCover", "covers", "original<PERSON>ength", "trigger", "areas", "isEnd", "shouldShowCover", "track", "p2", "p1", "dx", "dy", "dist", "getTrackEnds", "tail", "createBaseRectCover", "rectRangeConverter", "edgeNameSequences", "Rect", "name", "style", "makeStyle", "silent", "draggable", "cursor", "drift", "driftRect", "ondragend", "nameSequence", "join", "opacity", "invisible", "updateBaseRect", "localRange", "handleSize", "xa", "ya", "x2", "y2", "x2a", "y2a", "width", "height", "widtha", "heighta", "updateRectShape", "mainEl", "childAt", "useStyle", "childOfName", "globalDir", "getGlobalDirection1", "getGlobalDirection2", "h", "setShape", "pointsToRect", "clipByPanel", "strokeNoScale", "formatRectRange", "getTransform", "localDirName", "inverseMap", "left", "right", "top", "bottom", "dir", "transformDirection", "localDirNameSeq", "reverse", "dirNameSequence", "rectRange", "toRectRange", "localDelta", "toLocalD<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ind", "fromRectRange", "driftPolygon", "point", "localD", "transformCoordToLocal", "localZero", "data", "clipPath", "points", "xmin", "ymin", "xmax", "ymax", "resetCursor", "isOutsideZrArea", "offsetX", "offsetY", "currPanel", "_dragging", "i", "contain", "setCursorStyle", "preventDefault", "rawE", "event", "mainShapeContain", "updateCoverByMouse", "_creatingPanel", "thisBrushOption", "eventParams", "push", "slice", "determineBrushType", "coverBrushOption", "getCreatingRange", "defaultBrushType", "mousedown", "handleDragEnd", "target", "mousemove", "mouseup", "getWidth", "getHeight", "lineX", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lineY", "rect", "returnInput", "localTrack", "ends", "polygon", "Polyline", "Polygon", "xyIndex", "otherExtent", "getLinearBrushOtherExtent"], "sources": ["F:/常规项目/华通宝/adminweb/node_modules/echarts/lib/component/helper/BrushController.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { curry, each, map, bind, merge, clone, defaults, assert } from 'zrender/lib/core/util.js';\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as interactionMutex from './interactionMutex.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nvar BRUSH_PANEL_GLOBAL = true;\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathPow = Math.pow;\nvar COVER_Z = 10000;\nvar UNSELECT_THRESHOLD = 6;\nvar MIN_RESIZE_LINE_WIDTH = 6;\nvar MUTEX_RESOURCE_KEY = 'globalPan';\nvar DIRECTION_MAP = {\n  w: [0, 0],\n  e: [0, 1],\n  n: [1, 0],\n  s: [1, 1]\n};\nvar CURSOR_MAP = {\n  w: 'ew',\n  e: 'ew',\n  n: 'ns',\n  s: 'ns',\n  ne: 'nesw',\n  sw: 'nesw',\n  nw: 'nwse',\n  se: 'nwse'\n};\nvar DEFAULT_BRUSH_OPT = {\n  brushStyle: {\n    lineWidth: 2,\n    stroke: 'rgba(210,219,238,0.3)',\n    fill: '#D2DBEE'\n  },\n  transformable: true,\n  brushMode: 'single',\n  removeOnClick: false\n};\nvar baseUID = 0;\n/**\n * params:\n *     areas: Array.<Array>, coord relates to container group,\n *                             If no container specified, to global.\n *     opt {\n *         isEnd: boolean,\n *         removeOnClick: boolean\n *     }\n */\nvar BrushController = /** @class */function (_super) {\n  __extends(BrushController, _super);\n  function BrushController(zr) {\n    var _this = _super.call(this) || this;\n    /**\n     * @internal\n     */\n    _this._track = [];\n    /**\n     * @internal\n     */\n    _this._covers = [];\n    _this._handlers = {};\n    if (process.env.NODE_ENV !== 'production') {\n      assert(zr);\n    }\n    _this._zr = zr;\n    _this.group = new graphic.Group();\n    _this._uid = 'brushController_' + baseUID++;\n    each(pointerHandlers, function (handler, eventName) {\n      this._handlers[eventName] = bind(handler, this);\n    }, _this);\n    return _this;\n  }\n  /**\n   * If set to `false`, select disabled.\n   */\n  BrushController.prototype.enableBrush = function (brushOption) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(this._mounted);\n    }\n    this._brushType && this._doDisableBrush();\n    brushOption.brushType && this._doEnableBrush(brushOption);\n    return this;\n  };\n  BrushController.prototype._doEnableBrush = function (brushOption) {\n    var zr = this._zr;\n    // Consider roam, which takes globalPan too.\n    if (!this._enableGlobalPan) {\n      interactionMutex.take(zr, MUTEX_RESOURCE_KEY, this._uid);\n    }\n    each(this._handlers, function (handler, eventName) {\n      zr.on(eventName, handler);\n    });\n    this._brushType = brushOption.brushType;\n    this._brushOption = merge(clone(DEFAULT_BRUSH_OPT), brushOption, true);\n  };\n  BrushController.prototype._doDisableBrush = function () {\n    var zr = this._zr;\n    interactionMutex.release(zr, MUTEX_RESOURCE_KEY, this._uid);\n    each(this._handlers, function (handler, eventName) {\n      zr.off(eventName, handler);\n    });\n    this._brushType = this._brushOption = null;\n  };\n  /**\n   * @param panelOpts If not pass, it is global brush.\n   */\n  BrushController.prototype.setPanels = function (panelOpts) {\n    if (panelOpts && panelOpts.length) {\n      var panels_1 = this._panels = {};\n      each(panelOpts, function (panelOpts) {\n        panels_1[panelOpts.panelId] = clone(panelOpts);\n      });\n    } else {\n      this._panels = null;\n    }\n    return this;\n  };\n  BrushController.prototype.mount = function (opt) {\n    opt = opt || {};\n    if (process.env.NODE_ENV !== 'production') {\n      this._mounted = true; // should be at first.\n    }\n\n    this._enableGlobalPan = opt.enableGlobalPan;\n    var thisGroup = this.group;\n    this._zr.add(thisGroup);\n    thisGroup.attr({\n      x: opt.x || 0,\n      y: opt.y || 0,\n      rotation: opt.rotation || 0,\n      scaleX: opt.scaleX || 1,\n      scaleY: opt.scaleY || 1\n    });\n    this._transform = thisGroup.getLocalTransform();\n    return this;\n  };\n  // eachCover(cb, context): void {\n  //     each(this._covers, cb, context);\n  // }\n  /**\n   * Update covers.\n   * @param coverConfigList\n   *        If coverConfigList is null/undefined, all covers removed.\n   */\n  BrushController.prototype.updateCovers = function (coverConfigList) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(this._mounted);\n    }\n    coverConfigList = map(coverConfigList, function (coverConfig) {\n      return merge(clone(DEFAULT_BRUSH_OPT), coverConfig, true);\n    });\n    var tmpIdPrefix = '\\0-brush-index-';\n    var oldCovers = this._covers;\n    var newCovers = this._covers = [];\n    var controller = this;\n    var creatingCover = this._creatingCover;\n    new DataDiffer(oldCovers, coverConfigList, oldGetKey, getKey).add(addOrUpdate).update(addOrUpdate).remove(remove).execute();\n    return this;\n    function getKey(brushOption, index) {\n      return (brushOption.id != null ? brushOption.id : tmpIdPrefix + index) + '-' + brushOption.brushType;\n    }\n    function oldGetKey(cover, index) {\n      return getKey(cover.__brushOption, index);\n    }\n    function addOrUpdate(newIndex, oldIndex) {\n      var newBrushInternal = coverConfigList[newIndex];\n      // Consider setOption in event listener of brushSelect,\n      // where updating cover when creating should be forbidden.\n      if (oldIndex != null && oldCovers[oldIndex] === creatingCover) {\n        newCovers[newIndex] = oldCovers[oldIndex];\n      } else {\n        var cover = newCovers[newIndex] = oldIndex != null ? (oldCovers[oldIndex].__brushOption = newBrushInternal, oldCovers[oldIndex]) : endCreating(controller, createCover(controller, newBrushInternal));\n        updateCoverAfterCreation(controller, cover);\n      }\n    }\n    function remove(oldIndex) {\n      if (oldCovers[oldIndex] !== creatingCover) {\n        controller.group.remove(oldCovers[oldIndex]);\n      }\n    }\n  };\n  BrushController.prototype.unmount = function () {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!this._mounted) {\n        return;\n      }\n    }\n    this.enableBrush(false);\n    // container may 'removeAll' outside.\n    clearCovers(this);\n    this._zr.remove(this.group);\n    if (process.env.NODE_ENV !== 'production') {\n      this._mounted = false; // should be at last.\n    }\n\n    return this;\n  };\n  BrushController.prototype.dispose = function () {\n    this.unmount();\n    this.off();\n  };\n  return BrushController;\n}(Eventful);\nfunction createCover(controller, brushOption) {\n  var cover = coverRenderers[brushOption.brushType].createCover(controller, brushOption);\n  cover.__brushOption = brushOption;\n  updateZ(cover, brushOption);\n  controller.group.add(cover);\n  return cover;\n}\nfunction endCreating(controller, creatingCover) {\n  var coverRenderer = getCoverRenderer(creatingCover);\n  if (coverRenderer.endCreating) {\n    coverRenderer.endCreating(controller, creatingCover);\n    updateZ(creatingCover, creatingCover.__brushOption);\n  }\n  return creatingCover;\n}\nfunction updateCoverShape(controller, cover) {\n  var brushOption = cover.__brushOption;\n  getCoverRenderer(cover).updateCoverShape(controller, cover, brushOption.range, brushOption);\n}\nfunction updateZ(cover, brushOption) {\n  var z = brushOption.z;\n  z == null && (z = COVER_Z);\n  cover.traverse(function (el) {\n    el.z = z;\n    el.z2 = z; // Consider in given container.\n  });\n}\n\nfunction updateCoverAfterCreation(controller, cover) {\n  getCoverRenderer(cover).updateCommon(controller, cover);\n  updateCoverShape(controller, cover);\n}\nfunction getCoverRenderer(cover) {\n  return coverRenderers[cover.__brushOption.brushType];\n}\n// return target panel or `true` (means global panel)\nfunction getPanelByPoint(controller, e, localCursorPoint) {\n  var panels = controller._panels;\n  if (!panels) {\n    return BRUSH_PANEL_GLOBAL; // Global panel\n  }\n\n  var panel;\n  var transform = controller._transform;\n  each(panels, function (pn) {\n    pn.isTargetByCursor(e, localCursorPoint, transform) && (panel = pn);\n  });\n  return panel;\n}\n// Return a panel or true\nfunction getPanelByCover(controller, cover) {\n  var panels = controller._panels;\n  if (!panels) {\n    return BRUSH_PANEL_GLOBAL; // Global panel\n  }\n\n  var panelId = cover.__brushOption.panelId;\n  // User may give cover without coord sys info,\n  // which is then treated as global panel.\n  return panelId != null ? panels[panelId] : BRUSH_PANEL_GLOBAL;\n}\nfunction clearCovers(controller) {\n  var covers = controller._covers;\n  var originalLength = covers.length;\n  each(covers, function (cover) {\n    controller.group.remove(cover);\n  }, controller);\n  covers.length = 0;\n  return !!originalLength;\n}\nfunction trigger(controller, opt) {\n  var areas = map(controller._covers, function (cover) {\n    var brushOption = cover.__brushOption;\n    var range = clone(brushOption.range);\n    return {\n      brushType: brushOption.brushType,\n      panelId: brushOption.panelId,\n      range: range\n    };\n  });\n  controller.trigger('brush', {\n    areas: areas,\n    isEnd: !!opt.isEnd,\n    removeOnClick: !!opt.removeOnClick\n  });\n}\nfunction shouldShowCover(controller) {\n  var track = controller._track;\n  if (!track.length) {\n    return false;\n  }\n  var p2 = track[track.length - 1];\n  var p1 = track[0];\n  var dx = p2[0] - p1[0];\n  var dy = p2[1] - p1[1];\n  var dist = mathPow(dx * dx + dy * dy, 0.5);\n  return dist > UNSELECT_THRESHOLD;\n}\nfunction getTrackEnds(track) {\n  var tail = track.length - 1;\n  tail < 0 && (tail = 0);\n  return [track[0], track[tail]];\n}\n;\nfunction createBaseRectCover(rectRangeConverter, controller, brushOption, edgeNameSequences) {\n  var cover = new graphic.Group();\n  cover.add(new graphic.Rect({\n    name: 'main',\n    style: makeStyle(brushOption),\n    silent: true,\n    draggable: true,\n    cursor: 'move',\n    drift: curry(driftRect, rectRangeConverter, controller, cover, ['n', 's', 'w', 'e']),\n    ondragend: curry(trigger, controller, {\n      isEnd: true\n    })\n  }));\n  each(edgeNameSequences, function (nameSequence) {\n    cover.add(new graphic.Rect({\n      name: nameSequence.join(''),\n      style: {\n        opacity: 0\n      },\n      draggable: true,\n      silent: true,\n      invisible: true,\n      drift: curry(driftRect, rectRangeConverter, controller, cover, nameSequence),\n      ondragend: curry(trigger, controller, {\n        isEnd: true\n      })\n    }));\n  });\n  return cover;\n}\nfunction updateBaseRect(controller, cover, localRange, brushOption) {\n  var lineWidth = brushOption.brushStyle.lineWidth || 0;\n  var handleSize = mathMax(lineWidth, MIN_RESIZE_LINE_WIDTH);\n  var x = localRange[0][0];\n  var y = localRange[1][0];\n  var xa = x - lineWidth / 2;\n  var ya = y - lineWidth / 2;\n  var x2 = localRange[0][1];\n  var y2 = localRange[1][1];\n  var x2a = x2 - handleSize + lineWidth / 2;\n  var y2a = y2 - handleSize + lineWidth / 2;\n  var width = x2 - x;\n  var height = y2 - y;\n  var widtha = width + lineWidth;\n  var heighta = height + lineWidth;\n  updateRectShape(controller, cover, 'main', x, y, width, height);\n  if (brushOption.transformable) {\n    updateRectShape(controller, cover, 'w', xa, ya, handleSize, heighta);\n    updateRectShape(controller, cover, 'e', x2a, ya, handleSize, heighta);\n    updateRectShape(controller, cover, 'n', xa, ya, widtha, handleSize);\n    updateRectShape(controller, cover, 's', xa, y2a, widtha, handleSize);\n    updateRectShape(controller, cover, 'nw', xa, ya, handleSize, handleSize);\n    updateRectShape(controller, cover, 'ne', x2a, ya, handleSize, handleSize);\n    updateRectShape(controller, cover, 'sw', xa, y2a, handleSize, handleSize);\n    updateRectShape(controller, cover, 'se', x2a, y2a, handleSize, handleSize);\n  }\n}\nfunction updateCommon(controller, cover) {\n  var brushOption = cover.__brushOption;\n  var transformable = brushOption.transformable;\n  var mainEl = cover.childAt(0);\n  mainEl.useStyle(makeStyle(brushOption));\n  mainEl.attr({\n    silent: !transformable,\n    cursor: transformable ? 'move' : 'default'\n  });\n  each([['w'], ['e'], ['n'], ['s'], ['s', 'e'], ['s', 'w'], ['n', 'e'], ['n', 'w']], function (nameSequence) {\n    var el = cover.childOfName(nameSequence.join(''));\n    var globalDir = nameSequence.length === 1 ? getGlobalDirection1(controller, nameSequence[0]) : getGlobalDirection2(controller, nameSequence);\n    el && el.attr({\n      silent: !transformable,\n      invisible: !transformable,\n      cursor: transformable ? CURSOR_MAP[globalDir] + '-resize' : null\n    });\n  });\n}\nfunction updateRectShape(controller, cover, name, x, y, w, h) {\n  var el = cover.childOfName(name);\n  el && el.setShape(pointsToRect(clipByPanel(controller, cover, [[x, y], [x + w, y + h]])));\n}\nfunction makeStyle(brushOption) {\n  return defaults({\n    strokeNoScale: true\n  }, brushOption.brushStyle);\n}\nfunction formatRectRange(x, y, x2, y2) {\n  var min = [mathMin(x, x2), mathMin(y, y2)];\n  var max = [mathMax(x, x2), mathMax(y, y2)];\n  return [[min[0], max[0]], [min[1], max[1]] // y range\n  ];\n}\n\nfunction getTransform(controller) {\n  return graphic.getTransform(controller.group);\n}\nfunction getGlobalDirection1(controller, localDirName) {\n  var map = {\n    w: 'left',\n    e: 'right',\n    n: 'top',\n    s: 'bottom'\n  };\n  var inverseMap = {\n    left: 'w',\n    right: 'e',\n    top: 'n',\n    bottom: 's'\n  };\n  var dir = graphic.transformDirection(map[localDirName], getTransform(controller));\n  return inverseMap[dir];\n}\nfunction getGlobalDirection2(controller, localDirNameSeq) {\n  var globalDir = [getGlobalDirection1(controller, localDirNameSeq[0]), getGlobalDirection1(controller, localDirNameSeq[1])];\n  (globalDir[0] === 'e' || globalDir[0] === 'w') && globalDir.reverse();\n  return globalDir.join('');\n}\nfunction driftRect(rectRangeConverter, controller, cover, dirNameSequence, dx, dy) {\n  var brushOption = cover.__brushOption;\n  var rectRange = rectRangeConverter.toRectRange(brushOption.range);\n  var localDelta = toLocalDelta(controller, dx, dy);\n  each(dirNameSequence, function (dirName) {\n    var ind = DIRECTION_MAP[dirName];\n    rectRange[ind[0]][ind[1]] += localDelta[ind[0]];\n  });\n  brushOption.range = rectRangeConverter.fromRectRange(formatRectRange(rectRange[0][0], rectRange[1][0], rectRange[0][1], rectRange[1][1]));\n  updateCoverAfterCreation(controller, cover);\n  trigger(controller, {\n    isEnd: false\n  });\n}\nfunction driftPolygon(controller, cover, dx, dy) {\n  var range = cover.__brushOption.range;\n  var localDelta = toLocalDelta(controller, dx, dy);\n  each(range, function (point) {\n    point[0] += localDelta[0];\n    point[1] += localDelta[1];\n  });\n  updateCoverAfterCreation(controller, cover);\n  trigger(controller, {\n    isEnd: false\n  });\n}\nfunction toLocalDelta(controller, dx, dy) {\n  var thisGroup = controller.group;\n  var localD = thisGroup.transformCoordToLocal(dx, dy);\n  var localZero = thisGroup.transformCoordToLocal(0, 0);\n  return [localD[0] - localZero[0], localD[1] - localZero[1]];\n}\nfunction clipByPanel(controller, cover, data) {\n  var panel = getPanelByCover(controller, cover);\n  return panel && panel !== BRUSH_PANEL_GLOBAL ? panel.clipPath(data, controller._transform) : clone(data);\n}\nfunction pointsToRect(points) {\n  var xmin = mathMin(points[0][0], points[1][0]);\n  var ymin = mathMin(points[0][1], points[1][1]);\n  var xmax = mathMax(points[0][0], points[1][0]);\n  var ymax = mathMax(points[0][1], points[1][1]);\n  return {\n    x: xmin,\n    y: ymin,\n    width: xmax - xmin,\n    height: ymax - ymin\n  };\n}\nfunction resetCursor(controller, e, localCursorPoint) {\n  if (\n  // Check active\n  !controller._brushType\n  // resetCursor should be always called when mouse is in zr area,\n  // but not called when mouse is out of zr area to avoid bad influence\n  // if `mousemove`, `mouseup` are triggered from `document` event.\n  || isOutsideZrArea(controller, e.offsetX, e.offsetY)) {\n    return;\n  }\n  var zr = controller._zr;\n  var covers = controller._covers;\n  var currPanel = getPanelByPoint(controller, e, localCursorPoint);\n  // Check whether in covers.\n  if (!controller._dragging) {\n    for (var i = 0; i < covers.length; i++) {\n      var brushOption = covers[i].__brushOption;\n      if (currPanel && (currPanel === BRUSH_PANEL_GLOBAL || brushOption.panelId === currPanel.panelId) && coverRenderers[brushOption.brushType].contain(covers[i], localCursorPoint[0], localCursorPoint[1])) {\n        // Use cursor style set on cover.\n        return;\n      }\n    }\n  }\n  currPanel && zr.setCursorStyle('crosshair');\n}\nfunction preventDefault(e) {\n  var rawE = e.event;\n  rawE.preventDefault && rawE.preventDefault();\n}\nfunction mainShapeContain(cover, x, y) {\n  return cover.childOfName('main').contain(x, y);\n}\nfunction updateCoverByMouse(controller, e, localCursorPoint, isEnd) {\n  var creatingCover = controller._creatingCover;\n  var panel = controller._creatingPanel;\n  var thisBrushOption = controller._brushOption;\n  var eventParams;\n  controller._track.push(localCursorPoint.slice());\n  if (shouldShowCover(controller) || creatingCover) {\n    if (panel && !creatingCover) {\n      thisBrushOption.brushMode === 'single' && clearCovers(controller);\n      var brushOption = clone(thisBrushOption);\n      brushOption.brushType = determineBrushType(brushOption.brushType, panel);\n      brushOption.panelId = panel === BRUSH_PANEL_GLOBAL ? null : panel.panelId;\n      creatingCover = controller._creatingCover = createCover(controller, brushOption);\n      controller._covers.push(creatingCover);\n    }\n    if (creatingCover) {\n      var coverRenderer = coverRenderers[determineBrushType(controller._brushType, panel)];\n      var coverBrushOption = creatingCover.__brushOption;\n      coverBrushOption.range = coverRenderer.getCreatingRange(clipByPanel(controller, creatingCover, controller._track));\n      if (isEnd) {\n        endCreating(controller, creatingCover);\n        coverRenderer.updateCommon(controller, creatingCover);\n      }\n      updateCoverShape(controller, creatingCover);\n      eventParams = {\n        isEnd: isEnd\n      };\n    }\n  } else if (isEnd && thisBrushOption.brushMode === 'single' && thisBrushOption.removeOnClick) {\n    // Help user to remove covers easily, only by a tiny drag, in 'single' mode.\n    // But a single click do not clear covers, because user may have casual\n    // clicks (for example, click on other component and do not expect covers\n    // disappear).\n    // Only some cover removed, trigger action, but not every click trigger action.\n    if (getPanelByPoint(controller, e, localCursorPoint) && clearCovers(controller)) {\n      eventParams = {\n        isEnd: isEnd,\n        removeOnClick: true\n      };\n    }\n  }\n  return eventParams;\n}\nfunction determineBrushType(brushType, panel) {\n  if (brushType === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(panel && panel.defaultBrushType, 'MUST have defaultBrushType when brushType is \"atuo\"');\n    }\n    return panel.defaultBrushType;\n  }\n  return brushType;\n}\nvar pointerHandlers = {\n  mousedown: function (e) {\n    if (this._dragging) {\n      // In case some browser do not support globalOut,\n      // and release mouse out side the browser.\n      handleDragEnd(this, e);\n    } else if (!e.target || !e.target.draggable) {\n      preventDefault(e);\n      var localCursorPoint = this.group.transformCoordToLocal(e.offsetX, e.offsetY);\n      this._creatingCover = null;\n      var panel = this._creatingPanel = getPanelByPoint(this, e, localCursorPoint);\n      if (panel) {\n        this._dragging = true;\n        this._track = [localCursorPoint.slice()];\n      }\n    }\n  },\n  mousemove: function (e) {\n    var x = e.offsetX;\n    var y = e.offsetY;\n    var localCursorPoint = this.group.transformCoordToLocal(x, y);\n    resetCursor(this, e, localCursorPoint);\n    if (this._dragging) {\n      preventDefault(e);\n      var eventParams = updateCoverByMouse(this, e, localCursorPoint, false);\n      eventParams && trigger(this, eventParams);\n    }\n  },\n  mouseup: function (e) {\n    handleDragEnd(this, e);\n  }\n};\nfunction handleDragEnd(controller, e) {\n  if (controller._dragging) {\n    preventDefault(e);\n    var x = e.offsetX;\n    var y = e.offsetY;\n    var localCursorPoint = controller.group.transformCoordToLocal(x, y);\n    var eventParams = updateCoverByMouse(controller, e, localCursorPoint, true);\n    controller._dragging = false;\n    controller._track = [];\n    controller._creatingCover = null;\n    // trigger event should be at final, after procedure will be nested.\n    eventParams && trigger(controller, eventParams);\n  }\n}\nfunction isOutsideZrArea(controller, x, y) {\n  var zr = controller._zr;\n  return x < 0 || x > zr.getWidth() || y < 0 || y > zr.getHeight();\n}\n/**\n * key: brushType\n */\nvar coverRenderers = {\n  lineX: getLineRenderer(0),\n  lineY: getLineRenderer(1),\n  rect: {\n    createCover: function (controller, brushOption) {\n      function returnInput(range) {\n        return range;\n      }\n      return createBaseRectCover({\n        toRectRange: returnInput,\n        fromRectRange: returnInput\n      }, controller, brushOption, [['w'], ['e'], ['n'], ['s'], ['s', 'e'], ['s', 'w'], ['n', 'e'], ['n', 'w']]);\n    },\n    getCreatingRange: function (localTrack) {\n      var ends = getTrackEnds(localTrack);\n      return formatRectRange(ends[1][0], ends[1][1], ends[0][0], ends[0][1]);\n    },\n    updateCoverShape: function (controller, cover, localRange, brushOption) {\n      updateBaseRect(controller, cover, localRange, brushOption);\n    },\n    updateCommon: updateCommon,\n    contain: mainShapeContain\n  },\n  polygon: {\n    createCover: function (controller, brushOption) {\n      var cover = new graphic.Group();\n      // Do not use graphic.Polygon because graphic.Polyline do not close the\n      // border of the shape when drawing, which is a better experience for user.\n      cover.add(new graphic.Polyline({\n        name: 'main',\n        style: makeStyle(brushOption),\n        silent: true\n      }));\n      return cover;\n    },\n    getCreatingRange: function (localTrack) {\n      return localTrack;\n    },\n    endCreating: function (controller, cover) {\n      cover.remove(cover.childAt(0));\n      // Use graphic.Polygon close the shape.\n      cover.add(new graphic.Polygon({\n        name: 'main',\n        draggable: true,\n        drift: curry(driftPolygon, controller, cover),\n        ondragend: curry(trigger, controller, {\n          isEnd: true\n        })\n      }));\n    },\n    updateCoverShape: function (controller, cover, localRange, brushOption) {\n      cover.childAt(0).setShape({\n        points: clipByPanel(controller, cover, localRange)\n      });\n    },\n    updateCommon: updateCommon,\n    contain: mainShapeContain\n  }\n};\nfunction getLineRenderer(xyIndex) {\n  return {\n    createCover: function (controller, brushOption) {\n      return createBaseRectCover({\n        toRectRange: function (range) {\n          var rectRange = [range, [0, 100]];\n          xyIndex && rectRange.reverse();\n          return rectRange;\n        },\n        fromRectRange: function (rectRange) {\n          return rectRange[xyIndex];\n        }\n      }, controller, brushOption, [[['w'], ['e']], [['n'], ['s']]][xyIndex]);\n    },\n    getCreatingRange: function (localTrack) {\n      var ends = getTrackEnds(localTrack);\n      var min = mathMin(ends[0][xyIndex], ends[1][xyIndex]);\n      var max = mathMax(ends[0][xyIndex], ends[1][xyIndex]);\n      return [min, max];\n    },\n    updateCoverShape: function (controller, cover, localRange, brushOption) {\n      var otherExtent;\n      // If brushWidth not specified, fit the panel.\n      var panel = getPanelByCover(controller, cover);\n      if (panel !== BRUSH_PANEL_GLOBAL && panel.getLinearBrushOtherExtent) {\n        otherExtent = panel.getLinearBrushOtherExtent(xyIndex);\n      } else {\n        var zr = controller._zr;\n        otherExtent = [0, [zr.getWidth(), zr.getHeight()][1 - xyIndex]];\n      }\n      var rectRange = [localRange, otherExtent];\n      xyIndex && rectRange.reverse();\n      updateBaseRect(controller, cover, rectRange, brushOption);\n    },\n    updateCommon: updateCommon,\n    contain: mainShapeContain\n  };\n}\nexport default BrushController;"], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,0BAA0B;AACjG,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,gBAAgB,MAAM,uBAAuB;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,OAAO,GAAGJ,IAAI,CAACK,GAAG;AACtB,IAAIC,OAAO,GAAG,KAAK;AACnB,IAAIC,kBAAkB,GAAG,CAAC;AAC1B,IAAIC,qBAAqB,GAAG,CAAC;AAC7B,IAAIC,kBAAkB,GAAG,WAAW;AACpC,IAAIC,aAAa,GAAG;EAClBC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACTC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACTC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACTC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACV,CAAC;AACD,IAAIC,UAAU,GAAG;EACfJ,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPE,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,iBAAiB,GAAG;EACtBC,UAAU,EAAE;IACVC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,uBAAuB;IAC/BC,IAAI,EAAE;EACR,CAAC;EACDC,aAAa,EAAE,IAAI;EACnBC,SAAS,EAAE,QAAQ;EACnBC,aAAa,EAAE;AACjB,CAAC;AACD,IAAIC,OAAO,GAAG,CAAC;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnD7C,SAAS,CAAC4C,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAACE,EAAE,EAAE;IAC3B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrC;AACJ;AACA;IACID,KAAK,CAACE,MAAM,GAAG,EAAE;IACjB;AACJ;AACA;IACIF,KAAK,CAACG,OAAO,GAAG,EAAE;IAClBH,KAAK,CAACI,SAAS,GAAG,CAAC,CAAC;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC9C,MAAM,CAACsC,EAAE,CAAC;IACZ;IACAC,KAAK,CAACQ,GAAG,GAAGT,EAAE;IACdC,KAAK,CAACS,KAAK,GAAG,IAAI9C,OAAO,CAAC+C,KAAK,CAAC,CAAC;IACjCV,KAAK,CAACW,IAAI,GAAG,kBAAkB,GAAGf,OAAO,EAAE;IAC3CzC,IAAI,CAACyD,eAAe,EAAE,UAAUC,OAAO,EAAEC,SAAS,EAAE;MAClD,IAAI,CAACV,SAAS,CAACU,SAAS,CAAC,GAAGzD,IAAI,CAACwD,OAAO,EAAE,IAAI,CAAC;IACjD,CAAC,EAAEb,KAAK,CAAC;IACT,OAAOA,KAAK;EACd;EACA;AACF;AACA;EACEH,eAAe,CAACkB,SAAS,CAACC,WAAW,GAAG,UAAUC,WAAW,EAAE;IAC7D,IAAIZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC9C,MAAM,CAAC,IAAI,CAACyD,QAAQ,CAAC;IACvB;IACA,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC;IACzCH,WAAW,CAACI,SAAS,IAAI,IAAI,CAACC,cAAc,CAACL,WAAW,CAAC;IACzD,OAAO,IAAI;EACb,CAAC;EACDpB,eAAe,CAACkB,SAAS,CAACO,cAAc,GAAG,UAAUL,WAAW,EAAE;IAChE,IAAIlB,EAAE,GAAG,IAAI,CAACS,GAAG;IACjB;IACA,IAAI,CAAC,IAAI,CAACe,gBAAgB,EAAE;MAC1B3D,gBAAgB,CAAC4D,IAAI,CAACzB,EAAE,EAAEtB,kBAAkB,EAAE,IAAI,CAACkC,IAAI,CAAC;IAC1D;IACAxD,IAAI,CAAC,IAAI,CAACiD,SAAS,EAAE,UAAUS,OAAO,EAAEC,SAAS,EAAE;MACjDf,EAAE,CAAC0B,EAAE,CAACX,SAAS,EAAED,OAAO,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACM,UAAU,GAAGF,WAAW,CAACI,SAAS;IACvC,IAAI,CAACK,YAAY,GAAGpE,KAAK,CAACC,KAAK,CAAC6B,iBAAiB,CAAC,EAAE6B,WAAW,EAAE,IAAI,CAAC;EACxE,CAAC;EACDpB,eAAe,CAACkB,SAAS,CAACK,eAAe,GAAG,YAAY;IACtD,IAAIrB,EAAE,GAAG,IAAI,CAACS,GAAG;IACjB5C,gBAAgB,CAAC+D,OAAO,CAAC5B,EAAE,EAAEtB,kBAAkB,EAAE,IAAI,CAACkC,IAAI,CAAC;IAC3DxD,IAAI,CAAC,IAAI,CAACiD,SAAS,EAAE,UAAUS,OAAO,EAAEC,SAAS,EAAE;MACjDf,EAAE,CAAC6B,GAAG,CAACd,SAAS,EAAED,OAAO,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACM,UAAU,GAAG,IAAI,CAACO,YAAY,GAAG,IAAI;EAC5C,CAAC;EACD;AACF;AACA;EACE7B,eAAe,CAACkB,SAAS,CAACc,SAAS,GAAG,UAAUC,SAAS,EAAE;IACzD,IAAIA,SAAS,IAAIA,SAAS,CAACC,MAAM,EAAE;MACjC,IAAIC,QAAQ,GAAG,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;MAChC9E,IAAI,CAAC2E,SAAS,EAAE,UAAUA,SAAS,EAAE;QACnCE,QAAQ,CAACF,SAAS,CAACI,OAAO,CAAC,GAAG3E,KAAK,CAACuE,SAAS,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACG,OAAO,GAAG,IAAI;IACrB;IACA,OAAO,IAAI;EACb,CAAC;EACDpC,eAAe,CAACkB,SAAS,CAACoB,KAAK,GAAG,UAAUC,GAAG,EAAE;IAC/CA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACW,QAAQ,GAAG,IAAI,CAAC,CAAC;IACxB;IAEA,IAAI,CAACK,gBAAgB,GAAGa,GAAG,CAACC,eAAe;IAC3C,IAAIC,SAAS,GAAG,IAAI,CAAC7B,KAAK;IAC1B,IAAI,CAACD,GAAG,CAAC+B,GAAG,CAACD,SAAS,CAAC;IACvBA,SAAS,CAACE,IAAI,CAAC;MACbC,CAAC,EAAEL,GAAG,CAACK,CAAC,IAAI,CAAC;MACbC,CAAC,EAAEN,GAAG,CAACM,CAAC,IAAI,CAAC;MACbC,QAAQ,EAAEP,GAAG,CAACO,QAAQ,IAAI,CAAC;MAC3BC,MAAM,EAAER,GAAG,CAACQ,MAAM,IAAI,CAAC;MACvBC,MAAM,EAAET,GAAG,CAACS,MAAM,IAAI;IACxB,CAAC,CAAC;IACF,IAAI,CAACC,UAAU,GAAGR,SAAS,CAACS,iBAAiB,CAAC,CAAC;IAC/C,OAAO,IAAI;EACb,CAAC;EACD;EACA;EACA;EACA;AACF;AACA;AACA;AACA;EACElD,eAAe,CAACkB,SAAS,CAACiC,YAAY,GAAG,UAAUC,eAAe,EAAE;IAClE,IAAI5C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC9C,MAAM,CAAC,IAAI,CAACyD,QAAQ,CAAC;IACvB;IACA+B,eAAe,GAAG7F,GAAG,CAAC6F,eAAe,EAAE,UAAUC,WAAW,EAAE;MAC5D,OAAO5F,KAAK,CAACC,KAAK,CAAC6B,iBAAiB,CAAC,EAAE8D,WAAW,EAAE,IAAI,CAAC;IAC3D,CAAC,CAAC;IACF,IAAIC,WAAW,GAAG,iBAAiB;IACnC,IAAIC,SAAS,GAAG,IAAI,CAACjD,OAAO;IAC5B,IAAIkD,SAAS,GAAG,IAAI,CAAClD,OAAO,GAAG,EAAE;IACjC,IAAImD,UAAU,GAAG,IAAI;IACrB,IAAIC,aAAa,GAAG,IAAI,CAACC,cAAc;IACvC,IAAI3F,UAAU,CAACuF,SAAS,EAAEH,eAAe,EAAEQ,SAAS,EAAEC,MAAM,CAAC,CAACnB,GAAG,CAACoB,WAAW,CAAC,CAACC,MAAM,CAACD,WAAW,CAAC,CAACE,MAAM,CAACA,MAAM,CAAC,CAACC,OAAO,CAAC,CAAC;IAC3H,OAAO,IAAI;IACX,SAASJ,MAAMA,CAACzC,WAAW,EAAE8C,KAAK,EAAE;MAClC,OAAO,CAAC9C,WAAW,CAAC+C,EAAE,IAAI,IAAI,GAAG/C,WAAW,CAAC+C,EAAE,GAAGb,WAAW,GAAGY,KAAK,IAAI,GAAG,GAAG9C,WAAW,CAACI,SAAS;IACtG;IACA,SAASoC,SAASA,CAACQ,KAAK,EAAEF,KAAK,EAAE;MAC/B,OAAOL,MAAM,CAACO,KAAK,CAACC,aAAa,EAAEH,KAAK,CAAC;IAC3C;IACA,SAASJ,WAAWA,CAACQ,QAAQ,EAAEC,QAAQ,EAAE;MACvC,IAAIC,gBAAgB,GAAGpB,eAAe,CAACkB,QAAQ,CAAC;MAChD;MACA;MACA,IAAIC,QAAQ,IAAI,IAAI,IAAIhB,SAAS,CAACgB,QAAQ,CAAC,KAAKb,aAAa,EAAE;QAC7DF,SAAS,CAACc,QAAQ,CAAC,GAAGf,SAAS,CAACgB,QAAQ,CAAC;MAC3C,CAAC,MAAM;QACL,IAAIH,KAAK,GAAGZ,SAAS,CAACc,QAAQ,CAAC,GAAGC,QAAQ,IAAI,IAAI,IAAIhB,SAAS,CAACgB,QAAQ,CAAC,CAACF,aAAa,GAAGG,gBAAgB,EAAEjB,SAAS,CAACgB,QAAQ,CAAC,IAAIE,WAAW,CAAChB,UAAU,EAAEiB,WAAW,CAACjB,UAAU,EAAEe,gBAAgB,CAAC,CAAC;QACrMG,wBAAwB,CAAClB,UAAU,EAAEW,KAAK,CAAC;MAC7C;IACF;IACA,SAASJ,MAAMA,CAACO,QAAQ,EAAE;MACxB,IAAIhB,SAAS,CAACgB,QAAQ,CAAC,KAAKb,aAAa,EAAE;QACzCD,UAAU,CAAC7C,KAAK,CAACoD,MAAM,CAACT,SAAS,CAACgB,QAAQ,CAAC,CAAC;MAC9C;IACF;EACF,CAAC;EACDvE,eAAe,CAACkB,SAAS,CAAC0D,OAAO,GAAG,YAAY;IAC9C,IAAIpE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAAC,IAAI,CAACW,QAAQ,EAAE;QAClB;MACF;IACF;IACA,IAAI,CAACF,WAAW,CAAC,KAAK,CAAC;IACvB;IACA0D,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI,CAAClE,GAAG,CAACqD,MAAM,CAAC,IAAI,CAACpD,KAAK,CAAC;IAC3B,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACW,QAAQ,GAAG,KAAK,CAAC,CAAC;IACzB;IAEA,OAAO,IAAI;EACb,CAAC;EACDrB,eAAe,CAACkB,SAAS,CAAC4D,OAAO,GAAG,YAAY;IAC9C,IAAI,CAACF,OAAO,CAAC,CAAC;IACd,IAAI,CAAC7C,GAAG,CAAC,CAAC;EACZ,CAAC;EACD,OAAO/B,eAAe;AACxB,CAAC,CAACnC,QAAQ,CAAC;AACX,SAAS6G,WAAWA,CAACjB,UAAU,EAAErC,WAAW,EAAE;EAC5C,IAAIgD,KAAK,GAAGW,cAAc,CAAC3D,WAAW,CAACI,SAAS,CAAC,CAACkD,WAAW,CAACjB,UAAU,EAAErC,WAAW,CAAC;EACtFgD,KAAK,CAACC,aAAa,GAAGjD,WAAW;EACjC4D,OAAO,CAACZ,KAAK,EAAEhD,WAAW,CAAC;EAC3BqC,UAAU,CAAC7C,KAAK,CAAC8B,GAAG,CAAC0B,KAAK,CAAC;EAC3B,OAAOA,KAAK;AACd;AACA,SAASK,WAAWA,CAAChB,UAAU,EAAEC,aAAa,EAAE;EAC9C,IAAIuB,aAAa,GAAGC,gBAAgB,CAACxB,aAAa,CAAC;EACnD,IAAIuB,aAAa,CAACR,WAAW,EAAE;IAC7BQ,aAAa,CAACR,WAAW,CAAChB,UAAU,EAAEC,aAAa,CAAC;IACpDsB,OAAO,CAACtB,aAAa,EAAEA,aAAa,CAACW,aAAa,CAAC;EACrD;EACA,OAAOX,aAAa;AACtB;AACA,SAASyB,gBAAgBA,CAAC1B,UAAU,EAAEW,KAAK,EAAE;EAC3C,IAAIhD,WAAW,GAAGgD,KAAK,CAACC,aAAa;EACrCa,gBAAgB,CAACd,KAAK,CAAC,CAACe,gBAAgB,CAAC1B,UAAU,EAAEW,KAAK,EAAEhD,WAAW,CAACgE,KAAK,EAAEhE,WAAW,CAAC;AAC7F;AACA,SAAS4D,OAAOA,CAACZ,KAAK,EAAEhD,WAAW,EAAE;EACnC,IAAIiE,CAAC,GAAGjE,WAAW,CAACiE,CAAC;EACrBA,CAAC,IAAI,IAAI,KAAKA,CAAC,GAAG5G,OAAO,CAAC;EAC1B2F,KAAK,CAACkB,QAAQ,CAAC,UAAUC,EAAE,EAAE;IAC3BA,EAAE,CAACF,CAAC,GAAGA,CAAC;IACRE,EAAE,CAACC,EAAE,GAAGH,CAAC,CAAC,CAAC;EACb,CAAC,CAAC;AACJ;AAEA,SAASV,wBAAwBA,CAAClB,UAAU,EAAEW,KAAK,EAAE;EACnDc,gBAAgB,CAACd,KAAK,CAAC,CAACqB,YAAY,CAAChC,UAAU,EAAEW,KAAK,CAAC;EACvDe,gBAAgB,CAAC1B,UAAU,EAAEW,KAAK,CAAC;AACrC;AACA,SAASc,gBAAgBA,CAACd,KAAK,EAAE;EAC/B,OAAOW,cAAc,CAACX,KAAK,CAACC,aAAa,CAAC7C,SAAS,CAAC;AACtD;AACA;AACA,SAASkE,eAAeA,CAACjC,UAAU,EAAE1E,CAAC,EAAE4G,gBAAgB,EAAE;EACxD,IAAIC,MAAM,GAAGnC,UAAU,CAACrB,OAAO;EAC/B,IAAI,CAACwD,MAAM,EAAE;IACX,OAAO3H,kBAAkB,CAAC,CAAC;EAC7B;EAEA,IAAI4H,KAAK;EACT,IAAIC,SAAS,GAAGrC,UAAU,CAACR,UAAU;EACrC3F,IAAI,CAACsI,MAAM,EAAE,UAAUG,EAAE,EAAE;IACzBA,EAAE,CAACC,gBAAgB,CAACjH,CAAC,EAAE4G,gBAAgB,EAAEG,SAAS,CAAC,KAAKD,KAAK,GAAGE,EAAE,CAAC;EACrE,CAAC,CAAC;EACF,OAAOF,KAAK;AACd;AACA;AACA,SAASI,eAAeA,CAACxC,UAAU,EAAEW,KAAK,EAAE;EAC1C,IAAIwB,MAAM,GAAGnC,UAAU,CAACrB,OAAO;EAC/B,IAAI,CAACwD,MAAM,EAAE;IACX,OAAO3H,kBAAkB,CAAC,CAAC;EAC7B;EAEA,IAAIoE,OAAO,GAAG+B,KAAK,CAACC,aAAa,CAAChC,OAAO;EACzC;EACA;EACA,OAAOA,OAAO,IAAI,IAAI,GAAGuD,MAAM,CAACvD,OAAO,CAAC,GAAGpE,kBAAkB;AAC/D;AACA,SAAS4G,WAAWA,CAACpB,UAAU,EAAE;EAC/B,IAAIyC,MAAM,GAAGzC,UAAU,CAACnD,OAAO;EAC/B,IAAI6F,cAAc,GAAGD,MAAM,CAAChE,MAAM;EAClC5E,IAAI,CAAC4I,MAAM,EAAE,UAAU9B,KAAK,EAAE;IAC5BX,UAAU,CAAC7C,KAAK,CAACoD,MAAM,CAACI,KAAK,CAAC;EAChC,CAAC,EAAEX,UAAU,CAAC;EACdyC,MAAM,CAAChE,MAAM,GAAG,CAAC;EACjB,OAAO,CAAC,CAACiE,cAAc;AACzB;AACA,SAASC,OAAOA,CAAC3C,UAAU,EAAElB,GAAG,EAAE;EAChC,IAAI8D,KAAK,GAAG9I,GAAG,CAACkG,UAAU,CAACnD,OAAO,EAAE,UAAU8D,KAAK,EAAE;IACnD,IAAIhD,WAAW,GAAGgD,KAAK,CAACC,aAAa;IACrC,IAAIe,KAAK,GAAG1H,KAAK,CAAC0D,WAAW,CAACgE,KAAK,CAAC;IACpC,OAAO;MACL5D,SAAS,EAAEJ,WAAW,CAACI,SAAS;MAChCa,OAAO,EAAEjB,WAAW,CAACiB,OAAO;MAC5B+C,KAAK,EAAEA;IACT,CAAC;EACH,CAAC,CAAC;EACF3B,UAAU,CAAC2C,OAAO,CAAC,OAAO,EAAE;IAC1BC,KAAK,EAAEA,KAAK;IACZC,KAAK,EAAE,CAAC,CAAC/D,GAAG,CAAC+D,KAAK;IAClBxG,aAAa,EAAE,CAAC,CAACyC,GAAG,CAACzC;EACvB,CAAC,CAAC;AACJ;AACA,SAASyG,eAAeA,CAAC9C,UAAU,EAAE;EACnC,IAAI+C,KAAK,GAAG/C,UAAU,CAACpD,MAAM;EAC7B,IAAI,CAACmG,KAAK,CAACtE,MAAM,EAAE;IACjB,OAAO,KAAK;EACd;EACA,IAAIuE,EAAE,GAAGD,KAAK,CAACA,KAAK,CAACtE,MAAM,GAAG,CAAC,CAAC;EAChC,IAAIwE,EAAE,GAAGF,KAAK,CAAC,CAAC,CAAC;EACjB,IAAIG,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtB,IAAIE,EAAE,GAAGH,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;EACtB,IAAIG,IAAI,GAAGtI,OAAO,CAACoI,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,EAAE,GAAG,CAAC;EAC1C,OAAOC,IAAI,GAAGnI,kBAAkB;AAClC;AACA,SAASoI,YAAYA,CAACN,KAAK,EAAE;EAC3B,IAAIO,IAAI,GAAGP,KAAK,CAACtE,MAAM,GAAG,CAAC;EAC3B6E,IAAI,GAAG,CAAC,KAAKA,IAAI,GAAG,CAAC,CAAC;EACtB,OAAO,CAACP,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAACO,IAAI,CAAC,CAAC;AAChC;AACA;AACA,SAASC,mBAAmBA,CAACC,kBAAkB,EAAExD,UAAU,EAAErC,WAAW,EAAE8F,iBAAiB,EAAE;EAC3F,IAAI9C,KAAK,GAAG,IAAItG,OAAO,CAAC+C,KAAK,CAAC,CAAC;EAC/BuD,KAAK,CAAC1B,GAAG,CAAC,IAAI5E,OAAO,CAACqJ,IAAI,CAAC;IACzBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAEC,SAAS,CAAClG,WAAW,CAAC;IAC7BmG,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAErK,KAAK,CAACsK,SAAS,EAAEV,kBAAkB,EAAExD,UAAU,EAAEW,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACpFwD,SAAS,EAAEvK,KAAK,CAAC+I,OAAO,EAAE3C,UAAU,EAAE;MACpC6C,KAAK,EAAE;IACT,CAAC;EACH,CAAC,CAAC,CAAC;EACHhJ,IAAI,CAAC4J,iBAAiB,EAAE,UAAUW,YAAY,EAAE;IAC9CzD,KAAK,CAAC1B,GAAG,CAAC,IAAI5E,OAAO,CAACqJ,IAAI,CAAC;MACzBC,IAAI,EAAES,YAAY,CAACC,IAAI,CAAC,EAAE,CAAC;MAC3BT,KAAK,EAAE;QACLU,OAAO,EAAE;MACX,CAAC;MACDP,SAAS,EAAE,IAAI;MACfD,MAAM,EAAE,IAAI;MACZS,SAAS,EAAE,IAAI;MACfN,KAAK,EAAErK,KAAK,CAACsK,SAAS,EAAEV,kBAAkB,EAAExD,UAAU,EAAEW,KAAK,EAAEyD,YAAY,CAAC;MAC5ED,SAAS,EAAEvK,KAAK,CAAC+I,OAAO,EAAE3C,UAAU,EAAE;QACpC6C,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAOlC,KAAK;AACd;AACA,SAAS6D,cAAcA,CAACxE,UAAU,EAAEW,KAAK,EAAE8D,UAAU,EAAE9G,WAAW,EAAE;EAClE,IAAI3B,SAAS,GAAG2B,WAAW,CAAC5B,UAAU,CAACC,SAAS,IAAI,CAAC;EACrD,IAAI0I,UAAU,GAAG9J,OAAO,CAACoB,SAAS,EAAEd,qBAAqB,CAAC;EAC1D,IAAIiE,CAAC,GAAGsF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,IAAIrF,CAAC,GAAGqF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,IAAIE,EAAE,GAAGxF,CAAC,GAAGnD,SAAS,GAAG,CAAC;EAC1B,IAAI4I,EAAE,GAAGxF,CAAC,GAAGpD,SAAS,GAAG,CAAC;EAC1B,IAAI6I,EAAE,GAAGJ,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,IAAIK,EAAE,GAAGL,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,IAAIM,GAAG,GAAGF,EAAE,GAAGH,UAAU,GAAG1I,SAAS,GAAG,CAAC;EACzC,IAAIgJ,GAAG,GAAGF,EAAE,GAAGJ,UAAU,GAAG1I,SAAS,GAAG,CAAC;EACzC,IAAIiJ,KAAK,GAAGJ,EAAE,GAAG1F,CAAC;EAClB,IAAI+F,MAAM,GAAGJ,EAAE,GAAG1F,CAAC;EACnB,IAAI+F,MAAM,GAAGF,KAAK,GAAGjJ,SAAS;EAC9B,IAAIoJ,OAAO,GAAGF,MAAM,GAAGlJ,SAAS;EAChCqJ,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,MAAM,EAAExB,CAAC,EAAEC,CAAC,EAAE6F,KAAK,EAAEC,MAAM,CAAC;EAC/D,IAAIvH,WAAW,CAACxB,aAAa,EAAE;IAC7BkJ,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,GAAG,EAAEgE,EAAE,EAAEC,EAAE,EAAEF,UAAU,EAAEU,OAAO,CAAC;IACpEC,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,GAAG,EAAEoE,GAAG,EAAEH,EAAE,EAAEF,UAAU,EAAEU,OAAO,CAAC;IACrEC,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,GAAG,EAAEgE,EAAE,EAAEC,EAAE,EAAEO,MAAM,EAAET,UAAU,CAAC;IACnEW,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,GAAG,EAAEgE,EAAE,EAAEK,GAAG,EAAEG,MAAM,EAAET,UAAU,CAAC;IACpEW,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,IAAI,EAAEgE,EAAE,EAAEC,EAAE,EAAEF,UAAU,EAAEA,UAAU,CAAC;IACxEW,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,IAAI,EAAEoE,GAAG,EAAEH,EAAE,EAAEF,UAAU,EAAEA,UAAU,CAAC;IACzEW,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,IAAI,EAAEgE,EAAE,EAAEK,GAAG,EAAEN,UAAU,EAAEA,UAAU,CAAC;IACzEW,eAAe,CAACrF,UAAU,EAAEW,KAAK,EAAE,IAAI,EAAEoE,GAAG,EAAEC,GAAG,EAAEN,UAAU,EAAEA,UAAU,CAAC;EAC5E;AACF;AACA,SAAS1C,YAAYA,CAAChC,UAAU,EAAEW,KAAK,EAAE;EACvC,IAAIhD,WAAW,GAAGgD,KAAK,CAACC,aAAa;EACrC,IAAIzE,aAAa,GAAGwB,WAAW,CAACxB,aAAa;EAC7C,IAAImJ,MAAM,GAAG3E,KAAK,CAAC4E,OAAO,CAAC,CAAC,CAAC;EAC7BD,MAAM,CAACE,QAAQ,CAAC3B,SAAS,CAAClG,WAAW,CAAC,CAAC;EACvC2H,MAAM,CAACpG,IAAI,CAAC;IACV4E,MAAM,EAAE,CAAC3H,aAAa;IACtB6H,MAAM,EAAE7H,aAAa,GAAG,MAAM,GAAG;EACnC,CAAC,CAAC;EACFtC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,UAAUuK,YAAY,EAAE;IACzG,IAAItC,EAAE,GAAGnB,KAAK,CAAC8E,WAAW,CAACrB,YAAY,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;IACjD,IAAIqB,SAAS,GAAGtB,YAAY,CAAC3F,MAAM,KAAK,CAAC,GAAGkH,mBAAmB,CAAC3F,UAAU,EAAEoE,YAAY,CAAC,CAAC,CAAC,CAAC,GAAGwB,mBAAmB,CAAC5F,UAAU,EAAEoE,YAAY,CAAC;IAC5ItC,EAAE,IAAIA,EAAE,CAAC5C,IAAI,CAAC;MACZ4E,MAAM,EAAE,CAAC3H,aAAa;MACtBoI,SAAS,EAAE,CAACpI,aAAa;MACzB6H,MAAM,EAAE7H,aAAa,GAAGV,UAAU,CAACiK,SAAS,CAAC,GAAG,SAAS,GAAG;IAC9D,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASL,eAAeA,CAACrF,UAAU,EAAEW,KAAK,EAAEgD,IAAI,EAAExE,CAAC,EAAEC,CAAC,EAAE/D,CAAC,EAAEwK,CAAC,EAAE;EAC5D,IAAI/D,EAAE,GAAGnB,KAAK,CAAC8E,WAAW,CAAC9B,IAAI,CAAC;EAChC7B,EAAE,IAAIA,EAAE,CAACgE,QAAQ,CAACC,YAAY,CAACC,WAAW,CAAChG,UAAU,EAAEW,KAAK,EAAE,CAAC,CAACxB,CAAC,EAAEC,CAAC,CAAC,EAAE,CAACD,CAAC,GAAG9D,CAAC,EAAE+D,CAAC,GAAGyG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3F;AACA,SAAShC,SAASA,CAAClG,WAAW,EAAE;EAC9B,OAAOzD,QAAQ,CAAC;IACd+L,aAAa,EAAE;EACjB,CAAC,EAAEtI,WAAW,CAAC5B,UAAU,CAAC;AAC5B;AACA,SAASmK,eAAeA,CAAC/G,CAAC,EAAEC,CAAC,EAAEyF,EAAE,EAAEC,EAAE,EAAE;EACrC,IAAInK,GAAG,GAAG,CAACF,OAAO,CAAC0E,CAAC,EAAE0F,EAAE,CAAC,EAAEpK,OAAO,CAAC2E,CAAC,EAAE0F,EAAE,CAAC,CAAC;EAC1C,IAAIjK,GAAG,GAAG,CAACD,OAAO,CAACuE,CAAC,EAAE0F,EAAE,CAAC,EAAEjK,OAAO,CAACwE,CAAC,EAAE0F,EAAE,CAAC,CAAC;EAC1C,OAAO,CAAC,CAACnK,GAAG,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAACF,GAAG,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAC1C;AACH;AAEA,SAASsL,YAAYA,CAACnG,UAAU,EAAE;EAChC,OAAO3F,OAAO,CAAC8L,YAAY,CAACnG,UAAU,CAAC7C,KAAK,CAAC;AAC/C;AACA,SAASwI,mBAAmBA,CAAC3F,UAAU,EAAEoG,YAAY,EAAE;EACrD,IAAItM,GAAG,GAAG;IACRuB,CAAC,EAAE,MAAM;IACTC,CAAC,EAAE,OAAO;IACVC,CAAC,EAAE,KAAK;IACRC,CAAC,EAAE;EACL,CAAC;EACD,IAAI6K,UAAU,GAAG;IACfC,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRC,MAAM,EAAE;EACV,CAAC;EACD,IAAIC,GAAG,GAAGrM,OAAO,CAACsM,kBAAkB,CAAC7M,GAAG,CAACsM,YAAY,CAAC,EAAED,YAAY,CAACnG,UAAU,CAAC,CAAC;EACjF,OAAOqG,UAAU,CAACK,GAAG,CAAC;AACxB;AACA,SAASd,mBAAmBA,CAAC5F,UAAU,EAAE4G,eAAe,EAAE;EACxD,IAAIlB,SAAS,GAAG,CAACC,mBAAmB,CAAC3F,UAAU,EAAE4G,eAAe,CAAC,CAAC,CAAC,CAAC,EAAEjB,mBAAmB,CAAC3F,UAAU,EAAE4G,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1H,CAAClB,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,KAAKA,SAAS,CAACmB,OAAO,CAAC,CAAC;EACrE,OAAOnB,SAAS,CAACrB,IAAI,CAAC,EAAE,CAAC;AAC3B;AACA,SAASH,SAASA,CAACV,kBAAkB,EAAExD,UAAU,EAAEW,KAAK,EAAEmG,eAAe,EAAE5D,EAAE,EAAEC,EAAE,EAAE;EACjF,IAAIxF,WAAW,GAAGgD,KAAK,CAACC,aAAa;EACrC,IAAImG,SAAS,GAAGvD,kBAAkB,CAACwD,WAAW,CAACrJ,WAAW,CAACgE,KAAK,CAAC;EACjE,IAAIsF,UAAU,GAAGC,YAAY,CAAClH,UAAU,EAAEkD,EAAE,EAAEC,EAAE,CAAC;EACjDtJ,IAAI,CAACiN,eAAe,EAAE,UAAUK,OAAO,EAAE;IACvC,IAAIC,GAAG,GAAGhM,aAAa,CAAC+L,OAAO,CAAC;IAChCJ,SAAS,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIH,UAAU,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,CAAC;EACFzJ,WAAW,CAACgE,KAAK,GAAG6B,kBAAkB,CAAC6D,aAAa,CAACnB,eAAe,CAACa,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzI7F,wBAAwB,CAAClB,UAAU,EAAEW,KAAK,CAAC;EAC3CgC,OAAO,CAAC3C,UAAU,EAAE;IAClB6C,KAAK,EAAE;EACT,CAAC,CAAC;AACJ;AACA,SAASyE,YAAYA,CAACtH,UAAU,EAAEW,KAAK,EAAEuC,EAAE,EAAEC,EAAE,EAAE;EAC/C,IAAIxB,KAAK,GAAGhB,KAAK,CAACC,aAAa,CAACe,KAAK;EACrC,IAAIsF,UAAU,GAAGC,YAAY,CAAClH,UAAU,EAAEkD,EAAE,EAAEC,EAAE,CAAC;EACjDtJ,IAAI,CAAC8H,KAAK,EAAE,UAAU4F,KAAK,EAAE;IAC3BA,KAAK,CAAC,CAAC,CAAC,IAAIN,UAAU,CAAC,CAAC,CAAC;IACzBM,KAAK,CAAC,CAAC,CAAC,IAAIN,UAAU,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC;EACF/F,wBAAwB,CAAClB,UAAU,EAAEW,KAAK,CAAC;EAC3CgC,OAAO,CAAC3C,UAAU,EAAE;IAClB6C,KAAK,EAAE;EACT,CAAC,CAAC;AACJ;AACA,SAASqE,YAAYA,CAAClH,UAAU,EAAEkD,EAAE,EAAEC,EAAE,EAAE;EACxC,IAAInE,SAAS,GAAGgB,UAAU,CAAC7C,KAAK;EAChC,IAAIqK,MAAM,GAAGxI,SAAS,CAACyI,qBAAqB,CAACvE,EAAE,EAAEC,EAAE,CAAC;EACpD,IAAIuE,SAAS,GAAG1I,SAAS,CAACyI,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;EACrD,OAAO,CAACD,MAAM,CAAC,CAAC,CAAC,GAAGE,SAAS,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,CAAC,GAAGE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7D;AACA,SAAS1B,WAAWA,CAAChG,UAAU,EAAEW,KAAK,EAAEgH,IAAI,EAAE;EAC5C,IAAIvF,KAAK,GAAGI,eAAe,CAACxC,UAAU,EAAEW,KAAK,CAAC;EAC9C,OAAOyB,KAAK,IAAIA,KAAK,KAAK5H,kBAAkB,GAAG4H,KAAK,CAACwF,QAAQ,CAACD,IAAI,EAAE3H,UAAU,CAACR,UAAU,CAAC,GAAGvF,KAAK,CAAC0N,IAAI,CAAC;AAC1G;AACA,SAAS5B,YAAYA,CAAC8B,MAAM,EAAE;EAC5B,IAAIC,IAAI,GAAGrN,OAAO,CAACoN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,IAAIE,IAAI,GAAGtN,OAAO,CAACoN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,IAAIG,IAAI,GAAGpN,OAAO,CAACiN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,IAAII,IAAI,GAAGrN,OAAO,CAACiN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,OAAO;IACL1I,CAAC,EAAE2I,IAAI;IACP1I,CAAC,EAAE2I,IAAI;IACP9C,KAAK,EAAE+C,IAAI,GAAGF,IAAI;IAClB5C,MAAM,EAAE+C,IAAI,GAAGF;EACjB,CAAC;AACH;AACA,SAASG,WAAWA,CAAClI,UAAU,EAAE1E,CAAC,EAAE4G,gBAAgB,EAAE;EACpD;EACA;EACA,CAAClC,UAAU,CAACnC;EACZ;EACA;EACA;EAAA,GACGsK,eAAe,CAACnI,UAAU,EAAE1E,CAAC,CAAC8M,OAAO,EAAE9M,CAAC,CAAC+M,OAAO,CAAC,EAAE;IACpD;EACF;EACA,IAAI5L,EAAE,GAAGuD,UAAU,CAAC9C,GAAG;EACvB,IAAIuF,MAAM,GAAGzC,UAAU,CAACnD,OAAO;EAC/B,IAAIyL,SAAS,GAAGrG,eAAe,CAACjC,UAAU,EAAE1E,CAAC,EAAE4G,gBAAgB,CAAC;EAChE;EACA,IAAI,CAAClC,UAAU,CAACuI,SAAS,EAAE;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/F,MAAM,CAAChE,MAAM,EAAE+J,CAAC,EAAE,EAAE;MACtC,IAAI7K,WAAW,GAAG8E,MAAM,CAAC+F,CAAC,CAAC,CAAC5H,aAAa;MACzC,IAAI0H,SAAS,KAAKA,SAAS,KAAK9N,kBAAkB,IAAImD,WAAW,CAACiB,OAAO,KAAK0J,SAAS,CAAC1J,OAAO,CAAC,IAAI0C,cAAc,CAAC3D,WAAW,CAACI,SAAS,CAAC,CAAC0K,OAAO,CAAChG,MAAM,CAAC+F,CAAC,CAAC,EAAEtG,gBAAgB,CAAC,CAAC,CAAC,EAAEA,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE;QACtM;QACA;MACF;IACF;EACF;EACAoG,SAAS,IAAI7L,EAAE,CAACiM,cAAc,CAAC,WAAW,CAAC;AAC7C;AACA,SAASC,cAAcA,CAACrN,CAAC,EAAE;EACzB,IAAIsN,IAAI,GAAGtN,CAAC,CAACuN,KAAK;EAClBD,IAAI,CAACD,cAAc,IAAIC,IAAI,CAACD,cAAc,CAAC,CAAC;AAC9C;AACA,SAASG,gBAAgBA,CAACnI,KAAK,EAAExB,CAAC,EAAEC,CAAC,EAAE;EACrC,OAAOuB,KAAK,CAAC8E,WAAW,CAAC,MAAM,CAAC,CAACgD,OAAO,CAACtJ,CAAC,EAAEC,CAAC,CAAC;AAChD;AACA,SAAS2J,kBAAkBA,CAAC/I,UAAU,EAAE1E,CAAC,EAAE4G,gBAAgB,EAAEW,KAAK,EAAE;EAClE,IAAI5C,aAAa,GAAGD,UAAU,CAACE,cAAc;EAC7C,IAAIkC,KAAK,GAAGpC,UAAU,CAACgJ,cAAc;EACrC,IAAIC,eAAe,GAAGjJ,UAAU,CAAC5B,YAAY;EAC7C,IAAI8K,WAAW;EACflJ,UAAU,CAACpD,MAAM,CAACuM,IAAI,CAACjH,gBAAgB,CAACkH,KAAK,CAAC,CAAC,CAAC;EAChD,IAAItG,eAAe,CAAC9C,UAAU,CAAC,IAAIC,aAAa,EAAE;IAChD,IAAImC,KAAK,IAAI,CAACnC,aAAa,EAAE;MAC3BgJ,eAAe,CAAC7M,SAAS,KAAK,QAAQ,IAAIgF,WAAW,CAACpB,UAAU,CAAC;MACjE,IAAIrC,WAAW,GAAG1D,KAAK,CAACgP,eAAe,CAAC;MACxCtL,WAAW,CAACI,SAAS,GAAGsL,kBAAkB,CAAC1L,WAAW,CAACI,SAAS,EAAEqE,KAAK,CAAC;MACxEzE,WAAW,CAACiB,OAAO,GAAGwD,KAAK,KAAK5H,kBAAkB,GAAG,IAAI,GAAG4H,KAAK,CAACxD,OAAO;MACzEqB,aAAa,GAAGD,UAAU,CAACE,cAAc,GAAGe,WAAW,CAACjB,UAAU,EAAErC,WAAW,CAAC;MAChFqC,UAAU,CAACnD,OAAO,CAACsM,IAAI,CAAClJ,aAAa,CAAC;IACxC;IACA,IAAIA,aAAa,EAAE;MACjB,IAAIuB,aAAa,GAAGF,cAAc,CAAC+H,kBAAkB,CAACrJ,UAAU,CAACnC,UAAU,EAAEuE,KAAK,CAAC,CAAC;MACpF,IAAIkH,gBAAgB,GAAGrJ,aAAa,CAACW,aAAa;MAClD0I,gBAAgB,CAAC3H,KAAK,GAAGH,aAAa,CAAC+H,gBAAgB,CAACvD,WAAW,CAAChG,UAAU,EAAEC,aAAa,EAAED,UAAU,CAACpD,MAAM,CAAC,CAAC;MAClH,IAAIiG,KAAK,EAAE;QACT7B,WAAW,CAAChB,UAAU,EAAEC,aAAa,CAAC;QACtCuB,aAAa,CAACQ,YAAY,CAAChC,UAAU,EAAEC,aAAa,CAAC;MACvD;MACAyB,gBAAgB,CAAC1B,UAAU,EAAEC,aAAa,CAAC;MAC3CiJ,WAAW,GAAG;QACZrG,KAAK,EAAEA;MACT,CAAC;IACH;EACF,CAAC,MAAM,IAAIA,KAAK,IAAIoG,eAAe,CAAC7M,SAAS,KAAK,QAAQ,IAAI6M,eAAe,CAAC5M,aAAa,EAAE;IAC3F;IACA;IACA;IACA;IACA;IACA,IAAI4F,eAAe,CAACjC,UAAU,EAAE1E,CAAC,EAAE4G,gBAAgB,CAAC,IAAId,WAAW,CAACpB,UAAU,CAAC,EAAE;MAC/EkJ,WAAW,GAAG;QACZrG,KAAK,EAAEA,KAAK;QACZxG,aAAa,EAAE;MACjB,CAAC;IACH;EACF;EACA,OAAO6M,WAAW;AACpB;AACA,SAASG,kBAAkBA,CAACtL,SAAS,EAAEqE,KAAK,EAAE;EAC5C,IAAIrE,SAAS,KAAK,MAAM,EAAE;IACxB,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC9C,MAAM,CAACiI,KAAK,IAAIA,KAAK,CAACoH,gBAAgB,EAAE,qDAAqD,CAAC;IAChG;IACA,OAAOpH,KAAK,CAACoH,gBAAgB;EAC/B;EACA,OAAOzL,SAAS;AAClB;AACA,IAAIT,eAAe,GAAG;EACpBmM,SAAS,EAAE,SAAXA,SAASA,CAAYnO,CAAC,EAAE;IACtB,IAAI,IAAI,CAACiN,SAAS,EAAE;MAClB;MACA;MACAmB,aAAa,CAAC,IAAI,EAAEpO,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI,CAACA,CAAC,CAACqO,MAAM,IAAI,CAACrO,CAAC,CAACqO,MAAM,CAAC5F,SAAS,EAAE;MAC3C4E,cAAc,CAACrN,CAAC,CAAC;MACjB,IAAI4G,gBAAgB,GAAG,IAAI,CAAC/E,KAAK,CAACsK,qBAAqB,CAACnM,CAAC,CAAC8M,OAAO,EAAE9M,CAAC,CAAC+M,OAAO,CAAC;MAC7E,IAAI,CAACnI,cAAc,GAAG,IAAI;MAC1B,IAAIkC,KAAK,GAAG,IAAI,CAAC4G,cAAc,GAAG/G,eAAe,CAAC,IAAI,EAAE3G,CAAC,EAAE4G,gBAAgB,CAAC;MAC5E,IAAIE,KAAK,EAAE;QACT,IAAI,CAACmG,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC3L,MAAM,GAAG,CAACsF,gBAAgB,CAACkH,KAAK,CAAC,CAAC,CAAC;MAC1C;IACF;EACF,CAAC;EACDQ,SAAS,EAAE,SAAXA,SAASA,CAAYtO,CAAC,EAAE;IACtB,IAAI6D,CAAC,GAAG7D,CAAC,CAAC8M,OAAO;IACjB,IAAIhJ,CAAC,GAAG9D,CAAC,CAAC+M,OAAO;IACjB,IAAInG,gBAAgB,GAAG,IAAI,CAAC/E,KAAK,CAACsK,qBAAqB,CAACtI,CAAC,EAAEC,CAAC,CAAC;IAC7D8I,WAAW,CAAC,IAAI,EAAE5M,CAAC,EAAE4G,gBAAgB,CAAC;IACtC,IAAI,IAAI,CAACqG,SAAS,EAAE;MAClBI,cAAc,CAACrN,CAAC,CAAC;MACjB,IAAI4N,WAAW,GAAGH,kBAAkB,CAAC,IAAI,EAAEzN,CAAC,EAAE4G,gBAAgB,EAAE,KAAK,CAAC;MACtEgH,WAAW,IAAIvG,OAAO,CAAC,IAAI,EAAEuG,WAAW,CAAC;IAC3C;EACF,CAAC;EACDW,OAAO,EAAE,SAATA,OAAOA,CAAYvO,CAAC,EAAE;IACpBoO,aAAa,CAAC,IAAI,EAAEpO,CAAC,CAAC;EACxB;AACF,CAAC;AACD,SAASoO,aAAaA,CAAC1J,UAAU,EAAE1E,CAAC,EAAE;EACpC,IAAI0E,UAAU,CAACuI,SAAS,EAAE;IACxBI,cAAc,CAACrN,CAAC,CAAC;IACjB,IAAI6D,CAAC,GAAG7D,CAAC,CAAC8M,OAAO;IACjB,IAAIhJ,CAAC,GAAG9D,CAAC,CAAC+M,OAAO;IACjB,IAAInG,gBAAgB,GAAGlC,UAAU,CAAC7C,KAAK,CAACsK,qBAAqB,CAACtI,CAAC,EAAEC,CAAC,CAAC;IACnE,IAAI8J,WAAW,GAAGH,kBAAkB,CAAC/I,UAAU,EAAE1E,CAAC,EAAE4G,gBAAgB,EAAE,IAAI,CAAC;IAC3ElC,UAAU,CAACuI,SAAS,GAAG,KAAK;IAC5BvI,UAAU,CAACpD,MAAM,GAAG,EAAE;IACtBoD,UAAU,CAACE,cAAc,GAAG,IAAI;IAChC;IACAgJ,WAAW,IAAIvG,OAAO,CAAC3C,UAAU,EAAEkJ,WAAW,CAAC;EACjD;AACF;AACA,SAASf,eAAeA,CAACnI,UAAU,EAAEb,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAI3C,EAAE,GAAGuD,UAAU,CAAC9C,GAAG;EACvB,OAAOiC,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG1C,EAAE,CAACqN,QAAQ,CAAC,CAAC,IAAI1K,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG3C,EAAE,CAACsN,SAAS,CAAC,CAAC;AAClE;AACA;AACA;AACA;AACA,IAAIzI,cAAc,GAAG;EACnB0I,KAAK,EAAEC,eAAe,CAAC,CAAC,CAAC;EACzBC,KAAK,EAAED,eAAe,CAAC,CAAC,CAAC;EACzBE,IAAI,EAAE;IACJlJ,WAAW,EAAE,SAAbA,WAAWA,CAAYjB,UAAU,EAAErC,WAAW,EAAE;MAC9C,SAASyM,WAAWA,CAACzI,KAAK,EAAE;QAC1B,OAAOA,KAAK;MACd;MACA,OAAO4B,mBAAmB,CAAC;QACzByD,WAAW,EAAEoD,WAAW;QACxB/C,aAAa,EAAE+C;MACjB,CAAC,EAAEpK,UAAU,EAAErC,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3G,CAAC;IACD4L,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYc,UAAU,EAAE;MACtC,IAAIC,IAAI,GAAGjH,YAAY,CAACgH,UAAU,CAAC;MACnC,OAAOnE,eAAe,CAACoE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;IACD5I,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAY1B,UAAU,EAAEW,KAAK,EAAE8D,UAAU,EAAE9G,WAAW,EAAE;MACtE6G,cAAc,CAACxE,UAAU,EAAEW,KAAK,EAAE8D,UAAU,EAAE9G,WAAW,CAAC;IAC5D,CAAC;IACDqE,YAAY,EAAEA,YAAY;IAC1ByG,OAAO,EAAEK;EACX,CAAC;EACDyB,OAAO,EAAE;IACPtJ,WAAW,EAAE,SAAbA,WAAWA,CAAYjB,UAAU,EAAErC,WAAW,EAAE;MAC9C,IAAIgD,KAAK,GAAG,IAAItG,OAAO,CAAC+C,KAAK,CAAC,CAAC;MAC/B;MACA;MACAuD,KAAK,CAAC1B,GAAG,CAAC,IAAI5E,OAAO,CAACmQ,QAAQ,CAAC;QAC7B7G,IAAI,EAAE,MAAM;QACZC,KAAK,EAAEC,SAAS,CAAClG,WAAW,CAAC;QAC7BmG,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;MACH,OAAOnD,KAAK;IACd,CAAC;IACD4I,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYc,UAAU,EAAE;MACtC,OAAOA,UAAU;IACnB,CAAC;IACDrJ,WAAW,EAAE,SAAbA,WAAWA,CAAYhB,UAAU,EAAEW,KAAK,EAAE;MACxCA,KAAK,CAACJ,MAAM,CAACI,KAAK,CAAC4E,OAAO,CAAC,CAAC,CAAC,CAAC;MAC9B;MACA5E,KAAK,CAAC1B,GAAG,CAAC,IAAI5E,OAAO,CAACoQ,OAAO,CAAC;QAC5B9G,IAAI,EAAE,MAAM;QACZI,SAAS,EAAE,IAAI;QACfE,KAAK,EAAErK,KAAK,CAAC0N,YAAY,EAAEtH,UAAU,EAAEW,KAAK,CAAC;QAC7CwD,SAAS,EAAEvK,KAAK,CAAC+I,OAAO,EAAE3C,UAAU,EAAE;UACpC6C,KAAK,EAAE;QACT,CAAC;MACH,CAAC,CAAC,CAAC;IACL,CAAC;IACDnB,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAY1B,UAAU,EAAEW,KAAK,EAAE8D,UAAU,EAAE9G,WAAW,EAAE;MACtEgD,KAAK,CAAC4E,OAAO,CAAC,CAAC,CAAC,CAACO,QAAQ,CAAC;QACxB+B,MAAM,EAAE7B,WAAW,CAAChG,UAAU,EAAEW,KAAK,EAAE8D,UAAU;MACnD,CAAC,CAAC;IACJ,CAAC;IACDzC,YAAY,EAAEA,YAAY;IAC1ByG,OAAO,EAAEK;EACX;AACF,CAAC;AACD,SAASmB,eAAeA,CAACS,OAAO,EAAE;EAChC,OAAO;IACLzJ,WAAW,EAAE,SAAbA,WAAWA,CAAYjB,UAAU,EAAErC,WAAW,EAAE;MAC9C,OAAO4F,mBAAmB,CAAC;QACzByD,WAAW,EAAE,SAAbA,WAAWA,CAAYrF,KAAK,EAAE;UAC5B,IAAIoF,SAAS,GAAG,CAACpF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;UACjC+I,OAAO,IAAI3D,SAAS,CAACF,OAAO,CAAC,CAAC;UAC9B,OAAOE,SAAS;QAClB,CAAC;QACDM,aAAa,EAAE,SAAfA,aAAaA,CAAYN,SAAS,EAAE;UAClC,OAAOA,SAAS,CAAC2D,OAAO,CAAC;QAC3B;MACF,CAAC,EAAE1K,UAAU,EAAErC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC+M,OAAO,CAAC,CAAC;IACxE,CAAC;IACDnB,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAYc,UAAU,EAAE;MACtC,IAAIC,IAAI,GAAGjH,YAAY,CAACgH,UAAU,CAAC;MACnC,IAAI1P,GAAG,GAAGF,OAAO,CAAC6P,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAC,EAAEJ,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC;MACrD,IAAI7P,GAAG,GAAGD,OAAO,CAAC0P,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAC,EAAEJ,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC;MACrD,OAAO,CAAC/P,GAAG,EAAEE,GAAG,CAAC;IACnB,CAAC;IACD6G,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAY1B,UAAU,EAAEW,KAAK,EAAE8D,UAAU,EAAE9G,WAAW,EAAE;MACtE,IAAIgN,WAAW;MACf;MACA,IAAIvI,KAAK,GAAGI,eAAe,CAACxC,UAAU,EAAEW,KAAK,CAAC;MAC9C,IAAIyB,KAAK,KAAK5H,kBAAkB,IAAI4H,KAAK,CAACwI,yBAAyB,EAAE;QACnED,WAAW,GAAGvI,KAAK,CAACwI,yBAAyB,CAACF,OAAO,CAAC;MACxD,CAAC,MAAM;QACL,IAAIjO,EAAE,GAAGuD,UAAU,CAAC9C,GAAG;QACvByN,WAAW,GAAG,CAAC,CAAC,EAAE,CAAClO,EAAE,CAACqN,QAAQ,CAAC,CAAC,EAAErN,EAAE,CAACsN,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGW,OAAO,CAAC,CAAC;MACjE;MACA,IAAI3D,SAAS,GAAG,CAACtC,UAAU,EAAEkG,WAAW,CAAC;MACzCD,OAAO,IAAI3D,SAAS,CAACF,OAAO,CAAC,CAAC;MAC9BrC,cAAc,CAACxE,UAAU,EAAEW,KAAK,EAAEoG,SAAS,EAAEpJ,WAAW,CAAC;IAC3D,CAAC;IACDqE,YAAY,EAAEA,YAAY;IAC1ByG,OAAO,EAAEK;EACX,CAAC;AACH;AACA,eAAevM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}