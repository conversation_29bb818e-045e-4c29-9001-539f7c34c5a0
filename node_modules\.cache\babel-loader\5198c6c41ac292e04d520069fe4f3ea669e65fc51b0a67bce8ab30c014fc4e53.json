{"ast": null, "code": "import \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nexport default {\n  name: 'UserTopology',\n  data: function data() {\n    return {\n      searchQuery: '',\n      treeHeight: 400,\n      expandedKeys: [1],\n      defaultProps: {\n        children: 'children',\n        label: 'label'\n      },\n      treeData: [{\n        id: 1,\n        name: '张三',\n        phone: '13800138000',\n        level: '总经理',\n        performance: 1000000,\n        children: [{\n          id: 2,\n          name: '李四',\n          phone: '13800138001',\n          level: '华南区经理',\n          performance: 500000,\n          children: [{\n            id: 5,\n            name: '王五',\n            phone: '13800138004',\n            level: '广东省代理',\n            performance: 200000,\n            children: [{\n              id: 9,\n              name: '赵六',\n              phone: '13800138008',\n              level: '广州市代理',\n              performance: 100000\n            }\n            // ... 其他子节点\n            ]\n          }\n          // ... 其他子节点\n          ]\n        }\n        // ... 其他子节点\n        ]\n      }]\n    };\n  },\n  mounted: function mounted() {\n    this.setTreeHeight();\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.setTreeHeight);\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 移除监听器\n    window.removeEventListener('resize', this.setTreeHeight);\n  },\n  watch: {\n    searchQuery: function searchQuery(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  methods: {\n    filterNode: function filterNode(value, data) {\n      if (!value) return true;\n      return data.label.toLowerCase().includes(value.toLowerCase());\n    },\n    handleSearch: function handleSearch() {\n      this.$refs.tree.filter(this.searchQuery);\n    },\n    // 设置树的高度\n    setTreeHeight: function setTreeHeight() {\n      // 获取视窗高度\n      var windowHeight = window.innerHeight;\n      // 减去其他元素的高度（头部导航、搜索框等）\n      // 200 = 头部导航(60) + 页面padding(40) + 卡片padding(40) + 搜索区域(60)\n      var otherHeight = 200;\n      // 设置最小高度\n      this.treeHeight = Math.max(400, windowHeight - otherHeight);\n    },\n    handleNodeClick: function handleNodeClick(data) {\n      if (data.children && data.children.length > 0) {\n        var isExpanded = this.expandedKeys.includes(data.id);\n        if (isExpanded) {\n          // 如果已展开，则收起\n          this.expandedKeys = this.expandedKeys.filter(function (key) {\n            return key !== data.id;\n          });\n        } else {\n          // 如果未展开，则展开\n          this.expandedKeys.push(data.id);\n        }\n      }\n    },\n    formatNumber: function formatNumber(num) {\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "searchQuery", "treeHeight", "expandedKeys", "defaultProps", "children", "label", "treeData", "id", "phone", "level", "performance", "mounted", "setTreeHeight", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "watch", "val", "$refs", "tree", "filter", "methods", "filterNode", "value", "toLowerCase", "includes", "handleSearch", "windowHeight", "innerHeight", "otherHeight", "Math", "max", "handleNodeClick", "length", "isExpanded", "key", "push", "formatNumber", "num", "toString", "replace"], "sources": ["src/views/user/topology/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-input\r\n          v-model=\"searchQuery\"\r\n          placeholder=\"搜索用户名\"\r\n          style=\"width: 200px\"\r\n          class=\"filter-item\"\r\n          @input=\"handleSearch\"\r\n        />\r\n      </div>\r\n\r\n      <!-- 树形结构区域 -->\r\n      <el-tree\r\n        ref=\"tree\"\r\n        :data=\"treeData\"\r\n        :props=\"defaultProps\"\r\n        node-key=\"id\"\r\n        :default-expanded-keys=\"expandedKeys\"\r\n        :expand-on-click-node=\"false\"\r\n        :filter-node-method=\"filterNode\"\r\n        class=\"filter-tree\"\r\n        :style=\"{ minHeight: treeHeight + 'px' }\"\r\n      >\r\n        <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n          <span @click=\"handleNodeClick(data)\">\r\n            <span class=\"node-name\">{{ data.name }}</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-phone\">{{ data.phone }}</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-level\">{{ data.level }}</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-performance\">¥{{ formatNumber(data.performance) }}</span>\r\n          </span>\r\n        </span>\r\n      </el-tree>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'UserTopology',\r\n  data() {\r\n    return {\r\n      searchQuery: '',\r\n      treeHeight: 400,\r\n      expandedKeys: [1],\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      treeData: [\r\n        {\r\n          id: 1,\r\n          name: '张三',\r\n          phone: '13800138000',\r\n          level: '总经理',\r\n          performance: 1000000,\r\n          children: [\r\n            {\r\n              id: 2,\r\n              name: '李四',\r\n              phone: '13800138001',\r\n              level: '华南区经理',\r\n              performance: 500000,\r\n              children: [\r\n                {\r\n                  id: 5,\r\n                  name: '王五',\r\n                  phone: '13800138004',\r\n                  level: '广东省代理',\r\n                  performance: 200000,\r\n                  children: [\r\n                    {\r\n                      id: 9,\r\n                      name: '赵六',\r\n                      phone: '13800138008',\r\n                      level: '广州市代理',\r\n                      performance: 100000\r\n                    }\r\n                    // ... 其他子节点\r\n                  ]\r\n                }\r\n                // ... 其他子节点\r\n              ]\r\n            }\r\n            // ... 其他子节点\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.setTreeHeight()\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.setTreeHeight)\r\n  },\r\n  beforeDestroy() {\r\n    // 移除监听器\r\n    window.removeEventListener('resize', this.setTreeHeight)\r\n  },\r\n  watch: {\r\n    searchQuery(val) {\r\n      this.$refs.tree.filter(val)\r\n    }\r\n  },\r\n  methods: {\r\n    filterNode(value, data) {\r\n      if (!value) return true\r\n      return data.label.toLowerCase().includes(value.toLowerCase())\r\n    },\r\n    handleSearch() {\r\n      this.$refs.tree.filter(this.searchQuery)\r\n    },\r\n    // 设置树的高度\r\n    setTreeHeight() {\r\n      // 获取视窗高度\r\n      const windowHeight = window.innerHeight\r\n      // 减去其他元素的高度（头部导航、搜索框等）\r\n      // 200 = 头部导航(60) + 页面padding(40) + 卡片padding(40) + 搜索区域(60)\r\n      const otherHeight = 200\r\n      // 设置最小高度\r\n      this.treeHeight = Math.max(400, windowHeight - otherHeight)\r\n    },\r\n    handleNodeClick(data) {\r\n      if (data.children && data.children.length > 0) {\r\n        const isExpanded = this.expandedKeys.includes(data.id)\r\n        if (isExpanded) {\r\n          // 如果已展开，则收起\r\n          this.expandedKeys = this.expandedKeys.filter(key => key !== data.id)\r\n        } else {\r\n          // 如果未展开，则展开\r\n          this.expandedKeys.push(data.id)\r\n        }\r\n      }\r\n    },\r\n    formatNumber(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  height: 100%;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    .filter-item {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .filter-tree {\r\n    margin-top: 20px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .custom-tree-node {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    font-size: 14px;\r\n    padding-right: 8px;\r\n\r\n    > span {\r\n      cursor: pointer;\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n\r\n      &:hover {\r\n        .node-name {\r\n          color: #409EFF;\r\n        }\r\n      }\r\n\r\n      .node-name {\r\n        font-weight: bold;\r\n        color: #303133;\r\n      }\r\n\r\n      .node-divider {\r\n        color: #DCDFE6;\r\n        font-weight: normal;\r\n      }\r\n\r\n      .node-phone {\r\n        color: #606266;\r\n        font-family: Consolas, monospace;\r\n      }\r\n\r\n      .node-level {\r\n        color: #409EFF;\r\n        background-color: #ecf5ff;\r\n        padding: 2px 6px;\r\n        border-radius: 4px;\r\n        font-size: 12px;\r\n      }\r\n\r\n      .node-performance {\r\n        color: #67C23A;\r\n        font-weight: bold;\r\n        font-family: Consolas, monospace;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 修改 el-card 样式以支持全高度\r\n::v-deep .box-card {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .el-card__body {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;AA2CA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,UAAA;MACAC,YAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,QAAA,GACA;QACAC,EAAA;QACAT,IAAA;QACAU,KAAA;QACAC,KAAA;QACAC,WAAA;QACAN,QAAA,GACA;UACAG,EAAA;UACAT,IAAA;UACAU,KAAA;UACAC,KAAA;UACAC,WAAA;UACAN,QAAA,GACA;YACAG,EAAA;YACAT,IAAA;YACAU,KAAA;YACAC,KAAA;YACAC,WAAA;YACAN,QAAA,GACA;cACAG,EAAA;cACAT,IAAA;cACAU,KAAA;cACAC,KAAA;cACAC,WAAA;YACA;YACA;YAAA;UAEA;UACA;UAAA;QAEA;QACA;QAAA;MAEA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAF,aAAA;EACA;EACAG,aAAA,WAAAA,cAAA;IACA;IACAF,MAAA,CAAAG,mBAAA,gBAAAJ,aAAA;EACA;EACAK,KAAA;IACAjB,WAAA,WAAAA,YAAAkB,GAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAH,GAAA;IACA;EACA;EACAI,OAAA;IACAC,UAAA,WAAAA,WAAAC,KAAA,EAAAzB,IAAA;MACA,KAAAyB,KAAA;MACA,OAAAzB,IAAA,CAAAM,KAAA,CAAAoB,WAAA,GAAAC,QAAA,CAAAF,KAAA,CAAAC,WAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAR,KAAA,CAAAC,IAAA,CAAAC,MAAA,MAAArB,WAAA;IACA;IACA;IACAY,aAAA,WAAAA,cAAA;MACA;MACA,IAAAgB,YAAA,GAAAf,MAAA,CAAAgB,WAAA;MACA;MACA;MACA,IAAAC,WAAA;MACA;MACA,KAAA7B,UAAA,GAAA8B,IAAA,CAAAC,GAAA,MAAAJ,YAAA,GAAAE,WAAA;IACA;IACAG,eAAA,WAAAA,gBAAAlC,IAAA;MACA,IAAAA,IAAA,CAAAK,QAAA,IAAAL,IAAA,CAAAK,QAAA,CAAA8B,MAAA;QACA,IAAAC,UAAA,QAAAjC,YAAA,CAAAwB,QAAA,CAAA3B,IAAA,CAAAQ,EAAA;QACA,IAAA4B,UAAA;UACA;UACA,KAAAjC,YAAA,QAAAA,YAAA,CAAAmB,MAAA,WAAAe,GAAA;YAAA,OAAAA,GAAA,KAAArC,IAAA,CAAAQ,EAAA;UAAA;QACA;UACA;UACA,KAAAL,YAAA,CAAAmC,IAAA,CAAAtC,IAAA,CAAAQ,EAAA;QACA;MACA;IACA;IACA+B,YAAA,WAAAA,aAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,QAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}