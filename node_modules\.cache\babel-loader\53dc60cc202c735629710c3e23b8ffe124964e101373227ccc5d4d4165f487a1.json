{"ast": null, "code": "var _typeof = require(\"E:/\\u65B0\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\n//! moment.js locale configuration\n//! locale : Danish [da]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/mrbase\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var da = moment.defineLocale('da', {\n    months: 'januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n    weekdays: 'søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag'.split('_'),\n    weekdaysShort: 'søn_man_tir_ons_tor_fre_lør'.split('_'),\n    weekdaysMin: 'sø_ma_ti_on_to_fr_lø'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY HH:mm',\n      LLLL: 'dddd [d.] D. MMMM YYYY [kl.] HH:mm'\n    },\n    calendar: {\n      sameDay: '[i dag kl.] LT',\n      nextDay: '[i morgen kl.] LT',\n      nextWeek: 'på dddd [kl.] LT',\n      lastDay: '[i går kl.] LT',\n      lastWeek: '[i] dddd[s kl.] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'om %s',\n      past: '%s siden',\n      s: 'få sekunder',\n      ss: '%d sekunder',\n      m: 'et minut',\n      mm: '%d minutter',\n      h: 'en time',\n      hh: '%d timer',\n      d: 'en dag',\n      dd: '%d dage',\n      M: 'en måned',\n      MM: '%d måneder',\n      y: 'et år',\n      yy: '%d år'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return da;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "da", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["E:/新项目/adminweb/node_modules/moment/locale/da.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Danish [da]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/mrbase\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var da = moment.defineLocale('da', {\n        months: 'januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december'.split(\n            '_'\n        ),\n        monthsShort: 'jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n        weekdays: 'søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag'.split('_'),\n        weekdaysShort: 'søn_man_tir_ons_tor_fre_lør'.split('_'),\n        weekdaysMin: 'sø_ma_ti_on_to_fr_lø'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY HH:mm',\n            LLLL: 'dddd [d.] D. MMMM YYYY [kl.] HH:mm',\n        },\n        calendar: {\n            sameDay: '[i dag kl.] LT',\n            nextDay: '[i morgen kl.] LT',\n            nextWeek: 'på dddd [kl.] LT',\n            lastDay: '[i går kl.] LT',\n            lastWeek: '[i] dddd[s kl.] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'om %s',\n            past: '%s siden',\n            s: 'få sekunder',\n            ss: '%d sekunder',\n            m: 'et minut',\n            mm: '%d minutter',\n            h: 'en time',\n            hh: '%d timer',\n            d: 'en dag',\n            dd: '%d dage',\n            M: 'en måned',\n            MM: '%d måneder',\n            y: 'et år',\n            yy: '%d år',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return da;\n\n})));\n"], "mappings": ";AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,qFAAqF,CAACC,KAAK,CAC/F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,oDAAoD,CAACF,KAAK,CAAC,GAAG,CAAC;IACzEG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,kBAAkB;MAC5BC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOzC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}