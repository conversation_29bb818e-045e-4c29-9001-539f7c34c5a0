{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取充值记录列表\nexport function getRechargeList(params) {\n  return request({\n    url: '/finance/recharge/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 获取充值统计信息\nexport function getRechargeStatistics() {\n  return request({\n    url: '/finance/recharge/statistics',\n    method: 'get'\n  });\n}\n\n// 审核充值记录\nexport function auditRecharge(id, data) {\n  return request({\n    url: \"/finance/recharge/audit/\".concat(id),\n    method: 'post',\n    data: data\n  });\n}\n\n// 添加导出充值记录接口\nexport function exportRechargeRecord(data) {\n  return request({\n    url: '/finance/recharge/export',\n    method: 'post',\n    data: data,\n    responseType: 'blob' // 指定响应类型为blob\n  });\n}", "map": {"version": 3, "names": ["request", "getRechargeList", "params", "url", "method", "getRechargeStatistics", "auditRecharge", "id", "data", "concat", "exportRechargeRecord", "responseType"], "sources": ["E:/最新项目文件/交易所/adminweb/src/api/finance/recharge.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取充值记录列表\r\nexport function getRechargeList(params) {\r\n  return request({\r\n    url: '/finance/recharge/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取充值统计信息\r\nexport function getRechargeStatistics() {\r\n  return request({\r\n    url: '/finance/recharge/statistics',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 审核充值记录\r\nexport function auditRecharge(id, data) {\r\n  return request({\r\n    url: `/finance/recharge/audit/${id}`,\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 添加导出充值记录接口\r\nexport function exportRechargeRecord(data) {\r\n  return request({\r\n    url: '/finance/recharge/export',\r\n    method: 'post',\r\n    data,\r\n    responseType: 'blob'  // 指定响应类型为blob\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAE;EACtC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,qBAAqBA,CAAA,EAAG;EACtC,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,aAAaA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACtC,OAAOR,OAAO,CAAC;IACbG,GAAG,6BAAAM,MAAA,CAA6BF,EAAE,CAAE;IACpCH,MAAM,EAAE,MAAM;IACdI,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,oBAAoBA,CAACF,IAAI,EAAE;EACzC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAJA,IAAI;IACJG,YAAY,EAAE,MAAM,CAAE;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}