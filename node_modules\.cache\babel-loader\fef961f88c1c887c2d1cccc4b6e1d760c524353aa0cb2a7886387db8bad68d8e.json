{"ast": null, "code": "import _objectSpread from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport { getJob<PERSON>ist, addJob, update<PERSON>ob, toggle<PERSON>ob<PERSON>tat<PERSON>, delete<PERSON><PERSON>, execute<PERSON>ob, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getJobInfo } from '@/api/task/job';\nexport default {\n  name: 'TaskList',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        jobName: undefined,\n        status: undefined\n      },\n      loading: false,\n      submitLoading: false,\n      total: 0,\n      tableData: [],\n      // 弹窗相关\n      dialogVisible: false,\n      dialogTitle: '',\n      form: {\n        jobName: '',\n        jobType: '',\n        invokeTarget: '',\n        cronExpression: '',\n        misfirePolicy: 3,\n        concurrent: 0,\n        remark: ''\n      },\n      rules: {\n        jobName: [{\n          required: true,\n          message: '请输入任务名称',\n          trigger: 'blur'\n        }],\n        jobType: [{\n          required: true,\n          message: '请选择任务类型',\n          trigger: 'change'\n        }],\n        invokeTarget: [{\n          required: true,\n          message: '请输入调用目标',\n          trigger: 'blur'\n        }],\n        cronExpression: [{\n          required: true,\n          message: '请输入Cron表达式',\n          trigger: 'blur'\n        }]\n      },\n      // 日志相关\n      logVisible: false,\n      logLoading: false,\n      logData: [],\n      logQuery: {\n        jobId: undefined,\n        page: 1,\n        limit: 10\n      },\n      logTotal: 0,\n      currentJob: {},\n      cronHelperVisible: false,\n      dailyExamples: [{\n        desc: '每天凌晨12点执行',\n        exp: '0 0 0 * * ?'\n      }, {\n        desc: '每天凌晨1点执行',\n        exp: '0 0 1 * * ?'\n      }, {\n        desc: '每天凌晨2点执行',\n        exp: '0 0 2 * * ?'\n      }, {\n        desc: '每天中午12点执行',\n        exp: '0 0 12 * * ?'\n      }, {\n        desc: '每天23点执行',\n        exp: '0 0 23 * * ?'\n      }],\n      intervalExamples: [{\n        desc: '每隔5分钟执行',\n        exp: '0 0/5 * * * ?'\n      }, {\n        desc: '每隔10分钟执行',\n        exp: '0 0/10 * * * ?'\n      }, {\n        desc: '每隔1小时执行',\n        exp: '0 0 0/1 * * ?'\n      }, {\n        desc: '每隔2小时执行',\n        exp: '0 0 0/2 * * ?'\n      }, {\n        desc: '每30分钟执行一次',\n        exp: '0 0/30 * * * ?'\n      }],\n      weeklyExamples: [{\n        desc: '每周一凌晨1点执行',\n        exp: '0 0 1 ? * MON'\n      }, {\n        desc: '每周二凌晨2点执行',\n        exp: '0 0 2 ? * TUE'\n      }, {\n        desc: '每周三凌晨3点执行',\n        exp: '0 0 3 ? * WED'\n      }, {\n        desc: '每周四凌晨4点执行',\n        exp: '0 0 4 ? * THU'\n      }, {\n        desc: '每周五凌晨5点执行',\n        exp: '0 0 5 ? * FRI'\n      }],\n      monthlyExamples: [{\n        desc: '每月1号凌晨1点执行',\n        exp: '0 0 1 1 * ?'\n      }, {\n        desc: '每月15号凌晨2点执行',\n        exp: '0 0 2 15 * ?'\n      }, {\n        desc: '每月最后一天23点执行',\n        exp: '0 0 23 L * ?'\n      }, {\n        desc: '每月第一个周一凌晨3点执行',\n        exp: '0 0 3 ? * 2#1'\n      }]\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    // 获取任务类型标签样式\n    getTaskTypeTag: function getTaskTypeTag(type) {\n      var map = {\n        'SYSTEM': 'primary',\n        'MONITOR': 'success',\n        'BACKUP': 'warning'\n      };\n      return map[type] || 'info';\n    },\n    // 获取任务类型文本\n    getTaskTypeText: function getTaskTypeText(type) {\n      var map = {\n        'SYSTEM': '系统任务',\n        'MONITOR': '监控任务',\n        'BACKUP': '备份任务'\n      };\n      return map[type] || '未知类型';\n    },\n    // 获取执行策略标签样式\n    getMisfirePolicyTag: function getMisfirePolicyTag(policy) {\n      var map = {\n        1: 'success',\n        2: 'warning',\n        3: 'info'\n      };\n      return map[policy] || 'info';\n    },\n    // 获取执行策略文本\n    getMisfirePolicyText: function getMisfirePolicyText(policy) {\n      var map = {\n        1: '立即执行',\n        2: '执行一次',\n        3: '放弃执行'\n      };\n      return map[policy] || '未知策略';\n    },\n    // 获取Cron表达式描述\n    getCronDescription: function getCronDescription(cron) {\n      // TODO: 添加Cron表达式解析逻辑\n      return cron;\n    },\n    // 重置查询\n    handleReset: function handleReset() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        jobName: undefined,\n        status: undefined\n      };\n      this.getList();\n    },\n    // 获取列表数据\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _this.loading = true;\n              _context.next = 4;\n              return getJobList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.tableData = res.data.rows || [];\n                _this.total = res.data.total || 0;\n              } else {\n                _this.$message.error(res.msg || '获取列表失败');\n              }\n              _context.next = 11;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('获取列表失败:', _context.t0);\n            case 11:\n              _context.prev = 11;\n              _this.loading = false;\n              return _context.finish(11);\n            case 14:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 8, 11, 14]]);\n      }))();\n    },\n    // 分页相关\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    // 表单相关\n    handleAdd: function handleAdd() {\n      this.dialogTitle = '新增任务';\n      this.form = {\n        jobName: '',\n        jobType: '',\n        invokeTarget: '',\n        cronExpression: '',\n        misfirePolicy: 3,\n        concurrent: 0,\n        remark: ''\n      };\n      this.dialogVisible = true;\n    },\n    handleEdit: function handleEdit(row) {\n      this.dialogTitle = '修改任务';\n      this.form = _objectSpread({}, row);\n      this.dialogVisible = true;\n    },\n    submitForm: function submitForm() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _this2.$refs.form.validate(/*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(valid) {\n                  var api, res;\n                  return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n                    while (1) switch (_context2.prev = _context2.next) {\n                      case 0:\n                        if (!valid) {\n                          _context2.next = 16;\n                          break;\n                        }\n                        _context2.prev = 1;\n                        _this2.submitLoading = true;\n                        api = _this2.form.id ? updateJob : addJob;\n                        _context2.next = 6;\n                        return api(_this2.form);\n                      case 6:\n                        res = _context2.sent;\n                        if (res.code === 0) {\n                          _this2.$message.success(_this2.form.id ? '修改成功' : '新增成功');\n                          _this2.dialogVisible = false;\n                          _this2.getList();\n                        } else {\n                          _this2.$message.error(res.msg || '操作失败');\n                        }\n                        _context2.next = 13;\n                        break;\n                      case 10:\n                        _context2.prev = 10;\n                        _context2.t0 = _context2[\"catch\"](1);\n                        console.error('保存失败:', _context2.t0);\n                      case 13:\n                        _context2.prev = 13;\n                        _this2.submitLoading = false;\n                        return _context2.finish(13);\n                      case 16:\n                      case \"end\":\n                        return _context2.stop();\n                    }\n                  }, _callee2, null, [[1, 10, 13, 16]]);\n                }));\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }());\n            case 1:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }))();\n    },\n    // 状态操作\n    handleToggleStatus: function handleToggleStatus(row) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var newStatus, res;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.prev = 0;\n              newStatus = row.status === 1 ? 0 : 1;\n              _context4.next = 4;\n              return toggleJobStatus(row.id, newStatus);\n            case 4:\n              res = _context4.sent;\n              if (res.code === 0) {\n                _this3.$message.success(\"\".concat(newStatus === 1 ? '启用' : '暂停', \"\\u6210\\u529F\"));\n                row.status = newStatus;\n              } else {\n                _this3.$message.error(res.msg || '操作失败');\n              }\n              _context4.next = 11;\n              break;\n            case 8:\n              _context4.prev = 8;\n              _context4.t0 = _context4[\"catch\"](0);\n              console.error('修改状态失败:', _context4.t0);\n            case 11:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[0, 8]]);\n      }))();\n    },\n    // 删除操作\n    handleDelete: function handleDelete(row) {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return _this4.$confirm('确认要删除该任务吗？', '警告', {\n                type: 'warning'\n              });\n            case 3:\n              _context5.next = 5;\n              return deleteJob(row.id);\n            case 5:\n              res = _context5.sent;\n              if (res.code === 0) {\n                _this4.$message.success('删除成功');\n                _this4.getList();\n              } else {\n                _this4.$message.error(res.msg || '删除失败');\n              }\n              _context5.next = 12;\n              break;\n            case 9:\n              _context5.prev = 9;\n              _context5.t0 = _context5[\"catch\"](0);\n              if (_context5.t0 !== 'cancel') {\n                console.error('删除失败:', _context5.t0);\n              }\n            case 12:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 9]]);\n      }))();\n    },\n    // 执行任务\n    handleExecute: function handleExecute(row) {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              if (!row.isRunning) {\n                _context6.next = 2;\n                break;\n              }\n              return _context6.abrupt(\"return\");\n            case 2:\n              _context6.prev = 2;\n              // 设置执行中状态\n              _this5.$set(row, 'isRunning', true);\n              _context6.next = 6;\n              return executeJob(row.id);\n            case 6:\n              res = _context6.sent;\n              if (res.code === 0) {\n                _this5.$message.success('执行成功');\n                // 刷新列表以获取最新状态\n                _this5.getList();\n              } else {\n                _this5.$message.error(res.msg || '执行失败');\n              }\n              _context6.next = 14;\n              break;\n            case 10:\n              _context6.prev = 10;\n              _context6.t0 = _context6[\"catch\"](2);\n              console.error('执行失败:', _context6.t0);\n              _this5.$message.error('执行失败');\n            case 14:\n              _context6.prev = 14;\n              // 无论成功失败，都移除执行中状态\n              _this5.$set(row, 'isRunning', false);\n              return _context6.finish(14);\n            case 17:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6, null, [[2, 10, 14, 17]]);\n      }))();\n    },\n    // 查看日志\n    handleLog: function handleLog(row) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _this6.currentJob = row;\n              _this6.logVisible = true;\n              _this6.logQuery.jobId = row.id;\n              _this6.logQuery.page = 1;\n              _context7.next = 6;\n              return _this6.getLogList();\n            case 6:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7);\n      }))();\n    },\n    // 获取日志列表\n    getLogList: function getLogList() {\n      var _this7 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n          while (1) switch (_context8.prev = _context8.next) {\n            case 0:\n              _context8.prev = 0;\n              _this7.logLoading = true;\n              _context8.next = 4;\n              return getJobLogList({\n                jobId: _this7.logQuery.jobId,\n                page: _this7.logQuery.page,\n                limit: _this7.logQuery.limit\n              });\n            case 4:\n              res = _context8.sent;\n              if (res.code === 0) {\n                _this7.logData = res.data.rows || [];\n                _this7.logTotal = res.data.total || 0;\n              } else {\n                _this7.$message.error(res.msg || '获取日志失败');\n              }\n              _context8.next = 12;\n              break;\n            case 8:\n              _context8.prev = 8;\n              _context8.t0 = _context8[\"catch\"](0);\n              console.error('获取日志失败:', _context8.t0);\n              _this7.$message.error('获取日志失败');\n            case 12:\n              _context8.prev = 12;\n              _this7.logLoading = false;\n              return _context8.finish(12);\n            case 15:\n            case \"end\":\n              return _context8.stop();\n          }\n        }, _callee8, null, [[0, 8, 12, 15]]);\n      }))();\n    },\n    // 日志分页\n    handleLogSizeChange: function handleLogSizeChange(val) {\n      this.logQuery.limit = val;\n      this.getLogList();\n    },\n    handleLogCurrentChange: function handleLogCurrentChange(val) {\n      this.logQuery.page = val;\n      this.getLogList();\n    },\n    // 显示Cron表达式帮助\n    showCronHelper: function showCronHelper() {\n      this.cronHelperVisible = true;\n    },\n    // 添加时间格式化方法\n    parseTime: function parseTime(time) {\n      if (!time) return '';\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    }\n  }\n};", "map": {"version": 3, "names": ["getJobList", "addJob", "updateJob", "toggleJobStatus", "deleteJob", "<PERSON><PERSON>ob", "getJobLogList", "getJobInfo", "name", "data", "list<PERSON>uery", "page", "limit", "job<PERSON>ame", "undefined", "status", "loading", "submitLoading", "total", "tableData", "dialogVisible", "dialogTitle", "form", "jobType", "invoke<PERSON><PERSON><PERSON>", "cronExpression", "misfirePolicy", "concurrent", "remark", "rules", "required", "message", "trigger", "logVisible", "logLoading", "logData", "log<PERSON><PERSON>y", "jobId", "logTotal", "<PERSON><PERSON><PERSON>", "cronHelperVisible", "dailyExamples", "desc", "exp", "intervalExamples", "weeklyExamples", "monthlyExamples", "created", "getList", "methods", "getTaskTypeTag", "type", "map", "getTaskTypeText", "getMisfirePolicyTag", "policy", "getMisfirePolicyText", "getCronDescription", "cron", "handleReset", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "rows", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleSizeChange", "val", "handleCurrentChange", "handleAdd", "handleEdit", "row", "_objectSpread", "submitForm", "_this2", "_callee3", "_callee3$", "_context3", "$refs", "validate", "_ref", "_callee2", "valid", "api", "_callee2$", "_context2", "id", "success", "_x", "apply", "arguments", "handleToggleStatus", "_this3", "_callee4", "newStatus", "_callee4$", "_context4", "concat", "handleDelete", "_this4", "_callee5", "_callee5$", "_context5", "$confirm", "handleExecute", "_this5", "_callee6", "_callee6$", "_context6", "isRunning", "abrupt", "$set", "handleLog", "_this6", "_callee7", "_callee7$", "_context7", "getLogList", "_this7", "_callee8", "_callee8$", "_context8", "handleLogSizeChange", "handleLogCurrentChange", "showCronHelper", "parseTime", "time", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["src/views/task/list/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <!-- 搜索和操作区域 -->\n      <div class=\"filter-container\">\n        <el-input\n          v-model=\"listQuery.jobName\"\n          placeholder=\"任务名称\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          @keyup.enter.native=\"getList\"\n          clearable\n        />\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"任务状态\"\n          clearable\n          class=\"filter-item\"\n          style=\"width: 130px\"\n        >\n          <el-option label=\"正常\" :value=\"1\" />\n          <el-option label=\"暂停\" :value=\"0\" />\n        </el-select>\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getList\">搜索</el-button>\n        <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"handleReset\">重置</el-button>\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增任务</el-button>\n      </div>\n\n      <!-- 表格区域 -->\n      <el-table\n        :data=\"tableData\"\n        border\n        style=\"width: 100%\"\n        v-loading=\"loading\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column label=\"序号\" type=\"index\" width=\"80\" align=\"center\" />\n        <el-table-column label=\"任务名称\" prop=\"jobName\" align=\"center\" />\n        <el-table-column label=\"任务类型\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getTaskTypeTag(scope.row.jobType)\">\n              {{ getTaskTypeText(scope.row.jobType) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"执行规则\" prop=\"cronExpression\" align=\"center\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-tooltip :content=\"getCronDescription(scope.row.cronExpression)\" placement=\"top\">\n              <span>{{ scope.row.cronExpression }}</span>\n            </el-tooltip>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"执行策略\" align=\"center\" width=\"120\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getMisfirePolicyTag(scope.row.misfirePolicy)\">\n              {{ getMisfirePolicyText(scope.row.misfirePolicy) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"并发执行\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"scope.row.concurrent === 0 ? 'success' : 'warning'\">\n              {{ scope.row.concurrent === 0 ? '允许' : '禁止' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"scope.row.status === 1 ? 'success' : 'info'\">\n              {{ scope.row.status === 1 ? '正常' : '暂停' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"备注\" prop=\"remark\" align=\"center\" show-overflow-tooltip />\n        <el-table-column label=\"操作\" align=\"center\" width=\"260\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"handleEdit(scope.row)\">修改</el-button>\n            <el-button \n              type=\"text\" \n              @click=\"handleExecute(scope.row)\"\n              v-if=\"scope.row.status === 1\"\n              :disabled=\"scope.row.isRunning\"\n            >\n              {{ scope.row.isRunning ? '执行中' : '执行' }}\n            </el-button>\n            <el-button type=\"text\" @click=\"handleLog(scope.row)\">日志</el-button>\n            <el-button \n              type=\"text\" \n              :style=\"{ color: scope.row.status === 1 ? '#F56C6C' : '#67C23A' }\"\n              @click=\"handleToggleStatus(scope.row)\"\n            >\n              {{ scope.row.status === 1 ? '暂停' : '恢复' }}\n            </el-button>\n            <el-button type=\"text\" style=\"color: #F56C6C\" @click=\"handleDelete(scope.row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页区域 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"listQuery.page\"\n          :page-sizes=\"[10, 20, 30, 50]\"\n          :page-size=\"listQuery.limit\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"total\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 新增/修改对话框 -->\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"任务名称\" prop=\"jobName\">\n          <el-input v-model=\"form.jobName\" placeholder=\"请输入任务名称\" />\n        </el-form-item>\n        <el-form-item label=\"任务类型\" prop=\"jobType\">\n          <el-select v-model=\"form.jobType\" placeholder=\"请选择任务类型\" style=\"width: 100%\">\n            <el-option label=\"系统任务\" value=\"SYSTEM\" />\n            <el-option label=\"监控任务\" value=\"MONITOR\" />\n            <el-option label=\"备份任务\" value=\"BACKUP\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"调用目标\" prop=\"invokeTarget\">\n          <el-input v-model=\"form.invokeTarget\" placeholder=\"请输入调用目标字符串\" />\n        </el-form-item>\n        <el-form-item label=\"执行规则\" prop=\"cronExpression\">\n          <el-input v-model=\"form.cronExpression\" placeholder=\"请输入Cron表达式\">\n            <el-button slot=\"append\" @click=\"showCronHelper\">帮助</el-button>\n          </el-input>\n        </el-form-item>\n        <el-form-item label=\"执行策略\" prop=\"misfirePolicy\">\n          <el-radio-group v-model=\"form.misfirePolicy\">\n            <el-radio :label=\"1\">立即执行</el-radio>\n            <el-radio :label=\"2\">执行一次</el-radio>\n            <el-radio :label=\"3\">放弃执行</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"并发执行\" prop=\"concurrent\">\n          <el-radio-group v-model=\"form.concurrent\">\n            <el-radio :label=\"0\">允许</el-radio>\n            <el-radio :label=\"1\">禁止</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input\n            type=\"textarea\"\n            :rows=\"2\"\n            placeholder=\"请输入备注信息\"\n            v-model=\"form.remark\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 任务日志对话框 -->\n    <el-dialog \n      title=\"执行日志\" \n      :visible.sync=\"logVisible\" \n      width=\"1000px\" \n      append-to-body\n      :close-on-click-modal=\"false\"\n      custom-class=\"log-dialog\"\n    >\n      <div class=\"log-header\">\n        <span class=\"job-name\">任务名称：{{ currentJob.jobName }}</span>\n        <span class=\"job-type\">任务类型：{{ getTaskTypeText(currentJob.jobType) }}</span>\n      </div>\n\n      <el-table\n        :data=\"logData\"\n        border\n        v-loading=\"logLoading\"\n        style=\"width: 100%\"\n        size=\"small\"\n      >\n        <el-table-column \n          label=\"执行时间\" \n          prop=\"executionTime\" \n          align=\"center\" \n          width=\"160\"\n        >\n          <template slot-scope=\"scope\">\n            <span>{{ parseTime(scope.row.executionTime) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"执行耗时\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag size=\"mini\" type=\"info\">\n              {{ scope.row.executionDuration }}ms\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"执行状态\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag size=\"mini\" :type=\"scope.row.executionResult === '成功' ? 'success' : 'danger'\">\n              {{ scope.row.executionResult }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"执行信息\" prop=\"executionMessage\" align=\"left\" min-width=\"200\" show-overflow-tooltip>\n          <template slot-scope=\"scope\">\n            <span :class=\"scope.row.executionResult === '成功' ? 'success-msg' : 'error-msg'\">\n              {{ scope.row.executionMessage }}\n            </span>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <!-- 日志分页 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          @size-change=\"handleLogSizeChange\"\n          @current-change=\"handleLogCurrentChange\"\n          :current-page=\"logQuery.page\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :page-size=\"logQuery.limit\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"logTotal\">\n        </el-pagination>\n      </div>\n    </el-dialog>\n\n    <!-- Cron表达式帮助对话框 -->\n    <el-dialog\n      title=\"Cron表达式帮助\"\n      :visible.sync=\"cronHelperVisible\"\n      width=\"600px\"\n      custom-class=\"cron-helper-dialog\"\n    >\n      <div class=\"cron-helper-content\">\n        <div class=\"cron-section\">\n          <h4>每天执行</h4>\n          <el-card shadow=\"never\" class=\"cron-card\">\n            <div class=\"cron-item\" v-for=\"(item, index) in dailyExamples\" :key=\"'daily'+index\">\n              <div class=\"cron-desc\">{{ item.desc }}</div>\n              <div class=\"cron-exp\">{{ item.exp }}</div>\n            </div>\n          </el-card>\n        </div>\n\n        <div class=\"cron-section\">\n          <h4>固定间隔执行</h4>\n          <el-card shadow=\"never\" class=\"cron-card\">\n            <div class=\"cron-item\" v-for=\"(item, index) in intervalExamples\" :key=\"'interval'+index\">\n              <div class=\"cron-desc\">{{ item.desc }}</div>\n              <div class=\"cron-exp\">{{ item.exp }}</div>\n            </div>\n          </el-card>\n        </div>\n\n        <div class=\"cron-section\">\n          <h4>每周定时执行</h4>\n          <el-card shadow=\"never\" class=\"cron-card\">\n            <div class=\"cron-item\" v-for=\"(item, index) in weeklyExamples\" :key=\"'weekly'+index\">\n              <div class=\"cron-desc\">{{ item.desc }}</div>\n              <div class=\"cron-exp\">{{ item.exp }}</div>\n            </div>\n          </el-card>\n        </div>\n\n        <div class=\"cron-section\">\n          <h4>每月定时执行</h4>\n          <el-card shadow=\"never\" class=\"cron-card\">\n            <div class=\"cron-item\" v-for=\"(item, index) in monthlyExamples\" :key=\"'monthly'+index\">\n              <div class=\"cron-desc\">{{ item.desc }}</div>\n              <div class=\"cron-exp\">{{ item.exp }}</div>\n            </div>\n          </el-card>\n        </div>\n\n        <div class=\"cron-section\">\n          <h4>格式说明</h4>\n          <el-card shadow=\"never\" class=\"cron-card\">\n            <div class=\"format-item\">\n              <div>格式：[秒] [分] [时] [日] [月] [周] [年]</div>\n              <div>* 第一位: 秒(0-59)</div>\n              <div>* 第二位: 分(0-59)</div>\n              <div>* 第三位: 时(0-23)</div>\n              <div>* 第四位: 日(1-31)</div>\n              <div>* 第五位: 月(1-12)</div>\n              <div>* 第六位: 星期(1-7,1代表星期日)</div>\n              <div>* 第七位: 年(可选)</div>\n            </div>\n          </el-card>\n        </div>\n\n        <div class=\"cron-section\">\n          <h4>特殊字符说明</h4>\n          <el-card shadow=\"never\" class=\"cron-card\">\n            <div class=\"special-item\">\n              <div>* : 表示所有值</div>\n              <div>? : 表示不指定值</div>\n              <div>- : 表示区间</div>\n              <div>, : 表示多个值</div>\n              <div>/ : 表示递增</div>\n              <div>L : 表示最后</div>\n              <div>W : 表示工作日</div>\n              <div># : 表示第几个星期几</div>\n            </div>\n          </el-card>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getJobList, addJob, updateJob, toggleJobStatus, deleteJob, executeJob, getJobLogList, getJobInfo } from '@/api/task/job'\n\nexport default {\n  name: 'TaskList',\n  data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        jobName: undefined,\n        status: undefined\n      },\n      loading: false,\n      submitLoading: false,\n      total: 0,\n      tableData: [],\n      // 弹窗相关\n      dialogVisible: false,\n      dialogTitle: '',\n      form: {\n        jobName: '',\n        jobType: '',\n        invokeTarget: '',\n        cronExpression: '',\n        misfirePolicy: 3,\n        concurrent: 0,\n        remark: ''\n      },\n      rules: {\n        jobName: [\n          { required: true, message: '请输入任务名称', trigger: 'blur' }\n        ],\n        jobType: [\n          { required: true, message: '请选择任务类型', trigger: 'change' }\n        ],\n        invokeTarget: [\n          { required: true, message: '请输入调用目标', trigger: 'blur' }\n        ],\n        cronExpression: [\n          { required: true, message: '请输入Cron表达式', trigger: 'blur' }\n        ]\n      },\n      // 日志相关\n      logVisible: false,\n      logLoading: false,\n      logData: [],\n      logQuery: {\n        jobId: undefined,\n        page: 1,\n        limit: 10\n      },\n      logTotal: 0,\n      currentJob: {},\n      cronHelperVisible: false,\n      dailyExamples: [\n        { desc: '每天凌晨12点执行', exp: '0 0 0 * * ?' },\n        { desc: '每天凌晨1点执行', exp: '0 0 1 * * ?' },\n        { desc: '每天凌晨2点执行', exp: '0 0 2 * * ?' },\n        { desc: '每天中午12点执行', exp: '0 0 12 * * ?' },\n        { desc: '每天23点执行', exp: '0 0 23 * * ?' }\n      ],\n      intervalExamples: [\n        { desc: '每隔5分钟执行', exp: '0 0/5 * * * ?' },\n        { desc: '每隔10分钟执行', exp: '0 0/10 * * * ?' },\n        { desc: '每隔1小时执行', exp: '0 0 0/1 * * ?' },\n        { desc: '每隔2小时执行', exp: '0 0 0/2 * * ?' },\n        { desc: '每30分钟执行一次', exp: '0 0/30 * * * ?' }\n      ],\n      weeklyExamples: [\n        { desc: '每周一凌晨1点执行', exp: '0 0 1 ? * MON' },\n        { desc: '每周二凌晨2点执行', exp: '0 0 2 ? * TUE' },\n        { desc: '每周三凌晨3点执行', exp: '0 0 3 ? * WED' },\n        { desc: '每周四凌晨4点执行', exp: '0 0 4 ? * THU' },\n        { desc: '每周五凌晨5点执行', exp: '0 0 5 ? * FRI' }\n      ],\n      monthlyExamples: [\n        { desc: '每月1号凌晨1点执行', exp: '0 0 1 1 * ?' },\n        { desc: '每月15号凌晨2点执行', exp: '0 0 2 15 * ?' },\n        { desc: '每月最后一天23点执行', exp: '0 0 23 L * ?' },\n        { desc: '每月第一个周一凌晨3点执行', exp: '0 0 3 ? * 2#1' }\n      ]\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    // 获取任务类型标签样式\n    getTaskTypeTag(type) {\n      const map = {\n        'SYSTEM': 'primary',\n        'MONITOR': 'success',\n        'BACKUP': 'warning'\n      }\n      return map[type] || 'info'\n    },\n    // 获取任务类型文本\n    getTaskTypeText(type) {\n      const map = {\n        'SYSTEM': '系统任务',\n        'MONITOR': '监控任务',\n        'BACKUP': '备份任务'\n      }\n      return map[type] || '未知类型'\n    },\n    // 获取执行策略标签样式\n    getMisfirePolicyTag(policy) {\n      const map = {\n        1: 'success',\n        2: 'warning',\n        3: 'info'\n      }\n      return map[policy] || 'info'\n    },\n    // 获取执行策略文本\n    getMisfirePolicyText(policy) {\n      const map = {\n        1: '立即执行',\n        2: '执行一次',\n        3: '放弃执行'\n      }\n      return map[policy] || '未知策略'\n    },\n    // 获取Cron表达式描述\n    getCronDescription(cron) {\n      // TODO: 添加Cron表达式解析逻辑\n      return cron\n    },\n    // 重置查询\n    handleReset() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        jobName: undefined,\n        status: undefined\n      }\n      this.getList()\n    },\n    // 获取列表数据\n    async getList() {\n      try {\n        this.loading = true\n        const res = await getJobList(this.listQuery)\n        if (res.code === 0) {\n          this.tableData = res.data.rows || []\n          this.total = res.data.total || 0\n        } else {\n          this.$message.error(res.msg || '获取列表失败')\n        }\n      } catch (error) {\n        console.error('获取列表失败:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n    // 分页相关\n    handleSizeChange(val) {\n      this.listQuery.limit = val\n      this.getList()\n    },\n    handleCurrentChange(val) {\n      this.listQuery.page = val\n      this.getList()\n    },\n    // 表单相关\n    handleAdd() {\n      this.dialogTitle = '新增任务'\n      this.form = {\n        jobName: '',\n        jobType: '',\n        invokeTarget: '',\n        cronExpression: '',\n        misfirePolicy: 3,\n        concurrent: 0,\n        remark: ''\n      }\n      this.dialogVisible = true\n    },\n    handleEdit(row) {\n      this.dialogTitle = '修改任务'\n      this.form = { ...row }\n      this.dialogVisible = true\n    },\n    async submitForm() {\n      this.$refs.form.validate(async valid => {\n        if (valid) {\n          try {\n            this.submitLoading = true\n            const api = this.form.id ? updateJob : addJob\n            const res = await api(this.form)\n            if (res.code === 0) {\n              this.$message.success(this.form.id ? '修改成功' : '新增成功')\n              this.dialogVisible = false\n              this.getList()\n            } else {\n              this.$message.error(res.msg || '操作失败')\n            }\n          } catch (error) {\n            console.error('保存失败:', error)\n          } finally {\n            this.submitLoading = false\n          }\n        }\n      })\n    },\n    // 状态操作\n    async handleToggleStatus(row) {\n      try {\n        const newStatus = row.status === 1 ? 0 : 1\n        const res = await toggleJobStatus(row.id, newStatus)\n        if (res.code === 0) {\n          this.$message.success(`${newStatus === 1 ? '启用' : '暂停'}成功`)\n          row.status = newStatus\n        } else {\n          this.$message.error(res.msg || '操作失败')\n        }\n      } catch (error) {\n        console.error('修改状态失败:', error)\n      }\n    },\n    // 删除操作\n    async handleDelete(row) {\n      try {\n        await this.$confirm('确认要删除该任务吗？', '警告', {\n          type: 'warning'\n        })\n        const res = await deleteJob(row.id)\n        if (res.code === 0) {\n          this.$message.success('删除成功')\n          this.getList()\n        } else {\n          this.$message.error(res.msg || '删除失败')\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n        }\n      }\n    },\n    // 执行任务\n    async handleExecute(row) {\n      if (row.isRunning) {\n        return // 如果任务正在执行中，直接返回\n      }\n      \n      try {\n        // 设置执行中状态\n        this.$set(row, 'isRunning', true)\n        \n        const res = await executeJob(row.id)\n        if (res.code === 0) {\n          this.$message.success('执行成功')\n          // 刷新列表以获取最新状态\n          this.getList()\n        } else {\n          this.$message.error(res.msg || '执行失败')\n        }\n      } catch (error) {\n        console.error('执行失败:', error)\n        this.$message.error('执行失败')\n      } finally {\n        // 无论成功失败，都移除执行中状态\n        this.$set(row, 'isRunning', false)\n      }\n    },\n    // 查看日志\n    async handleLog(row) {\n      this.currentJob = row;\n      this.logVisible = true;\n      this.logQuery.jobId = row.id;\n      this.logQuery.page = 1;\n      await this.getLogList();\n    },\n    // 获取日志列表\n    async getLogList() {\n      try {\n        this.logLoading = true;\n        const res = await getJobLogList({\n          jobId: this.logQuery.jobId,\n          page: this.logQuery.page,\n          limit: this.logQuery.limit\n        });\n        \n        if (res.code === 0) {\n          this.logData = res.data.rows || [];\n          this.logTotal = res.data.total || 0;\n        } else {\n          this.$message.error(res.msg || '获取日志失败');\n        }\n      } catch (error) {\n        console.error('获取日志失败:', error);\n        this.$message.error('获取日志失败');\n      } finally {\n        this.logLoading = false;\n      }\n    },\n    // 日志分页\n    handleLogSizeChange(val) {\n      this.logQuery.limit = val\n      this.getLogList()\n    },\n    handleLogCurrentChange(val) {\n      this.logQuery.page = val\n      this.getLogList()\n    },\n    // 显示Cron表达式帮助\n    showCronHelper() {\n      this.cronHelperVisible = true\n    },\n    // 添加时间格式化方法\n    parseTime(time) {\n      if (!time) return '';\n      const date = new Date(time);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      const seconds = String(date.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  padding: 20px;\n\n  .filter-container {\n    padding-bottom: 20px;\n    .filter-item {\n      margin-right: 10px;\n    }\n  }\n\n  .pagination-container {\n    padding: 20px 0;\n    text-align: right;\n  }\n}\n\n.log-dialog {\n  .log-header {\n    margin-bottom: 15px;\n    padding: 10px;\n    background-color: #f5f7fa;\n    border-radius: 4px;\n\n    .job-name {\n      font-weight: bold;\n      margin-right: 20px;\n    }\n\n    .job-type {\n      color: #606266;\n    }\n  }\n\n  .success-msg {\n    color: #67C23A;\n  }\n\n  .error-msg {\n    color: #F56C6C;\n  }\n\n  .el-table {\n    margin: 10px 0;\n  }\n\n  ::v-deep .el-dialog__body {\n    padding: 10px 20px;\n  }\n\n  .pagination-container {\n    padding: 10px 0;\n    text-align: right;\n  }\n\n  .execution-time {\n    font-family: monospace;\n    white-space: nowrap;\n  }\n}\n\n.cron-helper-dialog {\n  .cron-helper-content {\n    max-height: 500px;\n    overflow-y: auto;\n    padding: 0 10px;\n  }\n  \n  .cron-section {\n    margin-bottom: 20px;\n    \n    h4 {\n      margin: 10px 0;\n      color: #409EFF;\n      font-weight: bold;\n    }\n  }\n  \n  .cron-card {\n    margin-bottom: 10px;\n  }\n  \n  .cron-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 0;\n    border-bottom: 1px solid #EBEEF5;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .cron-desc {\n      color: #606266;\n    }\n    \n    .cron-exp {\n      color: #409EFF;\n      font-family: monospace;\n      background-color: #f5f7fa;\n      padding: 2px 6px;\n      border-radius: 3px;\n      cursor: pointer;\n      \n      &:hover {\n        background-color: #ecf5ff;\n      }\n    }\n  }\n  \n  .format-item, .special-item {\n    div {\n      line-height: 1.8;\n      color: #606266;\n    }\n  }\n}\n\n::v-deep .el-dialog__body {\n  padding: 20px;\n}\n</style> "], "mappings": ";;;;;;AA4TA,SAAAA,UAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,aAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACAE,OAAA;MACAC,aAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,aAAA;MACAC,WAAA;MACAC,IAAA;QACAT,OAAA;QACAU,OAAA;QACAC,YAAA;QACAC,cAAA;QACAC,aAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACAC,KAAA;QACAhB,OAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,OAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,YAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,cAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,UAAA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;QACAC,KAAA,EAAAvB,SAAA;QACAH,IAAA;QACAC,KAAA;MACA;MACA0B,QAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,aAAA,GACA;QAAAC,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,EACA;MACAC,gBAAA,GACA;QAAAF,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,EACA;MACAE,cAAA,GACA;QAAAH,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,EACA;MACAG,eAAA,GACA;QAAAJ,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,GAAA;MAAA;IAEA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,IAAA;MACA,IAAAC,GAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAAD,IAAA;IACA;IACA;IACAE,eAAA,WAAAA,gBAAAF,IAAA;MACA,IAAAC,GAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAAD,IAAA;IACA;IACA;IACAG,mBAAA,WAAAA,oBAAAC,MAAA;MACA,IAAAH,GAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAAG,MAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAAD,MAAA;MACA,IAAAH,GAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAAG,MAAA;IACA;IACA;IACAE,kBAAA,WAAAA,mBAAAC,IAAA;MACA;MACA,OAAAA,IAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAjD,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA,KAAAkC,OAAA;IACA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAAY,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAA5C,OAAA;cAAAoD,QAAA,CAAAE,IAAA;cAAA,OACAtE,UAAA,CAAA4D,KAAA,CAAAlD,SAAA;YAAA;cAAAuD,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAZ,KAAA,CAAAzC,SAAA,GAAA8C,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;gBACAb,KAAA,CAAA1C,KAAA,GAAA+C,GAAA,CAAAxD,IAAA,CAAAS,KAAA;cACA;gBACA0C,KAAA,CAAAc,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,YAAAP,QAAA,CAAAS,EAAA;YAAA;cAAAT,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAA5C,OAAA;cAAA,OAAAoD,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IAEA;IACA;IACAiB,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAxE,SAAA,CAAAE,KAAA,GAAAsE,GAAA;MACA,KAAAlC,OAAA;IACA;IACAmC,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAxE,SAAA,CAAAC,IAAA,GAAAuE,GAAA;MACA,KAAAlC,OAAA;IACA;IACA;IACAoC,SAAA,WAAAA,UAAA;MACA,KAAA/D,WAAA;MACA,KAAAC,IAAA;QACAT,OAAA;QACAU,OAAA;QACAC,YAAA;QACAC,cAAA;QACAC,aAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA,KAAAR,aAAA;IACA;IACAiE,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAjE,WAAA;MACA,KAAAC,IAAA,GAAAiE,aAAA,KAAAD,GAAA;MACA,KAAAlE,aAAA;IACA;IACAoE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,OAAA5B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA2B,SAAA;QAAA,OAAA5B,mBAAA,GAAAI,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cACAmB,MAAA,CAAAI,KAAA,CAAAvE,IAAA,CAAAwE,QAAA;gBAAA,IAAAC,IAAA,GAAAlC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiC,SAAAC,KAAA;kBAAA,IAAAC,GAAA,EAAAjC,GAAA;kBAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiC,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA/B,IAAA,GAAA+B,SAAA,CAAA9B,IAAA;sBAAA;wBAAA,KACA2B,KAAA;0BAAAG,SAAA,CAAA9B,IAAA;0BAAA;wBAAA;wBAAA8B,SAAA,CAAA/B,IAAA;wBAEAoB,MAAA,CAAAxE,aAAA;wBACAiF,GAAA,GAAAT,MAAA,CAAAnE,IAAA,CAAA+E,EAAA,GAAAnG,SAAA,GAAAD,MAAA;wBAAAmG,SAAA,CAAA9B,IAAA;wBAAA,OACA4B,GAAA,CAAAT,MAAA,CAAAnE,IAAA;sBAAA;wBAAA2C,GAAA,GAAAmC,SAAA,CAAA7B,IAAA;wBACA,IAAAN,GAAA,CAAAO,IAAA;0BACAiB,MAAA,CAAAf,QAAA,CAAA4B,OAAA,CAAAb,MAAA,CAAAnE,IAAA,CAAA+E,EAAA;0BACAZ,MAAA,CAAArE,aAAA;0BACAqE,MAAA,CAAAzC,OAAA;wBACA;0BACAyC,MAAA,CAAAf,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;wBACA;wBAAAwB,SAAA,CAAA9B,IAAA;wBAAA;sBAAA;wBAAA8B,SAAA,CAAA/B,IAAA;wBAAA+B,SAAA,CAAAvB,EAAA,GAAAuB,SAAA;wBAEAtB,OAAA,CAAAH,KAAA,UAAAyB,SAAA,CAAAvB,EAAA;sBAAA;wBAAAuB,SAAA,CAAA/B,IAAA;wBAEAoB,MAAA,CAAAxE,aAAA;wBAAA,OAAAmF,SAAA,CAAArB,MAAA;sBAAA;sBAAA;wBAAA,OAAAqB,SAAA,CAAApB,IAAA;oBAAA;kBAAA,GAAAgB,QAAA;gBAAA,CAGA;gBAAA,iBAAAO,EAAA;kBAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAAb,SAAA,CAAAZ,IAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IACA;IACA;IACAgB,kBAAA,WAAAA,mBAAApB,GAAA;MAAA,IAAAqB,MAAA;MAAA,OAAA9C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6C,SAAA;QAAA,IAAAC,SAAA,EAAA5C,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA4C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1C,IAAA,GAAA0C,SAAA,CAAAzC,IAAA;YAAA;cAAAyC,SAAA,CAAA1C,IAAA;cAEAwC,SAAA,GAAAvB,GAAA,CAAAvE,MAAA;cAAAgG,SAAA,CAAAzC,IAAA;cAAA,OACAnE,eAAA,CAAAmF,GAAA,CAAAe,EAAA,EAAAQ,SAAA;YAAA;cAAA5C,GAAA,GAAA8C,SAAA,CAAAxC,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAmC,MAAA,CAAAjC,QAAA,CAAA4B,OAAA,IAAAU,MAAA,CAAAH,SAAA;gBACAvB,GAAA,CAAAvE,MAAA,GAAA8F,SAAA;cACA;gBACAF,MAAA,CAAAjC,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAmC,SAAA,CAAAzC,IAAA;cAAA;YAAA;cAAAyC,SAAA,CAAA1C,IAAA;cAAA0C,SAAA,CAAAlC,EAAA,GAAAkC,SAAA;cAEAjC,OAAA,CAAAH,KAAA,YAAAoC,SAAA,CAAAlC,EAAA;YAAA;YAAA;cAAA,OAAAkC,SAAA,CAAA/B,IAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IAEA;IACA;IACAK,YAAA,WAAAA,aAAA3B,GAAA;MAAA,IAAA4B,MAAA;MAAA,OAAArD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAoD,SAAA;QAAA,IAAAlD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAkD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhD,IAAA,GAAAgD,SAAA,CAAA/C,IAAA;YAAA;cAAA+C,SAAA,CAAAhD,IAAA;cAAAgD,SAAA,CAAA/C,IAAA;cAAA,OAEA4C,MAAA,CAAAI,QAAA;gBACAnE,IAAA;cACA;YAAA;cAAAkE,SAAA,CAAA/C,IAAA;cAAA,OACAlE,SAAA,CAAAkF,GAAA,CAAAe,EAAA;YAAA;cAAApC,GAAA,GAAAoD,SAAA,CAAA9C,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACA0C,MAAA,CAAAxC,QAAA,CAAA4B,OAAA;gBACAY,MAAA,CAAAlE,OAAA;cACA;gBACAkE,MAAA,CAAAxC,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAyC,SAAA,CAAA/C,IAAA;cAAA;YAAA;cAAA+C,SAAA,CAAAhD,IAAA;cAAAgD,SAAA,CAAAxC,EAAA,GAAAwC,SAAA;cAEA,IAAAA,SAAA,CAAAxC,EAAA;gBACAC,OAAA,CAAAH,KAAA,UAAA0C,SAAA,CAAAxC,EAAA;cACA;YAAA;YAAA;cAAA,OAAAwC,SAAA,CAAArC,IAAA;UAAA;QAAA,GAAAmC,QAAA;MAAA;IAEA;IACA;IACAI,aAAA,WAAAA,cAAAjC,GAAA;MAAA,IAAAkC,MAAA;MAAA,OAAA3D,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0D,SAAA;QAAA,IAAAxD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAwD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtD,IAAA,GAAAsD,SAAA,CAAArD,IAAA;YAAA;cAAA,KACAgB,GAAA,CAAAsC,SAAA;gBAAAD,SAAA,CAAArD,IAAA;gBAAA;cAAA;cAAA,OAAAqD,SAAA,CAAAE,MAAA;YAAA;cAAAF,SAAA,CAAAtD,IAAA;cAKA;cACAmD,MAAA,CAAAM,IAAA,CAAAxC,GAAA;cAAAqC,SAAA,CAAArD,IAAA;cAAA,OAEAjE,UAAA,CAAAiF,GAAA,CAAAe,EAAA;YAAA;cAAApC,GAAA,GAAA0D,SAAA,CAAApD,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAgD,MAAA,CAAA9C,QAAA,CAAA4B,OAAA;gBACA;gBACAkB,MAAA,CAAAxE,OAAA;cACA;gBACAwE,MAAA,CAAA9C,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA+C,SAAA,CAAArD,IAAA;cAAA;YAAA;cAAAqD,SAAA,CAAAtD,IAAA;cAAAsD,SAAA,CAAA9C,EAAA,GAAA8C,SAAA;cAEA7C,OAAA,CAAAH,KAAA,UAAAgD,SAAA,CAAA9C,EAAA;cACA2C,MAAA,CAAA9C,QAAA,CAAAC,KAAA;YAAA;cAAAgD,SAAA,CAAAtD,IAAA;cAEA;cACAmD,MAAA,CAAAM,IAAA,CAAAxC,GAAA;cAAA,OAAAqC,SAAA,CAAA5C,MAAA;YAAA;YAAA;cAAA,OAAA4C,SAAA,CAAA3C,IAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IAEA;IACA;IACAM,SAAA,WAAAA,UAAAzC,GAAA;MAAA,IAAA0C,MAAA;MAAA,OAAAnE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkE,SAAA;QAAA,OAAAnE,mBAAA,GAAAI,IAAA,UAAAgE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9D,IAAA,GAAA8D,SAAA,CAAA7D,IAAA;YAAA;cACA0D,MAAA,CAAAzF,UAAA,GAAA+C,GAAA;cACA0C,MAAA,CAAA/F,UAAA;cACA+F,MAAA,CAAA5F,QAAA,CAAAC,KAAA,GAAAiD,GAAA,CAAAe,EAAA;cACA2B,MAAA,CAAA5F,QAAA,CAAAzB,IAAA;cAAAwH,SAAA,CAAA7D,IAAA;cAAA,OACA0D,MAAA,CAAAI,UAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAAiD,QAAA;MAAA;IACA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,OAAAxE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAuE,SAAA;QAAA,IAAArE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAqE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnE,IAAA,GAAAmE,SAAA,CAAAlE,IAAA;YAAA;cAAAkE,SAAA,CAAAnE,IAAA;cAEAgE,MAAA,CAAAnG,UAAA;cAAAsG,SAAA,CAAAlE,IAAA;cAAA,OACAhE,aAAA;gBACA+B,KAAA,EAAAgG,MAAA,CAAAjG,QAAA,CAAAC,KAAA;gBACA1B,IAAA,EAAA0H,MAAA,CAAAjG,QAAA,CAAAzB,IAAA;gBACAC,KAAA,EAAAyH,MAAA,CAAAjG,QAAA,CAAAxB;cACA;YAAA;cAJAqD,GAAA,GAAAuE,SAAA,CAAAjE,IAAA;cAMA,IAAAN,GAAA,CAAAO,IAAA;gBACA6D,MAAA,CAAAlG,OAAA,GAAA8B,GAAA,CAAAxD,IAAA,CAAAgE,IAAA;gBACA4D,MAAA,CAAA/F,QAAA,GAAA2B,GAAA,CAAAxD,IAAA,CAAAS,KAAA;cACA;gBACAmH,MAAA,CAAA3D,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA4D,SAAA,CAAAlE,IAAA;cAAA;YAAA;cAAAkE,SAAA,CAAAnE,IAAA;cAAAmE,SAAA,CAAA3D,EAAA,GAAA2D,SAAA;cAEA1D,OAAA,CAAAH,KAAA,YAAA6D,SAAA,CAAA3D,EAAA;cACAwD,MAAA,CAAA3D,QAAA,CAAAC,KAAA;YAAA;cAAA6D,SAAA,CAAAnE,IAAA;cAEAgE,MAAA,CAAAnG,UAAA;cAAA,OAAAsG,SAAA,CAAAzD,MAAA;YAAA;YAAA;cAAA,OAAAyD,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA;IAEA;IACA;IACAG,mBAAA,WAAAA,oBAAAvD,GAAA;MACA,KAAA9C,QAAA,CAAAxB,KAAA,GAAAsE,GAAA;MACA,KAAAkD,UAAA;IACA;IACAM,sBAAA,WAAAA,uBAAAxD,GAAA;MACA,KAAA9C,QAAA,CAAAzB,IAAA,GAAAuE,GAAA;MACA,KAAAkD,UAAA;IACA;IACA;IACAO,cAAA,WAAAA,eAAA;MACA,KAAAnG,iBAAA;IACA;IACA;IACAoG,SAAA,WAAAA,UAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAArC,MAAA,CAAAgC,IAAA,OAAAhC,MAAA,CAAAkC,KAAA,OAAAlC,MAAA,CAAAsC,GAAA,OAAAtC,MAAA,CAAAwC,KAAA,OAAAxC,MAAA,CAAA0C,OAAA,OAAA1C,MAAA,CAAA4C,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}