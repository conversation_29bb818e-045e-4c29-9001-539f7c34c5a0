{"ast": null, "code": "import _objectSpread from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _regeneratorRuntime from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.number.to-fixed.js\";\nimport \"core-js/modules/es.parse-float.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport { getPointsList } from '@/api/finance/points';\nexport default {\n  name: 'FlowPoints',\n  data: function data() {\n    return {\n      loading: false,\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        deviceNo: '',\n        startDate: '',\n        endDate: ''\n      },\n      dateRange: [],\n      total: 0,\n      tableData: [],\n      detailDialogVisible: false,\n      currentDetail: {}\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    formatNumber: function formatNumber(num) {\n      if (!num && num !== 0) return '0.00';\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    },\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              console.log('请求参数:', _this.listQuery);\n              _context.next = 5;\n              return getPointsList(_this.listQuery);\n            case 5:\n              res = _context.sent;\n              console.log('响应数据:', res);\n              if (res.code === 0) {\n                _this.tableData = res.data || [];\n                _this.total = res.total || 0;\n                if (_this.tableData.length === 0) {\n                  _this.$message.info('暂无数据');\n                }\n              } else {\n                _this.$message.error(res.msg || '获取数据失败');\n              }\n              _context.next = 14;\n              break;\n            case 10:\n              _context.prev = 10;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取数据失败:', _context.t0);\n              _this.$message.error('获取数据失败');\n            case 14:\n              _context.prev = 14;\n              _this.loading = false;\n              return _context.finish(14);\n            case 17:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 10, 14, 17]]);\n      }))();\n    },\n    handleSearch: function handleSearch() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    handleReset: function handleReset() {\n      this.dateRange = [];\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        deviceNo: '',\n        startDate: '',\n        endDate: ''\n      };\n      this.getList();\n    },\n    handleViewDetail: function handleViewDetail(row) {\n      this.currentDetail = _objectSpread({}, row);\n      this.detailDialogVisible = true;\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    handleDateRangeChange: function handleDateRangeChange(val) {\n      if (val) {\n        this.listQuery.startDate = val[0].substring(0, 10) + ' 00:00:00';\n        this.listQuery.endDate = val[1].substring(0, 10) + ' 23:59:59';\n      } else {\n        this.listQuery.startDate = '';\n        this.listQuery.endDate = '';\n      }\n      this.handleSearch();\n    },\n    formatDateTime: function formatDateTime(datetime) {\n      if (!datetime) return '';\n      var date = new Date(datetime);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    }\n  }\n};", "map": {"version": 3, "names": ["getPointsList", "name", "data", "loading", "list<PERSON>uery", "page", "limit", "username", "deviceNo", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "total", "tableData", "detailDialogVisible", "currentDetail", "created", "getList", "methods", "formatNumber", "num", "parseFloat", "toFixed", "replace", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "console", "log", "sent", "code", "length", "$message", "info", "error", "msg", "t0", "finish", "stop", "handleSearch", "handleReset", "handleViewDetail", "row", "_objectSpread", "handleSizeChange", "val", "handleCurrentChange", "handleDateRangeChange", "substring", "formatDateTime", "datetime", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat"], "sources": ["src/views/finance/flow-points/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"10\" type=\"flex\" align=\"middle\">\r\n          <el-col :span=\"4\">\r\n            <el-input\r\n              v-model=\"listQuery.username\"\r\n              placeholder=\"手机号码\"\r\n              clearable\r\n              @clear=\"handleSearch\"\r\n              @keyup.enter.native=\"handleSearch\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-input\r\n              v-model=\"listQuery.deviceNo\"\r\n              placeholder=\"设备编号\"\r\n              clearable\r\n              @clear=\"handleSearch\"\r\n              @keyup.enter.native=\"handleSearch\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              :default-time=\"['00:00:00', '23:59:59']\"\r\n              :clearable=\"true\"\r\n              style=\"width: 100%\"\r\n              @change=\"handleDateRangeChange\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\" style=\"text-align: right\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"handleReset\">重置</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"index\" label=\"序号\" align=\"center\" width=\"60\" />\r\n        <el-table-column label=\"手机号码\" prop=\"username\" align=\"center\" min-width=\"120\" />\r\n        <el-table-column label=\"设备编号\" prop=\"deviceNo\" align=\"center\" min-width=\"120\" />\r\n        <el-table-column label=\"扣除金额\" align=\"center\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #F56C6C\">-¥{{ formatNumber(scope.row.deductAmount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"消费时间\" prop=\"createTime\" align=\"center\" min-width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.createTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"handleViewDetail(scope.row)\"\r\n            >查看明细</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n\r\n      <!-- 明细弹窗 -->\r\n      <el-dialog title=\"消费明细\" :visible.sync=\"detailDialogVisible\" width=\"500px\">\r\n        <div class=\"detail-container\">\r\n          <el-descriptions :column=\"1\" border>\r\n            <el-descriptions-item label=\"手机号码\">{{ currentDetail.username }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"设备号\">{{ currentDetail.deviceNo }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"扣除金额\">-¥{{ formatNumber(currentDetail.deductAmount) }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"消费时间\">{{ currentDetail.createTime }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getPointsList } from '@/api/finance/points'\r\n\r\nexport default {\r\n  name: 'FlowPoints',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        deviceNo: '',\r\n        startDate: '',\r\n        endDate: ''\r\n      },\r\n      dateRange: [],\r\n      total: 0,\r\n      tableData: [],\r\n      detailDialogVisible: false,\r\n      currentDetail: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    formatNumber(num) {\r\n      if (!num && num !== 0) return '0.00'\r\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    },\r\n    async getList() {\r\n      this.loading = true\r\n      try {\r\n        console.log('请求参数:', this.listQuery)\r\n        const res = await getPointsList(this.listQuery)\r\n        console.log('响应数据:', res)\r\n        if (res.code === 0) {\r\n          this.tableData = res.data || []\r\n          this.total = res.total || 0\r\n          \r\n          if (this.tableData.length === 0) {\r\n            this.$message.info('暂无数据')\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取数据失败:', error)\r\n        this.$message.error('获取数据失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.listQuery.page = 1\r\n      this.getList()\r\n    },\r\n    handleReset() {\r\n      this.dateRange = []\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        deviceNo: '',\r\n        startDate: '',\r\n        endDate: ''\r\n      }\r\n      this.getList()\r\n    },\r\n    handleViewDetail(row) {\r\n      this.currentDetail = { ...row }\r\n      this.detailDialogVisible = true\r\n    },\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n    handleDateRangeChange(val) {\r\n      if (val) {\r\n        this.listQuery.startDate = val[0].substring(0, 10) + ' 00:00:00'\r\n        this.listQuery.endDate = val[1].substring(0, 10) + ' 23:59:59'\r\n      } else {\r\n        this.listQuery.startDate = ''\r\n        this.listQuery.endDate = ''\r\n      }\r\n      this.handleSearch()\r\n    },\r\n    formatDateTime(datetime) {\r\n      if (!datetime) return '';\r\n      const date = new Date(datetime);\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .filter-container {\r\n    padding: 22px 0;\r\n    background-color: #fff;\r\n    \r\n    .el-row {\r\n      margin: 0 !important;\r\n    }\r\n    \r\n    .el-col {\r\n      padding: 0 5px;\r\n      \r\n      &:last-child {\r\n        text-align: right;\r\n      }\r\n    }\r\n\r\n    .el-button {\r\n      margin-left: 8px;\r\n      \r\n      &:first-child {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n.detail-container {\r\n  padding: 20px;\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;AA0GA,SAAAA,aAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACAC,SAAA;MACAC,KAAA;MACAC,SAAA;MACAC,mBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAA,GAAA,IAAAA,GAAA;MACA,OAAAC,UAAA,CAAAD,GAAA,EAAAE,OAAA,IAAAC,OAAA;IACA;IACAN,OAAA,WAAAA,QAAA;MAAA,IAAAO,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAArB,OAAA;cAAA6B,QAAA,CAAAC,IAAA;cAEAE,OAAA,CAAAC,GAAA,UAAAZ,KAAA,CAAApB,SAAA;cAAA4B,QAAA,CAAAE,IAAA;cAAA,OACAlC,aAAA,CAAAwB,KAAA,CAAApB,SAAA;YAAA;cAAAyB,GAAA,GAAAG,QAAA,CAAAK,IAAA;cACAF,OAAA,CAAAC,GAAA,UAAAP,GAAA;cACA,IAAAA,GAAA,CAAAS,IAAA;gBACAd,KAAA,CAAAX,SAAA,GAAAgB,GAAA,CAAA3B,IAAA;gBACAsB,KAAA,CAAAZ,KAAA,GAAAiB,GAAA,CAAAjB,KAAA;gBAEA,IAAAY,KAAA,CAAAX,SAAA,CAAA0B,MAAA;kBACAf,KAAA,CAAAgB,QAAA,CAAAC,IAAA;gBACA;cACA;gBACAjB,KAAA,CAAAgB,QAAA,CAAAE,KAAA,CAAAb,GAAA,CAAAc,GAAA;cACA;cAAAX,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAY,EAAA,GAAAZ,QAAA;cAEAG,OAAA,CAAAO,KAAA,YAAAV,QAAA,CAAAY,EAAA;cACApB,KAAA,CAAAgB,QAAA,CAAAE,KAAA;YAAA;cAAAV,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAArB,OAAA;cAAA,OAAA6B,QAAA,CAAAa,MAAA;YAAA;YAAA;cAAA,OAAAb,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAAlB,OAAA;MAAA;IAEA;IACAmB,YAAA,WAAAA,aAAA;MACA,KAAA3C,SAAA,CAAAC,IAAA;MACA,KAAAY,OAAA;IACA;IACA+B,WAAA,WAAAA,YAAA;MACA,KAAArC,SAAA;MACA,KAAAP,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA,KAAAO,OAAA;IACA;IACAgC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAnC,aAAA,GAAAoC,aAAA,KAAAD,GAAA;MACA,KAAApC,mBAAA;IACA;IACAsC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAjD,SAAA,CAAAE,KAAA,GAAA+C,GAAA;MACA,KAAApC,OAAA;IACA;IACAqC,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAjD,SAAA,CAAAC,IAAA,GAAAgD,GAAA;MACA,KAAApC,OAAA;IACA;IACAsC,qBAAA,WAAAA,sBAAAF,GAAA;MACA,IAAAA,GAAA;QACA,KAAAjD,SAAA,CAAAK,SAAA,GAAA4C,GAAA,IAAAG,SAAA;QACA,KAAApD,SAAA,CAAAM,OAAA,GAAA2C,GAAA,IAAAG,SAAA;MACA;QACA,KAAApD,SAAA,CAAAK,SAAA;QACA,KAAAL,SAAA,CAAAM,OAAA;MACA;MACA,KAAAqC,YAAA;IACA;IACAU,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,QAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}