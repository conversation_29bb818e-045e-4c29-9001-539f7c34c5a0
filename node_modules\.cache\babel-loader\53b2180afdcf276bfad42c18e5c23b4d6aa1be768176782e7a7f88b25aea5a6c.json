{"ast": null, "code": "import _objectSpread from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport request from '@/utils/request';\nexport default {\n  name: 'DeliveryOrder',\n  data: function data() {\n    return {\n      orderLoading: false,\n      orderList: [],\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        symbol: '',\n        userNo: '',\n        leaderNo: ''\n      },\n      detailDialogVisible: false,\n      detailRow: {},\n      showConfigDialog: false,\n      configList: [],\n      editConfigDialogVisible: false,\n      editConfigForm: {},\n      saveConfigLoading: false,\n      editConfigRules: {\n        name: [{\n          required: true,\n          message: '名称不能为空',\n          trigger: 'blur'\n        }],\n        copyType: [{\n          required: true,\n          message: '带单类型不能为空',\n          trigger: 'change'\n        }],\n        leverType: [{\n          required: true,\n          message: '杠杆类型不能为空',\n          trigger: 'change'\n        }],\n        minFollowAmount: [{\n          required: true,\n          message: '最低跟单金额不能为空',\n          trigger: 'blur'\n        }],\n        maxFollowAmount: [{\n          required: true,\n          message: '最高跟单金额不能为空',\n          trigger: 'blur'\n        }],\n        minFollowCount: [{\n          required: true,\n          message: '最低跟单人数不能为空',\n          trigger: 'blur'\n        }],\n        maxFollowCount: [{\n          required: true,\n          message: '最高跟单人数不能为空',\n          trigger: 'blur'\n        }],\n        lockTime: [{\n          required: true,\n          message: '锁仓时间不能为空',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.getOrderList();\n    this.getConfigList();\n  },\n  methods: {\n    getOrderList: function getOrderList() {\n      var _this = this;\n      this.orderLoading = true;\n      request({\n        url: '/api/deliveryOrder/list',\n        method: 'get',\n        params: this.queryParams\n      }).then(function (res) {\n        _this.orderList = res.data && res.data.records || res.data || [];\n        _this.total = res.data && res.data.total || res.total || 0;\n      })[\"finally\"](function () {\n        _this.orderLoading = false;\n      });\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.queryParams.pageSize = val;\n      this.getOrderList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.getOrderList();\n    },\n    handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getOrderList();\n    },\n    resetQuery: function resetQuery() {\n      this.queryParams.symbol = '';\n      this.queryParams.userNo = '';\n      this.queryParams.leaderNo = '';\n      this.handleQuery();\n    },\n    showDetail: function showDetail(row) {\n      this.detailRow = row;\n      this.detailDialogVisible = true;\n    },\n    getConfigList: function getConfigList() {\n      var _this2 = this;\n      request({\n        url: '/api/copyConfig/list',\n        method: 'get'\n      }).then(function (res) {\n        _this2.configList = res.data || res || [];\n      });\n    },\n    editConfig: function editConfig(row) {\n      this.editConfigForm = _objectSpread({}, row);\n      this.editConfigDialogVisible = true;\n    },\n    saveEditConfig: function saveEditConfig() {\n      var _this$$refs$editConfi,\n        _this$$refs$editConfi2,\n        _this3 = this;\n      (_this$$refs$editConfi = this.$refs['editConfigForm']) === null || _this$$refs$editConfi === void 0 || (_this$$refs$editConfi2 = _this$$refs$editConfi.validate) === null || _this$$refs$editConfi2 === void 0 || _this$$refs$editConfi2.call(_this$$refs$editConfi, function () {});\n      this.saveConfigLoading = true;\n      request({\n        url: '/api/copyConfig/update',\n        method: 'post',\n        data: this.editConfigForm\n      }).then(function (res) {\n        _this3.saveConfigLoading = false;\n        if (res.data === true || res === true || res.code === 0) {\n          _this3.$message.success('保存成功');\n          _this3.getConfigList();\n          _this3.editConfigDialogVisible = false;\n        } else {\n          _this3.$message.error(res.msg || '保存失败');\n        }\n      })[\"catch\"](function () {\n        _this3.saveConfigLoading = false;\n        _this3.$message.error('保存失败');\n      });\n    },\n    formatDateTime: function formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      var date = new Date(dateTime);\n      var pad = function pad(n) {\n        return n < 10 ? '0' + n : n;\n      };\n      return \"\".concat(date.getFullYear(), \"-\").concat(pad(date.getMonth() + 1), \"-\").concat(pad(date.getDate()), \" \") + \"\".concat(pad(date.getHours()), \":\").concat(pad(date.getMinutes()), \":\").concat(pad(date.getSeconds()));\n    },\n    getStatusText: function getStatusText(val) {\n      var map = {\n        1: '开仓',\n        2: '平仓'\n      };\n      return map[val] || val;\n    },\n    getProfitStatusText: function getProfitStatusText(val) {\n      var map = {\n        1: '盈利',\n        2: '亏损',\n        3: '持平'\n      };\n      return map[val] || val;\n    },\n    getCopyTypeText: function getCopyTypeText(val) {\n      var map = {\n        0: '短线',\n        1: '中线',\n        2: '长线'\n      };\n      return map[val] || val;\n    },\n    getLeverTypeText: function getLeverTypeText(val) {\n      var map = {\n        0: 'x5',\n        1: 'x10'\n      };\n      return map[val] || val;\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "orderLoading", "orderList", "total", "queryParams", "pageNum", "pageSize", "symbol", "userNo", "leader<PERSON><PERSON>", "detailDialogVisible", "detailRow", "showConfigDialog", "configList", "editConfigDialogVisible", "editConfigForm", "saveConfigLoading", "editConfigRules", "required", "message", "trigger", "copyType", "leverType", "minFollowAmount", "maxFollowAmount", "minFollowCount", "max<PERSON><PERSON><PERSON><PERSON>ount", "lockTime", "created", "getOrderList", "getConfigList", "methods", "_this", "url", "method", "params", "then", "res", "records", "handleSizeChange", "val", "handleCurrentChange", "handleQuery", "reset<PERSON><PERSON>y", "showDetail", "row", "_this2", "editConfig", "_objectSpread", "saveEditConfig", "_this$$refs$editConfi", "_this$$refs$editConfi2", "_this3", "$refs", "validate", "call", "code", "$message", "success", "error", "msg", "formatDateTime", "dateTime", "date", "Date", "pad", "n", "concat", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getStatusText", "map", "getProfitStatusText", "getCopyTypeText", "getLeverTypeText"], "sources": ["src/views/exchange/copy-trade/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <div style=\"display: flex; justify-content: flex-end; align-items: center; margin-bottom: 10px;\">\n        <el-button type=\"primary\" @click=\"showConfigDialog = true\">带单配置</el-button>\n      </div>\n      <!-- 筛选条件 -->\n      <div style=\"margin-bottom: 10px;\">\n        <el-form :inline=\"true\" :model=\"queryParams\" size=\"small\">\n          <el-form-item label=\"交易对\">\n            <el-input v-model=\"queryParams.symbol\" placeholder=\"交易对\" clearable />\n          </el-form-item>\n          <el-form-item label=\"用户UID\">\n            <el-input v-model=\"queryParams.userNo\" placeholder=\"用户UID\" clearable />\n          </el-form-item>\n          <el-form-item label=\"带单员UID\">\n            <el-input v-model=\"queryParams.leaderNo\" placeholder=\"带单员UID\" clearable />\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      <el-table :data=\"orderList\" v-loading=\"orderLoading\" border style=\"width: 100%;\">\n        <el-table-column prop=\"id\" label=\"订单ID\" align=\"center\" width=\"80\" />\n        <!-- 用户信息 -->\n        <el-table-column label=\"用户头像\" align=\"center\" width=\"80\">\n          <template slot-scope=\"scope\">\n            <img :src=\"scope.row.userAvatar || require('@/assets/default.png')\" style=\"width:40px;height:40px;border-radius:50%;\" />\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"userEmail\" label=\"用户邮箱\" align=\"center\" width=\"160\" />\n        <el-table-column prop=\"userNo\" label=\"用户UID\" align=\"center\" width=\"100\" />\n        <!-- 带单员信息 -->\n        <el-table-column label=\"带单员头像\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <img :src=\"scope.row.leaderAvatar || require('@/assets/default.png')\" style=\"width:40px;height:40px;border-radius:50%;\" />\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"leaderEmail\" label=\"带单员邮箱\" align=\"center\" width=\"160\" />\n        <el-table-column prop=\"leaderNo\" label=\"带单员UID\" align=\"center\" width=\"100\" />\n        <!-- 订单核心字段 -->\n        <el-table-column prop=\"userId\" label=\"用户ID\" align=\"center\" width=\"100\" />\n        <el-table-column prop=\"leaderId\" label=\"带单员ID\" align=\"center\" width=\"100\" />\n        <el-table-column prop=\"symbol\" label=\"交易对\" align=\"center\" width=\"100\" />\n        <el-table-column prop=\"marginAmount\" label=\"保证金额\" align=\"center\" width=\"120\" />\n        <el-table-column prop=\"positionAmount\" label=\"持仓量\" align=\"center\" width=\"100\" />\n        <el-table-column prop=\"lever\" label=\"杠杆倍数\" align=\"center\" width=\"80\" />\n        <el-table-column prop=\"direction\" label=\"方向\" align=\"center\" width=\"80\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.direction === 1 ? '买涨' : '买跌' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"takeProfit\" label=\"止盈价格\" align=\"center\" width=\"120\" />\n        <el-table-column prop=\"stopLoss\" label=\"止损价格\" align=\"center\" width=\"120\" />\n        <el-table-column prop=\"openPrice\" label=\"开仓价格\" align=\"center\" width=\"120\" />\n        <el-table-column prop=\"closePrice\" label=\"平仓价格\" align=\"center\" width=\"120\" />\n        <el-table-column prop=\"openTime\" label=\"开仓时间\" align=\"center\" width=\"160\" />\n        <el-table-column prop=\"closeTime\" label=\"平仓时间\" align=\"center\" width=\"160\" />\n        <el-table-column prop=\"status\" label=\"状态\" align=\"center\" width=\"80\">\n          <template slot-scope=\"scope\">\n            {{ getStatusText(scope.row.status) }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"profit\" label=\"盈利\" align=\"center\" width=\"100\" />\n        <el-table-column prop=\"rebateStatus\" label=\"返佣状态\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            {{ scope.row.rebateStatus === 1 ? '未返' : '已返' }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"profitStatus\" label=\"亏盈状态\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            {{ getProfitStatusText(scope.row.profitStatus) }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"createTime\" label=\"创建时间\" align=\"center\" width=\"160\" />\n        <el-table-column prop=\"updateTime\" label=\"更新时间\" align=\"center\" width=\"160\" />\n        <el-table-column label=\"操作\" align=\"center\" width=\"80\" fixed=\"right\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"mini\" @click=\"showDetail(scope.row)\">详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"queryParams.pageNum\"\n          :page-sizes=\"[10, 20, 30, 50]\"\n          :page-size=\"queryParams.pageSize\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"total\">\n        </el-pagination>\n      </div>\n      <!-- 详情弹窗 -->\n      <el-dialog :visible.sync=\"detailDialogVisible\" title=\"订单详情\" width=\"800px\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"订单ID\">{{ detailRow.id }}</el-descriptions-item>\n          <el-descriptions-item label=\"交易对\">{{ detailRow.symbol }}</el-descriptions-item>\n          <el-descriptions-item label=\"保证金额\">{{ detailRow.marginAmount }}</el-descriptions-item>\n          <el-descriptions-item label=\"持仓量\">{{ detailRow.positionAmount }}</el-descriptions-item>\n          <el-descriptions-item label=\"杠杆倍数\">{{ detailRow.lever }}</el-descriptions-item>\n          <el-descriptions-item label=\"方向\">{{ detailRow.direction === 1 ? '买涨' : '买跌' }}</el-descriptions-item>\n          <el-descriptions-item label=\"止盈价格\">{{ detailRow.takeProfit }}</el-descriptions-item>\n          <el-descriptions-item label=\"止损价格\">{{ detailRow.stopLoss }}</el-descriptions-item>\n          <el-descriptions-item label=\"开仓价格\">{{ detailRow.openPrice }}</el-descriptions-item>\n          <el-descriptions-item label=\"平仓价格\">{{ detailRow.closePrice }}</el-descriptions-item>\n          <el-descriptions-item label=\"开仓时间\">{{ formatDateTime(detailRow.openTime) }}</el-descriptions-item>\n          <el-descriptions-item label=\"平仓时间\">{{ formatDateTime(detailRow.closeTime) }}</el-descriptions-item>\n          <el-descriptions-item label=\"状态\">{{ getStatusText(detailRow.status) }}</el-descriptions-item>\n          <el-descriptions-item label=\"盈利\">{{ detailRow.profit }}</el-descriptions-item>\n          <el-descriptions-item label=\"返佣状态\">{{ detailRow.rebateStatus === 1 ? '未返' : '已返' }}</el-descriptions-item>\n          <el-descriptions-item label=\"亏盈状态\">{{ getProfitStatusText(detailRow.profitStatus) }}</el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\">{{ formatDateTime(detailRow.createTime) }}</el-descriptions-item>\n          <el-descriptions-item label=\"更新时间\">{{ formatDateTime(detailRow.updateTime) }}</el-descriptions-item>\n        </el-descriptions>\n        <el-divider>用户信息</el-divider>\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"用户UID\">{{ detailRow.userNo || '-' }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户邮箱\">{{ detailRow.userEmail || '-' }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户头像\">\n            <img :src=\"detailRow.userAvatar || require('@/assets/default.png')\" style=\"width:40px;height:40px;border-radius:50%;\" />\n          </el-descriptions-item>\n        </el-descriptions>\n        <el-divider>带单员信息</el-divider>\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"带单员UID\">{{ detailRow.leaderNo || '-' }}</el-descriptions-item>\n          <el-descriptions-item label=\"带单员邮箱\">{{ detailRow.leaderEmail || '-' }}</el-descriptions-item>\n          <el-descriptions-item label=\"带单员头像\">\n            <img :src=\"detailRow.leaderAvatar || require('@/assets/default.png')\" style=\"width:40px;height:40px;border-radius:50%;\" />\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-dialog>\n      <el-dialog :visible.sync=\"showConfigDialog\" title=\"带单配置管理\" width=\"1100px\">\n        <el-table :data=\"configList\" border style=\"width: 100%;\">\n          <el-table-column prop=\"id\" label=\"ID\" align=\"center\" width=\"60\" />\n          <el-table-column prop=\"name\" label=\"名称\" align=\"center\" min-width=\"120\" />\n          <el-table-column prop=\"copyType\" label=\"带单类型\" align=\"center\" min-width=\"100\">\n            <template slot-scope=\"scope\">\n              <span>{{ getCopyTypeText(scope.row.copyType) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"leverType\" label=\"杠杆类型\" align=\"center\" min-width=\"100\">\n            <template slot-scope=\"scope\">\n              <span>{{ getLeverTypeText(scope.row.leverType) }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"minFollowAmount\" label=\"最低跟单金额\" align=\"center\" min-width=\"120\" />\n          <el-table-column prop=\"maxFollowAmount\" label=\"最高跟单金额\" align=\"center\" min-width=\"120\" />\n          <el-table-column prop=\"minFollowCount\" label=\"最低跟单人数\" align=\"center\" min-width=\"120\" />\n          <el-table-column prop=\"maxFollowCount\" label=\"最高跟单人数\" align=\"center\" min-width=\"120\" />\n          <el-table-column prop=\"lockTime\" label=\"锁仓时间(天)\" align=\"center\" min-width=\"120\" />\n          <el-table-column prop=\"createTime\" label=\"创建时间\" align=\"center\" min-width=\"160\">\n            <template slot-scope=\"scope\">\n              {{ formatDateTime(scope.row.createTime) }}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"updateTime\" label=\"更新时间\" align=\"center\" min-width=\"160\">\n            <template slot-scope=\"scope\">\n              {{ formatDateTime(scope.row.updateTime) }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" align=\"center\" width=\"100\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"primary\" size=\"mini\" @click=\"editConfig(scope.row)\">修改</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"showConfigDialog = false\">关闭</el-button>\n        </span>\n      </el-dialog>\n      <el-dialog :visible.sync=\"editConfigDialogVisible\" title=\"编辑带单配置\" width=\"600px\">\n        <el-form ref=\"editConfigForm\" :model=\"editConfigForm\" :rules=\"editConfigRules\" label-width=\"120px\">\n          <el-form-item label=\"名称\" prop=\"name\">\n            <el-input v-model=\"editConfigForm.name\" />\n          </el-form-item>\n          <el-form-item label=\"带单类型\" prop=\"copyType\">\n            <el-select v-model=\"editConfigForm.copyType\">\n              <el-option label=\"短线\" :value=\"0\" />\n              <el-option label=\"中线\" :value=\"1\" />\n              <el-option label=\"长线\" :value=\"2\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"杠杆类型\" prop=\"leverType\">\n            <el-select v-model=\"editConfigForm.leverType\">\n              <el-option label=\"x5\" :value=\"0\" />\n              <el-option label=\"x10\" :value=\"1\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"最低跟单金额\" prop=\"minFollowAmount\">\n            <el-input-number v-model=\"editConfigForm.minFollowAmount\" :min=\"0\" />\n          </el-form-item>\n          <el-form-item label=\"最高跟单金额\" prop=\"maxFollowAmount\">\n            <el-input-number v-model=\"editConfigForm.maxFollowAmount\" :min=\"0\" />\n          </el-form-item>\n          <el-form-item label=\"最低跟单人数\" prop=\"minFollowCount\">\n            <el-input-number v-model=\"editConfigForm.minFollowCount\" :min=\"0\" />\n          </el-form-item>\n          <el-form-item label=\"最高跟单人数\" prop=\"maxFollowCount\">\n            <el-input-number v-model=\"editConfigForm.maxFollowCount\" :min=\"0\" />\n          </el-form-item>\n          <el-form-item label=\"锁仓时间(天)\" prop=\"lockTime\">\n            <el-input-number v-model=\"editConfigForm.lockTime\" :min=\"0\" />\n          </el-form-item>\n        </el-form>\n        <span slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"editConfigDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveEditConfig\" :loading=\"saveConfigLoading\">保存</el-button>\n        </span>\n      </el-dialog>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\nexport default {\n  name: 'DeliveryOrder',\n  data() {\n    return {\n      orderLoading: false,\n      orderList: [],\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        symbol: '',\n        userNo: '',\n        leaderNo: ''\n      },\n      detailDialogVisible: false,\n      detailRow: {},\n      showConfigDialog: false,\n      configList: [],\n      editConfigDialogVisible: false,\n      editConfigForm: {},\n      saveConfigLoading: false,\n      editConfigRules: {\n        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],\n        copyType: [{ required: true, message: '带单类型不能为空', trigger: 'change' }],\n        leverType: [{ required: true, message: '杠杆类型不能为空', trigger: 'change' }],\n        minFollowAmount: [{ required: true, message: '最低跟单金额不能为空', trigger: 'blur' }],\n        maxFollowAmount: [{ required: true, message: '最高跟单金额不能为空', trigger: 'blur' }],\n        minFollowCount: [{ required: true, message: '最低跟单人数不能为空', trigger: 'blur' }],\n        maxFollowCount: [{ required: true, message: '最高跟单人数不能为空', trigger: 'blur' }],\n        lockTime: [{ required: true, message: '锁仓时间不能为空', trigger: 'blur' }]\n      }\n    }\n  },\n  created() {\n    this.getOrderList();\n    this.getConfigList();\n  },\n  methods: {\n    getOrderList() {\n      this.orderLoading = true;\n      request({\n        url: '/api/deliveryOrder/list',\n        method: 'get',\n        params: this.queryParams\n      }).then(res => {\n        this.orderList = (res.data && res.data.records) || res.data || [];\n        this.total = (res.data && res.data.total) || res.total || 0;\n      }).finally(() => {\n        this.orderLoading = false;\n      });\n    },\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val;\n      this.getOrderList();\n    },\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val;\n      this.getOrderList();\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getOrderList();\n    },\n    resetQuery() {\n      this.queryParams.symbol = '';\n      this.queryParams.userNo = '';\n      this.queryParams.leaderNo = '';\n      this.handleQuery();\n    },\n    showDetail(row) {\n      this.detailRow = row;\n      this.detailDialogVisible = true;\n    },\n    getConfigList() {\n      request({\n        url: '/api/copyConfig/list',\n        method: 'get'\n      }).then(res => {\n        this.configList = res.data || res || [];\n      });\n    },\n    editConfig(row) {\n      this.editConfigForm = { ...row };\n      this.editConfigDialogVisible = true;\n    },\n    saveEditConfig() {\n      this.$refs['editConfigForm']?.validate?.(() => {});\n      this.saveConfigLoading = true;\n      request({\n        url: '/api/copyConfig/update',\n        method: 'post',\n        data: this.editConfigForm\n      }).then(res => {\n        this.saveConfigLoading = false;\n        if (res.data === true || res === true || res.code === 0) {\n          this.$message.success('保存成功');\n          this.getConfigList();\n          this.editConfigDialogVisible = false;\n        } else {\n          this.$message.error(res.msg || '保存失败');\n        }\n      }).catch(() => {\n        this.saveConfigLoading = false;\n        this.$message.error('保存失败');\n      });\n    },\n    formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      const date = new Date(dateTime);\n      const pad = n => n < 10 ? '0' + n : n;\n      return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +\n             `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;\n    },\n    getStatusText(val) {\n      const map = {1: '开仓', 2: '平仓'};\n      return map[val] || val;\n    },\n    getProfitStatusText(val) {\n      const map = {1: '盈利', 2: '亏损', 3: '持平'};\n      return map[val] || val;\n    },\n    getCopyTypeText(val) {\n      const map = {0: '短线', 1: '中线', 2: '长线'};\n      return map[val] || val;\n    },\n    getLeverTypeText(val) {\n      const map = {0: 'x5', 1: 'x10'};\n      return map[val] || val;\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  padding: 10px 0;\n}\n</style> "], "mappings": ";;;;AA0NA,OAAAA,OAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,SAAA;MACAC,KAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;MACA;MACAC,mBAAA;MACAC,SAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,uBAAA;MACAC,cAAA;MACAC,iBAAA;MACAC,eAAA;QACAlB,IAAA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,QAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAE,SAAA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAG,eAAA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAI,eAAA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAK,cAAA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAM,cAAA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAO,QAAA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAQ,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAF,YAAA,WAAAA,aAAA;MAAA,IAAAG,KAAA;MACA,KAAA/B,YAAA;MACAH,OAAA;QACAmC,GAAA;QACAC,MAAA;QACAC,MAAA,OAAA/B;MACA,GAAAgC,IAAA,WAAAC,GAAA;QACAL,KAAA,CAAA9B,SAAA,GAAAmC,GAAA,CAAArC,IAAA,IAAAqC,GAAA,CAAArC,IAAA,CAAAsC,OAAA,IAAAD,GAAA,CAAArC,IAAA;QACAgC,KAAA,CAAA7B,KAAA,GAAAkC,GAAA,CAAArC,IAAA,IAAAqC,GAAA,CAAArC,IAAA,CAAAG,KAAA,IAAAkC,GAAA,CAAAlC,KAAA;MACA;QACA6B,KAAA,CAAA/B,YAAA;MACA;IACA;IACAsC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAApC,WAAA,CAAAE,QAAA,GAAAkC,GAAA;MACA,KAAAX,YAAA;IACA;IACAY,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAApC,WAAA,CAAAC,OAAA,GAAAmC,GAAA;MACA,KAAAX,YAAA;IACA;IACAa,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAC,OAAA;MACA,KAAAwB,YAAA;IACA;IACAc,UAAA,WAAAA,WAAA;MACA,KAAAvC,WAAA,CAAAG,MAAA;MACA,KAAAH,WAAA,CAAAI,MAAA;MACA,KAAAJ,WAAA,CAAAK,QAAA;MACA,KAAAiC,WAAA;IACA;IACAE,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAlC,SAAA,GAAAkC,GAAA;MACA,KAAAnC,mBAAA;IACA;IACAoB,aAAA,WAAAA,cAAA;MAAA,IAAAgB,MAAA;MACAhD,OAAA;QACAmC,GAAA;QACAC,MAAA;MACA,GAAAE,IAAA,WAAAC,GAAA;QACAS,MAAA,CAAAjC,UAAA,GAAAwB,GAAA,CAAArC,IAAA,IAAAqC,GAAA;MACA;IACA;IACAU,UAAA,WAAAA,WAAAF,GAAA;MACA,KAAA9B,cAAA,GAAAiC,aAAA,KAAAH,GAAA;MACA,KAAA/B,uBAAA;IACA;IACAmC,cAAA,WAAAA,eAAA;MAAA,IAAAC,qBAAA;QAAAC,sBAAA;QAAAC,MAAA;MACA,CAAAF,qBAAA,QAAAG,KAAA,gCAAAH,qBAAA,gBAAAC,sBAAA,GAAAD,qBAAA,CAAAI,QAAA,cAAAH,sBAAA,eAAAA,sBAAA,CAAAI,IAAA,CAAAL,qBAAA;MACA,KAAAlC,iBAAA;MACAlB,OAAA;QACAmC,GAAA;QACAC,MAAA;QACAlC,IAAA,OAAAe;MACA,GAAAqB,IAAA,WAAAC,GAAA;QACAe,MAAA,CAAApC,iBAAA;QACA,IAAAqB,GAAA,CAAArC,IAAA,aAAAqC,GAAA,aAAAA,GAAA,CAAAmB,IAAA;UACAJ,MAAA,CAAAK,QAAA,CAAAC,OAAA;UACAN,MAAA,CAAAtB,aAAA;UACAsB,MAAA,CAAAtC,uBAAA;QACA;UACAsC,MAAA,CAAAK,QAAA,CAAAE,KAAA,CAAAtB,GAAA,CAAAuB,GAAA;QACA;MACA;QACAR,MAAA,CAAApC,iBAAA;QACAoC,MAAA,CAAAK,QAAA,CAAAE,KAAA;MACA;IACA;IACAE,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,QAAA;MACA,IAAAG,GAAA,YAAAA,IAAAC,CAAA;QAAA,OAAAA,CAAA,cAAAA,CAAA,GAAAA,CAAA;MAAA;MACA,UAAAC,MAAA,CAAAJ,IAAA,CAAAK,WAAA,SAAAD,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAM,QAAA,cAAAF,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAO,OAAA,eAAAH,MAAA,CACAF,GAAA,CAAAF,IAAA,CAAAQ,QAAA,UAAAJ,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAS,UAAA,UAAAL,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAU,UAAA;IACA;IACAC,aAAA,WAAAA,cAAAlC,GAAA;MACA,IAAAmC,GAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAAnC,GAAA,KAAAA,GAAA;IACA;IACAoC,mBAAA,WAAAA,oBAAApC,GAAA;MACA,IAAAmC,GAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAAnC,GAAA,KAAAA,GAAA;IACA;IACAqC,eAAA,WAAAA,gBAAArC,GAAA;MACA,IAAAmC,GAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAAnC,GAAA,KAAAA,GAAA;IACA;IACAsC,gBAAA,WAAAA,iBAAAtC,GAAA;MACA,IAAAmC,GAAA;QAAA;QAAA;MAAA;MACA,OAAAA,GAAA,CAAAnC,GAAA,KAAAA,GAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}