<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d84a095a-dce5-47fe-88df-4b10250a24b1" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2pn7F50tsIf6jLZKnRiHQp0c61m" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.adminapi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.adminapi [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.adminapi [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.AdminApplication.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/最新项目文件/交易所/adminapi/src/main/resources/db&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.27471265&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;settings.editor.splitter.proportion&quot;: &quot;0.1904277&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;5a4961367d65d570e20d2d69156581b4&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\最新项目文件\交易所\adminapi\src\main\resources\db" />
      <recent name="F:\常规项目\华通宝\adminapi\src\main\resources\lib" />
      <recent name="G:\备份9\adminapi\src\main\java\com\admin\alipay" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.admin.config" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.AdminApplication">
    <configuration name="AdminApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.admin.AdminApplication" />
      <module name="adminapi" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.admin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PasswordEncoderTest.generatePassword" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="adminapi" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.admin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.admin" />
      <option name="MAIN_CLASS_NAME" value="com.admin.PasswordEncoderTest" />
      <option name="METHOD_NAME" value="generatePassword" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="jeepay-sdk-java-max3.8.5.jar" type="JarApplication" temporary="true">
      <option name="JAR_PATH" value="$PROJECT_DIR$/src/main/resources/lib/jeepay-sdk-java-max3.8.5.jar" />
      <method v="2" />
    </configuration>
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="adminapi" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.admin.AdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.AdminApplication" />
        <item itemvalue="JAR 应用程序.jeepay-sdk-java-max3.8.5.jar" />
        <item itemvalue="JUnit.PasswordEncoderTest.generatePassword" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26574.91" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26574.91" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="d84a095a-dce5-47fe-88df-4b10250a24b1" name="更改" comment="" />
      <created>1733389880857</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1733389880857</updated>
      <workItem from="1733389881986" duration="14322000" />
      <workItem from="1733408413196" duration="1688000" />
      <workItem from="1733444772163" duration="35000" />
      <workItem from="1733444818165" duration="2153000" />
      <workItem from="1733462450698" duration="4987000" />
      <workItem from="1733471041086" duration="670000" />
      <workItem from="1733531842621" duration="30112000" />
      <workItem from="1733580040016" duration="100000" />
      <workItem from="1733582096525" duration="12051000" />
      <workItem from="1733600649479" duration="2205000" />
      <workItem from="1733625360514" duration="2092000" />
      <workItem from="1733628560655" duration="14346000" />
      <workItem from="1733661059792" duration="477000" />
      <workItem from="1733667341145" duration="1603000" />
      <workItem from="1733670426280" duration="4948000" />
      <workItem from="1733677406248" duration="40000" />
      <workItem from="1733822215965" duration="4000" />
      <workItem from="1733822252767" duration="911000" />
      <workItem from="1733830052552" duration="17040000" />
      <workItem from="1733901072541" duration="13137000" />
      <workItem from="1733981225411" duration="3157000" />
      <workItem from="1733991672046" duration="605000" />
      <workItem from="1733993959472" duration="4418000" />
      <workItem from="1734048867127" duration="302000" />
      <workItem from="1734051611464" duration="3264000" />
      <workItem from="1734093919885" duration="2126000" />
      <workItem from="1734153287381" duration="122000" />
      <workItem from="1734233509515" duration="15000" />
      <workItem from="1734318239932" duration="2453000" />
      <workItem from="1734322477987" duration="3598000" />
      <workItem from="1734483412281" duration="11000" />
      <workItem from="1734584831376" duration="21000" />
      <workItem from="1734676415267" duration="2897000" />
      <workItem from="1734686871372" duration="2000" />
      <workItem from="1734933448539" duration="905000" />
      <workItem from="1734934918671" duration="5639000" />
      <workItem from="1734950554037" duration="4190000" />
      <workItem from="1734966537242" duration="845000" />
      <workItem from="1735019557626" duration="1523000" />
      <workItem from="1735043086796" duration="599000" />
      <workItem from="1735108200525" duration="3058000" />
      <workItem from="1735123274682" duration="707000" />
      <workItem from="1735128678690" duration="443000" />
      <workItem from="1735269275772" duration="4664000" />
      <workItem from="1735281238363" duration="1536000" />
      <workItem from="1735624068397" duration="13039000" />
      <workItem from="1736053361846" duration="1736000" />
      <workItem from="1736055989128" duration="8927000" />
      <workItem from="1736143418397" duration="583000" />
      <workItem from="1736144311338" duration="270000" />
      <workItem from="1736147800691" duration="390000" />
      <workItem from="1736174873130" duration="1516000" />
      <workItem from="1736323097051" duration="2252000" />
      <workItem from="1736415372018" duration="221000" />
      <workItem from="1736417169392" duration="5499000" />
      <workItem from="1736425883217" duration="745000" />
      <workItem from="1736835783014" duration="1596000" />
      <workItem from="1737346374805" duration="4729000" />
      <workItem from="1737351752484" duration="286000" />
      <workItem from="1737352138872" duration="6685000" />
      <workItem from="1737359398837" duration="3879000" />
      <workItem from="1737363568282" duration="196000" />
      <workItem from="1737367425250" duration="2970000" />
      <workItem from="1737428277988" duration="12000" />
      <workItem from="1737433675316" duration="7947000" />
      <workItem from="1737448926269" duration="595000" />
      <workItem from="1737526796979" duration="1419000" />
      <workItem from="1737612904529" duration="2411000" />
      <workItem from="1738815396983" duration="6231000" />
      <workItem from="1738848316501" duration="9000" />
      <workItem from="1738910027999" duration="228000" />
      <workItem from="1739178215263" duration="42000" />
      <workItem from="1739178639705" duration="100000" />
      <workItem from="1739181459864" duration="179000" />
      <workItem from="1739689663220" duration="1212000" />
      <workItem from="1740034266805" duration="14204000" />
      <workItem from="1740052196178" duration="1913000" />
      <workItem from="1742178832129" duration="801000" />
      <workItem from="1742285524847" duration="2607000" />
      <workItem from="1742786613524" duration="15000" />
      <workItem from="1742786692944" duration="804000" />
      <workItem from="1742884450922" duration="24045000" />
      <workItem from="1742954951298" duration="20221000" />
      <workItem from="1743134556083" duration="4172000" />
      <workItem from="1743163398699" duration="2836000" />
      <workItem from="1743168790968" duration="741000" />
      <workItem from="1743174544108" duration="36000" />
      <workItem from="1743381142521" duration="407000" />
      <workItem from="1743382061671" duration="2371000" />
      <workItem from="1743391537519" duration="14000" />
      <workItem from="1743391561646" duration="10000" />
      <workItem from="1743418559237" duration="408000" />
      <workItem from="1743466886103" duration="479000" />
      <workItem from="1743482310132" duration="1506000" />
      <workItem from="1743517128031" duration="1442000" />
      <workItem from="1743566511951" duration="625000" />
      <workItem from="1743567247066" duration="1904000" />
      <workItem from="1743570067529" duration="795000" />
      <workItem from="1743574109663" duration="1014000" />
      <workItem from="1744769565560" duration="7904000" />
      <workItem from="1744788160829" duration="148000" />
      <workItem from="1744799594236" duration="461000" />
      <workItem from="1744894149152" duration="847000" />
      <workItem from="1745309486746" duration="1927000" />
      <workItem from="1745371702109" duration="22000" />
      <workItem from="1745372377353" duration="4788000" />
      <workItem from="1745840076676" duration="2822000" />
      <workItem from="1745843486246" duration="690000" />
      <workItem from="1745844894820" duration="21000" />
      <workItem from="1745909538871" duration="632000" />
      <workItem from="1746504935949" duration="395000" />
      <workItem from="1746584296402" duration="3110000" />
      <workItem from="1746626409197" duration="6000" />
      <workItem from="1747028662801" duration="935000" />
      <workItem from="1747102881628" duration="28000" />
      <workItem from="1747271673925" duration="4965000" />
      <workItem from="1747383788635" duration="510000" />
      <workItem from="1747411515408" duration="633000" />
      <workItem from="1747708035153" duration="71000" />
      <workItem from="1747710128576" duration="1275000" />
      <workItem from="1748270633404" duration="858000" />
      <workItem from="1748272717979" duration="7000" />
      <workItem from="1749041208557" duration="4985000" />
      <workItem from="1749107232161" duration="1687000" />
      <workItem from="1749348267144" duration="3107000" />
      <workItem from="1749397170732" duration="1601000" />
      <workItem from="1749614881035" duration="1209000" />
      <workItem from="1749644988803" duration="542000" />
      <workItem from="1751109729994" duration="651000" />
      <workItem from="1751149148367" duration="824000" />
      <workItem from="1751150312546" duration="519000" />
      <workItem from="1751335388592" duration="20512000" />
      <workItem from="1752140745472" duration="3055000" />
      <workItem from="1752213639832" duration="7037000" />
      <workItem from="1752293572462" duration="37015000" />
      <workItem from="1752357763084" duration="3672000" />
      <workItem from="1752378204888" duration="12929000" />
      <workItem from="1752403034105" duration="1525000" />
      <workItem from="1752406153665" duration="784000" />
      <workItem from="1752890739323" duration="15348000" />
      <workItem from="1752920927218" duration="63000" />
      <workItem from="1752921008848" duration="1000" />
      <workItem from="1752921199221" duration="339000" />
      <workItem from="1752921555593" duration="1051000" />
      <workItem from="1752995551611" duration="5537000" />
      <workItem from="1753073795391" duration="7775000" />
      <workItem from="1753115509886" duration="595000" />
      <workItem from="1753238364973" duration="10392000" />
      <workItem from="1753364008739" duration="1108000" />
      <workItem from="1753440384231" duration="8809000" />
      <workItem from="1753459847492" duration="884000" />
      <workItem from="1753463290250" duration="65000" />
      <workItem from="1753539890147" duration="2525000" />
      <workItem from="1753578947175" duration="308000" />
      <workItem from="1753590507462" duration="47000" />
      <workItem from="1753592095489" duration="54000" />
      <workItem from="1753619293030" duration="212000" />
      <workItem from="1753662691817" duration="1620000" />
      <workItem from="1753670790172" duration="630000" />
      <workItem from="1753675139702" duration="768000" />
      <workItem from="1753677563150" duration="838000" />
      <workItem from="1753717274417" duration="1262000" />
      <workItem from="1753778745260" duration="1835000" />
      <workItem from="1753852381260" duration="850000" />
      <workItem from="1753935173282" duration="790000" />
      <workItem from="1753938780350" duration="966000" />
      <workItem from="1753959417904" duration="496000" />
      <workItem from="1753960271571" duration="1198000" />
      <workItem from="1753966494894" duration="1646000" />
      <workItem from="1753987130608" duration="519000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.sql.SQLSyntaxErrorException" package="java.sql" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/admin/service/impl/WithdrawRecordServiceImpl.java</url>
          <line>120</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/admin/service/impl/WithdrawRecordServiceImpl.java</url>
          <line>125</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/admin/service/impl/WithdrawRecordServiceImpl.java</url>
          <line>71</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/admin/newpay/TransferMoney.java</url>
          <line>99</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/admin/service/impl/FrontUserServiceImpl.java</url>
          <line>96</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/admin/controller/WithdrawRecordController.java</url>
          <line>53</line>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/admin/newpay/TransferMoney.java</url>
          <line>113</line>
          <properties class="com.admin.newpay.TransferMoney">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="response" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/adminapi$AdminApplication.ic" NAME="AdminApplication 覆盖结果" MODIFIED="1752358178227" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>