{"ast": null, "code": "import _objectSpread from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _regeneratorRuntime from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.number.to-fixed.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.parse-float.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { getWithdrawList, getWithdrawStatistics, auditWithdraw, exportWithdrawRecord } from '@/api/finance/withdraw';\nexport default {\n  name: 'FinanceWithdrawRecord',\n  data: function data() {\n    return {\n      loading: false,\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        status: '',\n        startDate: '',\n        endDate: ''\n      },\n      dateRange: [],\n      total: 0,\n      tableData: [],\n      // 详情相关\n      detailVisible: false,\n      currentRecord: {},\n      // 审核相关\n      auditVisible: false,\n      auditForm: {\n        status: 1,\n        remark: ''\n      },\n      // ,\n      //   remark: [\n      //     { required: true, message: '请输入审核意见', trigger: 'blur' }\n      //   ]\n      auditRules: {\n        status: [{\n          required: true,\n          message: '请选择审核结果',\n          trigger: 'change'\n        }]\n      },\n      // 汇总数据\n      summary: {\n        todayAmount: 0,\n        todayCount: 0,\n        pendingAmount: 0,\n        pendingCount: 0,\n        totalPassAmount: 0\n      },\n      exportLoading: false\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    formatNumber: function formatNumber(num) {\n      if (!num && num !== 0) return '0.00';\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    },\n    formatDateTime: function formatDateTime(datetime) {\n      if (!datetime) return '-';\n      var date = new Date(datetime);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    getStatusType: function getStatusType(status) {\n      var map = {\n        0: 'info',\n        // 待审核\n        1: 'success',\n        // 已通过\n        2: 'danger',\n        // 已拒绝\n        3: 'warning' // 未通过\n      };\n      return map[status] || 'info';\n    },\n    getStatusText: function getStatusText(status) {\n      var map = {\n        0: '待审核',\n        1: '已通过',\n        2: '已拒绝',\n        3: '未通过'\n      };\n      return map[status] || '未知';\n    },\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              _context.next = 4;\n              return getWithdrawList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.tableData = res.records || [];\n                _this.total = res.total || 0;\n              } else {\n                _this.$message.error(res.msg || '获取数据失败');\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取提现记录失败:', _context.t0);\n              _this.$message.error('获取数据失败');\n            case 12:\n              _context.prev = 12;\n              _this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 8, 12, 15]]);\n      }))();\n    },\n    getStatistics: function getStatistics() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return getWithdrawStatistics();\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0) {\n                _this2.summary = res.data || {\n                  todayAmount: 0,\n                  todayCount: 0,\n                  pendingAmount: 0,\n                  pendingCount: 0,\n                  totalPassAmount: 0\n                };\n              }\n              _context2.next = 10;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              console.error('获取统计数据失败:', _context2.t0);\n            case 10:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    handleSearch: function handleSearch() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    handleReset: function handleReset() {\n      this.dateRange = [];\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        status: '',\n        startDate: '',\n        endDate: ''\n      };\n      this.getList();\n    },\n    handleDateRangeChange: function handleDateRangeChange(val) {\n      if (val) {\n        this.listQuery.startDate = val[0];\n        this.listQuery.endDate = val[1];\n      } else {\n        this.listQuery.startDate = '';\n        this.listQuery.endDate = '';\n      }\n      this.handleSearch();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    handleDetail: function handleDetail(row) {\n      this.currentRecord = _objectSpread({}, row);\n      this.detailVisible = true;\n    },\n    handleAudit: function handleAudit(row) {\n      this.currentRecord = row;\n      this.auditForm = {\n        status: 1,\n        remark: ''\n      };\n      this.auditVisible = true;\n    },\n    submitAudit: function submitAudit() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _this3.$refs.auditForm.validate(/*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(valid) {\n                  var res;\n                  return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n                    while (1) switch (_context3.prev = _context3.next) {\n                      case 0:\n                        if (!valid) {\n                          _context3.next = 16;\n                          break;\n                        }\n                        _context3.prev = 1;\n                        _context3.next = 4;\n                        return _this3.$confirm(\"\\u786E\\u8BA4\".concat(_this3.auditForm.status === 1 ? '通过' : '拒绝', \"\\u8BE5\\u63D0\\u73B0\\u7533\\u8BF7\\u5417\\uFF1F\"), '提示', {\n                          confirmButtonText: '确定',\n                          cancelButtonText: '取消',\n                          type: 'warning'\n                        });\n                      case 4:\n                        _context3.next = 6;\n                        return auditWithdraw(_this3.currentRecord.id, {\n                          status: _this3.auditForm.status,\n                          remark: _this3.auditForm.remark\n                        });\n                      case 6:\n                        res = _context3.sent;\n                        if (res.code === 0) {\n                          _this3.$message.success(res.msg || '审核成功');\n                          _this3.auditVisible = false;\n                          _this3.getList();\n                          _this3.getStatistics();\n                        } else {\n                          _this3.$message.error(res.msg || '审核失败,请查看服务费余额或者账户余额是否足够！');\n                        }\n                        _context3.next = 16;\n                        break;\n                      case 10:\n                        _context3.prev = 10;\n                        _context3.t0 = _context3[\"catch\"](1);\n                        if (!(_context3.t0 === 'cancel')) {\n                          _context3.next = 14;\n                          break;\n                        }\n                        return _context3.abrupt(\"return\");\n                      case 14:\n                        console.error('审核失败:', _context3.t0);\n                        _this3.$message.error(_context3.t0.message || '审核失败');\n                      case 16:\n                      case \"end\":\n                        return _context3.stop();\n                    }\n                  }, _callee3, null, [[1, 10]]);\n                }));\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }());\n            case 1:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }))();\n    },\n    handleExport: function handleExport() {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var params, res, blob, link;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _this4.exportLoading = true;\n\n              // 构建导出参数\n              params = {\n                username: _this4.listQuery.username,\n                status: _this4.listQuery.status,\n                startDate: _this4.listQuery.startDate,\n                endDate: _this4.listQuery.endDate\n              };\n              _context5.next = 5;\n              return exportWithdrawRecord(params);\n            case 5:\n              res = _context5.sent;\n              // 创建blob对象\n              blob = new Blob([res], {\n                type: 'application/vnd.ms-excel'\n              }); // 创建下载链接\n              link = document.createElement('a');\n              link.href = window.URL.createObjectURL(blob);\n              link.download = '提现记录.xlsx';\n\n              // 触发点击下载\n              document.body.appendChild(link);\n              link.click();\n              document.body.removeChild(link);\n              window.URL.revokeObjectURL(link.href);\n              _this4.$message.success('导出成功');\n              _context5.next = 21;\n              break;\n            case 17:\n              _context5.prev = 17;\n              _context5.t0 = _context5[\"catch\"](0);\n              console.error('导出失败:', _context5.t0);\n              _this4.$message.error('导出失败');\n            case 21:\n              _context5.prev = 21;\n              _this4.exportLoading = false;\n              return _context5.finish(21);\n            case 24:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 17, 21, 24]]);\n      }))();\n    }\n  }\n};", "map": {"version": 3, "names": ["getWithdrawList", "getWithdrawStatistics", "auditWithdraw", "exportWithdrawRecord", "name", "data", "loading", "list<PERSON>uery", "page", "limit", "username", "status", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "total", "tableData", "detailVisible", "currentRecord", "auditVisible", "auditForm", "remark", "auditRules", "required", "message", "trigger", "summary", "todayAmount", "todayCount", "pendingAmount", "pendingCount", "totalPassAmount", "exportLoading", "created", "getList", "getStatistics", "methods", "formatNumber", "num", "parseFloat", "toFixed", "replace", "formatDateTime", "datetime", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "getStatusType", "map", "getStatusText", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "records", "$message", "error", "msg", "t0", "console", "finish", "stop", "_this2", "_callee2", "_callee2$", "_context2", "handleSearch", "handleReset", "handleDateRangeChange", "val", "handleSizeChange", "handleCurrentChange", "handleDetail", "row", "_objectSpread", "handleAudit", "submitAudit", "_this3", "_callee4", "_callee4$", "_context4", "$refs", "validate", "_ref", "_callee3", "valid", "_callee3$", "_context3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "id", "success", "abrupt", "_x", "apply", "arguments", "handleExport", "_this4", "_callee5", "params", "blob", "link", "_callee5$", "_context5", "Blob", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL"], "sources": ["src/views/finance/withdraw-record/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card class=\"box-card\">\n      <!-- 数据汇总区域 -->\n      <el-row :gutter=\"20\" class=\"summary-container\">\n        <el-col :span=\"5\">\n          <div class=\"summary-item\">\n            <div class=\"label\">今日提现总额</div>\n            <div class=\"value\">¥{{ formatNumber(summary.todayAmount || 0) }}</div>\n          </div>\n        </el-col>\n        <el-col :span=\"5\">\n          <div class=\"summary-item\">\n            <div class=\"label\">今日提现笔数</div>\n            <div class=\"value\">{{ summary.todayCount || 0 }}笔</div>\n          </div>\n        </el-col>\n        <el-col :span=\"5\">\n          <div class=\"summary-item\">\n            <div class=\"label\">待审核总额</div>\n            <div class=\"value\">¥{{ formatNumber(summary.pendingAmount || 0) }}</div>\n          </div>\n        </el-col>\n        <el-col :span=\"5\">\n          <div class=\"summary-item\">\n            <div class=\"label\">待审核笔数</div>\n            <div class=\"value\">{{ summary.pendingCount || 0 }}笔</div>\n          </div>\n        </el-col>\n        <el-col :span=\"4\">\n          <div class=\"summary-item\">\n            <div class=\"label\">已通过总额</div>\n            <div class=\"value\">¥{{ formatNumber(summary.totalPassAmount || 0) }}</div>\n          </div>\n        </el-col>\n      </el-row>\n\n      <!-- 搜索区域 -->\n      <div class=\"filter-container\">\n        <!-- <el-input\n          v-model=\"listQuery.username\"\n          placeholder=\"用户名/手机号\"\n          style=\"width: 200px;\"\n          class=\"filter-item\"\n          clearable\n          @clear=\"handleSearch\"\n          @keyup.enter.native=\"handleSearch\"\n        /> -->\n        <el-select\n          v-model=\"listQuery.status\"\n          placeholder=\"提现状态\"\n          clearable\n          class=\"filter-item\"\n          style=\"width: 130px\"\n          @change=\"handleSearch\"\n        >\n          <el-option label=\"待审核\" value=\"0\" />\n          <el-option label=\"已通过\" value=\"1\" />\n          <el-option label=\"已拒绝\" value=\"2\" />\n          <el-option label=\"未通过\" value=\"3\" />\n        </el-select>\n        <el-date-picker\n          v-model=\"dateRange\"\n          type=\"daterange\"\n          range-separator=\"至\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          :default-time=\"['00:00:00', '23:59:59']\"\n          class=\"filter-item\"\n          @change=\"handleDateRangeChange\"\n        />\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索</el-button>\n        <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"handleReset\">重置</el-button>\n        <el-button \n          type=\"warning\" \n          icon=\"el-icon-download\" \n          @click=\"handleExport\"\n          :loading=\"exportLoading\"\n        >导出</el-button>\n      </div>\n\n      <!-- 表格区域 -->\n      <el-table\n        v-loading=\"loading\"\n        :data=\"tableData\"\n        border\n        style=\"width: 100%\"\n      >\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n        <!-- <el-table-column label=\"用户ID\" prop=\"userId\" min-width=\"80\" align=\"center\" /> -->\n        <el-table-column label=\"用户名称\" prop=\"username\" min-width=\"120\" align=\"center\" />\n        <el-table-column label=\"注册手机号\" prop=\"registerPhone\" min-width=\"120\" align=\"center\" />\n        <el-table-column label=\"提现金额\" align=\"center\" min-width=\"120\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #f56c6c\">¥{{ formatNumber(scope.row.amount) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"手续费\" align=\"center\" min-width=\"100\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #909399\">¥{{ formatNumber(scope.row.fee) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"实际到账\" align=\"center\" min-width=\"120\">\n          <template slot-scope=\"scope\">\n            <span style=\"color: #67C23A\">¥{{ formatNumber(scope.row.realAmount) }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" min-width=\"100\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getStatusType(scope.row.status)\">\n              {{ getStatusText(scope.row.status) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"申请时间\" min-width=\"160\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ formatDateTime(scope.row.createTime) }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" min-width=\"120\" align=\"center\" fixed=\"right\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"handleDetail(scope.row)\">详情</el-button>\n            <el-button \n              v-if=\"scope.row.status === 0\"\n              type=\"text\" \n              @click=\"handleAudit(scope.row)\"\n            >\n              审核\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页区域 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"listQuery.page\"\n          :page-sizes=\"[10, 20, 30, 50]\"\n          :page-size=\"listQuery.limit\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"total\">\n        </el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 详情对话框 -->\n    <el-dialog title=\"提现详情\" :visible.sync=\"detailVisible\" width=\"700px\">\n      <el-card class=\"detail-card\">\n        <div slot=\"header\">\n          <span class=\"card-title\">用户信息</span>\n        </div>\n        <el-descriptions :column=\"1\" border>\n          <!-- <el-descriptions-item label=\"用户ID\" width=\"180px\">{{ currentRecord.userId }}</el-descriptions-item> -->\n          <!-- <el-descriptions-item label=\"用户名\">{{ currentRecord.username }}</el-descriptions-item> -->\n          <el-descriptions-item label=\"注册手机号\">{{ currentRecord.registerPhone }}</el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <el-card class=\"detail-card\">\n        <div slot=\"header\">\n          <span class=\"card-title\">提现信息</span>\n        </div>\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"提现金额\">\n            <span class=\"amount-text\">¥{{ formatNumber(currentRecord.amount) }}</span>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"手续费\">\n            <span class=\"fee-text\">¥{{ formatNumber(currentRecord.fee) }}</span>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"实际到账\">\n            <span class=\"real-amount-text\">¥{{ formatNumber(currentRecord.realAmount) }}</span>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"状态\">\n            <el-tag :type=\"getStatusType(currentRecord.status)\">\n              {{ getStatusText(currentRecord.status) }}\n            </el-tag>\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <el-card class=\"detail-card\">\n        <div slot=\"header\">\n          <span class=\"card-title\">银行卡信息</span>\n        </div>\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"银行名称\">{{ currentRecord.bankName }}</el-descriptions-item>\n          <el-descriptions-item label=\"开户支行\">{{ currentRecord.bankBranch }}</el-descriptions-item>\n          <el-descriptions-item label=\"银行卡号\">{{ currentRecord.bankCard }}</el-descriptions-item>\n          <el-descriptions-item label=\"持卡人\">{{ currentRecord.cardHolder }}</el-descriptions-item>\n          <el-descriptions-item label=\"身份证号\">{{ currentRecord.idCard }}</el-descriptions-item>\n          <el-descriptions-item label=\"预留手机号\">{{ currentRecord.phone }}</el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <el-card class=\"detail-card\">\n        <div slot=\"header\">\n          <span class=\"card-title\">其他信息</span>\n        </div>\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"申请时间\">{{ formatDateTime(currentRecord.createTime) }}</el-descriptions-item>\n          <el-descriptions-item label=\"更新时间\">{{ formatDateTime(currentRecord.updateTime) }}</el-descriptions-item>\n          <el-descriptions-item label=\"备注\" :span=\"2\">{{ currentRecord.remark || '-' }}</el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n    </el-dialog>\n\n    <!-- 审核对话框 -->\n    <el-dialog title=\"提现审核\" :visible.sync=\"auditVisible\" width=\"500px\">\n      <el-form ref=\"auditForm\" :model=\"auditForm\" :rules=\"auditRules\" label-width=\"80px\">\n        <el-form-item label=\"审核结果\" prop=\"status\">\n          <el-radio-group v-model=\"auditForm.status\">\n            <el-radio :label=\"1\">通过</el-radio>\n            <el-radio :label=\"2\">拒绝</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"审核意见\" prop=\"remark\">\n          <el-input \n            type=\"textarea\" \n            v-model=\"auditForm.remark\"\n            :rows=\"3\"\n            placeholder=\"请输入审核意见\"\n          />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"auditVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getWithdrawList, getWithdrawStatistics, auditWithdraw, exportWithdrawRecord } from '@/api/finance/withdraw'\n\nexport default {\n  name: 'FinanceWithdrawRecord',\n  data() {\n    return {\n      loading: false,\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        status: '',\n        startDate: '',\n        endDate: ''\n      },\n      dateRange: [],\n      total: 0,\n      tableData: [],\n      // 详情相关\n      detailVisible: false,\n      currentRecord: {},\n      // 审核相关\n      auditVisible: false,\n      auditForm: {\n        status: 1,\n        remark: ''\n      },\n      // ,\n      //   remark: [\n      //     { required: true, message: '请输入审核意见', trigger: 'blur' }\n      //   ]\n      auditRules: {\n        status: [\n          { required: true, message: '请选择审核结果', trigger: 'change' }\n        ]\n      },\n      // 汇总数据\n      summary: {\n        todayAmount: 0,\n        todayCount: 0,\n        pendingAmount: 0,\n        pendingCount: 0,\n        totalPassAmount: 0\n      },\n      exportLoading: false\n    }\n  },\n  created() {\n    this.getList()\n    this.getStatistics()\n  },\n  methods: {\n    formatNumber(num) {\n      if (!num && num !== 0) return '0.00'\n      return parseFloat(num).toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\n    },\n    formatDateTime(datetime) {\n      if (!datetime) return '-'\n      const date = new Date(datetime)\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      const seconds = String(date.getSeconds()).padStart(2, '0')\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\n    },\n    getStatusType(status) {\n      const map = {\n        0: 'info',    // 待审核\n        1: 'success', // 已通过\n        2: 'danger',  // 已拒绝\n        3: 'warning'  // 未通过\n      }\n      return map[status] || 'info'\n    },\n    getStatusText(status) {\n      const map = {\n        0: '待审核',\n        1: '已通过',\n        2: '已拒绝',\n        3: '未通过'\n      }\n      return map[status] || '未知'\n    },\n    async getList() {\n      this.loading = true\n      try {\n        const res = await getWithdrawList(this.listQuery)\n        if (res.code === 0) {\n          this.tableData = res.records || []\n          this.total = res.total || 0\n        } else {\n          this.$message.error(res.msg || '获取数据失败')\n        }\n      } catch (error) {\n        console.error('获取提现记录失败:', error)\n        this.$message.error('获取数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    async getStatistics() {\n      try {\n        const res = await getWithdrawStatistics()\n        if (res.code === 0) {\n          this.summary = res.data || {\n            todayAmount: 0,\n            todayCount: 0,\n            pendingAmount: 0,\n            pendingCount: 0,\n            totalPassAmount: 0\n          }\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error)\n      }\n    },\n    handleSearch() {\n      this.listQuery.page = 1\n      this.getList()\n    },\n    handleReset() {\n      this.dateRange = []\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        status: '',\n        startDate: '',\n        endDate: ''\n      }\n      this.getList()\n    },\n    handleDateRangeChange(val) {\n      if (val) {\n        this.listQuery.startDate = val[0]\n        this.listQuery.endDate = val[1]\n      } else {\n        this.listQuery.startDate = ''\n        this.listQuery.endDate = ''\n      }\n      this.handleSearch()\n    },\n    handleSizeChange(val) {\n      this.listQuery.limit = val\n      this.getList()\n    },\n    handleCurrentChange(val) {\n      this.listQuery.page = val\n      this.getList()\n    },\n    handleDetail(row) {\n      this.currentRecord = { ...row }\n      this.detailVisible = true\n    },\n    handleAudit(row) {\n      this.currentRecord = row\n      this.auditForm = {\n        status: 1,\n        remark: ''\n      }\n      this.auditVisible = true\n    },\n    async submitAudit() {\n      this.$refs.auditForm.validate(async valid => {\n        if (valid) {\n          try {\n            await this.$confirm(\n              `确认${this.auditForm.status === 1 ? '通过' : '拒绝'}该提现申请吗？`, \n              '提示', \n              {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n              }\n            )\n            \n            const res = await auditWithdraw(this.currentRecord.id, {\n              status: this.auditForm.status,\n              remark: this.auditForm.remark\n            })\n\n            if (res.code === 0) {\n              this.$message.success(res.msg || '审核成功')\n              this.auditVisible = false\n              this.getList()\n              this.getStatistics()\n            } else {\n              this.$message.error(res.msg || '审核失败,请查看服务费余额或者账户余额是否足够！')\n            }\n          } catch (error) {\n            if (error === 'cancel') return\n            console.error('审核失败:', error)\n            this.$message.error(error.message || '审核失败')\n          }\n        }\n      })\n    },\n    async handleExport() {\n      try {\n        this.exportLoading = true\n        \n        // 构建导出参数\n        const params = {\n          username: this.listQuery.username,\n          status: this.listQuery.status,\n          startDate: this.listQuery.startDate,\n          endDate: this.listQuery.endDate\n        }\n\n        const res = await exportWithdrawRecord(params)\n        \n        // 创建blob对象\n        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })\n        \n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = '提现记录.xlsx'\n        \n        // 触发点击下载\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(link.href)\n\n        this.$message.success('导出成功')\n      } catch (error) {\n        console.error('导出失败:', error)\n        this.$message.error('导出失败')\n      } finally {\n        this.exportLoading = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-container {\n  padding: 20px;\n\n  .summary-container {\n    margin-bottom: 20px;\n    \n    .summary-item {\n      background: #fff;\n      padding: 20px;\n      border-radius: 4px;\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n      text-align: center;\n\n      .label {\n        color: #909399;\n        font-size: 14px;\n        margin-bottom: 10px;\n      }\n\n      .value {\n        color: #303133;\n        font-size: 24px;\n        font-weight: bold;\n      }\n    }\n  }\n\n  .filter-container {\n    padding-bottom: 20px;\n    .filter-item {\n      margin-right: 10px;\n    }\n  }\n\n  .pagination-container {\n    padding: 20px 0;\n    text-align: right;\n  }\n}\n\n.detail-card {\n  margin-bottom: 15px;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n\n  .card-title {\n    font-size: 15px;\n    font-weight: bold;\n    color: #303133;\n  }\n\n  ::v-deep .el-card__header {\n    padding: 10px 15px;\n    border-bottom: 1px solid #EBEEF5;\n    background-color: #F5F7FA;\n  }\n\n  ::v-deep .el-card__body {\n    padding: 15px;\n  }\n\n  .amount-text {\n    color: #f56c6c;\n    font-weight: bold;\n  }\n\n  .fee-text {\n    color: #909399;\n  }\n\n  .real-amount-text {\n    color: #67C23A;\n    font-weight: bold;\n  }\n}\n\n::v-deep .el-dialog__body {\n  padding: 20px;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;AA6OA,SAAAA,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACAC,SAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,aAAA;MACAC,aAAA;MACA;MACAC,YAAA;MACAC,SAAA;QACAT,MAAA;QACAU,MAAA;MACA;MACA;MACA;MACA;MACA;MACAC,UAAA;QACAX,MAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,OAAA;QACAC,WAAA;QACAC,UAAA;QACAC,aAAA;QACAC,YAAA;QACAC,eAAA;MACA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAA,GAAA,IAAAA,GAAA;MACA,OAAAC,UAAA,CAAAD,GAAA,EAAAE,OAAA,IAAAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,QAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IACAG,aAAA,WAAAA,cAAAlD,MAAA;MACA,IAAAmD,GAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,GAAA,CAAAnD,MAAA;IACA;IACAoD,aAAA,WAAAA,cAAApD,MAAA;MACA,IAAAmD,GAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAAnD,MAAA;IACA;IACAuB,OAAA,WAAAA,QAAA;MAAA,IAAA8B,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAA1D,OAAA;cAAAkE,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEA1E,eAAA,CAAAgE,KAAA,CAAAzD,SAAA;YAAA;cAAA8D,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAZ,KAAA,CAAAhD,SAAA,GAAAqD,GAAA,CAAAQ,OAAA;gBACAb,KAAA,CAAAjD,KAAA,GAAAsD,GAAA,CAAAtD,KAAA;cACA;gBACAiD,KAAA,CAAAc,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,cAAAP,QAAA,CAAAS,EAAA;cACAjB,KAAA,CAAAc,QAAA,CAAAC,KAAA;YAAA;cAAAP,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAA1D,OAAA;cAAA,OAAAkE,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IAEA;IACAjC,aAAA,WAAAA,cAAA;MAAA,IAAAkD,MAAA;MAAA,OAAApB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmB,SAAA;QAAA,IAAAjB,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cAAAc,SAAA,CAAAf,IAAA;cAAAe,SAAA,CAAAd,IAAA;cAAA,OAEAzE,qBAAA;YAAA;cAAAoE,GAAA,GAAAmB,SAAA,CAAAb,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAS,MAAA,CAAA3D,OAAA,GAAA2C,GAAA,CAAAhE,IAAA;kBACAsB,WAAA;kBACAC,UAAA;kBACAC,aAAA;kBACAC,YAAA;kBACAC,eAAA;gBACA;cACA;cAAAyD,SAAA,CAAAd,IAAA;cAAA;YAAA;cAAAc,SAAA,CAAAf,IAAA;cAAAe,SAAA,CAAAP,EAAA,GAAAO,SAAA;cAEAN,OAAA,CAAAH,KAAA,cAAAS,SAAA,CAAAP,EAAA;YAAA;YAAA;cAAA,OAAAO,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IACAG,YAAA,WAAAA,aAAA;MACA,KAAAlF,SAAA,CAAAC,IAAA;MACA,KAAA0B,OAAA;IACA;IACAwD,WAAA,WAAAA,YAAA;MACA,KAAA5E,SAAA;MACA,KAAAP,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA,KAAAqB,OAAA;IACA;IACAyD,qBAAA,WAAAA,sBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAArF,SAAA,CAAAK,SAAA,GAAAgF,GAAA;QACA,KAAArF,SAAA,CAAAM,OAAA,GAAA+E,GAAA;MACA;QACA,KAAArF,SAAA,CAAAK,SAAA;QACA,KAAAL,SAAA,CAAAM,OAAA;MACA;MACA,KAAA4E,YAAA;IACA;IACAI,gBAAA,WAAAA,iBAAAD,GAAA;MACA,KAAArF,SAAA,CAAAE,KAAA,GAAAmF,GAAA;MACA,KAAA1D,OAAA;IACA;IACA4D,mBAAA,WAAAA,oBAAAF,GAAA;MACA,KAAArF,SAAA,CAAAC,IAAA,GAAAoF,GAAA;MACA,KAAA1D,OAAA;IACA;IACA6D,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAA9E,aAAA,GAAA+E,aAAA,KAAAD,GAAA;MACA,KAAA/E,aAAA;IACA;IACAiF,WAAA,WAAAA,YAAAF,GAAA;MACA,KAAA9E,aAAA,GAAA8E,GAAA;MACA,KAAA5E,SAAA;QACAT,MAAA;QACAU,MAAA;MACA;MACA,KAAAF,YAAA;IACA;IACAgF,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAAnC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkC,SAAA;QAAA,OAAAnC,mBAAA,GAAAI,IAAA,UAAAgC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,IAAA,GAAA8B,SAAA,CAAA7B,IAAA;YAAA;cACA0B,MAAA,CAAAI,KAAA,CAAApF,SAAA,CAAAqF,QAAA;gBAAA,IAAAC,IAAA,GAAAzC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwC,SAAAC,KAAA;kBAAA,IAAAvC,GAAA;kBAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuC,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;sBAAA;wBAAA,KACAkC,KAAA;0BAAAE,SAAA,CAAApC,IAAA;0BAAA;wBAAA;wBAAAoC,SAAA,CAAArC,IAAA;wBAAAqC,SAAA,CAAApC,IAAA;wBAAA,OAEA0B,MAAA,CAAAW,QAAA,gBAAAnD,MAAA,CACAwC,MAAA,CAAAhF,SAAA,CAAAT,MAAA,qEACA,MACA;0BACAqG,iBAAA;0BACAC,gBAAA;0BACAC,IAAA;wBACA,CACA;sBAAA;wBAAAJ,SAAA,CAAApC,IAAA;wBAAA,OAEAxE,aAAA,CAAAkG,MAAA,CAAAlF,aAAA,CAAAiG,EAAA;0BACAxG,MAAA,EAAAyF,MAAA,CAAAhF,SAAA,CAAAT,MAAA;0BACAU,MAAA,EAAA+E,MAAA,CAAAhF,SAAA,CAAAC;wBACA;sBAAA;wBAHAgD,GAAA,GAAAyC,SAAA,CAAAnC,IAAA;wBAKA,IAAAN,GAAA,CAAAO,IAAA;0BACAwB,MAAA,CAAAtB,QAAA,CAAAsC,OAAA,CAAA/C,GAAA,CAAAW,GAAA;0BACAoB,MAAA,CAAAjF,YAAA;0BACAiF,MAAA,CAAAlE,OAAA;0BACAkE,MAAA,CAAAjE,aAAA;wBACA;0BACAiE,MAAA,CAAAtB,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;wBACA;wBAAA8B,SAAA,CAAApC,IAAA;wBAAA;sBAAA;wBAAAoC,SAAA,CAAArC,IAAA;wBAAAqC,SAAA,CAAA7B,EAAA,GAAA6B,SAAA;wBAAA,MAEAA,SAAA,CAAA7B,EAAA;0BAAA6B,SAAA,CAAApC,IAAA;0BAAA;wBAAA;wBAAA,OAAAoC,SAAA,CAAAO,MAAA;sBAAA;wBACAnC,OAAA,CAAAH,KAAA,UAAA+B,SAAA,CAAA7B,EAAA;wBACAmB,MAAA,CAAAtB,QAAA,CAAAC,KAAA,CAAA+B,SAAA,CAAA7B,EAAA,CAAAzD,OAAA;sBAAA;sBAAA;wBAAA,OAAAsF,SAAA,CAAA1B,IAAA;oBAAA;kBAAA,GAAAuB,QAAA;gBAAA,CAGA;gBAAA,iBAAAW,EAAA;kBAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAAjB,SAAA,CAAAnB,IAAA;UAAA;QAAA,GAAAiB,QAAA;MAAA;IACA;IACAoB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAzD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwD,SAAA;QAAA,IAAAC,MAAA,EAAAvD,GAAA,EAAAwD,IAAA,EAAAC,IAAA;QAAA,OAAA5D,mBAAA,GAAAI,IAAA,UAAAyD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvD,IAAA,GAAAuD,SAAA,CAAAtD,IAAA;YAAA;cAAAsD,SAAA,CAAAvD,IAAA;cAEAiD,MAAA,CAAA1F,aAAA;;cAEA;cACA4F,MAAA;gBACAlH,QAAA,EAAAgH,MAAA,CAAAnH,SAAA,CAAAG,QAAA;gBACAC,MAAA,EAAA+G,MAAA,CAAAnH,SAAA,CAAAI,MAAA;gBACAC,SAAA,EAAA8G,MAAA,CAAAnH,SAAA,CAAAK,SAAA;gBACAC,OAAA,EAAA6G,MAAA,CAAAnH,SAAA,CAAAM;cACA;cAAAmH,SAAA,CAAAtD,IAAA;cAAA,OAEAvE,oBAAA,CAAAyH,MAAA;YAAA;cAAAvD,GAAA,GAAA2D,SAAA,CAAArD,IAAA;cAEA;cACAkD,IAAA,OAAAI,IAAA,EAAA5D,GAAA;gBAAA6C,IAAA;cAAA,IAEA;cACAY,IAAA,GAAAI,QAAA,CAAAC,aAAA;cACAL,IAAA,CAAAM,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAV,IAAA;cACAC,IAAA,CAAAU,QAAA;;cAEA;cACAN,QAAA,CAAAO,IAAA,CAAAC,WAAA,CAAAZ,IAAA;cACAA,IAAA,CAAAa,KAAA;cACAT,QAAA,CAAAO,IAAA,CAAAG,WAAA,CAAAd,IAAA;cACAO,MAAA,CAAAC,GAAA,CAAAO,eAAA,CAAAf,IAAA,CAAAM,IAAA;cAEAV,MAAA,CAAA5C,QAAA,CAAAsC,OAAA;cAAAY,SAAA,CAAAtD,IAAA;cAAA;YAAA;cAAAsD,SAAA,CAAAvD,IAAA;cAAAuD,SAAA,CAAA/C,EAAA,GAAA+C,SAAA;cAEA9C,OAAA,CAAAH,KAAA,UAAAiD,SAAA,CAAA/C,EAAA;cACAyC,MAAA,CAAA5C,QAAA,CAAAC,KAAA;YAAA;cAAAiD,SAAA,CAAAvD,IAAA;cAEAiD,MAAA,CAAA1F,aAAA;cAAA,OAAAgG,SAAA,CAAA7C,MAAA;YAAA;YAAA;cAAA,OAAA6C,SAAA,CAAA5C,IAAA;UAAA;QAAA,GAAAuC,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}