{"ast": null, "code": "import _regeneratorRuntime from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport { getTeamTopology } from '@/api/user/user';\nexport default {\n  name: 'UserTopology',\n  data: function data() {\n    return {\n      searchQuery: '',\n      treeHeight: 400,\n      expandedKeys: [1],\n      defaultProps: {\n        children: 'children',\n        label: 'label'\n      },\n      treeData: [] // 初始化为空数组\n    };\n  },\n  created: function created() {\n    var _this = this;\n    return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return _this.getTopologyData();\n          case 2:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }))();\n  },\n  methods: {\n    // 获取拓扑数据\n    getTopologyData: function getTopologyData() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return getTeamTopology();\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this2.treeData = res.data || [];\n                // 默认展开第一级\n                if (_this2.treeData.length > 0) {\n                  _this2.expandedKeys = [_this2.treeData[0].id];\n                }\n              } else {\n                _this2.$message.error(res.msg || '获取团队结构失败');\n              }\n              _context2.next = 11;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              console.error('获取团队结构失败:', _context2.t0);\n              _this2.$message.error('获取团队结构失败');\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    filterNode: function filterNode(value, data) {\n      if (!value) return true;\n      return data.label.toLowerCase().includes(value.toLowerCase());\n    },\n    handleSearch: function handleSearch() {\n      this.$refs.tree.filter(this.searchQuery);\n    },\n    // 设置树的高度\n    setTreeHeight: function setTreeHeight() {\n      // 获取视窗高度\n      var windowHeight = window.innerHeight;\n      // 减去其他元素的高度（头部导航、搜索框等）\n      // 200 = 头部导航(60) + 页面padding(40) + 卡片padding(40) + 搜索区域(60)\n      var otherHeight = 200;\n      // 设置最小高度\n      this.treeHeight = Math.max(400, windowHeight - otherHeight);\n    },\n    handleNodeClick: function handleNodeClick(data) {\n      if (data.children && data.children.length > 0) {\n        var isExpanded = this.expandedKeys.includes(data.id);\n        if (isExpanded) {\n          // 如果已展开，则收起\n          this.expandedKeys = this.expandedKeys.filter(function (key) {\n            return key !== data.id;\n          });\n        } else {\n          // 如果未展开，则展开\n          this.expandedKeys.push(data.id);\n        }\n      }\n    },\n    formatNumber: function formatNumber(num) {\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    }\n  }\n};", "map": {"version": 3, "names": ["getTeamTopology", "name", "data", "searchQuery", "treeHeight", "expandedKeys", "defaultProps", "children", "label", "treeData", "created", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getTopologyData", "stop", "methods", "_this2", "_callee2", "res", "_callee2$", "_context2", "sent", "code", "length", "id", "$message", "error", "msg", "t0", "console", "filterNode", "value", "toLowerCase", "includes", "handleSearch", "$refs", "tree", "filter", "setTreeHeight", "windowHeight", "window", "innerHeight", "otherHeight", "Math", "max", "handleNodeClick", "isExpanded", "key", "push", "formatNumber", "num", "toString", "replace"], "sources": ["src/views/user/topology/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-input\r\n          v-model=\"searchQuery\"\r\n          placeholder=\"搜索用户名\"\r\n          style=\"width: 200px\"\r\n          class=\"filter-item\"\r\n          @input=\"handleSearch\"\r\n        />\r\n      </div>\r\n\r\n      <!-- 树形结构区域 -->\r\n      <el-tree\r\n        ref=\"tree\"\r\n        :data=\"treeData\"\r\n        :props=\"defaultProps\"\r\n        node-key=\"id\"\r\n        :default-expanded-keys=\"expandedKeys\"\r\n        :expand-on-click-node=\"false\"\r\n        :filter-node-method=\"filterNode\"\r\n        class=\"filter-tree\"\r\n        :style=\"{ minHeight: treeHeight + 'px' }\"\r\n      >\r\n        <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n          <span @click=\"handleNodeClick(data)\">\r\n            <span class=\"node-name\">{{ data.name }}</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-phone\">{{ data.phone }}</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-level\">{{ data.level }}</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-performance\">¥{{ formatNumber(data.performance) }}</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-devices\" style=\"color: #67C23A\">{{ data.totalBalance }}台</span>\r\n            <span class=\"node-divider\">|</span>\r\n            <span class=\"node-today\" style=\"color: #409EFF\">+{{ data.todayNewBalance }}台</span>\r\n          </span>\r\n        </span>\r\n      </el-tree>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTeamTopology } from '@/api/user/user'\r\n\r\nexport default {\r\n  name: 'UserTopology',\r\n  data() {\r\n    return {\r\n      searchQuery: '',\r\n      treeHeight: 400,\r\n      expandedKeys: [1],\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      treeData: []  // 初始化为空数组\r\n    }\r\n  },\r\n  \r\n  async created() {\r\n    await this.getTopologyData()\r\n  },\r\n  \r\n  methods: {\r\n    // 获取拓扑数据\r\n    async getTopologyData() {\r\n      try {\r\n        const res = await getTeamTopology()\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.treeData = res.data || []\r\n          // 默认展开第一级\r\n          if (this.treeData.length > 0) {\r\n            this.expandedKeys = [this.treeData[0].id]\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '获取团队结构失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取团队结构失败:', error)\r\n        this.$message.error('获取团队结构失败')\r\n      }\r\n    },\r\n\r\n    filterNode(value, data) {\r\n      if (!value) return true\r\n      return data.label.toLowerCase().includes(value.toLowerCase())\r\n    },\r\n    handleSearch() {\r\n      this.$refs.tree.filter(this.searchQuery)\r\n    },\r\n    // 设置树的高度\r\n    setTreeHeight() {\r\n      // 获取视窗高度\r\n      const windowHeight = window.innerHeight\r\n      // 减去其他元素的高度（头部导航、搜索框等）\r\n      // 200 = 头部导航(60) + 页面padding(40) + 卡片padding(40) + 搜索区域(60)\r\n      const otherHeight = 200\r\n      // 设置最小高度\r\n      this.treeHeight = Math.max(400, windowHeight - otherHeight)\r\n    },\r\n    handleNodeClick(data) {\r\n      if (data.children && data.children.length > 0) {\r\n        const isExpanded = this.expandedKeys.includes(data.id)\r\n        if (isExpanded) {\r\n          // 如果已展开，则收起\r\n          this.expandedKeys = this.expandedKeys.filter(key => key !== data.id)\r\n        } else {\r\n          // 如果未展开，则展开\r\n          this.expandedKeys.push(data.id)\r\n        }\r\n      }\r\n    },\r\n    formatNumber(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n  height: 100%;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    .filter-item {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .filter-tree {\r\n    margin-top: 20px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .custom-tree-node {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    font-size: 14px;\r\n    padding-right: 8px;\r\n\r\n    > span {\r\n      cursor: pointer;\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n\r\n      &:hover {\r\n        .node-name {\r\n          color: #409EFF;\r\n        }\r\n      }\r\n\r\n      .node-name {\r\n        font-weight: bold;\r\n        color: #303133;\r\n      }\r\n\r\n      .node-divider {\r\n        color: #DCDFE6;\r\n        font-weight: normal;\r\n      }\r\n\r\n      .node-phone {\r\n        color: #606266;\r\n        font-family: Consolas, monospace;\r\n      }\r\n\r\n      .node-level {\r\n        color: #409EFF;\r\n        background-color: #ecf5ff;\r\n        padding: 2px 6px;\r\n        border-radius: 4px;\r\n        font-size: 12px;\r\n      }\r\n\r\n      .node-performance {\r\n        color: #67C23A;\r\n        font-weight: bold;\r\n        font-family: Consolas, monospace;\r\n      }\r\n\r\n      .node-devices {\r\n        font-weight: bold;\r\n        color: #67C23A;\r\n      }\r\n\r\n      .node-today {\r\n        font-weight: bold;\r\n        color: #409EFF;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 修改 el-card 样式以支持全高度\r\n::v-deep .box-card {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .el-card__body {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;AA+CA,SAAAA,eAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,UAAA;MACAC,YAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,QAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;MAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,eAAA;UAAA;UAAA;YAAA,OAAAH,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAP,OAAA;IAAA;EACA;EAEAQ,OAAA;IACA;IACAF,eAAA,WAAAA,gBAAA;MAAA,IAAAG,MAAA;MAAA,OAAAZ,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAW,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAb,mBAAA,GAAAG,IAAA,UAAAW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAT,IAAA,GAAAS,SAAA,CAAAR,IAAA;YAAA;cAAAQ,SAAA,CAAAT,IAAA;cAAAS,SAAA,CAAAR,IAAA;cAAA,OAEApB,eAAA;YAAA;cAAA0B,GAAA,GAAAE,SAAA,CAAAC,IAAA;cACA,IAAAH,GAAA,CAAAI,IAAA,UAAAJ,GAAA,CAAAI,IAAA;gBACAN,MAAA,CAAAf,QAAA,GAAAiB,GAAA,CAAAxB,IAAA;gBACA;gBACA,IAAAsB,MAAA,CAAAf,QAAA,CAAAsB,MAAA;kBACAP,MAAA,CAAAnB,YAAA,IAAAmB,MAAA,CAAAf,QAAA,IAAAuB,EAAA;gBACA;cACA;gBACAR,MAAA,CAAAS,QAAA,CAAAC,KAAA,CAAAR,GAAA,CAAAS,GAAA;cACA;cAAAP,SAAA,CAAAR,IAAA;cAAA;YAAA;cAAAQ,SAAA,CAAAT,IAAA;cAAAS,SAAA,CAAAQ,EAAA,GAAAR,SAAA;cAEAS,OAAA,CAAAH,KAAA,cAAAN,SAAA,CAAAQ,EAAA;cACAZ,MAAA,CAAAS,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAN,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IAEA;IAEAa,UAAA,WAAAA,WAAAC,KAAA,EAAArC,IAAA;MACA,KAAAqC,KAAA;MACA,OAAArC,IAAA,CAAAM,KAAA,CAAAgC,WAAA,GAAAC,QAAA,CAAAF,KAAA,CAAAC,WAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,MAAA1C,WAAA;IACA;IACA;IACA2C,aAAA,WAAAA,cAAA;MACA;MACA,IAAAC,YAAA,GAAAC,MAAA,CAAAC,WAAA;MACA;MACA;MACA,IAAAC,WAAA;MACA;MACA,KAAA9C,UAAA,GAAA+C,IAAA,CAAAC,GAAA,MAAAL,YAAA,GAAAG,WAAA;IACA;IACAG,eAAA,WAAAA,gBAAAnD,IAAA;MACA,IAAAA,IAAA,CAAAK,QAAA,IAAAL,IAAA,CAAAK,QAAA,CAAAwB,MAAA;QACA,IAAAuB,UAAA,QAAAjD,YAAA,CAAAoC,QAAA,CAAAvC,IAAA,CAAA8B,EAAA;QACA,IAAAsB,UAAA;UACA;UACA,KAAAjD,YAAA,QAAAA,YAAA,CAAAwC,MAAA,WAAAU,GAAA;YAAA,OAAAA,GAAA,KAAArD,IAAA,CAAA8B,EAAA;UAAA;QACA;UACA;UACA,KAAA3B,YAAA,CAAAmD,IAAA,CAAAtD,IAAA,CAAA8B,EAAA;QACA;MACA;IACA;IACAyB,YAAA,WAAAA,aAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,QAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}