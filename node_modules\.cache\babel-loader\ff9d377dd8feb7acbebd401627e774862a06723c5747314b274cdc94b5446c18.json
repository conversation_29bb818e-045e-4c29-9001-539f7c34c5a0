{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.number.to-fixed.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { getRechargeList, getRechargeStatistics, auditRecharge, exportRechargeRecord } from '@/api/finance/recharge';\nexport default {\n  name: 'RechargeRecord',\n  data: function data() {\n    return {\n      loading: false,\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        phone: '',\n        rechargeType: '',\n        auditStatus: '',\n        startDate: '',\n        endDate: ''\n      },\n      dateRange: [],\n      total: 0,\n      statistics: {\n        adminTotal: 0,\n        userTotal: 0,\n        pendingTotal: 0\n      },\n      tableData: [],\n      voucherDialogVisible: false,\n      currentVoucher: '',\n      auditDialogVisible: false,\n      auditForm: {\n        id: null,\n        auditStatus: 1,\n        remark: ''\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    convertImage: function convertImage(src) {\n      return 'https://frontapi.huatongyun666.com' + src;\n    },\n    formatNumber: function formatNumber(num) {\n      if (!num) return '0.00';\n      return num.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    },\n    getStatusType: function getStatusType(status) {\n      var types = {\n        0: 'warning',\n        1: 'success',\n        2: 'danger'\n      };\n      return types[status] || 'info';\n    },\n    getStatusText: function getStatusText(status) {\n      var texts = {\n        0: '待审核',\n        1: '已通过',\n        2: '已拒绝'\n      };\n      return texts[status] || '未知';\n    },\n    handleDateRangeChange: function handleDateRangeChange(val) {\n      if (val) {\n        this.listQuery.startDate = val[0];\n        this.listQuery.endDate = val[1];\n      } else {\n        this.listQuery.startDate = '';\n        this.listQuery.endDate = '';\n      }\n      this.handleSearch();\n    },\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              _context.next = 4;\n              return getRechargeList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0) {\n                if (res.data) {\n                  _this.tableData = res.data || [];\n                  _this.total = res.total || 0;\n                }\n                // 如果没有数据，显示提示\n                if (_this.tableData.length === 0) {\n                  _this.$message.info('暂无数据');\n                }\n              } else {\n                // this.$message.error(res.msg || '获取充值记录失败')\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取充值记录失败:', _context.t0);\n              _this.$message.error('获取充值记录失败');\n            case 12:\n              _context.prev = 12;\n              _this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 8, 12, 15]]);\n      }))();\n    },\n    getStatistics: function getStatistics() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res, _res$data, _res$data$adminTotal, adminTotal, _res$data$userTotal, userTotal, _res$data$pendingTota, pendingTotal;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return getRechargeStatistics();\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0 && res.data) {\n                _res$data = res.data, _res$data$adminTotal = _res$data.adminTotal, adminTotal = _res$data$adminTotal === void 0 ? 0 : _res$data$adminTotal, _res$data$userTotal = _res$data.userTotal, userTotal = _res$data$userTotal === void 0 ? 0 : _res$data$userTotal, _res$data$pendingTota = _res$data.pendingTotal, pendingTotal = _res$data$pendingTota === void 0 ? 0 : _res$data$pendingTota;\n                _this2.statistics = {\n                  adminTotal: adminTotal,\n                  userTotal: userTotal,\n                  pendingTotal: pendingTotal\n                };\n              } else {\n                // this.$message.error(res.msg || '获取统计数据失败')\n              }\n              _context2.next = 11;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              console.error('获取统计数据失败:', _context2.t0);\n              _this2.$message.error('获取统计数据失败');\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    handleSearch: function handleSearch() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    handleReset: function handleReset() {\n      this.dateRange = [];\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        phone: '',\n        rechargeType: '',\n        auditStatus: '',\n        startDate: '',\n        endDate: ''\n      };\n      this.getList();\n    },\n    handleViewVoucher: function handleViewVoucher(row) {\n      this.currentVoucher = row.proofImage;\n      this.voucherDialogVisible = true;\n    },\n    handleAudit: function handleAudit(row) {\n      this.auditForm = {\n        id: row.id,\n        auditStatus: 1,\n        remark: ''\n      };\n      this.auditDialogVisible = true;\n    },\n    submitAudit: function submitAudit() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return auditRecharge(_this3.auditForm.id, {\n                auditStatus: _this3.auditForm.auditStatus,\n                remark: _this3.auditForm.remark\n              });\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0) {\n                _this3.$message.success('审核操作成功');\n                _this3.auditDialogVisible = false;\n                _this3.getList();\n                _this3.getStatistics();\n              } else {\n                _this3.$message.error(res.msg || '审核失败');\n              }\n              _context3.next = 11;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              console.error('审核失败:', _context3.t0);\n              _this3.$message.error('审核失败');\n            case 11:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '-';\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    handleExport: function handleExport() {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              try {\n                _this4.$confirm('确认导出充值记录?', '提示', {\n                  confirmButtonText: '确定',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n                  var params, res, blob, fileName, link;\n                  return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n                    while (1) switch (_context4.prev = _context4.next) {\n                      case 0:\n                        // 构建导出参数，使用当前的筛选条件\n                        params = {\n                          username: _this4.listQuery.username,\n                          phone: _this4.listQuery.phone,\n                          rechargeType: _this4.listQuery.rechargeType,\n                          auditStatus: _this4.listQuery.auditStatus,\n                          startDate: _this4.listQuery.startDate,\n                          endDate: _this4.listQuery.endDate\n                        };\n                        _context4.next = 3;\n                        return exportRechargeRecord(params);\n                      case 3:\n                        res = _context4.sent;\n                        blob = new Blob([res], {\n                          type: 'application/vnd.ms-excel'\n                        });\n                        fileName = \"\\u5145\\u503C\\u8BB0\\u5F55_\".concat(new Date().getTime(), \".xlsx\");\n                        if ('download' in document.createElement('a')) {\n                          link = document.createElement('a');\n                          link.download = fileName;\n                          link.style.display = 'none';\n                          link.href = URL.createObjectURL(blob);\n                          document.body.appendChild(link);\n                          link.click();\n                          URL.revokeObjectURL(link.href);\n                          document.body.removeChild(link);\n                        } else {\n                          navigator.msSaveBlob(blob, fileName);\n                        }\n                        _this4.$message.success('导出成功');\n                      case 8:\n                      case \"end\":\n                        return _context4.stop();\n                    }\n                  }, _callee4);\n                })));\n              } catch (error) {\n                console.error('导出失败:', error);\n                _this4.$message.error('导出失败');\n              }\n            case 1:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }))();\n    }\n  }\n};", "map": {"version": 3, "names": ["getRechargeList", "getRechargeStatistics", "auditRecharge", "exportRechargeRecord", "name", "data", "loading", "list<PERSON>uery", "page", "limit", "username", "phone", "rechargeType", "auditStatus", "startDate", "endDate", "date<PERSON><PERSON><PERSON>", "total", "statistics", "adminTotal", "userTotal", "pendingTotal", "tableData", "voucherDialogVisible", "currentVoucher", "auditDialogVisible", "auditForm", "id", "remark", "created", "getList", "getStatistics", "methods", "convertImage", "src", "formatNumber", "num", "toFixed", "replace", "getStatusType", "status", "types", "getStatusText", "texts", "handleDateRangeChange", "val", "handleSearch", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "length", "$message", "info", "t0", "console", "error", "finish", "stop", "_this2", "_callee2", "_res$data", "_res$data$adminTotal", "_res$data$userTotal", "_res$data$pendingTota", "_callee2$", "_context2", "handleReset", "handleViewVoucher", "row", "proofImage", "handleAudit", "submitAudit", "_this3", "_callee3", "_callee3$", "_context3", "success", "msg", "handleSizeChange", "handleCurrentChange", "formatDateTime", "time", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "handleExport", "_this4", "_callee5", "_callee5$", "_context5", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_callee4", "params", "blob", "fileName", "link", "_callee4$", "_context4", "Blob", "getTime", "document", "createElement", "download", "style", "display", "href", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msSaveBlob"], "sources": ["src/views/finance/recharge-record/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 统计信息区域 -->\r\n      <el-row :gutter=\"20\" class=\"statistics-container\" v-loading=\"loading\">\r\n        <el-col :span=\"8\">\r\n          <div class=\"statistics-card\">\r\n            <div class=\"title\">后台充值总额</div>\r\n            <div class=\"amount\">¥{{ formatNumber(statistics.adminTotal || 0) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"statistics-card\">\r\n            <div class=\"title\">用户充值总额</div>\r\n            <div class=\"amount\">¥{{ formatNumber(statistics.userTotal || 0) }}</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <div class=\"statistics-card\">\r\n            <div class=\"title\">待审核金额</div>\r\n            <div class=\"amount warning\">¥{{ formatNumber(statistics.pendingTotal || 0) }}</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"10\" type=\"flex\" align=\"middle\">\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model=\"listQuery.username\"\r\n              placeholder=\"用户名\"\r\n              clearable\r\n              @clear=\"handleSearch\"\r\n              @keyup.enter.native=\"handleSearch\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model=\"listQuery.phone\"\r\n              placeholder=\"手机号\"\r\n              clearable\r\n              @clear=\"handleSearch\"\r\n              @keyup.enter.native=\"handleSearch\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-select v-model=\"listQuery.rechargeType\" placeholder=\"充值类型\" clearable style=\"width: 100%\" @change=\"handleSearch\">\r\n              <el-option label=\"全部\" value=\"\" />\r\n              <el-option label=\"后台充值\" :value=\"2\" />\r\n              <el-option label=\"用户充值\" :value=\"1\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-select v-model=\"listQuery.auditStatus\" placeholder=\"审核状态\" clearable style=\"width: 100%\" @change=\"handleSearch\">\r\n              <el-option label=\"全部\" value=\"\" />\r\n              <el-option label=\"待审核\" :value=\"0\" />\r\n              <el-option label=\"已通过\" :value=\"1\" />\r\n              <el-option label=\"已拒绝\" :value=\"2\" />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              style=\"width: 100%\"\r\n              @change=\"handleDateRangeChange\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\" style=\"text-align: right\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" size=\"small\" @click=\"handleReset\">重置</el-button>\r\n            <el-button type=\"warning\" icon=\"el-icon-download\" size=\"small\" @click=\"handleExport\">导出</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"index\" label=\"序号\" align=\"center\" width=\"60\" />\r\n        <el-table-column label=\"用户名\" prop=\"username\" align=\"center\" min-width=\"120\" />\r\n        <el-table-column label=\"手机号码\" prop=\"phone\" align=\"center\" min-width=\"120\" />\r\n        <el-table-column label=\"充值金额\" align=\"center\" min-width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #67C23A\">¥{{ formatNumber(scope.row.amount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"充值类型\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.rechargeType === 2 ? 'success' : 'primary'\">\r\n              {{ scope.row.rechargeType === 2 ? '后台充值' : '用户充值' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"审核状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getStatusType(scope.row.auditStatus)\">\r\n              {{ getStatusText(scope.row.auditStatus) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"充值时间\" align=\"center\" min-width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.createTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" prop=\"remark\" align=\"center\" min-width=\"120\" show-overflow-tooltip />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"scope.row.rechargeType === 1 && scope.row.auditStatus === 0\"\r\n              type=\"primary\"\r\n              size=\"mini\"\r\n              @click=\"handleAudit(scope.row)\"\r\n            >审核</el-button>\r\n            <el-button\r\n              v-if=\"scope.row.rechargeType === 1 && scope.row.proofImage\"\r\n              type=\"text\"\r\n              size=\"mini\"\r\n              @click=\"handleViewVoucher(scope.row)\"\r\n            >查看凭证</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n\r\n      <!-- 查看凭证弹窗 -->\r\n      <el-dialog title=\"充值凭证\" :visible.sync=\"voucherDialogVisible\" width=\"500px\">\r\n        <div class=\"voucher-container\">\r\n          <img :src=\"convertImage(currentVoucher)\" alt=\"充值凭证\" style=\"max-width: 100%;\">\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 审核弹窗 -->\r\n      <el-dialog title=\"充值审核\" :visible.sync=\"auditDialogVisible\" width=\"400px\">\r\n        <el-form :model=\"auditForm\" label-width=\"80px\">\r\n          <el-form-item label=\"审核结果\">\r\n            <el-radio-group v-model=\"auditForm.auditStatus\">\r\n              <el-radio :label=\"1\">通过</el-radio>\r\n              <el-radio :label=\"2\">拒绝</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"审核备注\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"auditForm.remark\"\r\n              :rows=\"3\"\r\n              placeholder=\"请输入审核备注\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"auditDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitAudit\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getRechargeList, getRechargeStatistics, auditRecharge, exportRechargeRecord } from '@/api/finance/recharge'\r\n\r\nexport default {\r\n  name: 'RechargeRecord',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        phone: '',\r\n        rechargeType: '',\r\n        auditStatus: '',\r\n        startDate: '',\r\n        endDate: ''\r\n      },\r\n      dateRange: [],\r\n      total: 0,\r\n      statistics: {\r\n        adminTotal: 0,\r\n        userTotal: 0,\r\n        pendingTotal: 0\r\n      },\r\n      tableData: [],\r\n      voucherDialogVisible: false,\r\n      currentVoucher: '',\r\n      auditDialogVisible: false,\r\n      auditForm: {\r\n        id: null,\r\n        auditStatus: 1,\r\n        remark: ''\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getStatistics()\r\n  },\r\n  methods: {\r\n    convertImage(src){\r\n      return 'https://frontapi.huatongyun666.com'+src;\r\n    } ,\r\n    formatNumber(num) {\r\n      if (!num) return '0.00'\r\n      return num.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    },\r\n    getStatusType(status) {\r\n      const types = {\r\n        0: 'warning',\r\n        1: 'success',\r\n        2: 'danger'\r\n      }\r\n      return types[status] || 'info'\r\n    },\r\n    getStatusText(status) {\r\n      const texts = {\r\n        0: '待审核',\r\n        1: '已通过',\r\n        2: '已拒绝'\r\n      }\r\n      return texts[status] || '未知'\r\n    },\r\n    handleDateRangeChange(val) {\r\n      if (val) {\r\n        this.listQuery.startDate = val[0]\r\n        this.listQuery.endDate = val[1]\r\n      } else {\r\n        this.listQuery.startDate = ''\r\n        this.listQuery.endDate = ''\r\n      }\r\n      this.handleSearch()\r\n    },\r\n    async getList() {\r\n      this.loading = true\r\n      try {\r\n        const res = await getRechargeList(this.listQuery)\r\n        \r\n        if (res.code === 0) {\r\n          if(res.data){\r\n          this.tableData = res.data || []\r\n          this.total = res.total || 0\r\n          }\r\n          // 如果没有数据，显示提示\r\n          if (this.tableData.length === 0) {\r\n            this.$message.info('暂无数据')\r\n          }\r\n        } else {\r\n          // this.$message.error(res.msg || '获取充值记录失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取充值记录失败:', error)\r\n        this.$message.error('获取充值记录失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    async getStatistics() {\r\n      try {\r\n        const res = await getRechargeStatistics()\r\n        if (res.code === 0 && res.data) {\r\n          const { adminTotal = 0, userTotal = 0, pendingTotal = 0 } = res.data\r\n          this.statistics = {\r\n            adminTotal,\r\n            userTotal,\r\n            pendingTotal\r\n          }\r\n        } else {\r\n          // this.$message.error(res.msg || '获取统计数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取统计数据失败:', error)\r\n        this.$message.error('获取统计数据失败')\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.listQuery.page = 1\r\n      this.getList()\r\n    },\r\n    handleReset() {\r\n      this.dateRange = []\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        phone: '',\r\n        rechargeType: '',\r\n        auditStatus: '',\r\n        startDate: '',\r\n        endDate: ''\r\n      }\r\n      this.getList()\r\n    },\r\n    handleViewVoucher(row) {\r\n      this.currentVoucher = row.proofImage\r\n      this.voucherDialogVisible = true\r\n    },\r\n    handleAudit(row) {\r\n      this.auditForm = {\r\n        id: row.id,\r\n        auditStatus: 1,\r\n        remark: ''\r\n      }\r\n      this.auditDialogVisible = true\r\n    },\r\n    async submitAudit() {\r\n      try {\r\n        const res = await auditRecharge(this.auditForm.id, {\r\n          auditStatus: this.auditForm.auditStatus,\r\n          remark: this.auditForm.remark\r\n        })\r\n        if (res.code === 0) {\r\n          this.$message.success('审核操作成功')\r\n          this.auditDialogVisible = false\r\n          this.getList()\r\n          this.getStatistics()\r\n        } else {\r\n          this.$message.error(res.msg || '审核失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('审核失败:', error)\r\n        this.$message.error('审核失败')\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n    formatDateTime(time) {\r\n      if (!time) return '-'\r\n      const date = new Date(time)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    async handleExport() {\r\n      try {\r\n        this.$confirm('确认导出充值记录?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(async () => {\r\n          // 构建导出参数，使用当前的筛选条件\r\n          const params = {\r\n            username: this.listQuery.username,\r\n            phone: this.listQuery.phone,\r\n            rechargeType: this.listQuery.rechargeType,\r\n            auditStatus: this.listQuery.auditStatus,\r\n            startDate: this.listQuery.startDate,\r\n            endDate: this.listQuery.endDate\r\n          }\r\n          \r\n          const res = await exportRechargeRecord(params)\r\n          const blob = new Blob([res], { type: 'application/vnd.ms-excel' })\r\n          const fileName = `充值记录_${new Date().getTime()}.xlsx`\r\n          \r\n          if ('download' in document.createElement('a')) {\r\n            const link = document.createElement('a')\r\n            link.download = fileName\r\n            link.style.display = 'none'\r\n            link.href = URL.createObjectURL(blob)\r\n            document.body.appendChild(link)\r\n            link.click()\r\n            URL.revokeObjectURL(link.href)\r\n            document.body.removeChild(link)\r\n          } else {\r\n            navigator.msSaveBlob(blob, fileName)\r\n          }\r\n          \r\n          this.$message.success('导出成功')\r\n        })\r\n      } catch (error) {\r\n        console.error('导出失败:', error)\r\n        this.$message.error('导出失败')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .statistics-container {\r\n    margin-bottom: 20px;\r\n    \r\n    .statistics-card {\r\n      background: #fff;\r\n      padding: 20px;\r\n      border-radius: 4px;\r\n      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);\r\n      \r\n      .title {\r\n        font-size: 14px;\r\n        color: #606266;\r\n        margin-bottom: 10px;\r\n      }\r\n      \r\n      .amount {\r\n        font-size: 24px;\r\n        color: #67C23A;\r\n        font-weight: bold;\r\n        \r\n        &.warning {\r\n          color: #E6A23C;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .filter-container {\r\n    padding: 22px 0;\r\n    background-color: #fff;\r\n    \r\n    .el-row {\r\n      margin: 0 !important;\r\n    }\r\n    \r\n    .el-col {\r\n      padding: 0 5px;\r\n      \r\n      &:last-child {\r\n        text-align: right;\r\n        .el-button {\r\n          padding: 9px 15px;\r\n          margin-left: 5px;\r\n          \r\n          &:first-child {\r\n            margin-left: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .el-input,\r\n    .el-select,\r\n    .el-date-picker {\r\n      width: 100%;\r\n    }\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n.voucher-container {\r\n  text-align: center;\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;AAuLA,SAAAA,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,WAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACAC,SAAA;MACAC,KAAA;MACAC,UAAA;QACAC,UAAA;QACAC,SAAA;QACAC,YAAA;MACA;MACAC,SAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,SAAA;QACAC,EAAA;QACAd,WAAA;QACAe,MAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,8CAAAA,GAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAA,GAAA;MACA,OAAAA,GAAA,CAAAC,OAAA,IAAAC,OAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,MAAA;MACA,IAAAC,KAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,KAAA,CAAAD,MAAA;IACA;IACAE,aAAA,WAAAA,cAAAF,MAAA;MACA,IAAAG,KAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,KAAA,CAAAH,MAAA;IACA;IACAI,qBAAA,WAAAA,sBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAtC,SAAA,CAAAO,SAAA,GAAA+B,GAAA;QACA,KAAAtC,SAAA,CAAAQ,OAAA,GAAA8B,GAAA;MACA;QACA,KAAAtC,SAAA,CAAAO,SAAA;QACA,KAAAP,SAAA,CAAAQ,OAAA;MACA;MACA,KAAA+B,YAAA;IACA;IACAhB,OAAA,WAAAA,QAAA;MAAA,IAAAiB,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAzC,OAAA;cAAAiD,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAzD,eAAA,CAAA+C,KAAA,CAAAxC,SAAA;YAAA;cAAA6C,GAAA,GAAAG,QAAA,CAAAG,IAAA;cAEA,IAAAN,GAAA,CAAAO,IAAA;gBACA,IAAAP,GAAA,CAAA/C,IAAA;kBACA0C,KAAA,CAAAzB,SAAA,GAAA8B,GAAA,CAAA/C,IAAA;kBACA0C,KAAA,CAAA9B,KAAA,GAAAmC,GAAA,CAAAnC,KAAA;gBACA;gBACA;gBACA,IAAA8B,KAAA,CAAAzB,SAAA,CAAAsC,MAAA;kBACAb,KAAA,CAAAc,QAAA,CAAAC,IAAA;gBACA;cACA;gBACA;cAAA;cACAP,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAS,OAAA,CAAAC,KAAA,cAAAV,QAAA,CAAAQ,EAAA;cACAhB,KAAA,CAAAc,QAAA,CAAAI,KAAA;YAAA;cAAAV,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAAzC,OAAA;cAAA,OAAAiD,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IAEA;IACApB,aAAA,WAAAA,cAAA;MAAA,IAAAqC,MAAA;MAAA,OAAApB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmB,SAAA;QAAA,IAAAjB,GAAA,EAAAkB,SAAA,EAAAC,oBAAA,EAAApD,UAAA,EAAAqD,mBAAA,EAAApD,SAAA,EAAAqD,qBAAA,EAAApD,YAAA;QAAA,OAAA4B,mBAAA,GAAAI,IAAA,UAAAqB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnB,IAAA,GAAAmB,SAAA,CAAAlB,IAAA;YAAA;cAAAkB,SAAA,CAAAnB,IAAA;cAAAmB,SAAA,CAAAlB,IAAA;cAAA,OAEAxD,qBAAA;YAAA;cAAAmD,GAAA,GAAAuB,SAAA,CAAAjB,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAA/C,IAAA;gBAAAiE,SAAA,GACAlB,GAAA,CAAA/C,IAAA,EAAAkE,oBAAA,GAAAD,SAAA,CAAAnD,UAAA,EAAAA,UAAA,GAAAoD,oBAAA,kBAAAA,oBAAA,EAAAC,mBAAA,GAAAF,SAAA,CAAAlD,SAAA,EAAAA,SAAA,GAAAoD,mBAAA,kBAAAA,mBAAA,EAAAC,qBAAA,GAAAH,SAAA,CAAAjD,YAAA,EAAAA,YAAA,GAAAoD,qBAAA,kBAAAA,qBAAA;gBACAL,MAAA,CAAAlD,UAAA;kBACAC,UAAA,EAAAA,UAAA;kBACAC,SAAA,EAAAA,SAAA;kBACAC,YAAA,EAAAA;gBACA;cACA;gBACA;cAAA;cACAsD,SAAA,CAAAlB,IAAA;cAAA;YAAA;cAAAkB,SAAA,CAAAnB,IAAA;cAAAmB,SAAA,CAAAZ,EAAA,GAAAY,SAAA;cAEAX,OAAA,CAAAC,KAAA,cAAAU,SAAA,CAAAZ,EAAA;cACAK,MAAA,CAAAP,QAAA,CAAAI,KAAA;YAAA;YAAA;cAAA,OAAAU,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IACAvB,YAAA,WAAAA,aAAA;MACA,KAAAvC,SAAA,CAAAC,IAAA;MACA,KAAAsB,OAAA;IACA;IACA8C,WAAA,WAAAA,YAAA;MACA,KAAA5D,SAAA;MACA,KAAAT,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,KAAA;QACAC,YAAA;QACAC,WAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA,KAAAe,OAAA;IACA;IACA+C,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAtD,cAAA,GAAAsD,GAAA,CAAAC,UAAA;MACA,KAAAxD,oBAAA;IACA;IACAyD,WAAA,WAAAA,YAAAF,GAAA;MACA,KAAApD,SAAA;QACAC,EAAA,EAAAmD,GAAA,CAAAnD,EAAA;QACAd,WAAA;QACAe,MAAA;MACA;MACA,KAAAH,kBAAA;IACA;IACAwD,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAAlC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiC,SAAA;QAAA,IAAA/B,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAA5B,IAAA;cAAA,OAEAvD,aAAA,CAAAgF,MAAA,CAAAxD,SAAA,CAAAC,EAAA;gBACAd,WAAA,EAAAqE,MAAA,CAAAxD,SAAA,CAAAb,WAAA;gBACAe,MAAA,EAAAsD,MAAA,CAAAxD,SAAA,CAAAE;cACA;YAAA;cAHAwB,GAAA,GAAAiC,SAAA,CAAA3B,IAAA;cAIA,IAAAN,GAAA,CAAAO,IAAA;gBACAuB,MAAA,CAAArB,QAAA,CAAAyB,OAAA;gBACAJ,MAAA,CAAAzD,kBAAA;gBACAyD,MAAA,CAAApD,OAAA;gBACAoD,MAAA,CAAAnD,aAAA;cACA;gBACAmD,MAAA,CAAArB,QAAA,CAAAI,KAAA,CAAAb,GAAA,CAAAmC,GAAA;cACA;cAAAF,SAAA,CAAA5B,IAAA;cAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAAtB,EAAA,GAAAsB,SAAA;cAEArB,OAAA,CAAAC,KAAA,UAAAoB,SAAA,CAAAtB,EAAA;cACAmB,MAAA,CAAArB,QAAA,CAAAI,KAAA;YAAA;YAAA;cAAA,OAAAoB,SAAA,CAAAlB,IAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAEA;IACAK,gBAAA,WAAAA,iBAAA3C,GAAA;MACA,KAAAtC,SAAA,CAAAE,KAAA,GAAAoC,GAAA;MACA,KAAAf,OAAA;IACA;IACA2D,mBAAA,WAAAA,oBAAA5C,GAAA;MACA,KAAAtC,SAAA,CAAAC,IAAA,GAAAqC,GAAA;MACA,KAAAf,OAAA;IACA;IACA4D,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAA9D,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6D,SAAA;QAAA,OAAA9D,mBAAA,GAAAI,IAAA,UAAA2D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzD,IAAA,GAAAyD,SAAA,CAAAxD,IAAA;YAAA;cACA;gBACAqD,MAAA,CAAAI,QAAA;kBACAC,iBAAA;kBACAC,gBAAA;kBACAC,IAAA;gBACA,GAAAC,IAAA,cAAAtE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqE,SAAA;kBAAA,IAAAC,MAAA,EAAApE,GAAA,EAAAqE,IAAA,EAAAC,QAAA,EAAAC,IAAA;kBAAA,OAAA1E,mBAAA,GAAAI,IAAA,UAAAuE,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAApE,IAAA;sBAAA;wBACA;wBACA+D,MAAA;0BACA9G,QAAA,EAAAoG,MAAA,CAAAvG,SAAA,CAAAG,QAAA;0BACAC,KAAA,EAAAmG,MAAA,CAAAvG,SAAA,CAAAI,KAAA;0BACAC,YAAA,EAAAkG,MAAA,CAAAvG,SAAA,CAAAK,YAAA;0BACAC,WAAA,EAAAiG,MAAA,CAAAvG,SAAA,CAAAM,WAAA;0BACAC,SAAA,EAAAgG,MAAA,CAAAvG,SAAA,CAAAO,SAAA;0BACAC,OAAA,EAAA+F,MAAA,CAAAvG,SAAA,CAAAQ;wBACA;wBAAA8G,SAAA,CAAApE,IAAA;wBAAA,OAEAtD,oBAAA,CAAAqH,MAAA;sBAAA;wBAAApE,GAAA,GAAAyE,SAAA,CAAAnE,IAAA;wBACA+D,IAAA,OAAAK,IAAA,EAAA1E,GAAA;0BAAAiE,IAAA;wBAAA;wBACAK,QAAA,+BAAAd,MAAA,KAAAf,IAAA,GAAAkC,OAAA;wBAEA,kBAAAC,QAAA,CAAAC,aAAA;0BACAN,IAAA,GAAAK,QAAA,CAAAC,aAAA;0BACAN,IAAA,CAAAO,QAAA,GAAAR,QAAA;0BACAC,IAAA,CAAAQ,KAAA,CAAAC,OAAA;0BACAT,IAAA,CAAAU,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAd,IAAA;0BACAO,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAd,IAAA;0BACAA,IAAA,CAAAe,KAAA;0BACAJ,GAAA,CAAAK,eAAA,CAAAhB,IAAA,CAAAU,IAAA;0BACAL,QAAA,CAAAQ,IAAA,CAAAI,WAAA,CAAAjB,IAAA;wBACA;0BACAkB,SAAA,CAAAC,UAAA,CAAArB,IAAA,EAAAC,QAAA;wBACA;wBAEAZ,MAAA,CAAAjD,QAAA,CAAAyB,OAAA;sBAAA;sBAAA;wBAAA,OAAAuC,SAAA,CAAA1D,IAAA;oBAAA;kBAAA,GAAAoD,QAAA;gBAAA,CACA;cACA,SAAAtD,KAAA;gBACAD,OAAA,CAAAC,KAAA,UAAAA,KAAA;gBACA6C,MAAA,CAAAjD,QAAA,CAAAI,KAAA;cACA;YAAA;YAAA;cAAA,OAAAgD,SAAA,CAAA9C,IAAA;UAAA;QAAA,GAAA4C,QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}