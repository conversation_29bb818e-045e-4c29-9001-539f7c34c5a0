{"ast": null, "code": "import _regeneratorRuntime from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.number.to-fixed.js\";\nimport \"core-js/modules/es.parse-float.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport { getOrderList, getOrderStatistics, getOrderDetail, deleteOrder } from '@/api/finance/order';\nexport default {\n  name: 'FinanceOrderList',\n  data: function data() {\n    return {\n      loading: false,\n      listQuery: {\n        page: 1,\n        limit: 10,\n        orderNo: '',\n        username: '',\n        phone: '',\n        status: '',\n        startDate: '',\n        endDate: ''\n      },\n      total: 0,\n      tableData: [],\n      detailVisible: false,\n      currentOrder: {},\n      statistics: {\n        todayAmount: 0,\n        todayCount: 0,\n        monthAmount: 0,\n        monthCount: 0,\n        pendingAmount: 0,\n        pendingCount: 0\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getStatistics();\n  },\n  methods: {\n    // 获取列表数据\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              _context.next = 4;\n              return getOrderList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.tableData = res.data || [];\n                _this.total = res.total || 0;\n              } else {\n                _this.$message.error(res.msg || '获取数据失败');\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取数据失败:', _context.t0);\n              _this.$message.error('获取数据失败');\n            case 12:\n              _context.prev = 12;\n              _this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 8, 12, 15]]);\n      }))();\n    },\n    // 获取统计信息\n    getStatistics: function getStatistics() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return getOrderStatistics();\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0) {\n                _this2.statistics = res.data || {};\n              }\n              _context2.next = 10;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              console.error('获取统计信息失败:', _context2.t0);\n            case 10:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    // 查看详情\n    handleDetail: function handleDetail(row) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return getOrderDetail(row.id);\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0) {\n                _this3.currentOrder = res.data;\n                _this3.detailVisible = true;\n              } else {\n                _this3.$message.error(res.msg || '获取详情失败');\n              }\n              _context3.next = 11;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              console.error('获取详情失败:', _context3.t0);\n              _this3.$message.error('获取详情失败');\n            case 11:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n    },\n    getStatusType: function getStatusType(status) {\n      var map = {\n        '0': 'info',\n        '1': 'success',\n        '2': 'danger'\n      };\n      return map[status];\n    },\n    getStatusText: function getStatusText(status) {\n      var map = {\n        '0': '待支付',\n        '1': '已支付',\n        '2': '已取消'\n      };\n      return map[status];\n    },\n    formatNumber: function formatNumber(num) {\n      try {\n        if (!num && num !== 0) return '0.00';\n        var number = typeof num === 'string' ? parseFloat(num) : num;\n        if (isNaN(number)) return '0.00';\n        return number.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n      } catch (error) {\n        return '0.00';\n      }\n    },\n    // 式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    // 删除订单\n    handleDelete: function handleDelete(row) {\n      var _this4 = this;\n      this.$confirm('确认要删除该订单吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.prev = 0;\n              _context4.next = 3;\n              return deleteOrder(row.id);\n            case 3:\n              res = _context4.sent;\n              if (res.code === 0) {\n                _this4.$message.success('删除成功');\n                _this4.getList(); // 刷新列表\n                _this4.getStatistics(); // 刷新统计数据\n              } else {\n                _this4.$message.error(res.msg || '删除失败');\n              }\n              _context4.next = 11;\n              break;\n            case 7:\n              _context4.prev = 7;\n              _context4.t0 = _context4[\"catch\"](0);\n              console.error('删除失败:', _context4.t0);\n              _this4.$message.error('删除失败');\n            case 11:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消删除\n      });\n    },\n    // 重置查询条件\n    handleReset: function handleReset() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        orderNo: '',\n        username: '',\n        phone: '',\n        status: '',\n        startDate: '',\n        endDate: ''\n      };\n      this.dateRange = [];\n      this.getList();\n    }\n  }\n};", "map": {"version": 3, "names": ["getOrderList", "getOrderStatistics", "getOrderDetail", "deleteOrder", "name", "data", "loading", "list<PERSON>uery", "page", "limit", "orderNo", "username", "phone", "status", "startDate", "endDate", "total", "tableData", "detailVisible", "currentOrder", "statistics", "todayAmount", "todayCount", "monthAmount", "monthCount", "pendingAmount", "pendingCount", "created", "getList", "getStatistics", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "$message", "error", "msg", "t0", "console", "finish", "stop", "_this2", "_callee2", "_callee2$", "_context2", "handleDetail", "row", "_this3", "_callee3", "_callee3$", "_context3", "id", "handleSizeChange", "val", "handleCurrentChange", "getStatusType", "map", "getStatusText", "formatNumber", "num", "number", "parseFloat", "isNaN", "toFixed", "replace", "formatDateTime", "time", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "handleDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_callee4", "_callee4$", "_context4", "success", "handleReset", "date<PERSON><PERSON><PERSON>"], "sources": ["src/views/finance/order-list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 数据汇总区域 -->\r\n      <el-row :gutter=\"20\" class=\"data-summary\">\r\n        <el-col :span=\"6\">\r\n          <div class=\"summary-card\">\r\n            <div class=\"title\">总支付订单</div>\r\n            <div class=\"amount\">¥{{ formatNumber(statistics.totalAmount) }}</div>\r\n            <div class=\"count\">总订单数：{{ statistics.totalCount }}笔</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"summary-card\">\r\n            <div class=\"title\">今日订单总额</div>\r\n            <div class=\"amount\">¥{{ formatNumber(statistics.todayAmount) }}</div>\r\n            <div class=\"count\">今日订单数：{{ statistics.todayCount }}笔</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"summary-card\">\r\n            <div class=\"title\">本月订单总额</div>\r\n            <div class=\"amount\">¥{{ formatNumber(statistics.monthAmount) }}</div>\r\n            <div class=\"count\">本月订单数：{{ statistics.monthCount }}笔</div>\r\n          </div>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <div class=\"summary-card\">\r\n            <div class=\"title\">待支付订单</div>\r\n            <div class=\"amount\">¥{{ formatNumber(statistics.pendingAmount) }}</div>\r\n            <div class=\"count\">待支付笔数：{{ statistics.pendingCount }}笔</div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-input\r\n          v-model=\"listQuery.username\"\r\n          placeholder=\"用户编号\"\r\n          style=\"width: 200px\"\r\n          class=\"filter-item\"\r\n        />\r\n        <el-input\r\n          v-model=\"listQuery.phone\"\r\n          placeholder=\"手机号\"\r\n          style=\"width: 200px\"\r\n          class=\"filter-item\"\r\n        />\r\n        <el-input\r\n          v-model=\"listQuery.orderNo\"\r\n          placeholder=\"订单号\"\r\n          style=\"width: 200px\"\r\n          class=\"filter-item\"\r\n        />\r\n        <el-select\r\n          v-model=\"listQuery.status\"\r\n          placeholder=\"订单状态\"\r\n          clearable\r\n          class=\"filter-item\"\r\n          style=\"width: 130px\"\r\n        >\r\n          <el-option label=\"待支付\" value=\"0\" />\r\n          <el-option label=\"已支付\" value=\"1\" />\r\n          <el-option label=\"已取消\" value=\"2\" />\r\n        </el-select>\r\n        <el-date-picker\r\n          v-model=\"listQuery.dateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          class=\"filter-item\"\r\n        />\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getList\">搜索</el-button>\r\n        <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"handleReset\">重置</el-button>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\r\n        <el-table-column label=\"订单号\" prop=\"orderNo\" min-width=\"180\" align=\"center\" />\r\n        <el-table-column label=\"用户编号\" prop=\"userno\" min-width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"手机号码\" prop=\"phone\" min-width=\"120\" align=\"center\" />\r\n         \r\n        <el-table-column label=\"订单类型\" prop=\"orderType\" min-width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"订单金额\" min-width=\"120\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">¥{{ formatNumber(scope.row.totalAmount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" min-width=\"100\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getStatusType(scope.row.status)\">\r\n              {{ getStatusText(scope.row.status) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"支付时间\" min-width=\"160\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.payTime ? formatDateTime(scope.row.payTime) : '-' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" min-width=\"160\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.createTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" min-width=\"120\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n            <el-button \r\n              v-if=\"scope.row.status !== 1\" \r\n              type=\"text\" \r\n              style=\"color: #F56C6C\" \r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog title=\"订单详情\" :visible.sync=\"detailVisible\" width=\"600px\">\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"订单号\">{{ currentOrder.orderNo }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单状态\">\r\n          <el-tag :type=\"getStatusType(currentOrder.status)\">\r\n            {{ getStatusText(currentOrder.status) }}\r\n          </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"用户编号\">{{ currentOrder.userno }}</el-descriptions-item>\r\n        <!-- <el-descriptions-item label=\"用户名称\">{{ currentOrder.username }}</el-descriptions-item> -->\r\n        <el-descriptions-item label=\"手机号码\">{{ currentOrder.phone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单类型\">{{ currentOrder.orderType }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"订单金额\">\r\n          <span style=\"color: #f56c6c\">¥{{ formatNumber(currentOrder.totalAmount) }}</span>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"创建时间\">{{ formatDateTime(currentOrder.createTime) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"支付时间\">{{ currentOrder.payTime ? formatDateTime(currentOrder.payTime) : '-' }}</el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 商品详情 -->\r\n      <div class=\"goods-detail\">\r\n        <div class=\"section-title\">商品信息</div>\r\n        <el-table :data=\"currentOrder.goods || []\" border size=\"small\">\r\n          <el-table-column label=\"商品名称\" prop=\"goodsName\" align=\"center\" />\r\n          <el-table-column label=\"商品单价\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              ¥{{ formatNumber(scope.row.goodsPrice) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"购买数量\" prop=\"goodsQuantity\" align=\"center\" width=\"100\" />\r\n          <el-table-column label=\"小计\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              ¥{{ formatNumber(scope.row.goodsAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"detailVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getOrderList, getOrderStatistics, getOrderDetail, deleteOrder } from '@/api/finance/order'\r\n\r\nexport default {\r\n  name: 'FinanceOrderList',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        orderNo: '',\r\n        username: '',\r\n        phone: '',\r\n        status: '',\r\n        startDate: '',\r\n        endDate: ''\r\n      },\r\n      total: 0,\r\n      tableData: [],\r\n      detailVisible: false,\r\n      currentOrder: {},\r\n      statistics: {\r\n        todayAmount: 0,\r\n        todayCount: 0,\r\n        monthAmount: 0,\r\n        monthCount: 0,\r\n        pendingAmount: 0,\r\n        pendingCount: 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getStatistics()\r\n  },\r\n  methods: {\r\n    // 获取列表数据\r\n    async getList() {\r\n      this.loading = true\r\n      try {\r\n        const res = await getOrderList(this.listQuery)\r\n        if (res.code === 0) {\r\n          this.tableData = res.data || []\r\n          this.total = res.total || 0\r\n        } else {\r\n          this.$message.error(res.msg || '获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取数据失败:', error)\r\n        this.$message.error('获取数据失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 获取统计信息\r\n    async getStatistics() {\r\n      try {\r\n        const res = await getOrderStatistics()\r\n        if (res.code === 0) {\r\n          this.statistics = res.data || {}\r\n        }\r\n      } catch (error) {\r\n        console.error('获取统计信息失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 查看详情\r\n    async handleDetail(row) {\r\n      try {\r\n        const res = await getOrderDetail(row.id)\r\n        if (res.code === 0) {\r\n          this.currentOrder = res.data\r\n          this.detailVisible = true\r\n        } else {\r\n          this.$message.error(res.msg || '获取详情失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取详情失败:', error)\r\n        this.$message.error('获取详情失败')\r\n      }\r\n    },\r\n    \r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n    },\r\n    getStatusType(status) {\r\n      const map = {\r\n        '0': 'info',\r\n        '1': 'success',\r\n        '2': 'danger'\r\n      }\r\n      return map[status]\r\n    },\r\n    getStatusText(status) {\r\n      const map = {\r\n        '0': '待支付',\r\n        '1': '已支付',\r\n        '2': '已取消'\r\n      }\r\n      return map[status]\r\n    },\r\n    formatNumber(num) {\r\n      try {\r\n        if (!num && num !== 0) return '0.00'\r\n        const number = typeof num === 'string' ? parseFloat(num) : num\r\n        if (isNaN(number)) return '0.00'\r\n        return number.toFixed(2).replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n      } catch (error) {\r\n        return '0.00'\r\n      }\r\n    },\r\n    // 式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      const date = new Date(time)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    // 删除订单\r\n    handleDelete(row) {\r\n      this.$confirm('确认要删除该订单吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await deleteOrder(row.id)\r\n          if (res.code === 0) {\r\n            this.$message.success('删除成功')\r\n            this.getList() // 刷新列表\r\n            this.getStatistics() // 刷新统计数据\r\n          } else {\r\n            this.$message.error(res.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          console.error('删除失败:', error)\r\n          this.$message.error('删除失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消删除\r\n      })\r\n    },\r\n    // 重置查询条件\r\n    handleReset() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        orderNo: '',\r\n        username: '',\r\n        phone: '',\r\n        status: '',\r\n        startDate: '',\r\n        endDate: ''\r\n      }\r\n      this.dateRange = []\r\n      this.getList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .data-summary {\r\n    margin-bottom: 20px;\r\n\r\n    .summary-card {\r\n      background: #fff;\r\n      padding: 20px;\r\n      text-align: center;\r\n      border-radius: 4px;\r\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\r\n      .title {\r\n        color: #909399;\r\n        font-size: 14px;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .amount {\r\n        color: #303133;\r\n        font-size: 24px;\r\n        font-weight: bold;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .count {\r\n        color: #909399;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    \r\n    .filter-item {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n.goods-detail {\r\n  margin-top: 20px;\r\n  \r\n  .section-title {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    margin-bottom: 15px;\r\n    padding-left: 10px;\r\n    border-left: 4px solid #409EFF;\r\n  }\r\n}\r\n\r\n::v-deep .el-descriptions {\r\n  margin: 20px 0;\r\n  \r\n  .el-descriptions-item__label {\r\n    width: 120px;\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;AA4LA,SAAAA,YAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,WAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACAC,aAAA;MACAC,YAAA;MACAC,UAAA;QACAC,WAAA;QACAC,UAAA;QACAC,WAAA;QACAC,UAAA;QACAC,aAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAzB,OAAA;cAAAiC,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAzC,YAAA,CAAA+B,KAAA,CAAAxB,SAAA;YAAA;cAAA6B,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAZ,KAAA,CAAAd,SAAA,GAAAmB,GAAA,CAAA/B,IAAA;gBACA0B,KAAA,CAAAf,KAAA,GAAAoB,GAAA,CAAApB,KAAA;cACA;gBACAe,KAAA,CAAAa,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAAP,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAS,OAAA,CAAAH,KAAA,YAAAN,QAAA,CAAAQ,EAAA;cACAhB,KAAA,CAAAa,QAAA,CAAAC,KAAA;YAAA;cAAAN,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAAzB,OAAA;cAAA,OAAAiC,QAAA,CAAAU,MAAA;YAAA;YAAA;cAAA,OAAAV,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IAEA;IAEA;IACAN,aAAA,WAAAA,cAAA;MAAA,IAAAsB,MAAA;MAAA,OAAAnB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAkB,SAAA;QAAA,IAAAhB,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAgB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAd,IAAA,GAAAc,SAAA,CAAAb,IAAA;YAAA;cAAAa,SAAA,CAAAd,IAAA;cAAAc,SAAA,CAAAb,IAAA;cAAA,OAEAxC,kBAAA;YAAA;cAAAmC,GAAA,GAAAkB,SAAA,CAAAZ,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAQ,MAAA,CAAA/B,UAAA,GAAAgB,GAAA,CAAA/B,IAAA;cACA;cAAAiD,SAAA,CAAAb,IAAA;cAAA;YAAA;cAAAa,SAAA,CAAAd,IAAA;cAAAc,SAAA,CAAAP,EAAA,GAAAO,SAAA;cAEAN,OAAA,CAAAH,KAAA,cAAAS,SAAA,CAAAP,EAAA;YAAA;YAAA;cAAA,OAAAO,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAG,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAAzB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwB,SAAA;QAAA,IAAAtB,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAsB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApB,IAAA,GAAAoB,SAAA,CAAAnB,IAAA;YAAA;cAAAmB,SAAA,CAAApB,IAAA;cAAAoB,SAAA,CAAAnB,IAAA;cAAA,OAEAvC,cAAA,CAAAsD,GAAA,CAAAK,EAAA;YAAA;cAAAzB,GAAA,GAAAwB,SAAA,CAAAlB,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAc,MAAA,CAAAtC,YAAA,GAAAiB,GAAA,CAAA/B,IAAA;gBACAoD,MAAA,CAAAvC,aAAA;cACA;gBACAuC,MAAA,CAAAb,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAAc,SAAA,CAAAnB,IAAA;cAAA;YAAA;cAAAmB,SAAA,CAAApB,IAAA;cAAAoB,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAEAZ,OAAA,CAAAH,KAAA,YAAAe,SAAA,CAAAb,EAAA;cACAU,MAAA,CAAAb,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAe,SAAA,CAAAV,IAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IAEA;IAEAI,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAxD,SAAA,CAAAE,KAAA,GAAAsD,GAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAxD,SAAA,CAAAC,IAAA,GAAAuD,GAAA;IACA;IACAE,aAAA,WAAAA,cAAApD,MAAA;MACA,IAAAqD,GAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAArD,MAAA;IACA;IACAsD,aAAA,WAAAA,cAAAtD,MAAA;MACA,IAAAqD,GAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAArD,MAAA;IACA;IACAuD,YAAA,WAAAA,aAAAC,GAAA;MACA;QACA,KAAAA,GAAA,IAAAA,GAAA;QACA,IAAAC,MAAA,UAAAD,GAAA,gBAAAE,UAAA,CAAAF,GAAA,IAAAA,GAAA;QACA,IAAAG,KAAA,CAAAF,MAAA;QACA,OAAAA,MAAA,CAAAG,OAAA,IAAAC,OAAA;MACA,SAAA7B,KAAA;QACA;MACA;IACA;IACA;IACA8B,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IACA;IACAG,YAAA,WAAAA,aAAAtC,GAAA;MAAA,IAAAuC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAApE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAmE,SAAA;QAAA,IAAAjE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAiE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/D,IAAA,GAAA+D,SAAA,CAAA9D,IAAA;YAAA;cAAA8D,SAAA,CAAA/D,IAAA;cAAA+D,SAAA,CAAA9D,IAAA;cAAA,OAEAtC,WAAA,CAAAqD,GAAA,CAAAK,EAAA;YAAA;cAAAzB,GAAA,GAAAmE,SAAA,CAAA7D,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAoD,MAAA,CAAAnD,QAAA,CAAA4D,OAAA;gBACAT,MAAA,CAAAnE,OAAA;gBACAmE,MAAA,CAAAlE,aAAA;cACA;gBACAkE,MAAA,CAAAnD,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAAyD,SAAA,CAAA9D,IAAA;cAAA;YAAA;cAAA8D,SAAA,CAAA/D,IAAA;cAAA+D,SAAA,CAAAxD,EAAA,GAAAwD,SAAA;cAEAvD,OAAA,CAAAH,KAAA,UAAA0D,SAAA,CAAAxD,EAAA;cACAgD,MAAA,CAAAnD,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA0D,SAAA,CAAArD,IAAA;UAAA;QAAA,GAAAmD,QAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IACA;IACAI,WAAA,WAAAA,YAAA;MACA,KAAAlG,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA,KAAA2F,SAAA;MACA,KAAA9E,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}