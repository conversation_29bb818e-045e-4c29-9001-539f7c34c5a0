{"ast": null, "code": "export default {\n  name: 'App'\n};", "map": {"version": 3, "names": ["name"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'App'\r\n}\r\n</script>\r\n\r\n<style>\r\nhtml, body {\r\n  margin: 0;\r\n  padding: 0;\r\n  height: 100%;\r\n}\r\n\r\n#app {\r\n  height: 100%;\r\n}\r\n</style> "], "mappings": "AAOA;EACAA,IAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}