{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { assert, isArray } from 'zrender/lib/core/util.js';\n;\n/**\n * @param {Object} define\n * @return See the return of `createTask`.\n */\nexport function createTask(define) {\n  return new Task(define);\n}\nvar Task = /** @class */function () {\n  function Task(define) {\n    define = define || {};\n    this._reset = define.reset;\n    this._plan = define.plan;\n    this._count = define.count;\n    this._onDirty = define.onDirty;\n    this._dirty = true;\n  }\n  /**\n   * @param step Specified step.\n   * @param skip Skip customer perform call.\n   * @param modBy Sampling window size.\n   * @param modDataCount Sampling count.\n   * @return whether unfinished.\n   */\n  Task.prototype.perform = function (performArgs) {\n    var upTask = this._upstream;\n    var skip = performArgs && performArgs.skip;\n    // TODO some refactor.\n    // Pull data. Must pull data each time, because context.data\n    // may be updated by Series.setData.\n    if (this._dirty && upTask) {\n      var context = this.context;\n      context.data = context.outputData = upTask.context.outputData;\n    }\n    if (this.__pipeline) {\n      this.__pipeline.currentTask = this;\n    }\n    var planResult;\n    if (this._plan && !skip) {\n      planResult = this._plan(this.context);\n    }\n    // Support sharding by mod, which changes the render sequence and makes the rendered graphic\n    // elements uniformed distributed when progress, especially when moving or zooming.\n    var lastModBy = normalizeModBy(this._modBy);\n    var lastModDataCount = this._modDataCount || 0;\n    var modBy = normalizeModBy(performArgs && performArgs.modBy);\n    var modDataCount = performArgs && performArgs.modDataCount || 0;\n    if (lastModBy !== modBy || lastModDataCount !== modDataCount) {\n      planResult = 'reset';\n    }\n    function normalizeModBy(val) {\n      !(val >= 1) && (val = 1); // jshint ignore:line\n      return val;\n    }\n    var forceFirstProgress;\n    if (this._dirty || planResult === 'reset') {\n      this._dirty = false;\n      forceFirstProgress = this._doReset(skip);\n    }\n    this._modBy = modBy;\n    this._modDataCount = modDataCount;\n    var step = performArgs && performArgs.step;\n    if (upTask) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(upTask._outputDueEnd != null);\n      }\n      this._dueEnd = upTask._outputDueEnd;\n    }\n    // DataTask or overallTask\n    else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(!this._progress || this._count);\n      }\n      this._dueEnd = this._count ? this._count(this.context) : Infinity;\n    }\n    // Note: Stubs, that its host overall task let it has progress, has progress.\n    // If no progress, pass index from upstream to downstream each time plan called.\n    if (this._progress) {\n      var start = this._dueIndex;\n      var end = Math.min(step != null ? this._dueIndex + step : Infinity, this._dueEnd);\n      if (!skip && (forceFirstProgress || start < end)) {\n        var progress = this._progress;\n        if (isArray(progress)) {\n          for (var i = 0; i < progress.length; i++) {\n            this._doProgress(progress[i], start, end, modBy, modDataCount);\n          }\n        } else {\n          this._doProgress(progress, start, end, modBy, modDataCount);\n        }\n      }\n      this._dueIndex = end;\n      // If no `outputDueEnd`, assume that output data and\n      // input data is the same, so use `dueIndex` as `outputDueEnd`.\n      var outputDueEnd = this._settedOutputEnd != null ? this._settedOutputEnd : end;\n      if (process.env.NODE_ENV !== 'production') {\n        // ??? Can not rollback.\n        assert(outputDueEnd >= this._outputDueEnd);\n      }\n      this._outputDueEnd = outputDueEnd;\n    } else {\n      // (1) Some overall task has no progress.\n      // (2) Stubs, that its host overall task do not let it has progress, has no progress.\n      // This should always be performed so it can be passed to downstream.\n      this._dueIndex = this._outputDueEnd = this._settedOutputEnd != null ? this._settedOutputEnd : this._dueEnd;\n    }\n    return this.unfinished();\n  };\n  Task.prototype.dirty = function () {\n    this._dirty = true;\n    this._onDirty && this._onDirty(this.context);\n  };\n  Task.prototype._doProgress = function (progress, start, end, modBy, modDataCount) {\n    iterator.reset(start, end, modBy, modDataCount);\n    this._callingProgress = progress;\n    this._callingProgress({\n      start: start,\n      end: end,\n      count: end - start,\n      next: iterator.next\n    }, this.context);\n  };\n  Task.prototype._doReset = function (skip) {\n    this._dueIndex = this._outputDueEnd = this._dueEnd = 0;\n    this._settedOutputEnd = null;\n    var progress;\n    var forceFirstProgress;\n    if (!skip && this._reset) {\n      progress = this._reset(this.context);\n      if (progress && progress.progress) {\n        forceFirstProgress = progress.forceFirstProgress;\n        progress = progress.progress;\n      }\n      // To simplify no progress checking, array must has item.\n      if (isArray(progress) && !progress.length) {\n        progress = null;\n      }\n    }\n    this._progress = progress;\n    this._modBy = this._modDataCount = null;\n    var downstream = this._downstream;\n    downstream && downstream.dirty();\n    return forceFirstProgress;\n  };\n  Task.prototype.unfinished = function () {\n    return this._progress && this._dueIndex < this._dueEnd;\n  };\n  /**\n   * @param downTask The downstream task.\n   * @return The downstream task.\n   */\n  Task.prototype.pipe = function (downTask) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(downTask && !downTask._disposed && downTask !== this);\n    }\n    // If already downstream, do not dirty downTask.\n    if (this._downstream !== downTask || this._dirty) {\n      this._downstream = downTask;\n      downTask._upstream = this;\n      downTask.dirty();\n    }\n  };\n  Task.prototype.dispose = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._upstream && (this._upstream._downstream = null);\n    this._downstream && (this._downstream._upstream = null);\n    this._dirty = false;\n    this._disposed = true;\n  };\n  Task.prototype.getUpstream = function () {\n    return this._upstream;\n  };\n  Task.prototype.getDownstream = function () {\n    return this._downstream;\n  };\n  Task.prototype.setOutputEnd = function (end) {\n    // This only happens in dataTask, dataZoom, map, currently.\n    // where dataZoom do not set end each time, but only set\n    // when reset. So we should record the set end, in case\n    // that the stub of dataZoom perform again and earse the\n    // set end by upstream.\n    this._outputDueEnd = this._settedOutputEnd = end;\n  };\n  return Task;\n}();\nexport { Task };\nvar iterator = function () {\n  var end;\n  var current;\n  var modBy;\n  var modDataCount;\n  var winCount;\n  var it = {\n    reset: function reset(s, e, sStep, sCount) {\n      current = s;\n      end = e;\n      modBy = sStep;\n      modDataCount = sCount;\n      winCount = Math.ceil(modDataCount / modBy);\n      it.next = modBy > 1 && modDataCount > 0 ? modNext : sequentialNext;\n    }\n  };\n  return it;\n  function sequentialNext() {\n    return current < end ? current++ : null;\n  }\n  function modNext() {\n    var dataIndex = current % winCount * modBy + Math.ceil(current / winCount);\n    var result = current >= end ? null : dataIndex < modDataCount ? dataIndex\n    // If modDataCount is smaller than data.count() (consider `appendData` case),\n    // Use normal linear rendering mode.\n    : current;\n    current++;\n    return result;\n  }\n}();\n// -----------------------------------------------------------------------------\n// For stream debug (Should be commented out after used!)\n// @usage: printTask(this, 'begin');\n// @usage: printTask(this, null, {someExtraProp});\n// @usage: Use `__idxInPipeline` as conditional breakpiont.\n//\n// window.printTask = function (task: any, prefix: string, extra: { [key: string]: unknown }): void {\n//     window.ecTaskUID == null && (window.ecTaskUID = 0);\n//     task.uidDebug == null && (task.uidDebug = `task_${window.ecTaskUID++}`);\n//     task.agent && task.agent.uidDebug == null && (task.agent.uidDebug = `task_${window.ecTaskUID++}`);\n//     let props = [];\n//     if (task.__pipeline) {\n//         let val = `${task.__idxInPipeline}/${task.__pipeline.tail.__idxInPipeline} ${task.agent ? '(stub)' : ''}`;\n//         props.push({text: '__idxInPipeline/total', value: val});\n//     } else {\n//         let stubCount = 0;\n//         task.agentStubMap.each(() => stubCount++);\n//         props.push({text: 'idx', value: `overall (stubs: ${stubCount})`});\n//     }\n//     props.push({text: 'uid', value: task.uidDebug});\n//     if (task.__pipeline) {\n//         props.push({text: 'pipelineId', value: task.__pipeline.id});\n//         task.agent && props.push(\n//             {text: 'stubFor', value: task.agent.uidDebug}\n//         );\n//     }\n//     props.push(\n//         {text: 'dirty', value: task._dirty},\n//         {text: 'dueIndex', value: task._dueIndex},\n//         {text: 'dueEnd', value: task._dueEnd},\n//         {text: 'outputDueEnd', value: task._outputDueEnd}\n//     );\n//     if (extra) {\n//         Object.keys(extra).forEach(key => {\n//             props.push({text: key, value: extra[key]});\n//         });\n//     }\n//     let args = ['color: blue'];\n//     let msg = `%c[${prefix || 'T'}] %c` + props.map(item => (\n//         args.push('color: green', 'color: red'),\n//         `${item.text}: %c${item.value}`\n//     )).join('%c, ');\n//     console.log.apply(console, [msg].concat(args));\n//     // console.log(this);\n// };\n// window.printPipeline = function (task: any, prefix: string) {\n//     const pipeline = task.__pipeline;\n//     let currTask = pipeline.head;\n//     while (currTask) {\n//         window.printTask(currTask, prefix);\n//         currTask = currTask._downstream;\n//     }\n// };\n// window.showChain = function (chainHeadTask) {\n//     var chain = [];\n//     var task = chainHeadTask;\n//     while (task) {\n//         chain.push({\n//             task: task,\n//             up: task._upstream,\n//             down: task._downstream,\n//             idxInPipeline: task.__idxInPipeline\n//         });\n//         task = task._downstream;\n//     }\n//     return chain;\n// };\n// window.findTaskInChain = function (task, chainHeadTask) {\n//     let chain = window.showChain(chainHeadTask);\n//     let result = [];\n//     for (let i = 0; i < chain.length; i++) {\n//         let chainItem = chain[i];\n//         if (chainItem.task === task) {\n//             result.push(i);\n//         }\n//     }\n//     return result;\n// };\n// window.printChainAEachInChainB = function (chainHeadTaskA, chainHeadTaskB) {\n//     let chainA = window.showChain(chainHeadTaskA);\n//     for (let i = 0; i < chainA.length; i++) {\n//         console.log('chainAIdx:', i, 'inChainB:', window.findTaskInChain(chainA[i].task, chainHeadTaskB));\n//     }\n// };", "map": {"version": 3, "names": ["assert", "isArray", "createTask", "define", "Task", "_reset", "reset", "_plan", "plan", "_count", "count", "_onDirty", "onDirty", "_dirty", "prototype", "perform", "performArgs", "upTask", "_upstream", "skip", "context", "data", "outputData", "__pipeline", "currentTask", "planResult", "lastModBy", "normalizeModBy", "_modBy", "lastModDataCount", "_modDataCount", "modBy", "modDataCount", "val", "forceFirstProgress", "_doReset", "step", "process", "env", "NODE_ENV", "_outputDueEnd", "_dueEnd", "_progress", "Infinity", "start", "_dueIndex", "end", "Math", "min", "progress", "i", "length", "_doProgress", "outputDueEnd", "_settedOutputEnd", "unfinished", "dirty", "iterator", "_callingProgress", "next", "downstream", "_downstream", "pipe", "downTask", "_disposed", "dispose", "getUpstream", "getDownstream", "setOutputEnd", "current", "winCount", "it", "s", "e", "sStep", "sCount", "ceil", "modNext", "sequentialNext", "dataIndex", "result"], "sources": ["F:/常规项目/华通宝/adminweb/node_modules/echarts/lib/core/task.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { assert, isArray } from 'zrender/lib/core/util.js';\n;\n/**\n * @param {Object} define\n * @return See the return of `createTask`.\n */\nexport function createTask(define) {\n  return new Task(define);\n}\nvar Task = /** @class */function () {\n  function Task(define) {\n    define = define || {};\n    this._reset = define.reset;\n    this._plan = define.plan;\n    this._count = define.count;\n    this._onDirty = define.onDirty;\n    this._dirty = true;\n  }\n  /**\n   * @param step Specified step.\n   * @param skip Skip customer perform call.\n   * @param modBy Sampling window size.\n   * @param modDataCount Sampling count.\n   * @return whether unfinished.\n   */\n  Task.prototype.perform = function (performArgs) {\n    var upTask = this._upstream;\n    var skip = performArgs && performArgs.skip;\n    // TODO some refactor.\n    // Pull data. Must pull data each time, because context.data\n    // may be updated by Series.setData.\n    if (this._dirty && upTask) {\n      var context = this.context;\n      context.data = context.outputData = upTask.context.outputData;\n    }\n    if (this.__pipeline) {\n      this.__pipeline.currentTask = this;\n    }\n    var planResult;\n    if (this._plan && !skip) {\n      planResult = this._plan(this.context);\n    }\n    // Support sharding by mod, which changes the render sequence and makes the rendered graphic\n    // elements uniformed distributed when progress, especially when moving or zooming.\n    var lastModBy = normalizeModBy(this._modBy);\n    var lastModDataCount = this._modDataCount || 0;\n    var modBy = normalizeModBy(performArgs && performArgs.modBy);\n    var modDataCount = performArgs && performArgs.modDataCount || 0;\n    if (lastModBy !== modBy || lastModDataCount !== modDataCount) {\n      planResult = 'reset';\n    }\n    function normalizeModBy(val) {\n      !(val >= 1) && (val = 1); // jshint ignore:line\n      return val;\n    }\n    var forceFirstProgress;\n    if (this._dirty || planResult === 'reset') {\n      this._dirty = false;\n      forceFirstProgress = this._doReset(skip);\n    }\n    this._modBy = modBy;\n    this._modDataCount = modDataCount;\n    var step = performArgs && performArgs.step;\n    if (upTask) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(upTask._outputDueEnd != null);\n      }\n      this._dueEnd = upTask._outputDueEnd;\n    }\n    // DataTask or overallTask\n    else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(!this._progress || this._count);\n      }\n      this._dueEnd = this._count ? this._count(this.context) : Infinity;\n    }\n    // Note: Stubs, that its host overall task let it has progress, has progress.\n    // If no progress, pass index from upstream to downstream each time plan called.\n    if (this._progress) {\n      var start = this._dueIndex;\n      var end = Math.min(step != null ? this._dueIndex + step : Infinity, this._dueEnd);\n      if (!skip && (forceFirstProgress || start < end)) {\n        var progress = this._progress;\n        if (isArray(progress)) {\n          for (var i = 0; i < progress.length; i++) {\n            this._doProgress(progress[i], start, end, modBy, modDataCount);\n          }\n        } else {\n          this._doProgress(progress, start, end, modBy, modDataCount);\n        }\n      }\n      this._dueIndex = end;\n      // If no `outputDueEnd`, assume that output data and\n      // input data is the same, so use `dueIndex` as `outputDueEnd`.\n      var outputDueEnd = this._settedOutputEnd != null ? this._settedOutputEnd : end;\n      if (process.env.NODE_ENV !== 'production') {\n        // ??? Can not rollback.\n        assert(outputDueEnd >= this._outputDueEnd);\n      }\n      this._outputDueEnd = outputDueEnd;\n    } else {\n      // (1) Some overall task has no progress.\n      // (2) Stubs, that its host overall task do not let it has progress, has no progress.\n      // This should always be performed so it can be passed to downstream.\n      this._dueIndex = this._outputDueEnd = this._settedOutputEnd != null ? this._settedOutputEnd : this._dueEnd;\n    }\n    return this.unfinished();\n  };\n  Task.prototype.dirty = function () {\n    this._dirty = true;\n    this._onDirty && this._onDirty(this.context);\n  };\n  Task.prototype._doProgress = function (progress, start, end, modBy, modDataCount) {\n    iterator.reset(start, end, modBy, modDataCount);\n    this._callingProgress = progress;\n    this._callingProgress({\n      start: start,\n      end: end,\n      count: end - start,\n      next: iterator.next\n    }, this.context);\n  };\n  Task.prototype._doReset = function (skip) {\n    this._dueIndex = this._outputDueEnd = this._dueEnd = 0;\n    this._settedOutputEnd = null;\n    var progress;\n    var forceFirstProgress;\n    if (!skip && this._reset) {\n      progress = this._reset(this.context);\n      if (progress && progress.progress) {\n        forceFirstProgress = progress.forceFirstProgress;\n        progress = progress.progress;\n      }\n      // To simplify no progress checking, array must has item.\n      if (isArray(progress) && !progress.length) {\n        progress = null;\n      }\n    }\n    this._progress = progress;\n    this._modBy = this._modDataCount = null;\n    var downstream = this._downstream;\n    downstream && downstream.dirty();\n    return forceFirstProgress;\n  };\n  Task.prototype.unfinished = function () {\n    return this._progress && this._dueIndex < this._dueEnd;\n  };\n  /**\n   * @param downTask The downstream task.\n   * @return The downstream task.\n   */\n  Task.prototype.pipe = function (downTask) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(downTask && !downTask._disposed && downTask !== this);\n    }\n    // If already downstream, do not dirty downTask.\n    if (this._downstream !== downTask || this._dirty) {\n      this._downstream = downTask;\n      downTask._upstream = this;\n      downTask.dirty();\n    }\n  };\n  Task.prototype.dispose = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._upstream && (this._upstream._downstream = null);\n    this._downstream && (this._downstream._upstream = null);\n    this._dirty = false;\n    this._disposed = true;\n  };\n  Task.prototype.getUpstream = function () {\n    return this._upstream;\n  };\n  Task.prototype.getDownstream = function () {\n    return this._downstream;\n  };\n  Task.prototype.setOutputEnd = function (end) {\n    // This only happens in dataTask, dataZoom, map, currently.\n    // where dataZoom do not set end each time, but only set\n    // when reset. So we should record the set end, in case\n    // that the stub of dataZoom perform again and earse the\n    // set end by upstream.\n    this._outputDueEnd = this._settedOutputEnd = end;\n  };\n  return Task;\n}();\nexport { Task };\nvar iterator = function () {\n  var end;\n  var current;\n  var modBy;\n  var modDataCount;\n  var winCount;\n  var it = {\n    reset: function (s, e, sStep, sCount) {\n      current = s;\n      end = e;\n      modBy = sStep;\n      modDataCount = sCount;\n      winCount = Math.ceil(modDataCount / modBy);\n      it.next = modBy > 1 && modDataCount > 0 ? modNext : sequentialNext;\n    }\n  };\n  return it;\n  function sequentialNext() {\n    return current < end ? current++ : null;\n  }\n  function modNext() {\n    var dataIndex = current % winCount * modBy + Math.ceil(current / winCount);\n    var result = current >= end ? null : dataIndex < modDataCount ? dataIndex\n    // If modDataCount is smaller than data.count() (consider `appendData` case),\n    // Use normal linear rendering mode.\n    : current;\n    current++;\n    return result;\n  }\n}();\n// -----------------------------------------------------------------------------\n// For stream debug (Should be commented out after used!)\n// @usage: printTask(this, 'begin');\n// @usage: printTask(this, null, {someExtraProp});\n// @usage: Use `__idxInPipeline` as conditional breakpiont.\n//\n// window.printTask = function (task: any, prefix: string, extra: { [key: string]: unknown }): void {\n//     window.ecTaskUID == null && (window.ecTaskUID = 0);\n//     task.uidDebug == null && (task.uidDebug = `task_${window.ecTaskUID++}`);\n//     task.agent && task.agent.uidDebug == null && (task.agent.uidDebug = `task_${window.ecTaskUID++}`);\n//     let props = [];\n//     if (task.__pipeline) {\n//         let val = `${task.__idxInPipeline}/${task.__pipeline.tail.__idxInPipeline} ${task.agent ? '(stub)' : ''}`;\n//         props.push({text: '__idxInPipeline/total', value: val});\n//     } else {\n//         let stubCount = 0;\n//         task.agentStubMap.each(() => stubCount++);\n//         props.push({text: 'idx', value: `overall (stubs: ${stubCount})`});\n//     }\n//     props.push({text: 'uid', value: task.uidDebug});\n//     if (task.__pipeline) {\n//         props.push({text: 'pipelineId', value: task.__pipeline.id});\n//         task.agent && props.push(\n//             {text: 'stubFor', value: task.agent.uidDebug}\n//         );\n//     }\n//     props.push(\n//         {text: 'dirty', value: task._dirty},\n//         {text: 'dueIndex', value: task._dueIndex},\n//         {text: 'dueEnd', value: task._dueEnd},\n//         {text: 'outputDueEnd', value: task._outputDueEnd}\n//     );\n//     if (extra) {\n//         Object.keys(extra).forEach(key => {\n//             props.push({text: key, value: extra[key]});\n//         });\n//     }\n//     let args = ['color: blue'];\n//     let msg = `%c[${prefix || 'T'}] %c` + props.map(item => (\n//         args.push('color: green', 'color: red'),\n//         `${item.text}: %c${item.value}`\n//     )).join('%c, ');\n//     console.log.apply(console, [msg].concat(args));\n//     // console.log(this);\n// };\n// window.printPipeline = function (task: any, prefix: string) {\n//     const pipeline = task.__pipeline;\n//     let currTask = pipeline.head;\n//     while (currTask) {\n//         window.printTask(currTask, prefix);\n//         currTask = currTask._downstream;\n//     }\n// };\n// window.showChain = function (chainHeadTask) {\n//     var chain = [];\n//     var task = chainHeadTask;\n//     while (task) {\n//         chain.push({\n//             task: task,\n//             up: task._upstream,\n//             down: task._downstream,\n//             idxInPipeline: task.__idxInPipeline\n//         });\n//         task = task._downstream;\n//     }\n//     return chain;\n// };\n// window.findTaskInChain = function (task, chainHeadTask) {\n//     let chain = window.showChain(chainHeadTask);\n//     let result = [];\n//     for (let i = 0; i < chain.length; i++) {\n//         let chainItem = chain[i];\n//         if (chainItem.task === task) {\n//             result.push(i);\n//         }\n//     }\n//     return result;\n// };\n// window.printChainAEachInChainB = function (chainHeadTaskA, chainHeadTaskB) {\n//     let chainA = window.showChain(chainHeadTaskA);\n//     for (let i = 0; i < chainA.length; i++) {\n//         console.log('chainAIdx:', i, 'inChainB:', window.findTaskInChain(chainA[i].task, chainHeadTaskB));\n//     }\n// };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,OAAO,QAAQ,0BAA0B;AAC1D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAE;EACjC,OAAO,IAAIC,IAAI,CAACD,MAAM,CAAC;AACzB;AACA,IAAIC,IAAI,GAAG,aAAa,YAAY;EAClC,SAASA,IAAIA,CAACD,MAAM,EAAE;IACpBA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;IACrB,IAAI,CAACE,MAAM,GAAGF,MAAM,CAACG,KAAK;IAC1B,IAAI,CAACC,KAAK,GAAGJ,MAAM,CAACK,IAAI;IACxB,IAAI,CAACC,MAAM,GAAGN,MAAM,CAACO,KAAK;IAC1B,IAAI,CAACC,QAAQ,GAAGR,MAAM,CAACS,OAAO;IAC9B,IAAI,CAACC,MAAM,GAAG,IAAI;EACpB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACET,IAAI,CAACU,SAAS,CAACC,OAAO,GAAG,UAAUC,WAAW,EAAE;IAC9C,IAAIC,MAAM,GAAG,IAAI,CAACC,SAAS;IAC3B,IAAIC,IAAI,GAAGH,WAAW,IAAIA,WAAW,CAACG,IAAI;IAC1C;IACA;IACA;IACA,IAAI,IAAI,CAACN,MAAM,IAAII,MAAM,EAAE;MACzB,IAAIG,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1BA,OAAO,CAACC,IAAI,GAAGD,OAAO,CAACE,UAAU,GAAGL,MAAM,CAACG,OAAO,CAACE,UAAU;IAC/D;IACA,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACC,WAAW,GAAG,IAAI;IACpC;IACA,IAAIC,UAAU;IACd,IAAI,IAAI,CAAClB,KAAK,IAAI,CAACY,IAAI,EAAE;MACvBM,UAAU,GAAG,IAAI,CAAClB,KAAK,CAAC,IAAI,CAACa,OAAO,CAAC;IACvC;IACA;IACA;IACA,IAAIM,SAAS,GAAGC,cAAc,CAAC,IAAI,CAACC,MAAM,CAAC;IAC3C,IAAIC,gBAAgB,GAAG,IAAI,CAACC,aAAa,IAAI,CAAC;IAC9C,IAAIC,KAAK,GAAGJ,cAAc,CAACX,WAAW,IAAIA,WAAW,CAACe,KAAK,CAAC;IAC5D,IAAIC,YAAY,GAAGhB,WAAW,IAAIA,WAAW,CAACgB,YAAY,IAAI,CAAC;IAC/D,IAAIN,SAAS,KAAKK,KAAK,IAAIF,gBAAgB,KAAKG,YAAY,EAAE;MAC5DP,UAAU,GAAG,OAAO;IACtB;IACA,SAASE,cAAcA,CAACM,GAAG,EAAE;MAC3B,EAAEA,GAAG,IAAI,CAAC,CAAC,KAAKA,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1B,OAAOA,GAAG;IACZ;IACA,IAAIC,kBAAkB;IACtB,IAAI,IAAI,CAACrB,MAAM,IAAIY,UAAU,KAAK,OAAO,EAAE;MACzC,IAAI,CAACZ,MAAM,GAAG,KAAK;MACnBqB,kBAAkB,GAAG,IAAI,CAACC,QAAQ,CAAChB,IAAI,CAAC;IAC1C;IACA,IAAI,CAACS,MAAM,GAAGG,KAAK;IACnB,IAAI,CAACD,aAAa,GAAGE,YAAY;IACjC,IAAII,IAAI,GAAGpB,WAAW,IAAIA,WAAW,CAACoB,IAAI;IAC1C,IAAInB,MAAM,EAAE;MACV,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCvC,MAAM,CAACiB,MAAM,CAACuB,aAAa,IAAI,IAAI,CAAC;MACtC;MACA,IAAI,CAACC,OAAO,GAAGxB,MAAM,CAACuB,aAAa;IACrC;IACA;IAAA,KACK;MACH,IAAIH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCvC,MAAM,CAAC,CAAC,IAAI,CAAC0C,SAAS,IAAI,IAAI,CAACjC,MAAM,CAAC;MACxC;MACA,IAAI,CAACgC,OAAO,GAAG,IAAI,CAAChC,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,IAAI,CAACW,OAAO,CAAC,GAAGuB,QAAQ;IACnE;IACA;IACA;IACA,IAAI,IAAI,CAACD,SAAS,EAAE;MAClB,IAAIE,KAAK,GAAG,IAAI,CAACC,SAAS;MAC1B,IAAIC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACZ,IAAI,IAAI,IAAI,GAAG,IAAI,CAACS,SAAS,GAAGT,IAAI,GAAGO,QAAQ,EAAE,IAAI,CAACF,OAAO,CAAC;MACjF,IAAI,CAACtB,IAAI,KAAKe,kBAAkB,IAAIU,KAAK,GAAGE,GAAG,CAAC,EAAE;QAChD,IAAIG,QAAQ,GAAG,IAAI,CAACP,SAAS;QAC7B,IAAIzC,OAAO,CAACgD,QAAQ,CAAC,EAAE;UACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;YACxC,IAAI,CAACE,WAAW,CAACH,QAAQ,CAACC,CAAC,CAAC,EAAEN,KAAK,EAAEE,GAAG,EAAEf,KAAK,EAAEC,YAAY,CAAC;UAChE;QACF,CAAC,MAAM;UACL,IAAI,CAACoB,WAAW,CAACH,QAAQ,EAAEL,KAAK,EAAEE,GAAG,EAAEf,KAAK,EAAEC,YAAY,CAAC;QAC7D;MACF;MACA,IAAI,CAACa,SAAS,GAAGC,GAAG;MACpB;MACA;MACA,IAAIO,YAAY,GAAG,IAAI,CAACC,gBAAgB,IAAI,IAAI,GAAG,IAAI,CAACA,gBAAgB,GAAGR,GAAG;MAC9E,IAAIT,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC;QACAvC,MAAM,CAACqD,YAAY,IAAI,IAAI,CAACb,aAAa,CAAC;MAC5C;MACA,IAAI,CAACA,aAAa,GAAGa,YAAY;IACnC,CAAC,MAAM;MACL;MACA;MACA;MACA,IAAI,CAACR,SAAS,GAAG,IAAI,CAACL,aAAa,GAAG,IAAI,CAACc,gBAAgB,IAAI,IAAI,GAAG,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACb,OAAO;IAC5G;IACA,OAAO,IAAI,CAACc,UAAU,CAAC,CAAC;EAC1B,CAAC;EACDnD,IAAI,CAACU,SAAS,CAAC0C,KAAK,GAAG,YAAY;IACjC,IAAI,CAAC3C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACF,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC,IAAI,CAACS,OAAO,CAAC;EAC9C,CAAC;EACDhB,IAAI,CAACU,SAAS,CAACsC,WAAW,GAAG,UAAUH,QAAQ,EAAEL,KAAK,EAAEE,GAAG,EAAEf,KAAK,EAAEC,YAAY,EAAE;IAChFyB,QAAQ,CAACnD,KAAK,CAACsC,KAAK,EAAEE,GAAG,EAAEf,KAAK,EAAEC,YAAY,CAAC;IAC/C,IAAI,CAAC0B,gBAAgB,GAAGT,QAAQ;IAChC,IAAI,CAACS,gBAAgB,CAAC;MACpBd,KAAK,EAAEA,KAAK;MACZE,GAAG,EAAEA,GAAG;MACRpC,KAAK,EAAEoC,GAAG,GAAGF,KAAK;MAClBe,IAAI,EAAEF,QAAQ,CAACE;IACjB,CAAC,EAAE,IAAI,CAACvC,OAAO,CAAC;EAClB,CAAC;EACDhB,IAAI,CAACU,SAAS,CAACqB,QAAQ,GAAG,UAAUhB,IAAI,EAAE;IACxC,IAAI,CAAC0B,SAAS,GAAG,IAAI,CAACL,aAAa,GAAG,IAAI,CAACC,OAAO,GAAG,CAAC;IACtD,IAAI,CAACa,gBAAgB,GAAG,IAAI;IAC5B,IAAIL,QAAQ;IACZ,IAAIf,kBAAkB;IACtB,IAAI,CAACf,IAAI,IAAI,IAAI,CAACd,MAAM,EAAE;MACxB4C,QAAQ,GAAG,IAAI,CAAC5C,MAAM,CAAC,IAAI,CAACe,OAAO,CAAC;MACpC,IAAI6B,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,EAAE;QACjCf,kBAAkB,GAAGe,QAAQ,CAACf,kBAAkB;QAChDe,QAAQ,GAAGA,QAAQ,CAACA,QAAQ;MAC9B;MACA;MACA,IAAIhD,OAAO,CAACgD,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACE,MAAM,EAAE;QACzCF,QAAQ,GAAG,IAAI;MACjB;IACF;IACA,IAAI,CAACP,SAAS,GAAGO,QAAQ;IACzB,IAAI,CAACrB,MAAM,GAAG,IAAI,CAACE,aAAa,GAAG,IAAI;IACvC,IAAI8B,UAAU,GAAG,IAAI,CAACC,WAAW;IACjCD,UAAU,IAAIA,UAAU,CAACJ,KAAK,CAAC,CAAC;IAChC,OAAOtB,kBAAkB;EAC3B,CAAC;EACD9B,IAAI,CAACU,SAAS,CAACyC,UAAU,GAAG,YAAY;IACtC,OAAO,IAAI,CAACb,SAAS,IAAI,IAAI,CAACG,SAAS,GAAG,IAAI,CAACJ,OAAO;EACxD,CAAC;EACD;AACF;AACA;AACA;EACErC,IAAI,CAACU,SAAS,CAACgD,IAAI,GAAG,UAAUC,QAAQ,EAAE;IACxC,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCvC,MAAM,CAAC+D,QAAQ,IAAI,CAACA,QAAQ,CAACC,SAAS,IAAID,QAAQ,KAAK,IAAI,CAAC;IAC9D;IACA;IACA,IAAI,IAAI,CAACF,WAAW,KAAKE,QAAQ,IAAI,IAAI,CAAClD,MAAM,EAAE;MAChD,IAAI,CAACgD,WAAW,GAAGE,QAAQ;MAC3BA,QAAQ,CAAC7C,SAAS,GAAG,IAAI;MACzB6C,QAAQ,CAACP,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EACDpD,IAAI,CAACU,SAAS,CAACmD,OAAO,GAAG,YAAY;IACnC,IAAI,IAAI,CAACD,SAAS,EAAE;MAClB;IACF;IACA,IAAI,CAAC9C,SAAS,KAAK,IAAI,CAACA,SAAS,CAAC2C,WAAW,GAAG,IAAI,CAAC;IACrD,IAAI,CAACA,WAAW,KAAK,IAAI,CAACA,WAAW,CAAC3C,SAAS,GAAG,IAAI,CAAC;IACvD,IAAI,CAACL,MAAM,GAAG,KAAK;IACnB,IAAI,CAACmD,SAAS,GAAG,IAAI;EACvB,CAAC;EACD5D,IAAI,CAACU,SAAS,CAACoD,WAAW,GAAG,YAAY;IACvC,OAAO,IAAI,CAAChD,SAAS;EACvB,CAAC;EACDd,IAAI,CAACU,SAAS,CAACqD,aAAa,GAAG,YAAY;IACzC,OAAO,IAAI,CAACN,WAAW;EACzB,CAAC;EACDzD,IAAI,CAACU,SAAS,CAACsD,YAAY,GAAG,UAAUtB,GAAG,EAAE;IAC3C;IACA;IACA;IACA;IACA;IACA,IAAI,CAACN,aAAa,GAAG,IAAI,CAACc,gBAAgB,GAAGR,GAAG;EAClD,CAAC;EACD,OAAO1C,IAAI;AACb,CAAC,CAAC,CAAC;AACH,SAASA,IAAI;AACb,IAAIqD,QAAQ,GAAG,YAAY;EACzB,IAAIX,GAAG;EACP,IAAIuB,OAAO;EACX,IAAItC,KAAK;EACT,IAAIC,YAAY;EAChB,IAAIsC,QAAQ;EACZ,IAAIC,EAAE,GAAG;IACPjE,KAAK,EAAE,SAAPA,KAAKA,CAAYkE,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;MACpCN,OAAO,GAAGG,CAAC;MACX1B,GAAG,GAAG2B,CAAC;MACP1C,KAAK,GAAG2C,KAAK;MACb1C,YAAY,GAAG2C,MAAM;MACrBL,QAAQ,GAAGvB,IAAI,CAAC6B,IAAI,CAAC5C,YAAY,GAAGD,KAAK,CAAC;MAC1CwC,EAAE,CAACZ,IAAI,GAAG5B,KAAK,GAAG,CAAC,IAAIC,YAAY,GAAG,CAAC,GAAG6C,OAAO,GAAGC,cAAc;IACpE;EACF,CAAC;EACD,OAAOP,EAAE;EACT,SAASO,cAAcA,CAAA,EAAG;IACxB,OAAOT,OAAO,GAAGvB,GAAG,GAAGuB,OAAO,EAAE,GAAG,IAAI;EACzC;EACA,SAASQ,OAAOA,CAAA,EAAG;IACjB,IAAIE,SAAS,GAAGV,OAAO,GAAGC,QAAQ,GAAGvC,KAAK,GAAGgB,IAAI,CAAC6B,IAAI,CAACP,OAAO,GAAGC,QAAQ,CAAC;IAC1E,IAAIU,MAAM,GAAGX,OAAO,IAAIvB,GAAG,GAAG,IAAI,GAAGiC,SAAS,GAAG/C,YAAY,GAAG+C;IAChE;IACA;IAAA,EACEV,OAAO;IACTA,OAAO,EAAE;IACT,OAAOW,MAAM;EACf;AACF,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}