{"ast": null, "code": "export default {\n  name: 'App'\n};", "map": {"version": 3, "names": ["name"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'App'\r\n}\r\n</script>\r\n\r\n<style>\r\nhtml, body {\r\n  margin: 0;\r\n  padding: 0;\r\n  height: 100%;\r\n}\r\n\r\n#app {\r\n  height: 100%;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* Element UI 表格内容字体全局黑金主题覆盖 */\r\n.el-table__body td,\r\n.el-table__body td *,\r\n.el-table__body .cell,\r\n.el-table__body .cell *,\r\n.el-table__fixed-body-wrapper td,\r\n.el-table__fixed-body-wrapper td *,\r\n.el-table__fixed .cell,\r\n.el-table__fixed .cell * {\r\n  color: unset !important;\r\n  color: #fff !important;\r\n  -webkit-text-fill-color: #fff !important;\r\n}\r\n\r\n/* 黑金主题 Element UI 表格全局覆盖 */\r\n.el-table {\r\n  background: #181818 !important;\r\n  color: #fff !important;\r\n}\r\n.el-table th, .el-table td {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border-color: #FFD70033 !important;\r\n}\r\n.el-table__body tr:hover > td {\r\n  background: #292929 !important;\r\n}\r\n.el-table__header th {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n  font-weight: bold;\r\n}\r\n.el-table .el-button[type=\"text\"] {\r\n  color: #FFD700 !important;\r\n  font-weight: bold;\r\n}\r\n.el-tag.el-tag--info {\r\n  background: #232323 !important;\r\n  color: #FFD700 !important;\r\n  border: 1px solid #FFD700 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI 弹框全局覆盖 */\r\n.el-dialog {\r\n  background: #181818 !important;\r\n  border-radius: 12px !important;\r\n}\r\n.el-dialog__header {\r\n  color: #fff !important;\r\n  font-size: 20px !important;\r\n  font-weight: bold !important;\r\n  opacity: 1 !important;\r\n  border-bottom: 1px solid #FFD70033 !important;\r\n}\r\n.el-dialog__title {\r\n  color: #fff !important;\r\n  font-size: 20px !important;\r\n  font-weight: bold !important;\r\n  opacity: 1 !important;\r\n}\r\n.el-dialog__footer .el-button--primary {\r\n  background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;\r\n  color: #181818 !important;\r\n  border: none !important;\r\n  font-weight: bold !important;\r\n}\r\n.el-dialog__footer .el-button--primary:hover {\r\n  background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%) !important;\r\n  color: #000 !important;\r\n}\r\n.el-form-item__label {\r\n  color: #FFD700 !important;\r\n}\r\n.el-dialog .el-input__inner {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  color: #fff !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-descriptions 全局覆盖 */\r\n.el-descriptions {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border-radius: 8px !important;\r\n}\r\n.el-descriptions__header {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n  font-weight: bold !important;\r\n  border-bottom: 1px solid #FFD70033 !important;\r\n}\r\n.el-descriptions__title {\r\n  color: #FFD700 !important;\r\n  font-size: 18px !important;\r\n  font-weight: bold !important;\r\n}\r\n.el-descriptions__body {\r\n  background: #232323 !important;\r\n}\r\n.el-descriptions__table {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border-radius: 8px !important;\r\n}\r\n.el-descriptions__label,\r\n.el-descriptions-item__label,\r\n.el-descriptions-item__cell.is-label,\r\n.el-descriptions__cell.is-label {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n  font-weight: bold !important;\r\n  border-right: 1px solid #FFD70033 !important;\r\n}\r\n.el-descriptions__content {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n}\r\n.el-descriptions__cell {\r\n  border-color: #FFD70033 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI MessageBox 全局覆盖 */\r\n.el-message-box {\r\n  background: #181818 !important;\r\n  border-radius: 12px !important;\r\n  color: #fff !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n}\r\n.el-message-box__header {\r\n  color: #FFD700 !important;\r\n  font-size: 20px !important;\r\n  font-weight: bold !important;\r\n  border-bottom: 1px solid #FFD70033 !important;\r\n}\r\n.el-message-box__title {\r\n  color: #FFD700 !important;\r\n  font-size: 20px !important;\r\n  font-weight: bold !important;\r\n}\r\n.el-message-box__content {\r\n  color: #fff !important;\r\n  font-size: 16px !important;\r\n}\r\n.el-message-box__status {\r\n  color: #FFD700 !important;\r\n}\r\n.el-message-box__btns .el-button--primary {\r\n  background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%) !important;\r\n  color: #181818 !important;\r\n  border: none !important;\r\n  font-weight: bold !important;\r\n}\r\n.el-message-box__btns .el-button--primary:hover {\r\n  background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%) !important;\r\n  color: #000 !important;\r\n}\r\n.el-message-box__btns .el-button {\r\n  border-radius: 8px !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-card 全局覆盖 */\r\n.el-card,\r\n.el-card__body {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border-radius: 12px !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  box-shadow: 0 4px 24px #000a !important;\r\n}\r\n.el-card__header {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n  font-weight: bold !important;\r\n  border-bottom: 1px solid #FFD70033 !important;\r\n  border-radius: 12px 12px 0 0 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-tag 全局覆盖 */\r\n.el-tag {\r\n  background: #232323 !important;\r\n  color: #FFD700 !important;\r\n  border: 1px solid #FFD700 !important;\r\n}\r\n.el-tag.el-tag--info {\r\n  background: #232323 !important;\r\n  color: #FFD700 !important;\r\n  border: 1px solid #FFD700 !important;\r\n}\r\n.el-tag.el-tag--success {\r\n  background: #232323 !important;\r\n  color: #67C23A !important;\r\n  border: 1px solid #67C23A !important;\r\n}\r\n.el-tag.el-tag--danger {\r\n  background: #232323 !important;\r\n  color: #F56C6C !important;\r\n  border: 1px solid #F56C6C !important;\r\n}\r\n.el-tag.el-tag--warning {\r\n  background: #232323 !important;\r\n  color: #E6A23C !important;\r\n  border: 1px solid #E6A23C !important;\r\n}\r\n.el-tag.el-tag--primary {\r\n  background: #232323 !important;\r\n  color: #409EFF !important;\r\n  border: 1px solid #409EFF !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-select 全局覆盖 */\r\n.el-select .el-input__inner {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  color: #fff !important;\r\n}\r\n.el-select .el-input__inner::placeholder {\r\n  color: #b3b3b3 !important;\r\n}\r\n.el-select-dropdown {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n}\r\n.el-select-dropdown__item {\r\n  color: #fff !important;\r\n}\r\n.el-select-dropdown__item.selected,\r\n.el-select-dropdown__item:hover {\r\n  color: #FFD700 !important;\r\n  background: #181818 !important;\r\n}\r\n.el-select .el-input__suffix,\r\n.el-select .el-input__prefix {\r\n  color: #FFD700 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-input 全局覆盖 */\r\n.el-input__inner {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  color: #fff !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: none !important;\r\n}\r\n.el-input__inner::placeholder {\r\n  color: #b3b3b3 !important;\r\n}\r\n.el-input__prefix,\r\n.el-input__suffix {\r\n  color: #FFD700 !important;\r\n}\r\n.el-input.is-disabled .el-input__inner {\r\n  background: #232323 !important;\r\n  color: #b3b3b3 !important;\r\n  border-color: #FFD70055 !important;\r\n  opacity: 0.7 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-input textarea 全局覆盖 */\r\n.el-textarea__inner {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  color: #fff !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: none !important;\r\n  resize: vertical;\r\n}\r\n.el-textarea__inner::placeholder {\r\n  color: #b3b3b3 !important;\r\n}\r\n.el-input.is-disabled .el-textarea__inner {\r\n  background: #232323 !important;\r\n  color: #b3b3b3 !important;\r\n  border-color: #FFD70055 !important;\r\n  opacity: 0.7 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-tree 全局覆盖 */\r\n.el-tree {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border-radius: 8px !important;\r\n}\r\n.el-tree-node__content {\r\n  background: transparent !important;\r\n  color: #fff !important;\r\n}\r\n.el-tree-node__content:hover {\r\n  background: #292929 !important;\r\n  color: #FFD700 !important;\r\n}\r\n.el-tree-node.is-current > .el-tree-node__content {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n}\r\n.el-tree-node__label {\r\n  color: #fff !important;\r\n}\r\n.el-tree-node__expand-icon {\r\n  color: #FFD700 !important;\r\n}\r\n.el-tree .el-checkbox__input.is-checked .el-checkbox__inner {\r\n  background: #FFD700 !important;\r\n  border-color: #FFD700 !important;\r\n}\r\n.el-tree .el-checkbox__inner {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n}\r\n.el-tree .el-checkbox__input.is-indeterminate .el-checkbox__inner {\r\n  background: #FFD700 !important;\r\n  border-color: #FFD700 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-switch 全局覆盖 */\r\n.el-switch__core {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n}\r\n.el-switch__button {\r\n  background: #FFD700 !important;\r\n}\r\n.el-switch.is-checked .el-switch__core {\r\n  background: #FFD700 !important;\r\n  border-color: #FFD700 !important;\r\n}\r\n.el-switch.is-checked .el-switch__button {\r\n  background: #232323 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-input-number 全局覆盖 */\r\n.el-input-number {\r\n  background: #232323 !important;\r\n  border-radius: 8px !important;\r\n}\r\n.el-input-number__input {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  color: #fff !important;\r\n  border-radius: 8px !important;\r\n}\r\n.el-input-number__decrease,\r\n.el-input-number__increase {\r\n  background: #232323 !important;\r\n  color: #FFD700 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  border-radius: 0 0 8px 8px !important;\r\n}\r\n.el-input-number__decrease:hover,\r\n.el-input-number__increase:hover {\r\n  background: #FFD700 !important;\r\n  color: #232323 !important;\r\n}\r\n.el-input-number.is-disabled .el-input-number__input {\r\n  background: #232323 !important;\r\n  color: #b3b3b3 !important;\r\n  border-color: #FFD70055 !important;\r\n  opacity: 0.7 !important;\r\n}\r\n\r\n/* 黑金主题 Element UI el-date-picker 全局覆盖 */\r\n.el-date-editor .el-input__inner {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  border-radius: 8px !important;\r\n}\r\n.el-date-editor .el-input__prefix,\r\n.el-date-editor .el-input__suffix {\r\n  color: #FFD700 !important;\r\n}\r\n.el-date-editor .el-range-separator {\r\n  color: #FFD700 !important;\r\n}\r\n.el-picker-panel {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n}\r\n.el-picker-panel__footer {\r\n  background: #232323 !important;\r\n  border-top: 1px solid #FFD70033 !important;\r\n}\r\n.el-date-table th {\r\n  color: #FFD700 !important;\r\n}\r\n.el-date-table td {\r\n  color: #fff !important;\r\n}\r\n.el-date-table td.in-range {\r\n  background: #FFD70022 !important;\r\n}\r\n.el-date-table td.current,\r\n.el-date-table td.today {\r\n  color: #FFD700 !important;\r\n  font-weight: bold !important;\r\n}\r\n.el-date-table td.available:hover {\r\n  background: #FFD70033 !important;\r\n  color: #FFD700 !important;\r\n}\r\n.el-picker-panel__footer .el-button--text,\r\n.el-picker-panel__footer .el-button--default {\r\n  color: #FFD700 !important;\r\n}\r\n\r\n/* 黑金主题 el-date-picker 区间选择彻底覆盖 */\r\n.el-date-editor.el-range-editor,\r\n.el-date-editor.el-range-editor .el-range-input {\r\n  background: #232323 !important;\r\n  border: 1.5px solid #FFD700 !important;\r\n  color: #fff !important;\r\n  border-radius: 8px !important;\r\n}\r\n.el-date-editor.el-range-editor .el-range-input {\r\n  background: #232323 !important;\r\n  color: #fff !important;\r\n}\r\n.el-date-editor.el-range-editor .el-range-separator {\r\n  color: #FFD700 !important;\r\n}\r\n.el-date-editor.el-range-editor .el-input__prefix,\r\n.el-date-editor.el-range-editor .el-input__suffix {\r\n  color: #FFD700 !important;\r\n}\r\n.el-date-editor.el-range-editor.is-active,\r\n.el-date-editor.el-range-editor:focus,\r\n.el-date-editor.el-range-editor:hover {\r\n  border-color: #FFD700 !important;\r\n  box-shadow: 0 0 0 1.5px #FFD70055 !important;\r\n}\r\n</style> "], "mappings": "AAOA;EACAA,IAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}