{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport request from '@/utils/request';\n\n// 获取积分记录列表\nexport function getPointsList(params) {\n  return request({\n    url: '/points/record/list',\n    method: 'get',\n    params: params,\n    // 添加错误处理\n    error: function error(_error) {\n      console.error('请求积分记录列表失败:', _error);\n      return Promise.reject(_error);\n    }\n  });\n}\n\n// 获取用户总扣除金额\nexport function getTotalDeductAmount(userId) {\n  return request({\n    url: \"/points/record/total/\".concat(userId),\n    method: 'get'\n  });\n}", "map": {"version": 3, "names": ["request", "getPointsList", "params", "url", "method", "error", "console", "Promise", "reject", "getTotalDeductAmount", "userId", "concat"], "sources": ["F:/常规项目/adminweb/src/api/finance/points.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取积分记录列表\r\nexport function getPointsList(params) {\r\n  return request({\r\n    url: '/points/record/list',\r\n    method: 'get',\r\n    params,\r\n    // 添加错误处理\r\n    error: (error) => {\r\n      console.error('请求积分记录列表失败:', error)\r\n      return Promise.reject(error)\r\n    }\r\n  })\r\n}\r\n\r\n// 获取用户总扣除金额\r\nexport function getTotalDeductAmount(userId) {\r\n  return request({\r\n    url: `/points/record/total/${userId}`,\r\n    method: 'get'\r\n  })\r\n} "], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA,MAAM;IACN;IACAG,KAAK,EAAE,SAAPA,KAAKA,CAAGA,MAAK,EAAK;MAChBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,MAAK,CAAC;MACnC,OAAOE,OAAO,CAACC,MAAM,CAACH,MAAK,CAAC;IAC9B;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,oBAAoBA,CAACC,MAAM,EAAE;EAC3C,OAAOV,OAAO,CAAC;IACbG,GAAG,0BAAAQ,MAAA,CAA0BD,MAAM,CAAE;IACrCN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}