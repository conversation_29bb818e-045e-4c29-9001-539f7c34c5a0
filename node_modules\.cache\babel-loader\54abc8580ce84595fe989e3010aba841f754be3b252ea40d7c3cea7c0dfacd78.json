{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport { curry, each } from 'zrender/lib/core/util.js';\nfunction legendSelectActionHandler(methodName, payload, ecModel) {\n  var selectedMap = {};\n  var isToggleSelect = methodName === 'toggleSelected';\n  var isSelected;\n  // Update all legend components\n  ecModel.eachComponent('legend', function (legendModel) {\n    if (isToggleSelect && isSelected != null) {\n      // Force other legend has same selected status\n      // Or the first is toggled to true and other are toggled to false\n      // In the case one legend has some item unSelected in option. And if other legend\n      // doesn't has the item, they will assume it is selected.\n      legendModel[isSelected ? 'select' : 'unSelect'](payload.name);\n    } else if (methodName === 'allSelect' || methodName === 'inverseSelect') {\n      legendModel[methodName]();\n    } else {\n      legendModel[methodName](payload.name);\n      isSelected = legendModel.isSelected(payload.name);\n    }\n    var legendData = legendModel.getData();\n    each(legendData, function (model) {\n      var name = model.get('name');\n      // Wrap element\n      if (name === '\\n' || name === '') {\n        return;\n      }\n      var isItemSelected = legendModel.isSelected(name);\n      if (selectedMap.hasOwnProperty(name)) {\n        // Unselected if any legend is unselected\n        selectedMap[name] = selectedMap[name] && isItemSelected;\n      } else {\n        selectedMap[name] = isItemSelected;\n      }\n    });\n  });\n  // Return the event explicitly\n  return methodName === 'allSelect' || methodName === 'inverseSelect' ? {\n    selected: selectedMap\n  } : {\n    name: payload.name,\n    selected: selectedMap\n  };\n}\nexport function installLegendAction(registers) {\n  /**\n   * @event legendToggleSelect\n   * @type {Object}\n   * @property {string} type 'legendToggleSelect'\n   * @property {string} [from]\n   * @property {string} name Series name or data item name\n   */\n  registers.registerAction('legendToggleSelect', 'legendselectchanged', curry(legendSelectActionHandler, 'toggleSelected'));\n  registers.registerAction('legendAllSelect', 'legendselectall', curry(legendSelectActionHandler, 'allSelect'));\n  registers.registerAction('legendInverseSelect', 'legendinverseselect', curry(legendSelectActionHandler, 'inverseSelect'));\n  /**\n   * @event legendSelect\n   * @type {Object}\n   * @property {string} type 'legendSelect'\n   * @property {string} name Series name or data item name\n   */\n  registers.registerAction('legendSelect', 'legendselected', curry(legendSelectActionHandler, 'select'));\n  /**\n   * @event legendUnSelect\n   * @type {Object}\n   * @property {string} type 'legendUnSelect'\n   * @property {string} name Series name or data item name\n   */\n  registers.registerAction('legendUnSelect', 'legendunselected', curry(legendSelectActionHandler, 'unSelect'));\n}", "map": {"version": 3, "names": ["curry", "each", "legendSelectActionHandler", "methodName", "payload", "ecModel", "selectedMap", "isToggleSelect", "isSelected", "eachComponent", "legend<PERSON><PERSON><PERSON>", "name", "legendData", "getData", "model", "get", "isItemSelected", "hasOwnProperty", "selected", "installLegendAction", "registers", "registerAction"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/component/legend/legendAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport { curry, each } from 'zrender/lib/core/util.js';\nfunction legendSelectActionHandler(methodName, payload, ecModel) {\n  var selectedMap = {};\n  var isToggleSelect = methodName === 'toggleSelected';\n  var isSelected;\n  // Update all legend components\n  ecModel.eachComponent('legend', function (legendModel) {\n    if (isToggleSelect && isSelected != null) {\n      // Force other legend has same selected status\n      // Or the first is toggled to true and other are toggled to false\n      // In the case one legend has some item unSelected in option. And if other legend\n      // doesn't has the item, they will assume it is selected.\n      legendModel[isSelected ? 'select' : 'unSelect'](payload.name);\n    } else if (methodName === 'allSelect' || methodName === 'inverseSelect') {\n      legendModel[methodName]();\n    } else {\n      legendModel[methodName](payload.name);\n      isSelected = legendModel.isSelected(payload.name);\n    }\n    var legendData = legendModel.getData();\n    each(legendData, function (model) {\n      var name = model.get('name');\n      // Wrap element\n      if (name === '\\n' || name === '') {\n        return;\n      }\n      var isItemSelected = legendModel.isSelected(name);\n      if (selectedMap.hasOwnProperty(name)) {\n        // Unselected if any legend is unselected\n        selectedMap[name] = selectedMap[name] && isItemSelected;\n      } else {\n        selectedMap[name] = isItemSelected;\n      }\n    });\n  });\n  // Return the event explicitly\n  return methodName === 'allSelect' || methodName === 'inverseSelect' ? {\n    selected: selectedMap\n  } : {\n    name: payload.name,\n    selected: selectedMap\n  };\n}\nexport function installLegendAction(registers) {\n  /**\n   * @event legendToggleSelect\n   * @type {Object}\n   * @property {string} type 'legendToggleSelect'\n   * @property {string} [from]\n   * @property {string} name Series name or data item name\n   */\n  registers.registerAction('legendToggleSelect', 'legendselectchanged', curry(legendSelectActionHandler, 'toggleSelected'));\n  registers.registerAction('legendAllSelect', 'legendselectall', curry(legendSelectActionHandler, 'allSelect'));\n  registers.registerAction('legendInverseSelect', 'legendinverseselect', curry(legendSelectActionHandler, 'inverseSelect'));\n  /**\n   * @event legendSelect\n   * @type {Object}\n   * @property {string} type 'legendSelect'\n   * @property {string} name Series name or data item name\n   */\n  registers.registerAction('legendSelect', 'legendselected', curry(legendSelectActionHandler, 'select'));\n  /**\n   * @event legendUnSelect\n   * @type {Object}\n   * @property {string} type 'legendUnSelect'\n   * @property {string} name Series name or data item name\n   */\n  registers.registerAction('legendUnSelect', 'legendunselected', curry(legendSelectActionHandler, 'unSelect'));\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,IAAI,QAAQ,0BAA0B;AACtD,SAASC,yBAAyBA,CAACC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC/D,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIC,cAAc,GAAGJ,UAAU,KAAK,gBAAgB;EACpD,IAAIK,UAAU;EACd;EACAH,OAAO,CAACI,aAAa,CAAC,QAAQ,EAAE,UAAUC,WAAW,EAAE;IACrD,IAAIH,cAAc,IAAIC,UAAU,IAAI,IAAI,EAAE;MACxC;MACA;MACA;MACA;MACAE,WAAW,CAACF,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC,CAACJ,OAAO,CAACO,IAAI,CAAC;IAC/D,CAAC,MAAM,IAAIR,UAAU,KAAK,WAAW,IAAIA,UAAU,KAAK,eAAe,EAAE;MACvEO,WAAW,CAACP,UAAU,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLO,WAAW,CAACP,UAAU,CAAC,CAACC,OAAO,CAACO,IAAI,CAAC;MACrCH,UAAU,GAAGE,WAAW,CAACF,UAAU,CAACJ,OAAO,CAACO,IAAI,CAAC;IACnD;IACA,IAAIC,UAAU,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;IACtCZ,IAAI,CAACW,UAAU,EAAE,UAAUE,KAAK,EAAE;MAChC,IAAIH,IAAI,GAAGG,KAAK,CAACC,GAAG,CAAC,MAAM,CAAC;MAC5B;MACA,IAAIJ,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,EAAE,EAAE;QAChC;MACF;MACA,IAAIK,cAAc,GAAGN,WAAW,CAACF,UAAU,CAACG,IAAI,CAAC;MACjD,IAAIL,WAAW,CAACW,cAAc,CAACN,IAAI,CAAC,EAAE;QACpC;QACAL,WAAW,CAACK,IAAI,CAAC,GAAGL,WAAW,CAACK,IAAI,CAAC,IAAIK,cAAc;MACzD,CAAC,MAAM;QACLV,WAAW,CAACK,IAAI,CAAC,GAAGK,cAAc;MACpC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;EACA,OAAOb,UAAU,KAAK,WAAW,IAAIA,UAAU,KAAK,eAAe,GAAG;IACpEe,QAAQ,EAAEZ;EACZ,CAAC,GAAG;IACFK,IAAI,EAAEP,OAAO,CAACO,IAAI;IAClBO,QAAQ,EAAEZ;EACZ,CAAC;AACH;AACA,OAAO,SAASa,mBAAmBA,CAACC,SAAS,EAAE;EAC7C;AACF;AACA;AACA;AACA;AACA;AACA;EACEA,SAAS,CAACC,cAAc,CAAC,oBAAoB,EAAE,qBAAqB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,gBAAgB,CAAC,CAAC;EACzHkB,SAAS,CAACC,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,WAAW,CAAC,CAAC;EAC7GkB,SAAS,CAACC,cAAc,CAAC,qBAAqB,EAAE,qBAAqB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,eAAe,CAAC,CAAC;EACzH;AACF;AACA;AACA;AACA;AACA;EACEkB,SAAS,CAACC,cAAc,CAAC,cAAc,EAAE,gBAAgB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,QAAQ,CAAC,CAAC;EACtG;AACF;AACA;AACA;AACA;AACA;EACEkB,SAAS,CAACC,cAAc,CAAC,gBAAgB,EAAE,kBAAkB,EAAErB,KAAK,CAACE,yBAAyB,EAAE,UAAU,CAAC,CAAC;AAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}