{"ast": null, "code": "import \"core-js/modules/es.symbol.to-primitive.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.date.to-primitive.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "map": {"version": 3, "names": ["_typeof", "toPrimitive", "t", "r", "e", "Symbol", "i", "call", "TypeError", "String", "Number", "default"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/@babel/runtime/helpers/esm/toPrimitive.js"], "sourcesContent": ["import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };"], "mappings": ";;;;;AAAA,OAAOA,OAAO,MAAM,aAAa;AACjC,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAI,QAAQ,IAAIH,OAAO,CAACE,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAC1C,IAAIE,CAAC,GAAGF,CAAC,CAACG,MAAM,CAACJ,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKG,CAAC,EAAE;IAChB,IAAIE,CAAC,GAAGF,CAAC,CAACG,IAAI,CAACL,CAAC,EAAEC,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAIH,OAAO,CAACM,CAAC,CAAC,EAAE,OAAOA,CAAC;IACpC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKL,CAAC,GAAGM,MAAM,GAAGC,MAAM,EAAER,CAAC,CAAC;AAC9C;AACA,SAASD,WAAW,IAAIU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}