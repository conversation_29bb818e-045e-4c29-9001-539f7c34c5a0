{"ast": null, "code": "import _defineProperty from \"E:/\\u65B0\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\nexport var progressEventReducer = function progressEventReducer(listener, isDownloadStream) {\n  var freq = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 3;\n  var bytesNotified = 0;\n  var _speedometer = speedometer(50, 250);\n  return throttle(function (e) {\n    var loaded = e.loaded;\n    var total = e.lengthComputable ? e.total : undefined;\n    var progressBytes = loaded - bytesNotified;\n    var rate = _speedometer(progressBytes);\n    var inRange = loaded <= total;\n    bytesNotified = loaded;\n    var data = _defineProperty({\n      loaded: loaded,\n      total: total,\n      progress: total ? loaded / total : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null\n    }, isDownloadStream ? 'download' : 'upload', true);\n    listener(data);\n  }, freq);\n};\nexport var progressEventDecorator = function progressEventDecorator(total, throttled) {\n  var lengthComputable = total != null;\n  return [function (loaded) {\n    return throttled[0]({\n      lengthComputable: lengthComputable,\n      total: total,\n      loaded: loaded\n    });\n  }, throttled[1]];\n};\nexport var asyncDecorator = function asyncDecorator(fn) {\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return utils.asap(function () {\n      return fn.apply(void 0, args);\n    });\n  };\n};", "map": {"version": 3, "names": ["speedometer", "throttle", "utils", "progressEventReducer", "listener", "isDownloadStream", "freq", "arguments", "length", "undefined", "bytesNotified", "_speedometer", "e", "loaded", "total", "lengthComputable", "progressBytes", "rate", "inRange", "data", "_defineProperty", "progress", "bytes", "estimated", "event", "progressEventDecorator", "throttled", "asyncDecorator", "fn", "_len", "args", "Array", "_key", "asap", "apply"], "sources": ["E:/新项目/adminweb/node_modules/axios/lib/helpers/progressEventReducer.js"], "sourcesContent": ["import speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\n\nexport const progressEventReducer = (listener, isDownloadStream, freq = 3) => {\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n\n  return throttle(e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n\n    bytesNotified = loaded;\n\n    const data = {\n      loaded,\n      total,\n      progress: total ? (loaded / total) : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null,\n      [isDownloadStream ? 'download' : 'upload']: true\n    };\n\n    listener(data);\n  }, freq);\n}\n\nexport const progressEventDecorator = (total, throttled) => {\n  const lengthComputable = total != null;\n\n  return [(loaded) => throttled[0]({\n    lengthComputable,\n    total,\n    loaded\n  }), throttled[1]];\n}\n\nexport const asyncDecorator = (fn) => (...args) => utils.asap(() => fn(...args));\n"], "mappings": ";AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,KAAK,MAAM,aAAa;AAE/B,OAAO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,QAAQ,EAAEC,gBAAgB,EAAe;EAAA,IAAbC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EACvE,IAAIG,aAAa,GAAG,CAAC;EACrB,IAAMC,YAAY,GAAGX,WAAW,CAAC,EAAE,EAAE,GAAG,CAAC;EAEzC,OAAOC,QAAQ,CAAC,UAAAW,CAAC,EAAI;IACnB,IAAMC,MAAM,GAAGD,CAAC,CAACC,MAAM;IACvB,IAAMC,KAAK,GAAGF,CAAC,CAACG,gBAAgB,GAAGH,CAAC,CAACE,KAAK,GAAGL,SAAS;IACtD,IAAMO,aAAa,GAAGH,MAAM,GAAGH,aAAa;IAC5C,IAAMO,IAAI,GAAGN,YAAY,CAACK,aAAa,CAAC;IACxC,IAAME,OAAO,GAAGL,MAAM,IAAIC,KAAK;IAE/BJ,aAAa,GAAGG,MAAM;IAEtB,IAAMM,IAAI,GAAAC,eAAA;MACRP,MAAM,EAANA,MAAM;MACNC,KAAK,EAALA,KAAK;MACLO,QAAQ,EAAEP,KAAK,GAAID,MAAM,GAAGC,KAAK,GAAIL,SAAS;MAC9Ca,KAAK,EAAEN,aAAa;MACpBC,IAAI,EAAEA,IAAI,GAAGA,IAAI,GAAGR,SAAS;MAC7Bc,SAAS,EAAEN,IAAI,IAAIH,KAAK,IAAII,OAAO,GAAG,CAACJ,KAAK,GAAGD,MAAM,IAAII,IAAI,GAAGR,SAAS;MACzEe,KAAK,EAAEZ,CAAC;MACRG,gBAAgB,EAAED,KAAK,IAAI;IAAI,GAC9BT,gBAAgB,GAAG,UAAU,GAAG,QAAQ,EAAG,IAAI,CACjD;IAEDD,QAAQ,CAACe,IAAI,CAAC;EAChB,CAAC,EAAEb,IAAI,CAAC;AACV,CAAC;AAED,OAAO,IAAMmB,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAIX,KAAK,EAAEY,SAAS,EAAK;EAC1D,IAAMX,gBAAgB,GAAGD,KAAK,IAAI,IAAI;EAEtC,OAAO,CAAC,UAACD,MAAM;IAAA,OAAKa,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/BX,gBAAgB,EAAhBA,gBAAgB;MAChBD,KAAK,EAALA,KAAK;MACLD,MAAM,EAANA;IACF,CAAC,CAAC;EAAA,GAAEa,SAAS,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AAED,OAAO,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,EAAE;EAAA,OAAK;IAAA,SAAAC,IAAA,GAAAtB,SAAA,CAAAC,MAAA,EAAIsB,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAzB,SAAA,CAAAyB,IAAA;IAAA;IAAA,OAAK9B,KAAK,CAAC+B,IAAI,CAAC;MAAA,OAAML,EAAE,CAAAM,KAAA,SAAIJ,IAAI,CAAC;IAAA,EAAC;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}