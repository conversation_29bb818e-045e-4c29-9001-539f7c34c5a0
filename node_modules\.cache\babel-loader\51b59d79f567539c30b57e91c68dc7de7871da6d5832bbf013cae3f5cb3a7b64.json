{"ast": null, "code": "import _regeneratorRuntime from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.find.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.find.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport * as echarts from 'echarts';\nimport { getLoginLogList } from '@/api/log/login';\nimport { getOperationLogList } from '@/api/log/operation';\nvar provinceMap = {\n  'guangdong': '440000',\n  'beijing': '110000',\n  'shanghai': '310000',\n  'tianjin': '120000',\n  'chongqing': '500000',\n  'hebei': '130000',\n  'shanxi': '140000',\n  'liaoning': '210000',\n  'jilin': '220000',\n  'heilongjiang': '230000',\n  'jiangsu': '320000',\n  'zhejiang': '330000',\n  'anhui': '340000',\n  'fujian': '350000',\n  'jiangxi': '360000',\n  'shandong': '370000',\n  'henan': '410000',\n  'hubei': '420000',\n  'hunan': '430000',\n  'guangxi': '450000',\n  'hainan': '460000',\n  'sichuan': '510000',\n  'guizhou': '520000',\n  'yunnan': '530000',\n  'xizang': '540000',\n  'shaanxi': '610000',\n  'gansu': '620000',\n  'qinghai': '630000',\n  'ningxia': '640000',\n  'xinjiang': '650000'\n};\nvar cityMap = {\n  '440000': {\n    'guangzhou': '440100',\n    'shenzhen': '440300',\n    'zhuhai': '440400',\n    'shantou': '440500',\n    'foshan': '440600',\n    'shaoguan': '440200',\n    'zhanjiang': '440800',\n    'zhaoqing': '441200',\n    'jiangmen': '440700',\n    'maoming': '440900',\n    'huizhou': '441300',\n    'meizhou': '441400',\n    'shanwei': '441500',\n    'heyuan': '441600',\n    'yangjiang': '441700',\n    'qingyuan': '441800',\n    'dongguan': '441900',\n    'zhongshan': '442000',\n    'chaozhou': '445100',\n    'jieyang': '445200',\n    'yunfu': '445300'\n  },\n  '110000': {\n    'dongcheng': '110101',\n    'xicheng': '110102',\n    'chaoyang': '110105',\n    'haidian': '110108',\n    'fengtai': '110106',\n    'shijingshan': '110107'\n  },\n  '310000': {\n    'huangpu': '310101',\n    'xuhui': '310104',\n    'changning': '310105',\n    'jingan': '310106',\n    'putuo': '310107',\n    'hongkou': '310109'\n  },\n  '320000': {\n    'nanjing': '320100',\n    'suzhou': '320500',\n    'wuxi': '320200',\n    'changzhou': '320400',\n    'nantong': '320600',\n    'yangzhou': '321000'\n  },\n  '330000': {\n    'hangzhou': '330100',\n    'ningbo': '330200',\n    'wenzhou': '330300',\n    'jiaxing': '330400',\n    'huzhou': '330500',\n    'shaoxing': '330600'\n  }\n};\nexport default {\n  name: 'Dashboard',\n  data: function data() {\n    return {\n      // 登录日志\n      loginLogs: [],\n      // 操作日志\n      operationLogs: [],\n      // 添加新的地图关数据\n      mapChart: null,\n      currentArea: [],\n      // 当前区域层级 [{name: '广东省', code: '440000'}, {name: '深圳市', code: '440300'}]\n      mapData: {\n        china: [{\n          name: '北京',\n          value: 80,\n          children: true\n        }, {\n          name: '天津',\n          value: 45,\n          children: true\n        }, {\n          name: '河北',\n          value: 125,\n          children: true\n        }, {\n          name: '山西',\n          value: 85,\n          children: true\n        }, {\n          name: '内蒙古',\n          value: 95,\n          children: true\n        }, {\n          name: '辽宁',\n          value: 115,\n          children: true\n        }, {\n          name: '吉林',\n          value: 78,\n          children: true\n        }, {\n          name: '黑龙江',\n          value: 88,\n          children: true\n        }, {\n          name: '上海',\n          value: 120,\n          children: true\n        }, {\n          name: '江苏',\n          value: 142,\n          children: true\n        }, {\n          name: '浙江',\n          value: 138,\n          children: true\n        }, {\n          name: '安徽',\n          value: 98,\n          children: true\n        }, {\n          name: '福建',\n          value: 108,\n          children: true\n        }, {\n          name: '江西',\n          value: 85,\n          children: true\n        }, {\n          name: '山东',\n          value: 132,\n          children: true\n        }, {\n          name: '河南',\n          value: 128,\n          children: true\n        }, {\n          name: '湖北',\n          value: 110,\n          children: true\n        }, {\n          name: '湖南',\n          value: 105,\n          children: true\n        }, {\n          name: '广东',\n          value: 150,\n          children: true\n        }, {\n          name: '广西',\n          value: 92,\n          children: true\n        }, {\n          name: '海南',\n          value: 68,\n          children: true\n        }, {\n          name: '重庆',\n          value: 95,\n          children: true\n        }, {\n          name: '四川',\n          value: 122,\n          children: true\n        }, {\n          name: '贵州',\n          value: 82,\n          children: true\n        }, {\n          name: '云南',\n          value: 88,\n          children: true\n        }, {\n          name: '西藏',\n          value: 45,\n          children: true\n        }, {\n          name: '陕西',\n          value: 98,\n          children: true\n        }, {\n          name: '甘肃',\n          value: 75,\n          children: true\n        }, {\n          name: '青海',\n          value: 55,\n          children: true\n        }, {\n          name: '宁夏',\n          value: 48,\n          children: true\n        }, {\n          name: '新疆',\n          value: 72,\n          children: true\n        }, {\n          name: '台湾',\n          value: 0,\n          children: false\n        }, {\n          name: '香港',\n          value: 65,\n          children: true\n        }, {\n          name: '澳门',\n          value: 45,\n          children: true\n        }],\n        beijing: [{\n          name: '东城区',\n          value: 25,\n          hasDevice: true\n        }, {\n          name: '西城区',\n          value: 30,\n          hasDevice: true\n        }, {\n          name: '朝阳区',\n          value: 45,\n          hasDevice: true\n        }, {\n          name: '海淀区',\n          value: 50,\n          hasDevice: true\n        }, {\n          name: '丰台区',\n          value: 28,\n          hasDevice: true\n        }, {\n          name: '石景山区',\n          value: 20,\n          hasDevice: true\n        }],\n        shanghai: [{\n          name: '黄浦区',\n          value: 28,\n          hasDevice: true\n        }, {\n          name: '徐汇区',\n          value: 35,\n          hasDevice: true\n        }, {\n          name: '长宁区',\n          value: 30,\n          hasDevice: true\n        }, {\n          name: '静安区',\n          value: 40,\n          hasDevice: true\n        }, {\n          name: '普陀区',\n          value: 25,\n          hasDevice: true\n        }, {\n          name: '虹口区',\n          value: 22,\n          hasDevice: true\n        }],\n        jiangsu: [{\n          name: '南京',\n          value: 55,\n          children: true\n        }, {\n          name: '苏州',\n          value: 60,\n          children: true\n        }, {\n          name: '无锡',\n          value: 45,\n          children: true\n        }, {\n          name: '常州',\n          value: 35,\n          children: true\n        }, {\n          name: '南通',\n          value: 30,\n          children: true\n        }, {\n          name: '扬州',\n          value: 25,\n          children: true\n        }],\n        zhejiang: [{\n          name: '杭州',\n          value: 65,\n          children: true\n        }, {\n          name: '宁波',\n          value: 50,\n          children: true\n        }, {\n          name: '温州',\n          value: 40,\n          children: true\n        }, {\n          name: '嘉兴',\n          value: 30,\n          children: true\n        }, {\n          name: '湖州',\n          value: 25,\n          children: true\n        }, {\n          name: '绍兴',\n          value: 35,\n          children: true\n        }],\n        guangdong: [{\n          name: '广州',\n          value: 45,\n          children: true\n        }, {\n          name: '深圳',\n          value: 50,\n          children: true\n        }, {\n          name: '珠海',\n          value: 25,\n          children: true\n        }, {\n          name: '汕头',\n          value: 20,\n          children: true\n        }, {\n          name: '佛山',\n          value: 35,\n          children: true\n        }, {\n          name: '韶关',\n          value: 15,\n          children: true\n        }, {\n          name: '湛江',\n          value: 18,\n          children: true\n        }, {\n          name: '肇庆',\n          value: 22,\n          children: true\n        }, {\n          name: '江门',\n          value: 28,\n          children: true\n        }, {\n          name: '茂名',\n          value: 20,\n          children: true\n        }, {\n          name: '惠州',\n          value: 30,\n          children: true\n        }, {\n          name: '梅州',\n          value: 15,\n          children: true\n        }, {\n          name: '汕尾',\n          value: 12,\n          children: true\n        }, {\n          name: '河源',\n          value: 18,\n          children: true\n        }, {\n          name: '阳江',\n          value: 16,\n          children: true\n        }, {\n          name: '清远',\n          value: 25,\n          children: true\n        }, {\n          name: '东莞',\n          value: 40,\n          children: true\n        }, {\n          name: '中山',\n          value: 32,\n          children: true\n        }, {\n          name: '潮州',\n          value: 15,\n          children: true\n        }, {\n          name: '揭阳',\n          value: 18,\n          children: true\n        }, {\n          name: '云浮',\n          value: 12,\n          children: true\n        }],\n        guangzhou: [{\n          name: '越秀区',\n          value: 15,\n          hasDevice: true\n        }, {\n          name: '海珠区',\n          value: 12,\n          hasDevice: true\n        }, {\n          name: '荔湾区',\n          value: 10,\n          hasDevice: true\n        }, {\n          name: '天河区',\n          value: 20,\n          hasDevice: true\n        }, {\n          name: '白云区',\n          value: 18,\n          hasDevice: true\n        }, {\n          name: '黄埔区',\n          value: 15,\n          hasDevice: true\n        }],\n        shenzhen: [{\n          name: '福田区',\n          value: 35,\n          hasDevice: true\n        }, {\n          name: '罗湖区',\n          value: 28,\n          hasDevice: true\n        }, {\n          name: '南山区',\n          value: 42,\n          hasDevice: true\n        }, {\n          name: '宝安区',\n          value: 38,\n          hasDevice: true\n        }, {\n          name: '龙岗区',\n          value: 32,\n          hasDevice: true\n        }, {\n          name: '盐田区',\n          value: 15,\n          hasDevice: true\n        }, {\n          name: '龙华区',\n          value: 30,\n          hasDevice: true\n        }, {\n          name: '坪山区',\n          value: 18,\n          hasDevice: true\n        }, {\n          name: '光明区',\n          value: 20,\n          hasDevice: true\n        }, {\n          name: '大鹏新区',\n          value: 12,\n          hasDevice: true\n        }]\n      },\n      // 修改查询参数\n      logQuery: {\n        pageNum: 1,\n        pageSize: 3,\n        // 只获取3条记录\n        orderByColumn: 'createTime',\n        // 按创建时间排序\n        isAsc: 'desc' // 降序排序，最新的在前\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.initEchartsMap();\n    this.getLoginLogs();\n    this.getOperationLogs();\n  },\n  beforeDestroy: function beforeDestroy() {\n    if (this.mapChart) {\n      this.mapChart.dispose();\n    }\n  },\n  methods: {\n    initEchartsMap: function initEchartsMap() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var chartDom, response, chinaMap;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              chartDom = document.getElementById('deviceMap');\n              _this.mapChart = echarts.init(chartDom);\n\n              // 注册地图\n              _context.next = 5;\n              return fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json');\n            case 5:\n              response = _context.sent;\n              _context.next = 8;\n              return response.json();\n            case 8:\n              chinaMap = _context.sent;\n              echarts.registerMap('china', chinaMap);\n              _context.next = 12;\n              return _this.loadMap('china');\n            case 12:\n              _context.next = 17;\n              break;\n            case 14:\n              _context.prev = 14;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('初始化地图失败:', _context.t0);\n            case 17:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 14]]);\n      }))();\n    },\n    loadMap: function loadMap(mapName) {\n      var _arguments = arguments,\n        _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var data, code, response, mapJson, mapData, mapOption;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              data = _arguments.length > 1 && _arguments[1] !== undefined ? _arguments[1] : null;\n              _context3.prev = 1;\n              if (!(mapName !== 'china')) {\n                _context3.next = 12;\n                break;\n              }\n              code = _this2.getAreaCode(mapName);\n              console.log('Loading map for:', mapName, 'with code:', code);\n              _context3.next = 7;\n              return fetch(\"https://geo.datav.aliyun.com/areas_v3/bound/\".concat(code, \"_full.json\"));\n            case 7:\n              response = _context3.sent;\n              _context3.next = 10;\n              return response.json();\n            case 10:\n              mapJson = _context3.sent;\n              echarts.registerMap(mapName, mapJson);\n            case 12:\n              mapData = data || _this2.mapData[mapName.toLowerCase()] || [];\n              console.log('Map data for:', mapName, mapData);\n\n              // 抽取地图配置\n              mapOption = {\n                tooltip: {\n                  trigger: 'item',\n                  formatter: function formatter(params) {\n                    var value = params.value || 0;\n                    return \"\".concat(params.name, \"\\n\").concat(value);\n                  }\n                },\n                visualMap: {\n                  min: 0,\n                  max: 200,\n                  text: ['高', '低'],\n                  realtime: false,\n                  calculable: true,\n                  inRange: {\n                    color: ['#ffe4e1', '#ff4500']\n                  }\n                },\n                series: [{\n                  name: '设备分布',\n                  type: 'map',\n                  map: mapName,\n                  roam: true,\n                  zoom: 1.5,\n                  center: [104.297, 35.861],\n                  scaleLimit: {\n                    min: 1,\n                    max: 5\n                  },\n                  label: {\n                    show: true,\n                    formatter: function formatter(params) {\n                      var value = params.value || 0;\n                      return \"\".concat(params.name, \"\\n\").concat(value);\n                    },\n                    fontSize: 14,\n                    color: '#fff',\n                    backgroundColor: 'rgba(0, 150, 255, 0.5)',\n                    padding: [4, 8],\n                    borderRadius: 4,\n                    textBorderWidth: 2,\n                    textBorderColor: 'rgba(0, 0, 0, 0.8)'\n                  },\n                  itemStyle: {\n                    areaColor: '#0c2c5a',\n                    borderColor: '#00fcff',\n                    borderWidth: 1.5,\n                    shadowColor: 'rgba(0, 252, 255, 0.3)',\n                    shadowBlur: 10\n                  },\n                  emphasis: {\n                    label: {\n                      show: true,\n                      color: '#fff',\n                      fontSize: 16,\n                      backgroundColor: 'rgba(0, 150, 255, 0.8)'\n                    },\n                    itemStyle: {\n                      areaColor: '#0052d9',\n                      borderColor: '#00fcff',\n                      borderWidth: 2,\n                      shadowColor: 'rgba(0, 252, 255, 0.5)',\n                      shadowBlur: 15\n                    }\n                  },\n                  data: mapData.map(function (item) {\n                    return {\n                      name: item.name,\n                      value: item.value || 0,\n                      children: item.children,\n                      hasDevice: item.hasDevice,\n                      itemStyle: item.hasDevice ? {\n                        areaColor: '#ff4500',\n                        shadowColor: 'rgba(255, 69, 0, 0.5)',\n                        shadowBlur: 10\n                      } : null\n                    };\n                  })\n                }]\n              };\n              _this2.mapChart.setOption(mapOption);\n\n              // 修改点击事件处理\n              _this2.mapChart.off('click').on('click', /*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(params) {\n                  var data, areaName, nextLevel, _code;\n                  return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n                    while (1) switch (_context2.prev = _context2.next) {\n                      case 0:\n                        console.log('Clicked area:', params.name);\n                        data = mapData.find(function (item) {\n                          return item.name === params.name;\n                        });\n                        if (!(data && data.children)) {\n                          _context2.next = 11;\n                          break;\n                        }\n                        // 处理地区名称，移除各种后缀\n                        areaName = params.name.replace(/(省|市|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '');\n                        nextLevel = areaName.toLowerCase();\n                        console.log('Next level:', nextLevel);\n\n                        // 获取下一级区域的编码\n                        _context2.next = 8;\n                        return _this2.getNextLevelCode(areaName, _this2.currentArea.length);\n                      case 8:\n                        _code = _context2.sent;\n                        console.log('Next level code:', _code);\n                        if (_code) {\n                          _this2.currentArea.push({\n                            name: params.name,\n                            code: _code\n                          });\n                          _this2.loadMap(nextLevel);\n                        }\n                      case 11:\n                      case \"end\":\n                        return _context2.stop();\n                    }\n                  }, _callee2);\n                }));\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }());\n              _context3.next = 22;\n              break;\n            case 19:\n              _context3.prev = 19;\n              _context3.t0 = _context3[\"catch\"](1);\n              console.error('加载地图数据失败:', _context3.t0);\n            case 22:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[1, 19]]);\n      }))();\n    },\n    handleAreaClick: function handleAreaClick(index) {\n      if (index === 0) {\n        this.currentArea = [];\n        this.loadMap('china');\n      } else {\n        this.currentArea = this.currentArea.slice(0, index + 1);\n        var areaName = this.currentArea[index].name.replace(/(省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '');\n        this.loadMap(areaName.toLowerCase());\n      }\n    },\n    getAreaCode: function getAreaCode(name) {\n      var currentLevel = this.currentArea.length;\n      var areaName = name.toLowerCase();\n      console.log('Getting area code for:', name, 'level:', currentLevel);\n      if (currentLevel === 0) {\n        return '100000';\n      } else if (currentLevel === 1) {\n        return provinceMap[areaName] || '100000';\n      } else if (currentLevel === 2) {\n        var _cityMap$parentCode;\n        var parentCode = this.currentArea[0].code;\n        var cityCode = (_cityMap$parentCode = cityMap[parentCode]) === null || _cityMap$parentCode === void 0 ? void 0 : _cityMap$parentCode[areaName];\n        console.log('City code:', cityCode, 'for parent:', parentCode);\n        return cityCode || parentCode;\n      }\n      return '100000';\n    },\n    getNextLevelCode: function getNextLevelCode(name, currentLevel) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var areaName, _cityMap$parentCode2, parentCode, _parentCode, response, json, district;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.prev = 0;\n              areaName = name.toLowerCase();\n              if (!(currentLevel === 0)) {\n                _context4.next = 6;\n                break;\n              }\n              return _context4.abrupt(\"return\", provinceMap[areaName]);\n            case 6:\n              if (!(currentLevel === 1)) {\n                _context4.next = 11;\n                break;\n              }\n              // 获取市级编码\n              parentCode = _this3.currentArea[0].code;\n              return _context4.abrupt(\"return\", (_cityMap$parentCode2 = cityMap[parentCode]) === null || _cityMap$parentCode2 === void 0 ? void 0 : _cityMap$parentCode2[areaName]);\n            case 11:\n              if (!(currentLevel === 2)) {\n                _context4.next = 21;\n                break;\n              }\n              // 获取区级编码\n              _parentCode = _this3.currentArea[1].code;\n              _context4.next = 15;\n              return fetch(\"https://geo.datav.aliyun.com/areas_v3/bound/\".concat(_parentCode, \".json\"));\n            case 15:\n              response = _context4.sent;\n              _context4.next = 18;\n              return response.json();\n            case 18:\n              json = _context4.sent;\n              district = json.features.find(function (f) {\n                return f.properties.name.includes(name) || name.includes(f.properties.name);\n              });\n              return _context4.abrupt(\"return\", district === null || district === void 0 ? void 0 : district.properties.adcode);\n            case 21:\n              _context4.next = 27;\n              break;\n            case 23:\n              _context4.prev = 23;\n              _context4.t0 = _context4[\"catch\"](0);\n              console.error('获取区域编码失败:', _context4.t0);\n              return _context4.abrupt(\"return\", null);\n            case 27:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[0, 23]]);\n      }))();\n    },\n    // 获取登录日志\n    getLoginLogs: function getLoginLogs() {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return getLoginLogList(_this4.logQuery);\n            case 3:\n              res = _context5.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this4.loginLogs = res.data.records.map(function (item) {\n                  return {\n                    loginTime: _this4.formatDateTime(item.loginTime),\n                    ip: item.ipaddr,\n                    location: \"\".concat(item.loginLocation),\n                    status: item.status\n                  };\n                });\n              } else {\n                _this4.$message.error(res.msg || '获取登录日志失败');\n              }\n              _context5.next = 11;\n              break;\n            case 7:\n              _context5.prev = 7;\n              _context5.t0 = _context5[\"catch\"](0);\n              console.error('获取登录日志失败:', _context5.t0);\n              _this4.$message.error('获取登录日志失败');\n            case 11:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 7]]);\n      }))();\n    },\n    // 获取操作日志\n    getOperationLogs: function getOperationLogs() {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _context6.prev = 0;\n              _context6.next = 3;\n              return getOperationLogList(_this5.logQuery);\n            case 3:\n              res = _context6.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this5.operationLogs = res.data.records.map(function (item) {\n                  return {\n                    operateTime: _this5.formatDateTime(item.createTime),\n                    module: item.title,\n                    // 使用 title 作为模块名\n                    operation: item.operName,\n                    // 使用 operName 作为操作内容\n                    status: item.status\n                  };\n                });\n              } else {\n                _this5.$message.error(res.msg || '获取操作日志失败');\n              }\n              _context6.next = 11;\n              break;\n            case 7:\n              _context6.prev = 7;\n              _context6.t0 = _context6[\"catch\"](0);\n              console.error('获取操作日志失败:', _context6.t0);\n              _this5.$message.error('获取操作日志失败');\n            case 11:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6, null, [[0, 7]]);\n      }))();\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "getLoginLogList", "getOperationLogList", "provinceMap", "cityMap", "name", "data", "loginLogs", "operationLogs", "mapChart", "currentArea", "mapData", "china", "value", "children", "beijing", "hasDevice", "shanghai", "<PERSON><PERSON><PERSON>", "zhejiang", "guangdong", "guangzhou", "<PERSON><PERSON><PERSON>", "log<PERSON><PERSON>y", "pageNum", "pageSize", "orderByColumn", "isAsc", "mounted", "initEchartsMap", "getLoginLogs", "getOperationLogs", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "chartDom", "response", "chinaMap", "wrap", "_callee$", "_context", "prev", "next", "document", "getElementById", "init", "fetch", "sent", "json", "registerMap", "loadMap", "t0", "console", "error", "stop", "mapName", "_arguments", "arguments", "_this2", "_callee3", "code", "mapJson", "mapOption", "_callee3$", "_context3", "length", "undefined", "getAreaCode", "log", "concat", "toLowerCase", "tooltip", "trigger", "formatter", "params", "visualMap", "min", "max", "text", "realtime", "calculable", "inRange", "color", "series", "type", "map", "roam", "zoom", "center", "scaleLimit", "label", "show", "fontSize", "backgroundColor", "padding", "borderRadius", "textBorder<PERSON>idth", "textBorderColor", "itemStyle", "areaColor", "borderColor", "borderWidth", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "emphasis", "item", "setOption", "off", "on", "_ref", "_callee2", "areaName", "nextLevel", "_code", "_callee2$", "_context2", "find", "replace", "getNextLevelCode", "push", "_x", "apply", "handleAreaClick", "index", "slice", "currentLevel", "_cityMap$parentCode", "parentCode", "cityCode", "_this3", "_callee4", "_cityMap$parentCode2", "_parentCode", "district", "_callee4$", "_context4", "abrupt", "features", "f", "properties", "includes", "adcode", "_this4", "_callee5", "res", "_callee5$", "_context5", "records", "loginTime", "formatDateTime", "ip", "ipaddr", "location", "loginLocation", "status", "$message", "msg", "_this5", "_callee6", "_callee6$", "_context6", "operateTime", "createTime", "module", "title", "operation", "operName", "time", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["src/views/dashboard/home/<USER>"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日新增用户</div>\r\n            <div class=\"value\">128</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span class=\"up\">+12.5%</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-user\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日订单数</div>\r\n            <div class=\"value\">256</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span class=\"up\">+8.3%</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-s-order\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日交易额</div>\r\n            <div class=\"value\">￥25,800</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span class=\"down\">-5.2%</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-money\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"6\">\r\n        <el-card shadow=\"hover\" class=\"dashboard-card\">\r\n          <div class=\"card-header\">\r\n            <div class=\"title\">今日新增广告设备</div>\r\n            <div class=\"value\">128</div>\r\n            <div class=\"trend\">\r\n              较昨日\r\n              <span class=\"up\">+15.8%</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            <i class=\"el-icon-monitor\"></i>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 日志明细 -->\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n      <el-col :span=\"24\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>广告设备分布</span>\r\n            <el-breadcrumb v-if=\"currentArea.length > 0\" separator=\"/\" style=\"display: inline-block; margin-left: 15px;\">\r\n              <el-breadcrumb-item \r\n                v-for=\"(area, index) in currentArea\" \r\n                :key=\"index\"\r\n                @click.native=\"handleAreaClick(index)\">\r\n                {{ area.name }}\r\n              </el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n          </div>\r\n          <div id=\"deviceMap\" style=\"height: 700px; margin-top: -10px; background: linear-gradient(to bottom, #020b1c, #0a2b5a);\"></div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\r\n      <el-col :span=\"12\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>登录日志</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$router.push('/dashboard/log/login')\">\r\n              查看更多\r\n            </el-button>\r\n          </div>\r\n          <el-table :data=\"loginLogs\" stripe style=\"width: 100%\">\r\n            <el-table-column prop=\"loginTime\" label=\"登录时间\" width=\"180\" />\r\n            <el-table-column prop=\"ip\" label=\"登录IP\" width=\"140\" />\r\n            <el-table-column prop=\"location\" label=\"登录地点\" min-width=\"180\" />\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"scope.row.status === '1' ? 'success' : 'danger'\">\r\n                  {{ scope.row.status === '1' ? '成功' : '失败' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-card class=\"box-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>操作日志</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"$router.push('/dashboard/log/operation')\">\r\n              查看更多\r\n            </el-button>\r\n          </div>\r\n          <el-table :data=\"operationLogs\" stripe style=\"width: 100%\">\r\n            <el-table-column prop=\"operateTime\" label=\"操作时间\" width=\"180\" />\r\n            <el-table-column prop=\"module\" label=\"操作模块\" width=\"140\" />\r\n            <el-table-column prop=\"operation\" label=\"操作内容\" min-width=\"180\" />\r\n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag :type=\"scope.row.status === '1' ? 'success' : 'danger'\">\r\n                  {{ scope.row.status === '1' ? '成功' : '失败' }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { getLoginLogList } from '@/api/log/login'\r\nimport { getOperationLogList } from '@/api/log/operation'\r\n\r\nconst provinceMap = {\r\n  'guangdong': '440000',\r\n  'beijing': '110000',\r\n  'shanghai': '310000',\r\n  'tianjin': '120000',\r\n  'chongqing': '500000',\r\n  'hebei': '130000',\r\n  'shanxi': '140000',\r\n  'liaoning': '210000',\r\n  'jilin': '220000',\r\n  'heilongjiang': '230000',\r\n  'jiangsu': '320000',\r\n  'zhejiang': '330000',\r\n  'anhui': '340000',\r\n  'fujian': '350000',\r\n  'jiangxi': '360000',\r\n  'shandong': '370000',\r\n  'henan': '410000',\r\n  'hubei': '420000',\r\n  'hunan': '430000',\r\n  'guangxi': '450000',\r\n  'hainan': '460000',\r\n  'sichuan': '510000',\r\n  'guizhou': '520000',\r\n  'yunnan': '530000',\r\n  'xizang': '540000',\r\n  'shaanxi': '610000',\r\n  'gansu': '620000',\r\n  'qinghai': '630000',\r\n  'ningxia': '640000',\r\n  'xinjiang': '650000'\r\n}\r\n\r\nconst cityMap = {\r\n  '440000': {\r\n    'guangzhou': '440100',\r\n    'shenzhen': '440300',\r\n    'zhuhai': '440400',\r\n    'shantou': '440500',\r\n    'foshan': '440600',\r\n    'shaoguan': '440200',\r\n    'zhanjiang': '440800',\r\n    'zhaoqing': '441200',\r\n    'jiangmen': '440700',\r\n    'maoming': '440900',\r\n    'huizhou': '441300',\r\n    'meizhou': '441400',\r\n    'shanwei': '441500',\r\n    'heyuan': '441600',\r\n    'yangjiang': '441700',\r\n    'qingyuan': '441800',\r\n    'dongguan': '441900',\r\n    'zhongshan': '442000',\r\n    'chaozhou': '445100',\r\n    'jieyang': '445200',\r\n    'yunfu': '445300'\r\n  },\r\n  '110000': {\r\n    'dongcheng': '110101',\r\n    'xicheng': '110102',\r\n    'chaoyang': '110105',\r\n    'haidian': '110108',\r\n    'fengtai': '110106',\r\n    'shijingshan': '110107'\r\n  },\r\n  '310000': {\r\n    'huangpu': '310101',\r\n    'xuhui': '310104',\r\n    'changning': '310105',\r\n    'jingan': '310106',\r\n    'putuo': '310107',\r\n    'hongkou': '310109'\r\n  },\r\n  '320000': {\r\n    'nanjing': '320100',\r\n    'suzhou': '320500',\r\n    'wuxi': '320200',\r\n    'changzhou': '320400',\r\n    'nantong': '320600',\r\n    'yangzhou': '321000'\r\n  },\r\n  '330000': {\r\n    'hangzhou': '330100',\r\n    'ningbo': '330200',\r\n    'wenzhou': '330300',\r\n    'jiaxing': '330400',\r\n    'huzhou': '330500',\r\n    'shaoxing': '330600'\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  data() {\r\n    return {\r\n      // 登录日志\r\n      loginLogs: [],\r\n      // 操作日志\r\n      operationLogs: [],\r\n      // 添加新的地图关数据\r\n      mapChart: null,\r\n      currentArea: [], // 当前区域层级 [{name: '广东省', code: '440000'}, {name: '深圳市', code: '440300'}]\r\n      mapData: {\r\n        china: [\r\n          { name: '北京', value: 80, children: true },\r\n          { name: '天津', value: 45, children: true },\r\n          { name: '河北', value: 125, children: true },\r\n          { name: '山西', value: 85, children: true },\r\n          { name: '内蒙古', value: 95, children: true },\r\n          { name: '辽宁', value: 115, children: true },\r\n          { name: '吉林', value: 78, children: true },\r\n          { name: '黑龙江', value: 88, children: true },\r\n          { name: '上海', value: 120, children: true },\r\n          { name: '江苏', value: 142, children: true },\r\n          { name: '浙江', value: 138, children: true },\r\n          { name: '安徽', value: 98, children: true },\r\n          { name: '福建', value: 108, children: true },\r\n          { name: '江西', value: 85, children: true },\r\n          { name: '山东', value: 132, children: true },\r\n          { name: '河南', value: 128, children: true },\r\n          { name: '湖北', value: 110, children: true },\r\n          { name: '湖南', value: 105, children: true },\r\n          { name: '广东', value: 150, children: true },\r\n          { name: '广西', value: 92, children: true },\r\n          { name: '海南', value: 68, children: true },\r\n          { name: '重庆', value: 95, children: true },\r\n          { name: '四川', value: 122, children: true },\r\n          { name: '贵州', value: 82, children: true },\r\n          { name: '云南', value: 88, children: true },\r\n          { name: '西藏', value: 45, children: true },\r\n          { name: '陕西', value: 98, children: true },\r\n          { name: '甘肃', value: 75, children: true },\r\n          { name: '青海', value: 55, children: true },\r\n          { name: '宁夏', value: 48, children: true },\r\n          { name: '新疆', value: 72, children: true },\r\n          { name: '台湾', value: 0, children: false },\r\n          { name: '香港', value: 65, children: true },\r\n          { name: '澳门', value: 45, children: true }\r\n        ],\r\n        beijing: [\r\n          { name: '东城区', value: 25, hasDevice: true },\r\n          { name: '西城区', value: 30, hasDevice: true },\r\n          { name: '朝阳区', value: 45, hasDevice: true },\r\n          { name: '海淀区', value: 50, hasDevice: true },\r\n          { name: '丰台区', value: 28, hasDevice: true },\r\n          { name: '石景山区', value: 20, hasDevice: true }\r\n        ],\r\n        shanghai: [\r\n          { name: '黄浦区', value: 28, hasDevice: true },\r\n          { name: '徐汇区', value: 35, hasDevice: true },\r\n          { name: '长宁区', value: 30, hasDevice: true },\r\n          { name: '静安区', value: 40, hasDevice: true },\r\n          { name: '普陀区', value: 25, hasDevice: true },\r\n          { name: '虹口区', value: 22, hasDevice: true }\r\n        ],\r\n        jiangsu: [\r\n          { name: '南京', value: 55, children: true },\r\n          { name: '苏州', value: 60, children: true },\r\n          { name: '无锡', value: 45, children: true },\r\n          { name: '常州', value: 35, children: true },\r\n          { name: '南通', value: 30, children: true },\r\n          { name: '扬州', value: 25, children: true }\r\n        ],\r\n        zhejiang: [\r\n          { name: '杭州', value: 65, children: true },\r\n          { name: '宁波', value: 50, children: true },\r\n          { name: '温州', value: 40, children: true },\r\n          { name: '嘉兴', value: 30, children: true },\r\n          { name: '湖州', value: 25, children: true },\r\n          { name: '绍兴', value: 35, children: true }\r\n        ],\r\n        guangdong: [\r\n          { name: '广州', value: 45, children: true },\r\n          { name: '深圳', value: 50, children: true },\r\n          { name: '珠海', value: 25, children: true },\r\n          { name: '汕头', value: 20, children: true },\r\n          { name: '佛山', value: 35, children: true },\r\n          { name: '韶关', value: 15, children: true },\r\n          { name: '湛江', value: 18, children: true },\r\n          { name: '肇庆', value: 22, children: true },\r\n          { name: '江门', value: 28, children: true },\r\n          { name: '茂名', value: 20, children: true },\r\n          { name: '惠州', value: 30, children: true },\r\n          { name: '梅州', value: 15, children: true },\r\n          { name: '汕尾', value: 12, children: true },\r\n          { name: '河源', value: 18, children: true },\r\n          { name: '阳江', value: 16, children: true },\r\n          { name: '清远', value: 25, children: true },\r\n          { name: '东莞', value: 40, children: true },\r\n          { name: '中山', value: 32, children: true },\r\n          { name: '潮州', value: 15, children: true },\r\n          { name: '揭阳', value: 18, children: true },\r\n          { name: '云浮', value: 12, children: true }\r\n        ],\r\n        guangzhou: [\r\n          { name: '越秀区', value: 15, hasDevice: true },\r\n          { name: '海珠区', value: 12, hasDevice: true },\r\n          { name: '荔湾区', value: 10, hasDevice: true },\r\n          { name: '天河区', value: 20, hasDevice: true },\r\n          { name: '白云区', value: 18, hasDevice: true },\r\n          { name: '黄埔区', value: 15, hasDevice: true }\r\n        ],\r\n        shenzhen: [\r\n          { name: '福田区', value: 35, hasDevice: true },\r\n          { name: '罗湖区', value: 28, hasDevice: true },\r\n          { name: '南山区', value: 42, hasDevice: true },\r\n          { name: '宝安区', value: 38, hasDevice: true },\r\n          { name: '龙岗区', value: 32, hasDevice: true },\r\n          { name: '盐田区', value: 15, hasDevice: true },\r\n          { name: '龙华区', value: 30, hasDevice: true },\r\n          { name: '坪山区', value: 18, hasDevice: true },\r\n          { name: '光明区', value: 20, hasDevice: true },\r\n          { name: '大鹏新区', value: 12, hasDevice: true }\r\n        ]\r\n      },\r\n      // 修改查询参数\r\n      logQuery: {\r\n        pageNum: 1,\r\n        pageSize: 3,  // 只获取3条记录\r\n        orderByColumn: 'createTime',  // 按创建时间排序\r\n        isAsc: 'desc'  // 降序排序，最新的在前\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initEchartsMap()\r\n    this.getLoginLogs()\r\n    this.getOperationLogs()\r\n  },\r\n  beforeDestroy() {\r\n    if (this.mapChart) {\r\n      this.mapChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    async initEchartsMap() {\r\n      try {\r\n        const chartDom = document.getElementById('deviceMap')\r\n        this.mapChart = echarts.init(chartDom)\r\n        \r\n        // 注册地图\r\n        const response = await fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json')\r\n        const chinaMap = await response.json()\r\n        echarts.registerMap('china', chinaMap)\r\n        \r\n        await this.loadMap('china')\r\n      } catch (error) {\r\n        console.error('初始化地图失败:', error)\r\n      }\r\n    },\r\n\r\n    async loadMap(mapName, data = null) {\r\n      try {\r\n        if (mapName !== 'china') {\r\n          const code = this.getAreaCode(mapName)\r\n          console.log('Loading map for:', mapName, 'with code:', code)\r\n          const response = await fetch(`https://geo.datav.aliyun.com/areas_v3/bound/${code}_full.json`)\r\n          const mapJson = await response.json()\r\n          echarts.registerMap(mapName, mapJson)\r\n        }\r\n\r\n        const mapData = data || this.mapData[mapName.toLowerCase()] || []\r\n        console.log('Map data for:', mapName, mapData)\r\n\r\n        // 抽取地图配置\r\n        const mapOption = {\r\n          tooltip: {\r\n            trigger: 'item',\r\n            formatter: params => {\r\n              const value = params.value || 0\r\n              return `${params.name}\\n${value}`\r\n            }\r\n          },\r\n          visualMap: {\r\n            min: 0,\r\n            max: 200,\r\n            text: ['高', '低'],\r\n            realtime: false,\r\n            calculable: true,\r\n            inRange: {\r\n              color: ['#ffe4e1', '#ff4500']\r\n            }\r\n          },\r\n          series: [{\r\n            name: '设备分布',\r\n            type: 'map',\r\n            map: mapName,\r\n            roam: true,\r\n            zoom: 1.5,\r\n            center: [104.297, 35.861],\r\n            scaleLimit: {\r\n              min: 1,\r\n              max: 5\r\n            },\r\n            label: {\r\n              show: true,\r\n              formatter: params => {\r\n                const value = params.value || 0\r\n                return `${params.name}\\n${value}`\r\n              },\r\n              fontSize: 14,\r\n              color: '#fff',\r\n              backgroundColor: 'rgba(0, 150, 255, 0.5)',\r\n              padding: [4, 8],\r\n              borderRadius: 4,\r\n              textBorderWidth: 2,\r\n              textBorderColor: 'rgba(0, 0, 0, 0.8)'\r\n            },\r\n            itemStyle: {\r\n              areaColor: '#0c2c5a',\r\n              borderColor: '#00fcff',\r\n              borderWidth: 1.5,\r\n              shadowColor: 'rgba(0, 252, 255, 0.3)',\r\n              shadowBlur: 10\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                color: '#fff',\r\n                fontSize: 16,\r\n                backgroundColor: 'rgba(0, 150, 255, 0.8)'\r\n              },\r\n              itemStyle: {\r\n                areaColor: '#0052d9',\r\n                borderColor: '#00fcff',\r\n                borderWidth: 2,\r\n                shadowColor: 'rgba(0, 252, 255, 0.5)',\r\n                shadowBlur: 15\r\n              }\r\n            },\r\n            data: mapData.map(item => ({\r\n              name: item.name,\r\n              value: item.value || 0,\r\n              children: item.children,\r\n              hasDevice: item.hasDevice,\r\n              itemStyle: item.hasDevice ? {\r\n                areaColor: '#ff4500',\r\n                shadowColor: 'rgba(255, 69, 0, 0.5)',\r\n                shadowBlur: 10\r\n              } : null\r\n            }))\r\n          }]\r\n        }\r\n\r\n        this.mapChart.setOption(mapOption)\r\n\r\n        // 修改点击事件处理\r\n        this.mapChart.off('click').on('click', async params => {\r\n          console.log('Clicked area:', params.name)\r\n          const data = mapData.find(item => item.name === params.name)\r\n          if (data && data.children) {\r\n            // 处理地区名称，移除各种后缀\r\n            const areaName = params.name.replace(/(省|市|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '')\r\n            const nextLevel = areaName.toLowerCase()\r\n            console.log('Next level:', nextLevel)\r\n\r\n            // 获取下一级区域的编码\r\n            const code = await this.getNextLevelCode(areaName, this.currentArea.length)\r\n            console.log('Next level code:', code)\r\n            if (code) {\r\n              this.currentArea.push({\r\n                name: params.name,\r\n                code: code\r\n              })\r\n              this.loadMap(nextLevel)\r\n            }\r\n          }\r\n        })\r\n      } catch (error) {\r\n        console.error('加载地图数据失败:', error)\r\n      }\r\n    },\r\n\r\n    handleAreaClick(index) {\r\n      if (index === 0) {\r\n        this.currentArea = []\r\n        this.loadMap('china')\r\n      } else {\r\n        this.currentArea = this.currentArea.slice(0, index + 1)\r\n        const areaName = this.currentArea[index].name\r\n          .replace(/(省|自治区|维吾尔自治区|回族自治区|壮族自治区|特别行政区)$/, '')\r\n        this.loadMap(areaName.toLowerCase())\r\n      }\r\n    },\r\n\r\n    getAreaCode(name) {\r\n      const currentLevel = this.currentArea.length\r\n      const areaName = name.toLowerCase()\r\n      console.log('Getting area code for:', name, 'level:', currentLevel)\r\n      \r\n      if (currentLevel === 0) {\r\n        return '100000'\r\n      } else if (currentLevel === 1) {\r\n        return provinceMap[areaName] || '100000'\r\n      } else if (currentLevel === 2) {\r\n        const parentCode = this.currentArea[0].code\r\n        const cityCode = cityMap[parentCode]?.[areaName]\r\n        console.log('City code:', cityCode, 'for parent:', parentCode)\r\n        return cityCode || parentCode\r\n      }\r\n      return '100000'\r\n    },\r\n\r\n    async getNextLevelCode(name, currentLevel) {\r\n      try {\r\n        const areaName = name.toLowerCase()\r\n        if (currentLevel === 0) {\r\n          // 获取省级编码\r\n          return provinceMap[areaName]\r\n        } else if (currentLevel === 1) {\r\n          // 获取市级编码\r\n          const parentCode = this.currentArea[0].code\r\n          return cityMap[parentCode]?.[areaName]\r\n        } else if (currentLevel === 2) {\r\n          // 获取区级编码\r\n          const parentCode = this.currentArea[1].code\r\n          const response = await fetch(`https://geo.datav.aliyun.com/areas_v3/bound/${parentCode}.json`)\r\n          const json = await response.json()\r\n          const district = json.features.find(f => \r\n            f.properties.name.includes(name) || \r\n            name.includes(f.properties.name)\r\n          )\r\n          return district?.properties.adcode\r\n        }\r\n      } catch (error) {\r\n        console.error('获取区域编码失败:', error)\r\n        return null\r\n      }\r\n    },\r\n\r\n    // 获取登录日志\r\n    async getLoginLogs() {\r\n      try {\r\n        const res = await getLoginLogList(this.logQuery)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.loginLogs = res.data.records.map(item => ({\r\n            loginTime: this.formatDateTime(item.loginTime),\r\n            ip: item.ipaddr,\r\n            location: `${item.loginLocation }`,\r\n            status: item.status\r\n          }))\r\n        } else {\r\n          this.$message.error(res.msg || '获取登录日志失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取登录日志失败:', error)\r\n        this.$message.error('获取登录日志失败')\r\n      }\r\n    },\r\n\r\n    // 获取操作日志\r\n    async getOperationLogs() {\r\n      try {\r\n        const res = await getOperationLogList(this.logQuery)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.operationLogs = res.data.records.map(item => ({\r\n            operateTime: this.formatDateTime(item.createTime),\r\n            module: item.title,  // 使用 title 作为模块名\r\n            operation: item.operName,  // 使用 operName 作为操作内容\r\n            status: item.status\r\n          }))\r\n        } else {\r\n          this.$message.error(res.msg || '获取操作日志失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取操作日志失败:', error)\r\n        this.$message.error('获取操作日志失败')\r\n      }\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      const date = new Date(time)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-container {\r\n  padding: 20px;\r\n  height: calc(100vh - 84px);  // 修复滚动条问题\r\n  overflow-y: auto;  // 添加垂直滚动\r\n\r\n  .dashboard-card {\r\n    position: relative;\r\n    height: 120px;\r\n    overflow: hidden;\r\n\r\n    .card-header {\r\n      position: relative;\r\n      z-index: 1;\r\n\r\n      .title {\r\n        font-size: 14px;\r\n        color: #909399;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 24px;\r\n        font-weight: bold;\r\n        color: #303133;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .trend {\r\n        font-size: 13px;\r\n        color: #909399;\r\n\r\n        .up {\r\n          color: #67C23A;\r\n        }\r\n\r\n        .down {\r\n          color: #F56C6C;\r\n        }\r\n      }\r\n    }\r\n\r\n    .icon {\r\n      position: absolute;\r\n      right: 20px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      font-size: 48px;\r\n      opacity: 0.1;\r\n      transition: all 0.3s;\r\n\r\n      i {\r\n        color: #1890ff;\r\n      }\r\n    }\r\n\r\n    &:hover {\r\n      .icon {\r\n        opacity: 0.2;\r\n        transform: translateY(-50%) scale(1.1);\r\n      }\r\n    }\r\n  }\r\n\r\n  #userMap,\r\n  #deviceMap {\r\n    height: 700px;\r\n    width: 100%;\r\n    margin-top: -10px;\r\n    background: linear-gradient(to bottom, #020b1c, #0a2b5a);\r\n  }\r\n}\r\n\r\n.el-breadcrumb {\r\n  display: inline-block;\r\n  margin-left: 15px;\r\n  \r\n  :deep(.el-breadcrumb__item) {\r\n    cursor: pointer;\r\n    \r\n    &:hover {\r\n      color: #409EFF;\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;AAuIA,YAAAA,OAAA;AACA,SAAAC,eAAA;AACA,SAAAC,mBAAA;AAEA,IAAAC,WAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AAEA,IAAAC,OAAA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MACA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,WAAA;MAAA;MACAC,OAAA;QACAC,KAAA,GACA;UAAAP,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,EACA;QACAC,OAAA,GACA;UAAAV,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAZ,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,EACA;QACAE,OAAA,GACA;UAAAb,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,EACA;QACAK,QAAA,GACA;UAAAd,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,EACA;QACAM,SAAA,GACA;UAAAf,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,GACA;UAAAT,IAAA;UAAAQ,KAAA;UAAAC,QAAA;QAAA,EACA;QACAO,SAAA,GACA;UAAAhB,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,EACA;QACAM,QAAA,GACA;UAAAjB,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA,GACA;UAAAX,IAAA;UAAAQ,KAAA;UAAAG,SAAA;QAAA;MAEA;MACA;MACAO,QAAA;QACAC,OAAA;QACAC,QAAA;QAAA;QACAC,aAAA;QAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;IACA,KAAAC,YAAA;IACA,KAAAC,gBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAvB,QAAA;MACA,KAAAA,QAAA,CAAAwB,OAAA;IACA;EACA;EACAC,OAAA;IACAL,cAAA,WAAAA,eAAA;MAAA,IAAAM,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA;QAAA,OAAAL,mBAAA,GAAAM,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAN,QAAA,GAAAQ,QAAA,CAAAC,cAAA;cACAd,KAAA,CAAA1B,QAAA,GAAAT,OAAA,CAAAkD,IAAA,CAAAV,QAAA;;cAEA;cAAAK,QAAA,CAAAE,IAAA;cAAA,OACAI,KAAA;YAAA;cAAAV,QAAA,GAAAI,QAAA,CAAAO,IAAA;cAAAP,QAAA,CAAAE,IAAA;cAAA,OACAN,QAAA,CAAAY,IAAA;YAAA;cAAAX,QAAA,GAAAG,QAAA,CAAAO,IAAA;cACApD,OAAA,CAAAsD,WAAA,UAAAZ,QAAA;cAAAG,QAAA,CAAAE,IAAA;cAAA,OAEAZ,KAAA,CAAAoB,OAAA;YAAA;cAAAV,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAW,EAAA,GAAAX,QAAA;cAEAY,OAAA,CAAAC,KAAA,aAAAb,QAAA,CAAAW,EAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IAEAgB,OAAA,WAAAA,QAAAK,OAAA;MAAA,IAAAC,UAAA,GAAAC,SAAA;QAAAC,MAAA;MAAA,OAAA3B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA0B,SAAA;QAAA,IAAA1D,IAAA,EAAA2D,IAAA,EAAAxB,QAAA,EAAAyB,OAAA,EAAAvD,OAAA,EAAAwD,SAAA;QAAA,OAAA9B,mBAAA,GAAAM,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cAAAzC,IAAA,GAAAuD,UAAA,CAAAS,MAAA,QAAAT,UAAA,QAAAU,SAAA,GAAAV,UAAA;cAAAQ,SAAA,CAAAvB,IAAA;cAAA,MAEAc,OAAA;gBAAAS,SAAA,CAAAtB,IAAA;gBAAA;cAAA;cACAkB,IAAA,GAAAF,MAAA,CAAAS,WAAA,CAAAZ,OAAA;cACAH,OAAA,CAAAgB,GAAA,qBAAAb,OAAA,gBAAAK,IAAA;cAAAI,SAAA,CAAAtB,IAAA;cAAA,OACAI,KAAA,gDAAAuB,MAAA,CAAAT,IAAA;YAAA;cAAAxB,QAAA,GAAA4B,SAAA,CAAAjB,IAAA;cAAAiB,SAAA,CAAAtB,IAAA;cAAA,OACAN,QAAA,CAAAY,IAAA;YAAA;cAAAa,OAAA,GAAAG,SAAA,CAAAjB,IAAA;cACApD,OAAA,CAAAsD,WAAA,CAAAM,OAAA,EAAAM,OAAA;YAAA;cAGAvD,OAAA,GAAAL,IAAA,IAAAyD,MAAA,CAAApD,OAAA,CAAAiD,OAAA,CAAAe,WAAA;cACAlB,OAAA,CAAAgB,GAAA,kBAAAb,OAAA,EAAAjD,OAAA;;cAEA;cACAwD,SAAA;gBACAS,OAAA;kBACAC,OAAA;kBACAC,SAAA,WAAAA,UAAAC,MAAA;oBACA,IAAAlE,KAAA,GAAAkE,MAAA,CAAAlE,KAAA;oBACA,UAAA6D,MAAA,CAAAK,MAAA,CAAA1E,IAAA,QAAAqE,MAAA,CAAA7D,KAAA;kBACA;gBACA;gBACAmE,SAAA;kBACAC,GAAA;kBACAC,GAAA;kBACAC,IAAA;kBACAC,QAAA;kBACAC,UAAA;kBACAC,OAAA;oBACAC,KAAA;kBACA;gBACA;gBACAC,MAAA;kBACAnF,IAAA;kBACAoF,IAAA;kBACAC,GAAA,EAAA9B,OAAA;kBACA+B,IAAA;kBACAC,IAAA;kBACAC,MAAA;kBACAC,UAAA;oBACAb,GAAA;oBACAC,GAAA;kBACA;kBACAa,KAAA;oBACAC,IAAA;oBACAlB,SAAA,WAAAA,UAAAC,MAAA;sBACA,IAAAlE,KAAA,GAAAkE,MAAA,CAAAlE,KAAA;sBACA,UAAA6D,MAAA,CAAAK,MAAA,CAAA1E,IAAA,QAAAqE,MAAA,CAAA7D,KAAA;oBACA;oBACAoF,QAAA;oBACAV,KAAA;oBACAW,eAAA;oBACAC,OAAA;oBACAC,YAAA;oBACAC,eAAA;oBACAC,eAAA;kBACA;kBACAC,SAAA;oBACAC,SAAA;oBACAC,WAAA;oBACAC,WAAA;oBACAC,WAAA;oBACAC,UAAA;kBACA;kBACAC,QAAA;oBACAd,KAAA;sBACAC,IAAA;sBACAT,KAAA;sBACAU,QAAA;sBACAC,eAAA;oBACA;oBACAK,SAAA;sBACAC,SAAA;sBACAC,WAAA;sBACAC,WAAA;sBACAC,WAAA;sBACAC,UAAA;oBACA;kBACA;kBACAtG,IAAA,EAAAK,OAAA,CAAA+E,GAAA,WAAAoB,IAAA;oBAAA;sBACAzG,IAAA,EAAAyG,IAAA,CAAAzG,IAAA;sBACAQ,KAAA,EAAAiG,IAAA,CAAAjG,KAAA;sBACAC,QAAA,EAAAgG,IAAA,CAAAhG,QAAA;sBACAE,SAAA,EAAA8F,IAAA,CAAA9F,SAAA;sBACAuF,SAAA,EAAAO,IAAA,CAAA9F,SAAA;wBACAwF,SAAA;wBACAG,WAAA;wBACAC,UAAA;sBACA;oBACA;kBAAA;gBACA;cACA;cAEA7C,MAAA,CAAAtD,QAAA,CAAAsG,SAAA,CAAA5C,SAAA;;cAEA;cACAJ,MAAA,CAAAtD,QAAA,CAAAuG,GAAA,UAAAC,EAAA;gBAAA,IAAAC,IAAA,GAAA9E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6E,SAAApC,MAAA;kBAAA,IAAAzE,IAAA,EAAA8G,QAAA,EAAAC,SAAA,EAAAC,KAAA;kBAAA,OAAAjF,mBAAA,GAAAM,IAAA,UAAA4E,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAA1E,IAAA,GAAA0E,SAAA,CAAAzE,IAAA;sBAAA;wBACAU,OAAA,CAAAgB,GAAA,kBAAAM,MAAA,CAAA1E,IAAA;wBACAC,IAAA,GAAAK,OAAA,CAAA8G,IAAA,WAAAX,IAAA;0BAAA,OAAAA,IAAA,CAAAzG,IAAA,KAAA0E,MAAA,CAAA1E,IAAA;wBAAA;wBAAA,MACAC,IAAA,IAAAA,IAAA,CAAAQ,QAAA;0BAAA0G,SAAA,CAAAzE,IAAA;0BAAA;wBAAA;wBACA;wBACAqE,QAAA,GAAArC,MAAA,CAAA1E,IAAA,CAAAqH,OAAA;wBACAL,SAAA,GAAAD,QAAA,CAAAzC,WAAA;wBACAlB,OAAA,CAAAgB,GAAA,gBAAA4C,SAAA;;wBAEA;wBAAAG,SAAA,CAAAzE,IAAA;wBAAA,OACAgB,MAAA,CAAA4D,gBAAA,CAAAP,QAAA,EAAArD,MAAA,CAAArD,WAAA,CAAA4D,MAAA;sBAAA;wBAAAL,KAAA,GAAAuD,SAAA,CAAApE,IAAA;wBACAK,OAAA,CAAAgB,GAAA,qBAAAR,KAAA;wBACA,IAAAA,KAAA;0BACAF,MAAA,CAAArD,WAAA,CAAAkH,IAAA;4BACAvH,IAAA,EAAA0E,MAAA,CAAA1E,IAAA;4BACA4D,IAAA,EAAAA;0BACA;0BACAF,MAAA,CAAAR,OAAA,CAAA8D,SAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAAG,SAAA,CAAA7D,IAAA;oBAAA;kBAAA,GAAAwD,QAAA;gBAAA,CAEA;gBAAA,iBAAAU,EAAA;kBAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAhE,SAAA;gBAAA;cAAA;cAAAO,SAAA,CAAAtB,IAAA;cAAA;YAAA;cAAAsB,SAAA,CAAAvB,IAAA;cAAAuB,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAEAZ,OAAA,CAAAC,KAAA,cAAAW,SAAA,CAAAb,EAAA;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAAV,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IAEA;IAEA+D,eAAA,WAAAA,gBAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAAtH,WAAA;QACA,KAAA6C,OAAA;MACA;QACA,KAAA7C,WAAA,QAAAA,WAAA,CAAAuH,KAAA,IAAAD,KAAA;QACA,IAAAZ,QAAA,QAAA1G,WAAA,CAAAsH,KAAA,EAAA3H,IAAA,CACAqH,OAAA;QACA,KAAAnE,OAAA,CAAA6D,QAAA,CAAAzC,WAAA;MACA;IACA;IAEAH,WAAA,WAAAA,YAAAnE,IAAA;MACA,IAAA6H,YAAA,QAAAxH,WAAA,CAAA4D,MAAA;MACA,IAAA8C,QAAA,GAAA/G,IAAA,CAAAsE,WAAA;MACAlB,OAAA,CAAAgB,GAAA,2BAAApE,IAAA,YAAA6H,YAAA;MAEA,IAAAA,YAAA;QACA;MACA,WAAAA,YAAA;QACA,OAAA/H,WAAA,CAAAiH,QAAA;MACA,WAAAc,YAAA;QAAA,IAAAC,mBAAA;QACA,IAAAC,UAAA,QAAA1H,WAAA,IAAAuD,IAAA;QACA,IAAAoE,QAAA,IAAAF,mBAAA,GAAA/H,OAAA,CAAAgI,UAAA,eAAAD,mBAAA,uBAAAA,mBAAA,CAAAf,QAAA;QACA3D,OAAA,CAAAgB,GAAA,eAAA4D,QAAA,iBAAAD,UAAA;QACA,OAAAC,QAAA,IAAAD,UAAA;MACA;MACA;IACA;IAEAT,gBAAA,WAAAA,iBAAAtH,IAAA,EAAA6H,YAAA;MAAA,IAAAI,MAAA;MAAA,OAAAlG,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiG,SAAA;QAAA,IAAAnB,QAAA,EAAAoB,oBAAA,EAAAJ,UAAA,EAAAK,WAAA,EAAAhG,QAAA,EAAAY,IAAA,EAAAqF,QAAA;QAAA,OAAArG,mBAAA,GAAAM,IAAA,UAAAgG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9F,IAAA,GAAA8F,SAAA,CAAA7F,IAAA;YAAA;cAAA6F,SAAA,CAAA9F,IAAA;cAEAsE,QAAA,GAAA/G,IAAA,CAAAsE,WAAA;cAAA,MACAuD,YAAA;gBAAAU,SAAA,CAAA7F,IAAA;gBAAA;cAAA;cAAA,OAAA6F,SAAA,CAAAC,MAAA,WAEA1I,WAAA,CAAAiH,QAAA;YAAA;cAAA,MACAc,YAAA;gBAAAU,SAAA,CAAA7F,IAAA;gBAAA;cAAA;cACA;cACAqF,UAAA,GAAAE,MAAA,CAAA5H,WAAA,IAAAuD,IAAA;cAAA,OAAA2E,SAAA,CAAAC,MAAA,YAAAL,oBAAA,GACApI,OAAA,CAAAgI,UAAA,eAAAI,oBAAA,uBAAAA,oBAAA,CAAApB,QAAA;YAAA;cAAA,MACAc,YAAA;gBAAAU,SAAA,CAAA7F,IAAA;gBAAA;cAAA;cACA;cACAqF,WAAA,GAAAE,MAAA,CAAA5H,WAAA,IAAAuD,IAAA;cAAA2E,SAAA,CAAA7F,IAAA;cAAA,OACAI,KAAA,gDAAAuB,MAAA,CAAA0D,WAAA;YAAA;cAAA3F,QAAA,GAAAmG,SAAA,CAAAxF,IAAA;cAAAwF,SAAA,CAAA7F,IAAA;cAAA,OACAN,QAAA,CAAAY,IAAA;YAAA;cAAAA,IAAA,GAAAuF,SAAA,CAAAxF,IAAA;cACAsF,QAAA,GAAArF,IAAA,CAAAyF,QAAA,CAAArB,IAAA,WAAAsB,CAAA;gBAAA,OACAA,CAAA,CAAAC,UAAA,CAAA3I,IAAA,CAAA4I,QAAA,CAAA5I,IAAA,KACAA,IAAA,CAAA4I,QAAA,CAAAF,CAAA,CAAAC,UAAA,CAAA3I,IAAA;cAAA,CACA;cAAA,OAAAuI,SAAA,CAAAC,MAAA,WACAH,QAAA,aAAAA,QAAA,uBAAAA,QAAA,CAAAM,UAAA,CAAAE,MAAA;YAAA;cAAAN,SAAA,CAAA7F,IAAA;cAAA;YAAA;cAAA6F,SAAA,CAAA9F,IAAA;cAAA8F,SAAA,CAAApF,EAAA,GAAAoF,SAAA;cAGAnF,OAAA,CAAAC,KAAA,cAAAkF,SAAA,CAAApF,EAAA;cAAA,OAAAoF,SAAA,CAAAC,MAAA,WACA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAjF,IAAA;UAAA;QAAA,GAAA4E,QAAA;MAAA;IAEA;IAEA;IACAzG,YAAA,WAAAA,aAAA;MAAA,IAAAqH,MAAA;MAAA,OAAA/G,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8G,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAhH,mBAAA,GAAAM,IAAA,UAAA2G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzG,IAAA,GAAAyG,SAAA,CAAAxG,IAAA;YAAA;cAAAwG,SAAA,CAAAzG,IAAA;cAAAyG,SAAA,CAAAxG,IAAA;cAAA,OAEA9C,eAAA,CAAAkJ,MAAA,CAAA5H,QAAA;YAAA;cAAA8H,GAAA,GAAAE,SAAA,CAAAnG,IAAA;cACA,IAAAiG,GAAA,CAAApF,IAAA,UAAAoF,GAAA,CAAApF,IAAA;gBACAkF,MAAA,CAAA5I,SAAA,GAAA8I,GAAA,CAAA/I,IAAA,CAAAkJ,OAAA,CAAA9D,GAAA,WAAAoB,IAAA;kBAAA;oBACA2C,SAAA,EAAAN,MAAA,CAAAO,cAAA,CAAA5C,IAAA,CAAA2C,SAAA;oBACAE,EAAA,EAAA7C,IAAA,CAAA8C,MAAA;oBACAC,QAAA,KAAAnF,MAAA,CAAAoC,IAAA,CAAAgD,aAAA;oBACAC,MAAA,EAAAjD,IAAA,CAAAiD;kBACA;gBAAA;cACA;gBACAZ,MAAA,CAAAa,QAAA,CAAAtG,KAAA,CAAA2F,GAAA,CAAAY,GAAA;cACA;cAAAV,SAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,SAAA,CAAAzG,IAAA;cAAAyG,SAAA,CAAA/F,EAAA,GAAA+F,SAAA;cAEA9F,OAAA,CAAAC,KAAA,cAAA6F,SAAA,CAAA/F,EAAA;cACA2F,MAAA,CAAAa,QAAA,CAAAtG,KAAA;YAAA;YAAA;cAAA,OAAA6F,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAAyF,QAAA;MAAA;IAEA;IAEA;IACArH,gBAAA,WAAAA,iBAAA;MAAA,IAAAmI,MAAA;MAAA,OAAA9H,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6H,SAAA;QAAA,IAAAd,GAAA;QAAA,OAAAhH,mBAAA,GAAAM,IAAA,UAAAyH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvH,IAAA,GAAAuH,SAAA,CAAAtH,IAAA;YAAA;cAAAsH,SAAA,CAAAvH,IAAA;cAAAuH,SAAA,CAAAtH,IAAA;cAAA,OAEA7C,mBAAA,CAAAgK,MAAA,CAAA3I,QAAA;YAAA;cAAA8H,GAAA,GAAAgB,SAAA,CAAAjH,IAAA;cACA,IAAAiG,GAAA,CAAApF,IAAA,UAAAoF,GAAA,CAAApF,IAAA;gBACAiG,MAAA,CAAA1J,aAAA,GAAA6I,GAAA,CAAA/I,IAAA,CAAAkJ,OAAA,CAAA9D,GAAA,WAAAoB,IAAA;kBAAA;oBACAwD,WAAA,EAAAJ,MAAA,CAAAR,cAAA,CAAA5C,IAAA,CAAAyD,UAAA;oBACAC,MAAA,EAAA1D,IAAA,CAAA2D,KAAA;oBAAA;oBACAC,SAAA,EAAA5D,IAAA,CAAA6D,QAAA;oBAAA;oBACAZ,MAAA,EAAAjD,IAAA,CAAAiD;kBACA;gBAAA;cACA;gBACAG,MAAA,CAAAF,QAAA,CAAAtG,KAAA,CAAA2F,GAAA,CAAAY,GAAA;cACA;cAAAI,SAAA,CAAAtH,IAAA;cAAA;YAAA;cAAAsH,SAAA,CAAAvH,IAAA;cAAAuH,SAAA,CAAA7G,EAAA,GAAA6G,SAAA;cAEA5G,OAAA,CAAAC,KAAA,cAAA2G,SAAA,CAAA7G,EAAA;cACA0G,MAAA,CAAAF,QAAA,CAAAtG,KAAA;YAAA;YAAA;cAAA,OAAA2G,SAAA,CAAA1G,IAAA;UAAA;QAAA,GAAAwG,QAAA;MAAA;IAEA;IAEA;IACAT,cAAA,WAAAA,eAAAkB,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAA1G,MAAA,CAAAqG,IAAA,OAAArG,MAAA,CAAAuG,KAAA,OAAAvG,MAAA,CAAA2G,GAAA,OAAA3G,MAAA,CAAA6G,KAAA,OAAA7G,MAAA,CAAA+G,OAAA,OAAA/G,MAAA,CAAAiH,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}