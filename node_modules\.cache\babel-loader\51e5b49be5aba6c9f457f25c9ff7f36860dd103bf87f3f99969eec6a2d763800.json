{"ast": null, "code": "import _regeneratorRuntime from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getUserList, getUserDetail, updateUserStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels as _getAgentLevels, updateUserLevel } from '@/api/user/user';\nimport { parseTime, formatDate } from '@/utils/date';\nexport default {\n  name: 'UserList',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        // 用户名/手机号\n        status: '',\n        // 状态\n        agentLevel: '',\n        // 代理级别\n        shareCode: '',\n        // 邀请码\n        referrerPhone: '',\n        // 邀请人手机号\n        dateRange: [],\n        startDate: '',\n        endDate: ''\n      },\n      // 代理级别选项\n      agentLevelOptions: [{\n        label: '业务员',\n        value: '1'\n      }, {\n        label: '主管',\n        value: '2'\n      }, {\n        label: '经理',\n        value: '3'\n      }, {\n        label: '总监',\n        value: '4'\n      }, {\n        label: '总经理',\n        value: '5'\n      }],\n      loading: false,\n      total: 0,\n      tableData: [],\n      // 充值相关\n      rechargeVisible: false,\n      rechargeUser: {},\n      rechargeForm: {\n        amount: 100,\n        remark: ''\n      },\n      rechargeRules: {\n        amount: [{\n          required: true,\n          message: '请输入充值金额',\n          trigger: 'blur'\n        }]\n      },\n      // 详情相关\n      detailVisible: false,\n      detailUser: {\n        username: '',\n        phone: '',\n        realName: '',\n        agentLevel: '',\n        teamCount: 0,\n        teamPerformance: 0,\n        createTime: '',\n        lastLoginTime: '',\n        balance: 0,\n        status: '1',\n        referrer: '',\n        inviteCode: '',\n        totalRecharge: 0,\n        totalWithdraw: 0,\n        commission: 0\n      },\n      // 银行卡相关\n      bankCardsVisible: false,\n      bankCardsLoading: false,\n      bankCards: [],\n      // 修改等级相关\n      changeLevelVisible: false,\n      currentUser: {},\n      levelForm: {\n        agentLevel: ''\n      },\n      levelRules: {\n        agentLevel: [{\n          required: true,\n          message: '请选择代理等级',\n          trigger: 'change'\n        }]\n      },\n      agentLevels: [] // 存储代理等级列表\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getAgentLevels(); // 获取代理等级列表\n  },\n  watch: {\n    // 监听日期范围变化\n    'listQuery.dateRange': function listQueryDateRange(val) {\n      if (val && val.length === 2) {\n        this.listQuery.startDate = formatDate(val[0]);\n        this.listQuery.endDate = formatDate(val[1]);\n      } else {\n        this.listQuery.startDate = '';\n        this.listQuery.endDate = '';\n      }\n    }\n  },\n  methods: {\n    // 获取列表数据\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              _context.prev = 1;\n              _context.next = 4;\n              return getUserList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0 || res.code === 200) {\n                // 确保数据存在\n                if (res.data) {\n                  _this.tableData = res.data.records || [];\n                  _this.total = res.data.total || 0;\n                } else {\n                  _this.tableData = [];\n                  _this.total = 0;\n                }\n              } else {\n                _this.$message.error(res.msg || '获取用户列表失败');\n              }\n              _context.next = 12;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](1);\n              console.error('获取用户列表失败:', _context.t0);\n              _this.$message.error('获取用户列表失败');\n            case 12:\n              _context.prev = 12;\n              _this.loading = false;\n              return _context.finish(12);\n            case 15:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[1, 8, 12, 15]]);\n      }))();\n    },\n    // 搜索\n    handleSearch: function handleSearch() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    // 重置查询\n    resetQuery: function resetQuery() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        status: '',\n        agentLevel: '',\n        // 重置代理级别\n        shareCode: '',\n        // 重置邀请码\n        referrerPhone: '',\n        // 重置邀请人手机号\n        dateRange: [],\n        startDate: '',\n        endDate: ''\n      };\n      this.getList();\n    },\n    // 格式化数字\n    formatNumber: function formatNumber(num) {\n      return num ? num.toLocaleString() : '0';\n    },\n    // 获代理级别标签类型\n    getLevelType: function getLevelType(level) {\n      var typeMap = {\n        '1': 'info',\n        // 业务员\n        '2': 'warning',\n        // 主管\n        '3': 'success',\n        // 经理\n        '4': 'danger',\n        // 总\n        '5': '' // 总经理\n      };\n      return typeMap[level] || '';\n    },\n    // 处理状态变更\n    handleStatusChange: function handleStatusChange(row) {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return updateUserStatus(row.id, row.status);\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this2.$message.success(\"\".concat(row.status === 1 ? '启用' : '禁用', \"\\u6210\\u529F\"));\n              } else {\n                row.status = row.status === 1 ? 0 : 1;\n                _this2.$message.error(res.msg || '操作失败');\n              }\n              _context2.next = 11;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              row.status = row.status === 1 ? 0 : 1;\n              _this2.$message.error('操作失败');\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      }))();\n    },\n    // 查看详情\n    handleDetail: function handleDetail(row) {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return getUserDetail(row.id);\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this3.detailUser = res.data;\n                _this3.detailVisible = true;\n              } else {\n                _this3.$message.error(res.msg || '获取详情失败');\n              }\n              _context3.next = 10;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              _this3.$message.error('获取详情失败');\n            case 10:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    // 打开充值对话框\n    handleRecharge: function handleRecharge(row) {\n      this.rechargeUser = row;\n      this.rechargeForm = {\n        amount: 100,\n        remark: ''\n      };\n      this.rechargeVisible = true;\n    },\n    // 提交充值\n    submitRecharge: function submitRecharge() {\n      var _this4 = this;\n      this.$refs.rechargeForm.validate(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n            while (1) switch (_context4.prev = _context4.next) {\n              case 0:\n                if (!valid) {\n                  _context4.next = 11;\n                  break;\n                }\n                _context4.prev = 1;\n                _context4.next = 4;\n                return rechargeUser(_this4.rechargeUser.id, _this4.rechargeForm);\n              case 4:\n                res = _context4.sent;\n                if (res.code === 0 || res.code === 200) {\n                  _this4.$message.success('充值成功');\n                  _this4.rechargeVisible = false;\n                  _this4.getList(); // 刷新列表\n                } else {\n                  _this4.$message.error(res.msg || '充值失败');\n                }\n                _context4.next = 11;\n                break;\n              case 8:\n                _context4.prev = 8;\n                _context4.t0 = _context4[\"catch\"](1);\n                _this4.$message.error('充值失败');\n              case 11:\n              case \"end\":\n                return _context4.stop();\n            }\n          }, _callee4, null, [[1, 8]]);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    // 分页相关\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    // 格式化日期时间\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '';\n      return parseTime(time);\n    },\n    // 格式化日期\n    formatDate: function formatDate(time) {\n      if (!time) return '';\n      return parseTime(time, 'yyyy-MM-dd');\n    },\n    // 重置密码\n    handleReset: function handleReset(row) {\n      var _this5 = this;\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return resetUserPassword(row.id);\n            case 3:\n              res = _context5.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this5.$message.success('密码重置成功');\n              } else {\n                _this5.$message.error(res.msg || '密码重置失败');\n              }\n              _context5.next = 10;\n              break;\n            case 7:\n              _context5.prev = 7;\n              _context5.t0 = _context5[\"catch\"](0);\n              _this5.$message.error('密码重置失败');\n            case 10:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 7]]);\n      })))[\"catch\"](function () {\n        // 取消重置，不做任何操作\n      });\n    },\n    // 查看银行\n    handleBankCards: function handleBankCards(row) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n          while (1) switch (_context6.prev = _context6.next) {\n            case 0:\n              _this6.bankCardsVisible = true;\n              _this6.bankCardsLoading = true;\n              _context6.prev = 2;\n              _context6.next = 5;\n              return getUserBankCards(row.id);\n            case 5:\n              res = _context6.sent;\n              if (res.code === 0 || res.code === 200) {\n                _this6.bankCards = res.data || [];\n              } else {\n                _this6.$message.error(res.msg || '获取银行卡列表失败');\n              }\n              _context6.next = 13;\n              break;\n            case 9:\n              _context6.prev = 9;\n              _context6.t0 = _context6[\"catch\"](2);\n              console.error('获取银行卡失败:', _context6.t0); // 添加错误日志\n              _this6.$message.error('获取银行卡列表失败');\n            case 13:\n              _context6.prev = 13;\n              _this6.bankCardsLoading = false;\n              return _context6.finish(13);\n            case 16:\n            case \"end\":\n              return _context6.stop();\n          }\n        }, _callee6, null, [[2, 9, 13, 16]]);\n      }))();\n    },\n    // 获取代理等级列表\n    getAgentLevels: function getAgentLevels() {\n      var _this7 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n          while (1) switch (_context7.prev = _context7.next) {\n            case 0:\n              _context7.prev = 0;\n              _context7.next = 3;\n              return _getAgentLevels();\n            case 3:\n              res = _context7.sent;\n              // 需要添加这个API\n              if (res.code === 0 || res.code === 200) {\n                _this7.agentLevels = res.data || [];\n              }\n              _context7.next = 10;\n              break;\n            case 7:\n              _context7.prev = 7;\n              _context7.t0 = _context7[\"catch\"](0);\n              console.error('获取代理等级列表失败:', _context7.t0);\n            case 10:\n            case \"end\":\n              return _context7.stop();\n          }\n        }, _callee7, null, [[0, 7]]);\n      }))();\n    },\n    // 打开修改等级对话框\n    handleChangeLevel: function handleChangeLevel(row) {\n      this.currentUser = row;\n      this.levelForm.agentLevel = row.agentLevel;\n      this.changeLevelVisible = true;\n    },\n    // 提交修改等级\n    submitChangeLevel: function submitChangeLevel() {\n      var _this8 = this;\n      this.$refs.levelForm.validate(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee8(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee8$(_context8) {\n            while (1) switch (_context8.prev = _context8.next) {\n              case 0:\n                if (!valid) {\n                  _context8.next = 11;\n                  break;\n                }\n                _context8.prev = 1;\n                _context8.next = 4;\n                return updateUserLevel(_this8.currentUser.id, _this8.levelForm.agentLevel);\n              case 4:\n                res = _context8.sent;\n                // 需要添加这个API\n                if (res.code === 0 || res.code === 200) {\n                  _this8.$message.success('修改等级成功');\n                  _this8.changeLevelVisible = false;\n                  _this8.getList(); // 刷新列表\n                } else {\n                  _this8.$message.error(res.msg || '修改等级失败');\n                }\n                _context8.next = 11;\n                break;\n              case 8:\n                _context8.prev = 8;\n                _context8.t0 = _context8[\"catch\"](1);\n                _this8.$message.error('修改等级失败');\n              case 11:\n              case \"end\":\n                return _context8.stop();\n            }\n          }, _callee8, null, [[1, 8]]);\n        }));\n        return function (_x2) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    }\n  }\n};", "map": {"version": 3, "names": ["getUserList", "getUserDetail", "updateUserStatus", "resetUserPassword", "rechargeUser", "getUserBankCards", "getAgentLevels", "updateUserLevel", "parseTime", "formatDate", "name", "data", "list<PERSON>uery", "page", "limit", "username", "status", "agentLevel", "shareCode", "referrerPhone", "date<PERSON><PERSON><PERSON>", "startDate", "endDate", "agentLevelOptions", "label", "value", "loading", "total", "tableData", "rechargeVisible", "rechargeForm", "amount", "remark", "rechargeRules", "required", "message", "trigger", "detailVisible", "detailUser", "phone", "realName", "teamCount", "teamPerformance", "createTime", "lastLoginTime", "balance", "referrer", "inviteCode", "totalRecharge", "totalWithdraw", "commission", "bankCardsVisible", "bankCardsLoading", "bankCards", "changeLevelVisible", "currentUser", "levelForm", "levelRules", "agentLevels", "created", "getList", "watch", "listQueryDateRange", "val", "length", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "records", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleSearch", "reset<PERSON><PERSON>y", "formatNumber", "num", "toLocaleString", "getLevelType", "level", "typeMap", "handleStatusChange", "row", "_this2", "_callee2", "_callee2$", "_context2", "id", "success", "concat", "handleDetail", "_this3", "_callee3", "_callee3$", "_context3", "handleRecharge", "submit<PERSON>echarge", "_this4", "$refs", "validate", "_ref", "_callee4", "valid", "_callee4$", "_context4", "_x", "apply", "arguments", "handleSizeChange", "handleCurrentChange", "formatDateTime", "time", "handleReset", "_this5", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "_callee5", "_callee5$", "_context5", "handleBankCards", "_this6", "_callee6", "_callee6$", "_context6", "_this7", "_callee7", "_callee7$", "_context7", "handleChangeLevel", "submitChangeLevel", "_this8", "_ref3", "_callee8", "_callee8$", "_context8", "_x2"], "sources": ["src/views/user/list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"5\">\r\n            <el-input\r\n              v-model=\"listQuery.username\"\r\n              placeholder=\"用户名/手机号\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n          <!-- <el-col :span=\"3\">\r\n            <el-select\r\n              v-model=\"listQuery.status\"\r\n              placeholder=\"状态\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            >\r\n              <el-option label=\"正常\" value=\"1\" />\r\n              <el-option label=\"禁用\" value=\"0\" />\r\n            </el-select>\r\n          </el-col> -->\r\n          <el-col :span=\"3\">\r\n            <el-select\r\n              v-model=\"listQuery.agentLevel\"\r\n              placeholder=\"代理级别\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in agentLevelOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-col>\r\n          <el-col :span=\"3\">\r\n            <el-input\r\n              v-model=\"listQuery.shareCode\"\r\n              placeholder=\"邀请码\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <el-input\r\n              v-model=\"listQuery.referrerPhone\"\r\n              placeholder=\"邀请人手机号\"\r\n              clearable\r\n              class=\"filter-item\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-date-picker\r\n              v-model=\"listQuery.dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              class=\"filter-item date-range-picker\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n        <el-row style=\"margin-top: 10px\">\r\n          <el-col :span=\"24\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n            <!-- <el-button type=\"warning\" icon=\"el-icon-download\">导出</el-button> -->\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"用户编号\" prop=\"userNo\" width=\"130\" align=\"center\" />\r\n        <el-table-column label=\"用户名称\" prop=\"username\" align=\"center\"  width=\"120\"/>\r\n        <el-table-column label=\"手机号码\" prop=\"phone\" align=\"center\" width=\"120\" />\r\n        <el-table-column label=\"推荐人\" align=\"center\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.referrerPhone\">\r\n              {{ scope.row.referrerPhone }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ scope.row.referrerShareCode }}</el-tag>\r\n            </div>\r\n            <span v-else>-</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"分享码\" prop=\"shareCode\" align=\"center\" width=\"120\" />\r\n        <el-table-column label=\"代理级别\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getLevelType(scope.row.agentLevel)\">{{ scope.row.agentLevelName || '注册会员' }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"团队设备总数量\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #67C23A\">{{ formatNumber(scope.row.totalBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"团队今日新增设备数\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #409EFF\">{{ formatNumber(scope.row.todayNewBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"个人设备总数\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #409EFF\">{{ formatNumber(scope.row.deviceCount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"个人复投总设备数\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #409EFF\">{{ formatNumber(scope.row.reinvestCount) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"兑换券\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">¥{{ formatNumber(scope.row.availableBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"提现冻结账户\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">¥{{ formatNumber(scope.row.frozenBalance) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"当前待兑积分\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">¥{{ formatNumber(scope.row.dynamicQuota) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"累积自动兑换总额\" align=\"center\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #f56c6c\">¥{{ formatNumber(scope.row.autoExchangeTotal) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"注册时间\" align=\"center\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.createTime | formatDateTime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"最后登录\" align=\"center\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.updateTime | formatDateTime }}\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"350\" fixed=\"right\">\r\n          <template v-slot=\"{ row }\">\r\n            <el-button type=\"text\" @click=\"handleDetail(row)\">详情</el-button>\r\n            <el-button type=\"text\" @click=\"handleRecharge(row)\">充值</el-button>\r\n            <el-button type=\"text\" @click=\"handleBankCards(row)\" v-loading=\"bankCardsLoading\">银行卡</el-button>\r\n            <el-button type=\"text\" @click=\"handleChangeLevel(row)\">修改等级</el-button>\r\n            <el-button type=\"text\" style=\"color: #f56c6c\" @click=\"handleReset(row)\">重置密码</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n        />\r\n      </div>\r\n\r\n      <!-- 用户详情对话框 -->\r\n      <el-dialog\r\n        title=\"用户详情\"\r\n        :visible.sync=\"detailVisible\"\r\n        width=\"800px\"\r\n        :close-on-click-modal=\"false\"\r\n        custom-class=\"user-detail-dialog\"\r\n      >\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"用户编号\">{{ detailUser.userNo }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">{{ detailUser.phone }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户名称\">{{ detailUser.username }}</el-descriptions-item>\r\n        \r\n          <el-descriptions-item label=\"代理级别\">\r\n            <el-tag :type=\"getLevelType(detailUser.agentLevel)\">{{ detailUser.agentLevelName || '注册会员' }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"团队设备总数\">{{ formatNumber(detailUser.totalBalance) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"团队新增设备数\">{{ formatNumber(detailUser.todayNewBalance) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"注册时间\">{{ formatDateTime(detailUser.createTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"最后登录\">{{ formatDateTime(detailUser.updateTime) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"账户余额\">\r\n            <span style=\"color: #67C23A\">¥{{ formatNumber(detailUser.availableBalance) || '0' }}</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"冻结余额\">\r\n            <span style=\"color: #67C23A\">¥{{ formatNumber(detailUser.frozenBalance) || '0' }}</span>\r\n          </el-descriptions-item>\r\n          <!-- <el-descriptions-item label=\"账户状态\">\r\n            <el-tag :type=\"detailUser.status === '1' ? 'success' : 'danger'\">\r\n              {{ detailUser.status === '1' ? '正常' : '禁用' }}\r\n            </el-tag>\r\n          </el-descriptions-item> -->\r\n          <el-descriptions-item label=\"个人总设备数\">{{ formatNumber(detailUser.deviceCount) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"个人复投总设备数\">{{ formatNumber(detailUser.reinvestCount) || '0' }}</el-descriptions-item> \r\n          <el-descriptions-item label=\"当前待兑积分\">{{ formatNumber(detailUser.dynamicQuota) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"累积获得总奖金\">{{ formatNumber(detailUser.totalReward) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"累积激活额度\">{{ formatNumber(detailUser.activateQuotaTotal) || '0' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"累积自动兑换总额\">{{ formatNumber(detailUser.autoExchangeTotal) || '0' }}</el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"推荐人\">\r\n            <template v-if=\"detailUser.referrerPhone\">\r\n              {{ detailUser.referrerPhone }}\r\n              <el-tag size=\"mini\" type=\"info\">{{ detailUser.referrerShareCode }}</el-tag>\r\n            </template>\r\n            <span v-else>-</span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"邀请码\">{{ detailUser.shareCode || '-' }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </el-dialog>\r\n\r\n      <!-- 用户充值对话框 -->\r\n      <el-dialog\r\n        title=\"用户充值\"\r\n        :visible.sync=\"rechargeVisible\"\r\n        width=\"500px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"rechargeForm\"\r\n          :model=\"rechargeForm\"\r\n          :rules=\"rechargeRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"用户手机号\">\r\n            <span>{{ rechargeUser.phone }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"当前余额\">\r\n            <span style=\"color: #67C23A\">¥{{ formatNumber(rechargeUser.availableBalance) || '0' }}</span>\r\n          </el-form-item>\r\n          <el-form-item label=\"充值金额\" prop=\"amount\">\r\n            <el-input-number\r\n              v-model=\"rechargeForm.amount\"\r\n              :min=\"1\"\r\n              :precision=\"2\"\r\n              :step=\"100\"\r\n              style=\"width: 200px\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"remark\">\r\n            <el-input\r\n              v-model=\"rechargeForm.remark\"\r\n              type=\"textarea\"\r\n              :rows=\"2\"\r\n              placeholder=\"请输入充值备注\"\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"rechargeVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitRecharge\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 银行卡列表对话框 -->\r\n      <el-dialog\r\n        title=\"银行卡列表\"\r\n        :visible.sync=\"bankCardsVisible\"\r\n        width=\"900px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-table\r\n          :data=\"bankCards\"\r\n          border\r\n          style=\"width: 100%\"\r\n          v-loading=\"bankCardsLoading\"\r\n        >\r\n          <el-table-column label=\"银行名称\" prop=\"bankName\" align=\"center\" width=\"120\" />\r\n          <el-table-column label=\"开户支行\" prop=\"bankBranch\" align=\"center\" width=\"160\" />\r\n          <el-table-column label=\"银行卡号\" align=\"center\" width=\"205\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.cardNo.replace(/(\\d{4})(?=\\d)/g, '$1 ') }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"持卡人\" prop=\"holderName\" align=\"center\" width=\"80\" />\r\n          <el-table-column label=\"身份证号\" align=\"center\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.idCard.replace(/^(\\d{6})\\d+(\\d{4})$/, '$1****$2') }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"预留手机号\" prop=\"phone\" align=\"center\" width=\"120\" />\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"bankCardsVisible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 修改等级对话框 -->\r\n      <el-dialog\r\n        title=\"修改代理等级\"\r\n        :visible.sync=\"changeLevelVisible\"\r\n        width=\"400px\"\r\n        :close-on-click-modal=\"false\"\r\n      >\r\n        <el-form\r\n          ref=\"levelForm\"\r\n          :model=\"levelForm\"\r\n          :rules=\"levelRules\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-form-item label=\"当前等级\">\r\n            <el-tag :type=\"getLevelType(currentUser.agentLevel)\">\r\n              {{ currentUser.agentLevelName || '注册会员' }}\r\n            </el-tag>\r\n          </el-form-item>\r\n          <el-form-item label=\"新等级\" prop=\"agentLevel\">\r\n            <el-select v-model=\"levelForm.agentLevel\" placeholder=\"请选择代理等级\">\r\n              <el-option\r\n                v-for=\"item in agentLevels\"\r\n                :key=\"item.id\"\r\n                :label=\"item.levelName\"\r\n                :value=\"item.id\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"changeLevelVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitChangeLevel\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getUserList, getUserDetail, updateUserStatus, resetUserPassword, rechargeUser, getUserBankCards, getAgentLevels, updateUserLevel } from '@/api/user/user'\r\nimport { parseTime, formatDate } from '@/utils/date'\r\n\r\nexport default {\r\n  name: 'UserList',\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',      // 用户名/手机号\r\n        status: '',        // 状态\r\n        agentLevel: '',    // 代理级别\r\n        shareCode: '',     // 邀请码\r\n        referrerPhone: '', // 邀请人手机号\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: ''\r\n      },\r\n      // 代理级别选项\r\n      agentLevelOptions: [\r\n        { label: '业务员', value: '1' },\r\n        { label: '主管', value: '2' },\r\n        { label: '经理', value: '3' },\r\n        { label: '总监', value: '4' },\r\n        { label: '总经理', value: '5' }\r\n      ],\r\n      loading: false,\r\n      total: 0,\r\n      tableData: [],\r\n      // 充值相关\r\n      rechargeVisible: false,\r\n      rechargeUser: {},\r\n      rechargeForm: {\r\n        amount: 100,\r\n        remark: ''\r\n      },\r\n      rechargeRules: {\r\n        amount: [\r\n          { required: true, message: '请输入充值金额', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 详情相关\r\n      detailVisible: false,\r\n      detailUser: {\r\n        username: '',\r\n        phone: '',\r\n        realName: '',\r\n        agentLevel: '',\r\n        teamCount: 0,\r\n        teamPerformance: 0,\r\n        createTime: '',\r\n        lastLoginTime: '',\r\n        balance: 0,\r\n        status: '1',\r\n        referrer: '',\r\n        inviteCode: '',\r\n        totalRecharge: 0,\r\n        totalWithdraw: 0,\r\n        commission: 0\r\n      },\r\n      // 银行卡相关\r\n      bankCardsVisible: false,\r\n      bankCardsLoading: false,\r\n      bankCards: [],\r\n      // 修改等级相关\r\n      changeLevelVisible: false,\r\n      currentUser: {},\r\n      levelForm: {\r\n        agentLevel: ''\r\n      },\r\n      levelRules: {\r\n        agentLevel: [\r\n          { required: true, message: '请选择代理等级', trigger: 'change' }\r\n        ]\r\n      },\r\n      agentLevels: [] // 存储代理等级列表\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getAgentLevels() // 获取代理等级列表\r\n  },\r\n  watch: {\r\n    // 监听日期范围变化\r\n    'listQuery.dateRange'(val) {\r\n      if (val && val.length === 2) {\r\n        this.listQuery.startDate = formatDate(val[0])\r\n        this.listQuery.endDate = formatDate(val[1])\r\n      } else {\r\n        this.listQuery.startDate = ''\r\n        this.listQuery.endDate = ''\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取列表数据\r\n    async getList() {\r\n      this.loading = true\r\n      try { \r\n        const res = await getUserList(this.listQuery)  \r\n        if (res.code === 0 || res.code === 200) {\r\n          // 确保数据存在\r\n          if (res.data) {\r\n            this.tableData = res.data.records || []\r\n            this.total = res.data.total || 0 \r\n          } else {\r\n            this.tableData = []\r\n            this.total = 0\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '获取用户列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取用户列表失败:', error)\r\n        this.$message.error('获取用户列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索\r\n    handleSearch() {\r\n      this.listQuery.page = 1\r\n      this.getList()\r\n    },\r\n\r\n    // 重置查询\r\n    resetQuery() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        status: '',\r\n        agentLevel: '',    // 重置代理级别\r\n        shareCode: '',     // 重置邀请码\r\n        referrerPhone: '', // 重置邀请人手机号\r\n        dateRange: [],\r\n        startDate: '',\r\n        endDate: ''\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化数字\r\n    formatNumber(num) {\r\n      return num ? num.toLocaleString() : '0'\r\n    },\r\n\r\n    // 获代理级别标签类型\r\n    getLevelType(level) {\r\n      const typeMap = {\r\n        '1': 'info',      // 业务员\r\n        '2': 'warning',   // 主管\r\n        '3': 'success',   // 经理\r\n        '4': 'danger',    // 总\r\n        '5': ''          // 总经理\r\n      }\r\n      return typeMap[level] || ''\r\n    },\r\n\r\n    // 处理状态变更\r\n    async handleStatusChange(row) {\r\n      try {\r\n        const res = await updateUserStatus(row.id, row.status)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.$message.success(`${row.status === 1 ? '启用' : '禁用'}成功`)\r\n        } else {\r\n          row.status = row.status === 1 ? 0 : 1\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        row.status = row.status === 1 ? 0 : 1\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    // 查看详情\r\n    async handleDetail(row) {\r\n      try {\r\n        const res = await getUserDetail(row.id)\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.detailUser = res.data\r\n          this.detailVisible = true\r\n        } else {\r\n          this.$message.error(res.msg || '获取详情失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取详情失败')\r\n      }\r\n    },\r\n\r\n    // 打开充值对话框\r\n    handleRecharge(row) {\r\n      this.rechargeUser = row\r\n      this.rechargeForm = {\r\n        amount: 100,\r\n        remark: ''\r\n      }\r\n      this.rechargeVisible = true\r\n    },\r\n\r\n    // 提交充值\r\n    submitRecharge() {\r\n      this.$refs.rechargeForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await rechargeUser(this.rechargeUser.id, this.rechargeForm)\r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('充值成功')\r\n              this.rechargeVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '充值失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('充值失败')\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 分页相关\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(time) {\r\n      if (!time) return ''\r\n      return parseTime(time)\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(time) {\r\n      if (!time) return ''\r\n      return parseTime(time, 'yyyy-MM-dd')\r\n    },\r\n\r\n    // 重置密码\r\n    handleReset(row) {\r\n      this.$confirm('确认要将该用户密码重置为 123456 ?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await resetUserPassword(row.id)\r\n          if (res.code === 0 || res.code === 200) {\r\n            this.$message.success('密码重置成功')\r\n          } else {\r\n            this.$message.error(res.msg || '密码重置失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('密码重置失败')\r\n        }\r\n      }).catch(() => {\r\n        // 取消重置，不做任何操作\r\n      })\r\n    },\r\n\r\n    // 查看银行\r\n    async handleBankCards(row) {\r\n      \r\n      this.bankCardsVisible = true\r\n      this.bankCardsLoading = true\r\n      try {\r\n        const res = await getUserBankCards(row.id)\r\n       \r\n        if (res.code === 0 || res.code === 200) {\r\n          this.bankCards = res.data || []\r\n        } else {\r\n          this.$message.error(res.msg || '获取银行卡列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取银行卡失败:', error)  // 添加错误日志\r\n        this.$message.error('获取银行卡列表失败')\r\n      } finally {\r\n        this.bankCardsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取代理等级列表\r\n    async getAgentLevels() {\r\n      try {\r\n        const res = await getAgentLevels() // 需要添加这个API\r\n        if (res.code === 0 || res.code === 200) {\r\n          this.agentLevels = res.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取代理等级列表失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 打开修改等级对话框\r\n    handleChangeLevel(row) {\r\n      this.currentUser = row\r\n      this.levelForm.agentLevel = row.agentLevel\r\n      this.changeLevelVisible = true\r\n    },\r\n    \r\n    // 提交修改等级\r\n    submitChangeLevel() {\r\n      this.$refs.levelForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const res = await updateUserLevel(this.currentUser.id, this.levelForm.agentLevel) // 需要添加这个API\r\n            if (res.code === 0 || res.code === 200) {\r\n              this.$message.success('修改等级成功')\r\n              this.changeLevelVisible = false\r\n              this.getList() // 刷新列表\r\n            } else {\r\n              this.$message.error(res.msg || '修改等级失败')\r\n            }\r\n          } catch (error) {\r\n            this.$message.error('修改等级失败')\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n  \r\n  .filter-item {\r\n    width: 100%;\r\n  }\r\n  \r\n  .date-range-picker {\r\n    width: 100%;\r\n  }\r\n  \r\n  .el-button {\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .el-select {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n// 修改对话框样式\r\n.user-detail-dialog {\r\n  ::v-deep .el-dialog__body {\r\n    padding: 10px 20px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__header {\r\n    padding: 15px 20px 10px;\r\n  }\r\n  \r\n  ::v-deep .el-dialog__footer {\r\n    padding: 10px 20px 15px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  margin: 0;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  padding-top: 0;\r\n}\r\n</style>"], "mappings": ";;AA+VA,SAAAA,WAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,gBAAA,EAAAC,cAAA,IAAAA,eAAA,EAAAC,eAAA;AACA,SAAAC,SAAA,EAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QAAA;QACAC,MAAA;QAAA;QACAC,UAAA;QAAA;QACAC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA;MACAC,iBAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACAzB,YAAA;MACA0B,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACAC,aAAA;QACAF,MAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,aAAA;MACAC,UAAA;QACAvB,QAAA;QACAwB,KAAA;QACAC,QAAA;QACAvB,UAAA;QACAwB,SAAA;QACAC,eAAA;QACAC,UAAA;QACAC,aAAA;QACAC,OAAA;QACA7B,MAAA;QACA8B,QAAA;QACAC,UAAA;QACAC,aAAA;QACAC,aAAA;QACAC,UAAA;MACA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,kBAAA;MACAC,WAAA;MACAC,SAAA;QACAvC,UAAA;MACA;MACAwC,UAAA;QACAxC,UAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAsB,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAtD,cAAA;EACA;EACAuD,KAAA;IACA;IACA,gCAAAC,mBAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA,CAAAC,MAAA;QACA,KAAApD,SAAA,CAAAS,SAAA,GAAAZ,UAAA,CAAAsD,GAAA;QACA,KAAAnD,SAAA,CAAAU,OAAA,GAAAb,UAAA,CAAAsD,GAAA;MACA;QACA,KAAAnD,SAAA,CAAAS,SAAA;QACA,KAAAT,SAAA,CAAAU,OAAA;MACA;IACA;EACA;EACA2C,OAAA;IACA;IACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,KAAA,CAAAxC,OAAA;cAAAgD,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEA5E,WAAA,CAAAkE,KAAA,CAAAtD,SAAA;YAAA;cAAA2D,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA;gBACA,IAAAP,GAAA,CAAA5D,IAAA;kBACAuD,KAAA,CAAAtC,SAAA,GAAA2C,GAAA,CAAA5D,IAAA,CAAAoE,OAAA;kBACAb,KAAA,CAAAvC,KAAA,GAAA4C,GAAA,CAAA5D,IAAA,CAAAgB,KAAA;gBACA;kBACAuC,KAAA,CAAAtC,SAAA;kBACAsC,KAAA,CAAAvC,KAAA;gBACA;cACA;gBACAuC,KAAA,CAAAc,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,cAAAP,QAAA,CAAAS,EAAA;cACAjB,KAAA,CAAAc,QAAA,CAAAC,KAAA;YAAA;cAAAP,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAAxC,OAAA;cAAA,OAAAgD,QAAA,CAAAW,MAAA;YAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IAEA;IAEA;IACAiB,YAAA,WAAAA,aAAA;MACA,KAAA3E,SAAA,CAAAC,IAAA;MACA,KAAA+C,OAAA;IACA;IAEA;IACA4B,UAAA,WAAAA,WAAA;MACA,KAAA5E,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,UAAA;QAAA;QACAC,SAAA;QAAA;QACAC,aAAA;QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA,KAAAsC,OAAA;IACA;IAEA;IACA6B,YAAA,WAAAA,aAAAC,GAAA;MACA,OAAAA,GAAA,GAAAA,GAAA,CAAAC,cAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAC,KAAA;MACA,IAAAC,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,KAAA;IACA;IAEA;IACAE,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,OAAA9B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6B,SAAA;QAAA,IAAA3B,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA2B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;YAAA;cAAAwB,SAAA,CAAAzB,IAAA;cAAAyB,SAAA,CAAAxB,IAAA;cAAA,OAEA1E,gBAAA,CAAA8F,GAAA,CAAAK,EAAA,EAAAL,GAAA,CAAAhF,MAAA;YAAA;cAAAuD,GAAA,GAAA6B,SAAA,CAAAvB,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAmB,MAAA,CAAAjB,QAAA,CAAAsB,OAAA,IAAAC,MAAA,CAAAP,GAAA,CAAAhF,MAAA;cACA;gBACAgF,GAAA,CAAAhF,MAAA,GAAAgF,GAAA,CAAAhF,MAAA;gBACAiF,MAAA,CAAAjB,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAkB,SAAA,CAAAxB,IAAA;cAAA;YAAA;cAAAwB,SAAA,CAAAzB,IAAA;cAAAyB,SAAA,CAAAjB,EAAA,GAAAiB,SAAA;cAEAJ,GAAA,CAAAhF,MAAA,GAAAgF,GAAA,CAAAhF,MAAA;cACAiF,MAAA,CAAAjB,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAmB,SAAA,CAAAd,IAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IAEA;IAEA;IACAM,YAAA,WAAAA,aAAAR,GAAA;MAAA,IAAAS,MAAA;MAAA,OAAAtC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqC,SAAA;QAAA,IAAAnC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAmC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,IAAA,GAAAiC,SAAA,CAAAhC,IAAA;YAAA;cAAAgC,SAAA,CAAAjC,IAAA;cAAAiC,SAAA,CAAAhC,IAAA;cAAA,OAEA3E,aAAA,CAAA+F,GAAA,CAAAK,EAAA;YAAA;cAAA9B,GAAA,GAAAqC,SAAA,CAAA/B,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA2B,MAAA,CAAAnE,UAAA,GAAAiC,GAAA,CAAA5D,IAAA;gBACA8F,MAAA,CAAApE,aAAA;cACA;gBACAoE,MAAA,CAAAzB,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA0B,SAAA,CAAAhC,IAAA;cAAA;YAAA;cAAAgC,SAAA,CAAAjC,IAAA;cAAAiC,SAAA,CAAAzB,EAAA,GAAAyB,SAAA;cAEAH,MAAA,CAAAzB,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAA2B,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IAEA;IAEA;IACAG,cAAA,WAAAA,eAAAb,GAAA;MACA,KAAA5F,YAAA,GAAA4F,GAAA;MACA,KAAAlE,YAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,KAAAH,eAAA;IACA;IAEA;IACAiF,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAlF,YAAA,CAAAmF,QAAA;QAAA,IAAAC,IAAA,GAAA/C,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA8C,SAAAC,KAAA;UAAA,IAAA7C,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA6C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA3C,IAAA,GAAA2C,SAAA,CAAA1C,IAAA;cAAA;gBAAA,KACAwC,KAAA;kBAAAE,SAAA,CAAA1C,IAAA;kBAAA;gBAAA;gBAAA0C,SAAA,CAAA3C,IAAA;gBAAA2C,SAAA,CAAA1C,IAAA;gBAAA,OAEAxE,YAAA,CAAA2G,MAAA,CAAA3G,YAAA,CAAAiG,EAAA,EAAAU,MAAA,CAAAjF,YAAA;cAAA;gBAAAyC,GAAA,GAAA+C,SAAA,CAAAzC,IAAA;gBACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACAiC,MAAA,CAAA/B,QAAA,CAAAsB,OAAA;kBACAS,MAAA,CAAAlF,eAAA;kBACAkF,MAAA,CAAAnD,OAAA;gBACA;kBACAmD,MAAA,CAAA/B,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAoC,SAAA,CAAA1C,IAAA;gBAAA;cAAA;gBAAA0C,SAAA,CAAA3C,IAAA;gBAAA2C,SAAA,CAAAnC,EAAA,GAAAmC,SAAA;gBAEAP,MAAA,CAAA/B,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAqC,SAAA,CAAAhC,IAAA;YAAA;UAAA,GAAA6B,QAAA;QAAA,CAGA;QAAA,iBAAAI,EAAA;UAAA,OAAAL,IAAA,CAAAM,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA3D,GAAA;MACA,KAAAnD,SAAA,CAAAE,KAAA,GAAAiD,GAAA;MACA,KAAAH,OAAA;IACA;IACA+D,mBAAA,WAAAA,oBAAA5D,GAAA;MACA,KAAAnD,SAAA,CAAAC,IAAA,GAAAkD,GAAA;MACA,KAAAH,OAAA;IACA;IAEA;IACAgE,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,OAAArH,SAAA,CAAAqH,IAAA;IACA;IAEA;IACApH,UAAA,WAAAA,WAAAoH,IAAA;MACA,KAAAA,IAAA;MACA,OAAArH,SAAA,CAAAqH,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAAjE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgE,SAAA;QAAA,IAAA9D,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAA3D,IAAA;cAAA,OAEAzE,iBAAA,CAAA6F,GAAA,CAAAK,EAAA;YAAA;cAAA9B,GAAA,GAAAgE,SAAA,CAAA1D,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACAiD,MAAA,CAAA/C,QAAA,CAAAsB,OAAA;cACA;gBACAyB,MAAA,CAAA/C,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAqD,SAAA,CAAA3D,IAAA;cAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAApD,EAAA,GAAAoD,SAAA;cAEAR,MAAA,CAAA/C,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAsD,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA+C,QAAA;MAAA,CAEA;QACA;MAAA,CACA;IACA;IAEA;IACAG,eAAA,WAAAA,gBAAAxC,GAAA;MAAA,IAAAyC,MAAA;MAAA,OAAAtE,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqE,SAAA;QAAA,IAAAnE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAmE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjE,IAAA,GAAAiE,SAAA,CAAAhE,IAAA;YAAA;cAEA6D,MAAA,CAAAtF,gBAAA;cACAsF,MAAA,CAAArF,gBAAA;cAAAwF,SAAA,CAAAjE,IAAA;cAAAiE,SAAA,CAAAhE,IAAA;cAAA,OAEAvE,gBAAA,CAAA2F,GAAA,CAAAK,EAAA;YAAA;cAAA9B,GAAA,GAAAqE,SAAA,CAAA/D,IAAA;cAEA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA2D,MAAA,CAAApF,SAAA,GAAAkB,GAAA,CAAA5D,IAAA;cACA;gBACA8H,MAAA,CAAAzD,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAA0D,SAAA,CAAAhE,IAAA;cAAA;YAAA;cAAAgE,SAAA,CAAAjE,IAAA;cAAAiE,SAAA,CAAAzD,EAAA,GAAAyD,SAAA;cAEAxD,OAAA,CAAAH,KAAA,aAAA2D,SAAA,CAAAzD,EAAA;cACAsD,MAAA,CAAAzD,QAAA,CAAAC,KAAA;YAAA;cAAA2D,SAAA,CAAAjE,IAAA;cAEA8D,MAAA,CAAArF,gBAAA;cAAA,OAAAwF,SAAA,CAAAvD,MAAA;YAAA;YAAA;cAAA,OAAAuD,SAAA,CAAAtD,IAAA;UAAA;QAAA,GAAAoD,QAAA;MAAA;IAEA;IAEA;IACApI,cAAA,WAAAA,eAAA;MAAA,IAAAuI,MAAA;MAAA,OAAA1E,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyE,SAAA;QAAA,IAAAvE,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAApE,IAAA;YAAA;cAAAoE,SAAA,CAAArE,IAAA;cAAAqE,SAAA,CAAApE,IAAA;cAAA,OAEAtE,eAAA;YAAA;cAAAiE,GAAA,GAAAyE,SAAA,CAAAnE,IAAA;cAAA;cACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;gBACA+D,MAAA,CAAAnF,WAAA,GAAAa,GAAA,CAAA5D,IAAA;cACA;cAAAqI,SAAA,CAAApE,IAAA;cAAA;YAAA;cAAAoE,SAAA,CAAArE,IAAA;cAAAqE,SAAA,CAAA7D,EAAA,GAAA6D,SAAA;cAEA5D,OAAA,CAAAH,KAAA,gBAAA+D,SAAA,CAAA7D,EAAA;YAAA;YAAA;cAAA,OAAA6D,SAAA,CAAA1D,IAAA;UAAA;QAAA,GAAAwD,QAAA;MAAA;IAEA;IAEA;IACAG,iBAAA,WAAAA,kBAAAjD,GAAA;MACA,KAAAzC,WAAA,GAAAyC,GAAA;MACA,KAAAxC,SAAA,CAAAvC,UAAA,GAAA+E,GAAA,CAAA/E,UAAA;MACA,KAAAqC,kBAAA;IACA;IAEA;IACA4F,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAnC,KAAA,CAAAxD,SAAA,CAAAyD,QAAA;QAAA,IAAAmC,KAAA,GAAAjF,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgF,SAAAjC,KAAA;UAAA,IAAA7C,GAAA;UAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAA8E,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA5E,IAAA,GAAA4E,SAAA,CAAA3E,IAAA;cAAA;gBAAA,KACAwC,KAAA;kBAAAmC,SAAA,CAAA3E,IAAA;kBAAA;gBAAA;gBAAA2E,SAAA,CAAA5E,IAAA;gBAAA4E,SAAA,CAAA3E,IAAA;gBAAA,OAEArE,eAAA,CAAA4I,MAAA,CAAA5F,WAAA,CAAA8C,EAAA,EAAA8C,MAAA,CAAA3F,SAAA,CAAAvC,UAAA;cAAA;gBAAAsD,GAAA,GAAAgF,SAAA,CAAA1E,IAAA;gBAAA;gBACA,IAAAN,GAAA,CAAAO,IAAA,UAAAP,GAAA,CAAAO,IAAA;kBACAqE,MAAA,CAAAnE,QAAA,CAAAsB,OAAA;kBACA6C,MAAA,CAAA7F,kBAAA;kBACA6F,MAAA,CAAAvF,OAAA;gBACA;kBACAuF,MAAA,CAAAnE,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;gBACA;gBAAAqE,SAAA,CAAA3E,IAAA;gBAAA;cAAA;gBAAA2E,SAAA,CAAA5E,IAAA;gBAAA4E,SAAA,CAAApE,EAAA,GAAAoE,SAAA;gBAEAJ,MAAA,CAAAnE,QAAA,CAAAC,KAAA;cAAA;cAAA;gBAAA,OAAAsE,SAAA,CAAAjE,IAAA;YAAA;UAAA,GAAA+D,QAAA;QAAA,CAGA;QAAA,iBAAAG,GAAA;UAAA,OAAAJ,KAAA,CAAA5B,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}