{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isFunction, isString } from 'zrender/lib/core/util.js';\nvar samplers = {\n  average: function average(frame) {\n    var sum = 0;\n    var count = 0;\n    for (var i = 0; i < frame.length; i++) {\n      if (!isNaN(frame[i])) {\n        sum += frame[i];\n        count++;\n      }\n    }\n    // Return NaN if count is 0\n    return count === 0 ? NaN : sum / count;\n  },\n  sum: function sum(frame) {\n    var sum = 0;\n    for (var i = 0; i < frame.length; i++) {\n      // Ignore NaN\n      sum += frame[i] || 0;\n    }\n    return sum;\n  },\n  max: function max(frame) {\n    var max = -Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] > max && (max = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(max) ? max : NaN;\n  },\n  min: function min(frame) {\n    var min = Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] < min && (min = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(min) ? min : NaN;\n  },\n  minmax: function minmax(frame) {\n    var turningPointAbsoluteValue = -Infinity;\n    var turningPointOriginalValue = -Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      var originalValue = frame[i];\n      var absoluteValue = Math.abs(originalValue);\n      if (absoluteValue > turningPointAbsoluteValue) {\n        turningPointAbsoluteValue = absoluteValue;\n        turningPointOriginalValue = originalValue;\n      }\n    }\n    return isFinite(turningPointOriginalValue) ? turningPointOriginalValue : NaN;\n  },\n  // TODO\n  // Median\n  nearest: function nearest(frame) {\n    return frame[0];\n  }\n};\nvar indexSampler = function indexSampler(frame) {\n  return Math.round(frame.length / 2);\n};\nexport default function dataSample(seriesType) {\n  return {\n    seriesType: seriesType,\n    // FIXME:TS never used, so comment it\n    // modifyOutputEnd: true,\n    reset: function reset(seriesModel, ecModel, api) {\n      var data = seriesModel.getData();\n      var sampling = seriesModel.get('sampling');\n      var coordSys = seriesModel.coordinateSystem;\n      var count = data.count();\n      // Only cartesian2d support down sampling. Disable it when there is few data.\n      if (count > 10 && coordSys.type === 'cartesian2d' && sampling) {\n        var baseAxis = coordSys.getBaseAxis();\n        var valueAxis = coordSys.getOtherAxis(baseAxis);\n        var extent = baseAxis.getExtent();\n        var dpr = api.getDevicePixelRatio();\n        // Coordinste system has been resized\n        var size = Math.abs(extent[1] - extent[0]) * (dpr || 1);\n        var rate = Math.round(count / size);\n        if (isFinite(rate) && rate > 1) {\n          if (sampling === 'lttb') {\n            seriesModel.setData(data.lttbDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          }\n          var sampler = void 0;\n          if (isString(sampling)) {\n            sampler = samplers[sampling];\n          } else if (isFunction(sampling)) {\n            sampler = sampling;\n          }\n          if (sampler) {\n            // Only support sample the first dim mapped from value axis.\n            seriesModel.setData(data.downSample(data.mapDimension(valueAxis.dim), 1 / rate, sampler, indexSampler));\n          }\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["isFunction", "isString", "samplers", "average", "frame", "sum", "count", "i", "length", "isNaN", "NaN", "max", "Infinity", "isFinite", "min", "minmax", "turningPointAbsoluteValue", "turningPointOriginalValue", "originalValue", "absoluteValue", "Math", "abs", "nearest", "indexSampler", "round", "dataSample", "seriesType", "reset", "seriesModel", "ecModel", "api", "data", "getData", "sampling", "get", "coordSys", "coordinateSystem", "type", "baseAxis", "getBaseAxis", "valueAxis", "getOtherAxis", "extent", "getExtent", "dpr", "getDevicePixelRatio", "size", "rate", "setData", "lttbDownSample", "mapDimension", "dim", "sampler", "downSample"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/processor/dataSample.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isFunction, isString } from 'zrender/lib/core/util.js';\nvar samplers = {\n  average: function (frame) {\n    var sum = 0;\n    var count = 0;\n    for (var i = 0; i < frame.length; i++) {\n      if (!isNaN(frame[i])) {\n        sum += frame[i];\n        count++;\n      }\n    }\n    // Return NaN if count is 0\n    return count === 0 ? NaN : sum / count;\n  },\n  sum: function (frame) {\n    var sum = 0;\n    for (var i = 0; i < frame.length; i++) {\n      // Ignore NaN\n      sum += frame[i] || 0;\n    }\n    return sum;\n  },\n  max: function (frame) {\n    var max = -Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] > max && (max = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(max) ? max : NaN;\n  },\n  min: function (frame) {\n    var min = Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] < min && (min = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(min) ? min : NaN;\n  },\n  minmax: function (frame) {\n    var turningPointAbsoluteValue = -Infinity;\n    var turningPointOriginalValue = -Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      var originalValue = frame[i];\n      var absoluteValue = Math.abs(originalValue);\n      if (absoluteValue > turningPointAbsoluteValue) {\n        turningPointAbsoluteValue = absoluteValue;\n        turningPointOriginalValue = originalValue;\n      }\n    }\n    return isFinite(turningPointOriginalValue) ? turningPointOriginalValue : NaN;\n  },\n  // TODO\n  // Median\n  nearest: function (frame) {\n    return frame[0];\n  }\n};\nvar indexSampler = function (frame) {\n  return Math.round(frame.length / 2);\n};\nexport default function dataSample(seriesType) {\n  return {\n    seriesType: seriesType,\n    // FIXME:TS never used, so comment it\n    // modifyOutputEnd: true,\n    reset: function (seriesModel, ecModel, api) {\n      var data = seriesModel.getData();\n      var sampling = seriesModel.get('sampling');\n      var coordSys = seriesModel.coordinateSystem;\n      var count = data.count();\n      // Only cartesian2d support down sampling. Disable it when there is few data.\n      if (count > 10 && coordSys.type === 'cartesian2d' && sampling) {\n        var baseAxis = coordSys.getBaseAxis();\n        var valueAxis = coordSys.getOtherAxis(baseAxis);\n        var extent = baseAxis.getExtent();\n        var dpr = api.getDevicePixelRatio();\n        // Coordinste system has been resized\n        var size = Math.abs(extent[1] - extent[0]) * (dpr || 1);\n        var rate = Math.round(count / size);\n        if (isFinite(rate) && rate > 1) {\n          if (sampling === 'lttb') {\n            seriesModel.setData(data.lttbDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          }\n          var sampler = void 0;\n          if (isString(sampling)) {\n            sampler = samplers[sampling];\n          } else if (isFunction(sampling)) {\n            sampler = sampling;\n          }\n          if (sampler) {\n            // Only support sample the first dim mapped from value axis.\n            seriesModel.setData(data.downSample(data.mapDimension(valueAxis.dim), 1 / rate, sampler, indexSampler));\n          }\n        }\n      }\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,0BAA0B;AAC/D,IAAIC,QAAQ,GAAG;EACbC,OAAO,EAAE,SAATA,OAAOA,CAAYC,KAAK,EAAE;IACxB,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAI,CAACE,KAAK,CAACL,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE;QACpBF,GAAG,IAAID,KAAK,CAACG,CAAC,CAAC;QACfD,KAAK,EAAE;MACT;IACF;IACA;IACA,OAAOA,KAAK,KAAK,CAAC,GAAGI,GAAG,GAAGL,GAAG,GAAGC,KAAK;EACxC,CAAC;EACDD,GAAG,EAAE,SAALA,GAAGA,CAAYD,KAAK,EAAE;IACpB,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC;MACAF,GAAG,IAAID,KAAK,CAACG,CAAC,CAAC,IAAI,CAAC;IACtB;IACA,OAAOF,GAAG;EACZ,CAAC;EACDM,GAAG,EAAE,SAALA,GAAGA,CAAYP,KAAK,EAAE;IACpB,IAAIO,GAAG,GAAG,CAACC,QAAQ;IACnB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCH,KAAK,CAACG,CAAC,CAAC,GAAGI,GAAG,KAAKA,GAAG,GAAGP,KAAK,CAACG,CAAC,CAAC,CAAC;IACpC;IACA;IACA,OAAOM,QAAQ,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAGD,GAAG;EAClC,CAAC;EACDI,GAAG,EAAE,SAALA,GAAGA,CAAYV,KAAK,EAAE;IACpB,IAAIU,GAAG,GAAGF,QAAQ;IAClB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCH,KAAK,CAACG,CAAC,CAAC,GAAGO,GAAG,KAAKA,GAAG,GAAGV,KAAK,CAACG,CAAC,CAAC,CAAC;IACpC;IACA;IACA,OAAOM,QAAQ,CAACC,GAAG,CAAC,GAAGA,GAAG,GAAGJ,GAAG;EAClC,CAAC;EACDK,MAAM,EAAE,SAARA,MAAMA,CAAYX,KAAK,EAAE;IACvB,IAAIY,yBAAyB,GAAG,CAACJ,QAAQ;IACzC,IAAIK,yBAAyB,GAAG,CAACL,QAAQ;IACzC,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIW,aAAa,GAAGd,KAAK,CAACG,CAAC,CAAC;MAC5B,IAAIY,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACH,aAAa,CAAC;MAC3C,IAAIC,aAAa,GAAGH,yBAAyB,EAAE;QAC7CA,yBAAyB,GAAGG,aAAa;QACzCF,yBAAyB,GAAGC,aAAa;MAC3C;IACF;IACA,OAAOL,QAAQ,CAACI,yBAAyB,CAAC,GAAGA,yBAAyB,GAAGP,GAAG;EAC9E,CAAC;EACD;EACA;EACAY,OAAO,EAAE,SAATA,OAAOA,CAAYlB,KAAK,EAAE;IACxB,OAAOA,KAAK,CAAC,CAAC,CAAC;EACjB;AACF,CAAC;AACD,IAAImB,YAAY,GAAG,SAAfA,YAAYA,CAAanB,KAAK,EAAE;EAClC,OAAOgB,IAAI,CAACI,KAAK,CAACpB,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC;AACrC,CAAC;AACD,eAAe,SAASiB,UAAUA,CAACC,UAAU,EAAE;EAC7C,OAAO;IACLA,UAAU,EAAEA,UAAU;IACtB;IACA;IACAC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;MAC1C,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;MAChC,IAAIC,QAAQ,GAAGL,WAAW,CAACM,GAAG,CAAC,UAAU,CAAC;MAC1C,IAAIC,QAAQ,GAAGP,WAAW,CAACQ,gBAAgB;MAC3C,IAAI9B,KAAK,GAAGyB,IAAI,CAACzB,KAAK,CAAC,CAAC;MACxB;MACA,IAAIA,KAAK,GAAG,EAAE,IAAI6B,QAAQ,CAACE,IAAI,KAAK,aAAa,IAAIJ,QAAQ,EAAE;QAC7D,IAAIK,QAAQ,GAAGH,QAAQ,CAACI,WAAW,CAAC,CAAC;QACrC,IAAIC,SAAS,GAAGL,QAAQ,CAACM,YAAY,CAACH,QAAQ,CAAC;QAC/C,IAAII,MAAM,GAAGJ,QAAQ,CAACK,SAAS,CAAC,CAAC;QACjC,IAAIC,GAAG,GAAGd,GAAG,CAACe,mBAAmB,CAAC,CAAC;QACnC;QACA,IAAIC,IAAI,GAAG1B,IAAI,CAACC,GAAG,CAACqB,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIE,GAAG,IAAI,CAAC,CAAC;QACvD,IAAIG,IAAI,GAAG3B,IAAI,CAACI,KAAK,CAAClB,KAAK,GAAGwC,IAAI,CAAC;QACnC,IAAIjC,QAAQ,CAACkC,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE;UAC9B,IAAId,QAAQ,KAAK,MAAM,EAAE;YACvBL,WAAW,CAACoB,OAAO,CAACjB,IAAI,CAACkB,cAAc,CAAClB,IAAI,CAACmB,YAAY,CAACV,SAAS,CAACW,GAAG,CAAC,EAAE,CAAC,GAAGJ,IAAI,CAAC,CAAC;UACtF;UACA,IAAIK,OAAO,GAAG,KAAK,CAAC;UACpB,IAAInD,QAAQ,CAACgC,QAAQ,CAAC,EAAE;YACtBmB,OAAO,GAAGlD,QAAQ,CAAC+B,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAIjC,UAAU,CAACiC,QAAQ,CAAC,EAAE;YAC/BmB,OAAO,GAAGnB,QAAQ;UACpB;UACA,IAAImB,OAAO,EAAE;YACX;YACAxB,WAAW,CAACoB,OAAO,CAACjB,IAAI,CAACsB,UAAU,CAACtB,IAAI,CAACmB,YAAY,CAACV,SAAS,CAACW,GAAG,CAAC,EAAE,CAAC,GAAGJ,IAAI,EAAEK,OAAO,EAAE7B,YAAY,CAAC,CAAC;UACzG;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}