{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { use } from '../../extension.js';\nimport ComponentView from '../../view/Component.js';\nimport SingleAxisView from '../axis/SingleAxisView.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport SingleAxisModel from '../../coord/single/AxisModel.js';\nimport singleCreator from '../../coord/single/singleCreator.js';\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport AxisView from '../axis/AxisView.js';\nimport SingleAxisPointer from '../axisPointer/SingleAxisPointer.js';\nvar SingleView = /** @class */function (_super) {\n  __extends(SingleView, _super);\n  function SingleView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleView.type;\n    return _this;\n  }\n  SingleView.type = 'single';\n  return SingleView;\n}(ComponentView);\nexport function install(registers) {\n  use(installAxisPointer);\n  AxisView.registerAxisPointerClass('SingleAxisPointer', SingleAxisPointer);\n  registers.registerComponentView(SingleView);\n  // Axis\n  registers.registerComponentView(SingleAxisView);\n  registers.registerComponentModel(SingleAxisModel);\n  axisModelCreator(registers, 'single', SingleAxisModel, SingleAxisModel.defaultOption);\n  registers.registerCoordinateSystem('single', singleCreator);\n}", "map": {"version": 3, "names": ["__extends", "use", "ComponentView", "SingleAxisView", "axisModelCreator", "SingleAxisModel", "singleCreator", "install", "installAxisPointer", "AxisView", "SingleAxisPointer", "SingleView", "_super", "_this", "apply", "arguments", "type", "registers", "registerAxisPointerClass", "registerComponentView", "registerComponentModel", "defaultOption", "registerCoordinateSystem"], "sources": ["F:/常规项目/adminweb/node_modules/echarts/lib/component/singleAxis/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { use } from '../../extension.js';\nimport ComponentView from '../../view/Component.js';\nimport SingleAxisView from '../axis/SingleAxisView.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport SingleAxisModel from '../../coord/single/AxisModel.js';\nimport singleCreator from '../../coord/single/singleCreator.js';\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport AxisView from '../axis/AxisView.js';\nimport SingleAxisPointer from '../axisPointer/SingleAxisPointer.js';\nvar SingleView = /** @class */function (_super) {\n  __extends(SingleView, _super);\n  function SingleView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleView.type;\n    return _this;\n  }\n  SingleView.type = 'single';\n  return SingleView;\n}(ComponentView);\nexport function install(registers) {\n  use(installAxisPointer);\n  AxisView.registerAxisPointerClass('SingleAxisPointer', SingleAxisPointer);\n  registers.registerComponentView(SingleView);\n  // Axis\n  registers.registerComponentView(SingleAxisView);\n  registers.registerComponentModel(SingleAxisModel);\n  axisModelCreator(registers, 'single', SingleAxisModel, SingleAxisModel.defaultOption);\n  registers.registerCoordinateSystem('single', singleCreator);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,GAAG,QAAQ,oBAAoB;AACxC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,aAAa,MAAM,qCAAqC;AAC/D,SAASC,OAAO,IAAIC,kBAAkB,QAAQ,2BAA2B;AACzE,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CZ,SAAS,CAACW,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5B,OAAOH,KAAK;EACd;EACAF,UAAU,CAACK,IAAI,GAAG,QAAQ;EAC1B,OAAOL,UAAU;AACnB,CAAC,CAACT,aAAa,CAAC;AAChB,OAAO,SAASK,OAAOA,CAACU,SAAS,EAAE;EACjChB,GAAG,CAACO,kBAAkB,CAAC;EACvBC,QAAQ,CAACS,wBAAwB,CAAC,mBAAmB,EAAER,iBAAiB,CAAC;EACzEO,SAAS,CAACE,qBAAqB,CAACR,UAAU,CAAC;EAC3C;EACAM,SAAS,CAACE,qBAAqB,CAAChB,cAAc,CAAC;EAC/Cc,SAAS,CAACG,sBAAsB,CAACf,eAAe,CAAC;EACjDD,gBAAgB,CAACa,SAAS,EAAE,QAAQ,EAAEZ,eAAe,EAAEA,eAAe,CAACgB,aAAa,CAAC;EACrFJ,SAAS,CAACK,wBAAwB,CAAC,QAAQ,EAAEhB,aAAa,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}