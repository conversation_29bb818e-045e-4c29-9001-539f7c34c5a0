{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport * as util from '../util';\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction whitespace(rule, value, source, errors, options) {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(util.format(options.messages.whitespace, rule.fullField));\n  }\n}\nexport default whitespace;", "map": {"version": 3, "names": ["util", "whitespace", "rule", "value", "source", "errors", "options", "test", "push", "format", "messages", "fullField"], "sources": ["E:/新项目/adminweb/node_modules/async-validator/es/rule/whitespace.js"], "sourcesContent": ["import * as util from '../util';\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction whitespace(rule, value, source, errors, options) {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(util.format(options.messages.whitespace, rule.fullField));\n  }\n}\n\nexport default whitespace;"], "mappings": ";;;AAAA,OAAO,KAAKA,IAAI,MAAM,SAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACxD,IAAI,OAAO,CAACC,IAAI,CAACJ,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,EAAE;IACvCE,MAAM,CAACG,IAAI,CAACR,IAAI,CAACS,MAAM,CAACH,OAAO,CAACI,QAAQ,CAACT,UAAU,EAAEC,IAAI,CAACS,SAAS,CAAC,CAAC;EACvE;AACF;AAEA,eAAeV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}