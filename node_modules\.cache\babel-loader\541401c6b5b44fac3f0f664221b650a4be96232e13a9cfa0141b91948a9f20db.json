{"ast": null, "code": "import _toConsumableArray from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _toArray from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/toArray.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.trim.js\";\nimport \"core-js/modules/es.unescape.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport \"core-js/modules/web.btoa.js\";\nimport \"core-js/modules/web.dom-exception.constructor.js\";\nimport \"core-js/modules/web.dom-exception.stack.js\";\nimport \"core-js/modules/web.dom-exception.to-string-tag.js\";\nimport platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport isURLSameOrigin from \"./isURLSameOrigin.js\";\nimport cookies from \"./cookies.js\";\nimport buildFullPath from \"../core/buildFullPath.js\";\nimport mergeConfig from \"../core/mergeConfig.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport buildURL from \"./buildURL.js\";\nexport default (function (config) {\n  var newConfig = mergeConfig({}, config);\n  var data = newConfig.data,\n    withXSRFToken = newConfig.withXSRFToken,\n    xsrfHeaderName = newConfig.xsrfHeaderName,\n    xsrfCookieName = newConfig.xsrfCookieName,\n    headers = newConfig.headers,\n    auth = newConfig.auth;\n  newConfig.headers = headers = AxiosHeaders.from(headers);\n  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url), config.params, config.paramsSerializer);\n\n  // HTTP basic authentication\n  if (auth) {\n    headers.set('Authorization', 'Basic ' + btoa((auth.username || '') + ':' + (auth.password ? unescape(encodeURIComponent(auth.password)) : '')));\n  }\n  var contentType;\n  if (utils.isFormData(data)) {\n    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n      headers.setContentType(undefined); // Let the browser set it\n    } else if ((contentType = headers.getContentType()) !== false) {\n      // fix semicolon duplication issue for ReactNative FormData implementation\n      var _ref = contentType ? contentType.split(';').map(function (token) {\n          return token.trim();\n        }).filter(Boolean) : [],\n        _ref2 = _toArray(_ref),\n        type = _ref2[0],\n        tokens = _ref2.slice(1);\n      headers.setContentType([type || 'multipart/form-data'].concat(_toConsumableArray(tokens)).join('; '));\n    }\n  }\n\n  // Add xsrf header\n  // This is only done if running in a standard browser environment.\n  // Specifically not if we're in a web worker, or react-native.\n\n  if (platform.hasStandardBrowserEnv) {\n    withXSRFToken && utils.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n    if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {\n      // Add xsrf header\n      var xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n      if (xsrfValue) {\n        headers.set(xsrfHeaderName, xsrfValue);\n      }\n    }\n  }\n  return newConfig;\n});", "map": {"version": 3, "names": ["platform", "utils", "isURLSameOrigin", "cookies", "buildFullPath", "mergeConfig", "AxiosHeaders", "buildURL", "config", "newConfig", "data", "withXSRFToken", "xsrfHeaderName", "xsrfCookieName", "headers", "auth", "from", "url", "baseURL", "params", "paramsSerializer", "set", "btoa", "username", "password", "unescape", "encodeURIComponent", "contentType", "isFormData", "hasStandardBrowserEnv", "hasStandardBrowserWebWorkerEnv", "setContentType", "undefined", "getContentType", "_ref", "split", "map", "token", "trim", "filter", "Boolean", "_ref2", "_toArray", "type", "tokens", "slice", "concat", "_toConsumableArray", "join", "isFunction", "xsrfValue", "read"], "sources": ["E:/新项目/整理6/adminweb/node_modules/axios/lib/helpers/resolveConfig.js"], "sourcesContent": ["import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport isURLSameOrigin from \"./isURLSameOrigin.js\";\nimport cookies from \"./cookies.js\";\nimport buildFullPath from \"../core/buildFullPath.js\";\nimport mergeConfig from \"../core/mergeConfig.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport buildURL from \"./buildURL.js\";\n\nexport default (config) => {\n  const newConfig = mergeConfig({}, config);\n\n  let {data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth} = newConfig;\n\n  newConfig.headers = headers = AxiosHeaders.from(headers);\n\n  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url), config.params, config.paramsSerializer);\n\n  // HTTP basic authentication\n  if (auth) {\n    headers.set('Authorization', 'Basic ' +\n      btoa((auth.username || '') + ':' + (auth.password ? unescape(encodeURIComponent(auth.password)) : ''))\n    );\n  }\n\n  let contentType;\n\n  if (utils.isFormData(data)) {\n    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n      headers.setContentType(undefined); // Let the browser set it\n    } else if ((contentType = headers.getContentType()) !== false) {\n      // fix semicolon duplication issue for ReactNative FormData implementation\n      const [type, ...tokens] = contentType ? contentType.split(';').map(token => token.trim()).filter(Boolean) : [];\n      headers.setContentType([type || 'multipart/form-data', ...tokens].join('; '));\n    }\n  }\n\n  // Add xsrf header\n  // This is only done if running in a standard browser environment.\n  // Specifically not if we're in a web worker, or react-native.\n\n  if (platform.hasStandardBrowserEnv) {\n    withXSRFToken && utils.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n\n    if (withXSRFToken || (withXSRFToken !== false && isURLSameOrigin(newConfig.url))) {\n      // Add xsrf header\n      const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n\n      if (xsrfValue) {\n        headers.set(xsrfHeaderName, xsrfValue);\n      }\n    }\n  }\n\n  return newConfig;\n}\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,QAAQ,MAAM,eAAe;AAEpC,gBAAe,UAACC,MAAM,EAAK;EACzB,IAAMC,SAAS,GAAGJ,WAAW,CAAC,CAAC,CAAC,EAAEG,MAAM,CAAC;EAEzC,IAAKE,IAAI,GAAkED,SAAS,CAA/EC,IAAI;IAAEC,aAAa,GAAmDF,SAAS,CAAzEE,aAAa;IAAEC,cAAc,GAAmCH,SAAS,CAA1DG,cAAc;IAAEC,cAAc,GAAmBJ,SAAS,CAA1CI,cAAc;IAAEC,OAAO,GAAUL,SAAS,CAA1BK,OAAO;IAAEC,IAAI,GAAIN,SAAS,CAAjBM,IAAI;EAEvEN,SAAS,CAACK,OAAO,GAAGA,OAAO,GAAGR,YAAY,CAACU,IAAI,CAACF,OAAO,CAAC;EAExDL,SAAS,CAACQ,GAAG,GAAGV,QAAQ,CAACH,aAAa,CAACK,SAAS,CAACS,OAAO,EAAET,SAAS,CAACQ,GAAG,CAAC,EAAET,MAAM,CAACW,MAAM,EAAEX,MAAM,CAACY,gBAAgB,CAAC;;EAEjH;EACA,IAAIL,IAAI,EAAE;IACRD,OAAO,CAACO,GAAG,CAAC,eAAe,EAAE,QAAQ,GACnCC,IAAI,CAAC,CAACP,IAAI,CAACQ,QAAQ,IAAI,EAAE,IAAI,GAAG,IAAIR,IAAI,CAACS,QAAQ,GAAGC,QAAQ,CAACC,kBAAkB,CAACX,IAAI,CAACS,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CACvG,CAAC;EACH;EAEA,IAAIG,WAAW;EAEf,IAAI1B,KAAK,CAAC2B,UAAU,CAAClB,IAAI,CAAC,EAAE;IAC1B,IAAIV,QAAQ,CAAC6B,qBAAqB,IAAI7B,QAAQ,CAAC8B,8BAA8B,EAAE;MAC7EhB,OAAO,CAACiB,cAAc,CAACC,SAAS,CAAC,CAAC,CAAC;IACrC,CAAC,MAAM,IAAI,CAACL,WAAW,GAAGb,OAAO,CAACmB,cAAc,CAAC,CAAC,MAAM,KAAK,EAAE;MAC7D;MACA,IAAAC,IAAA,GAA0BP,WAAW,GAAGA,WAAW,CAACQ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAAC,KAAK;UAAA,OAAIA,KAAK,CAACC,IAAI,CAAC,CAAC;QAAA,EAAC,CAACC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;QAAAC,KAAA,GAAAC,QAAA,CAAAR,IAAA;QAAvGS,IAAI,GAAAF,KAAA;QAAKG,MAAM,GAAAH,KAAA,CAAAI,KAAA;MACtB/B,OAAO,CAACiB,cAAc,CAAC,CAACY,IAAI,IAAI,qBAAqB,EAAAG,MAAA,CAAAC,kBAAA,CAAKH,MAAM,GAAEI,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/E;EACF;;EAEA;EACA;EACA;;EAEA,IAAIhD,QAAQ,CAAC6B,qBAAqB,EAAE;IAClClB,aAAa,IAAIV,KAAK,CAACgD,UAAU,CAACtC,aAAa,CAAC,KAAKA,aAAa,GAAGA,aAAa,CAACF,SAAS,CAAC,CAAC;IAE9F,IAAIE,aAAa,IAAKA,aAAa,KAAK,KAAK,IAAIT,eAAe,CAACO,SAAS,CAACQ,GAAG,CAAE,EAAE;MAChF;MACA,IAAMiC,SAAS,GAAGtC,cAAc,IAAIC,cAAc,IAAIV,OAAO,CAACgD,IAAI,CAACtC,cAAc,CAAC;MAElF,IAAIqC,SAAS,EAAE;QACbpC,OAAO,CAACO,GAAG,CAACT,cAAc,EAAEsC,SAAS,CAAC;MACxC;IACF;EACF;EAEA,OAAOzC,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}