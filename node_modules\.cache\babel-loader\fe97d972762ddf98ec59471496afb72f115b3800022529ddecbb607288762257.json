{"ast": null, "code": "import request from '@/utils/request';\n\n// 查询用户列表\nexport function getUserList(params) {\n  return request({\n    url: '/system/user/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 新增用户\nexport function addUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改用户\nexport function updateUser(data) {\n  return request({\n    url: '/system/user',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除用户\nexport function deleteUser(id) {\n  return request({\n    url: \"/system/user/\".concat(id),\n    method: 'delete'\n  });\n}\n\n// 更新用户状态\nexport function updateUserStatus(id, status) {\n  return request({\n    url: '/system/user/status',\n    method: 'put',\n    params: {\n      id: id,\n      status: status\n    }\n  });\n}\n\n// 重置密码\nexport function resetUserPassword(id) {\n  return request({\n    url: \"/system/user/reset-password/\".concat(id),\n    method: 'post'\n  });\n}", "map": {"version": 3, "names": ["request", "getUserList", "params", "url", "method", "addUser", "data", "updateUser", "deleteUser", "id", "concat", "updateUserStatus", "status", "resetUserPassword"], "sources": ["F:/常规项目/华通云(V2)/adminweb/src/api/system/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询用户列表\r\nexport function getUserList(params) {\r\n  return request({\r\n    url: '/system/user/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 新增用户\r\nexport function addUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 修改用户\r\nexport function updateUser(data) {\r\n  return request({\r\n    url: '/system/user',\r\n    method: 'put',\r\n    data\r\n  })\r\n}\r\n\r\n// 删除用户\r\nexport function deleteUser(id) {\r\n  return request({\r\n    url: `/system/user/${id}`,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 更新用户状态\r\nexport function updateUserStatus(id, status) {\r\n  return request({\r\n    url: '/system/user/status',\r\n    method: 'put',\r\n    params: { id, status }\r\n  })\r\n}\r\n\r\n// 重置密码\r\nexport function resetUserPassword(id) {\r\n  return request({\r\n    url: `/system/user/reset-password/${id}`,\r\n    method: 'post'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,UAAUA,CAACC,EAAE,EAAE;EAC7B,OAAOT,OAAO,CAAC;IACbG,GAAG,kBAAAO,MAAA,CAAkBD,EAAE,CAAE;IACzBL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,gBAAgBA,CAACF,EAAE,EAAEG,MAAM,EAAE;EAC3C,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAEO,EAAE,EAAFA,EAAE;MAAEG,MAAM,EAANA;IAAO;EACvB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAACJ,EAAE,EAAE;EACpC,OAAOT,OAAO,CAAC;IACbG,GAAG,iCAAAO,MAAA,CAAiCD,EAAE,CAAE;IACxCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}