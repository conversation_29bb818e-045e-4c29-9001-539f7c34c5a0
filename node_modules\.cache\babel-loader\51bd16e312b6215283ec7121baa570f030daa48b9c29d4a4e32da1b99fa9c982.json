{"ast": null, "code": "import \"core-js/modules/es.array.index-of.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport { __extends } from \"tslib\";\nimport * as zrUtil from '../core/util.js';\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nvar Group = function (_super) {\n  __extends(Group, _super);\n  function Group(opts) {\n    var _this = _super.call(this) || this;\n    _this.isGroup = true;\n    _this._children = [];\n    _this.attr(opts);\n    return _this;\n  }\n  Group.prototype.childrenRef = function () {\n    return this._children;\n  };\n  Group.prototype.children = function () {\n    return this._children.slice();\n  };\n  Group.prototype.childAt = function (idx) {\n    return this._children[idx];\n  };\n  Group.prototype.childOfName = function (name) {\n    var children = this._children;\n    for (var i = 0; i < children.length; i++) {\n      if (children[i].name === name) {\n        return children[i];\n      }\n    }\n  };\n  Group.prototype.childCount = function () {\n    return this._children.length;\n  };\n  Group.prototype.add = function (child) {\n    if (child) {\n      if (child !== this && child.parent !== this) {\n        this._children.push(child);\n        this._doAdd(child);\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (child.__hostTarget) {\n          throw 'This elemenet has been used as an attachment';\n        }\n      }\n    }\n    return this;\n  };\n  Group.prototype.addBefore = function (child, nextSibling) {\n    if (child && child !== this && child.parent !== this && nextSibling && nextSibling.parent === this) {\n      var children = this._children;\n      var idx = children.indexOf(nextSibling);\n      if (idx >= 0) {\n        children.splice(idx, 0, child);\n        this._doAdd(child);\n      }\n    }\n    return this;\n  };\n  Group.prototype.replace = function (oldChild, newChild) {\n    var idx = zrUtil.indexOf(this._children, oldChild);\n    if (idx >= 0) {\n      this.replaceAt(newChild, idx);\n    }\n    return this;\n  };\n  Group.prototype.replaceAt = function (child, index) {\n    var children = this._children;\n    var old = children[index];\n    if (child && child !== this && child.parent !== this && child !== old) {\n      children[index] = child;\n      old.parent = null;\n      var zr = this.__zr;\n      if (zr) {\n        old.removeSelfFromZr(zr);\n      }\n      this._doAdd(child);\n    }\n    return this;\n  };\n  Group.prototype._doAdd = function (child) {\n    if (child.parent) {\n      child.parent.remove(child);\n    }\n    child.parent = this;\n    var zr = this.__zr;\n    if (zr && zr !== child.__zr) {\n      child.addSelfToZr(zr);\n    }\n    zr && zr.refresh();\n  };\n  Group.prototype.remove = function (child) {\n    var zr = this.__zr;\n    var children = this._children;\n    var idx = zrUtil.indexOf(children, child);\n    if (idx < 0) {\n      return this;\n    }\n    children.splice(idx, 1);\n    child.parent = null;\n    if (zr) {\n      child.removeSelfFromZr(zr);\n    }\n    zr && zr.refresh();\n    return this;\n  };\n  Group.prototype.removeAll = function () {\n    var children = this._children;\n    var zr = this.__zr;\n    for (var i = 0; i < children.length; i++) {\n      var child = children[i];\n      if (zr) {\n        child.removeSelfFromZr(zr);\n      }\n      child.parent = null;\n    }\n    children.length = 0;\n    return this;\n  };\n  Group.prototype.eachChild = function (cb, context) {\n    var children = this._children;\n    for (var i = 0; i < children.length; i++) {\n      var child = children[i];\n      cb.call(context, child, i);\n    }\n    return this;\n  };\n  Group.prototype.traverse = function (cb, context) {\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      var stopped = cb.call(context, child);\n      if (child.isGroup && !stopped) {\n        child.traverse(cb, context);\n      }\n    }\n    return this;\n  };\n  Group.prototype.addSelfToZr = function (zr) {\n    _super.prototype.addSelfToZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      child.addSelfToZr(zr);\n    }\n  };\n  Group.prototype.removeSelfFromZr = function (zr) {\n    _super.prototype.removeSelfFromZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      child.removeSelfFromZr(zr);\n    }\n  };\n  Group.prototype.getBoundingRect = function (includeChildren) {\n    var tmpRect = new BoundingRect(0, 0, 0, 0);\n    var children = includeChildren || this._children;\n    var tmpMat = [];\n    var rect = null;\n    for (var i = 0; i < children.length; i++) {\n      var child = children[i];\n      if (child.ignore || child.invisible) {\n        continue;\n      }\n      var childRect = child.getBoundingRect();\n      var transform = child.getLocalTransform(tmpMat);\n      if (transform) {\n        BoundingRect.applyTransform(tmpRect, childRect, transform);\n        rect = rect || tmpRect.clone();\n        rect.union(tmpRect);\n      } else {\n        rect = rect || childRect.clone();\n        rect.union(childRect);\n      }\n    }\n    return rect || tmpRect;\n  };\n  return Group;\n}(Element);\nGroup.prototype.type = 'group';\nexport default Group;", "map": {"version": 3, "names": ["__extends", "zrUtil", "Element", "BoundingRect", "Group", "_super", "opts", "_this", "call", "isGroup", "_children", "attr", "prototype", "childrenRef", "children", "slice", "childAt", "idx", "childOfName", "name", "i", "length", "childCount", "add", "child", "parent", "push", "_doAdd", "process", "env", "NODE_ENV", "__host<PERSON><PERSON>get", "addBefore", "nextS<PERSON>ling", "indexOf", "splice", "replace", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "replaceAt", "index", "old", "zr", "__zr", "removeSelfFromZr", "remove", "addSelfToZr", "refresh", "removeAll", "<PERSON><PERSON><PERSON><PERSON>", "cb", "context", "traverse", "stopped", "getBoundingRect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tmpRect", "tmpMat", "rect", "ignore", "invisible", "childRect", "transform", "getLocalTransform", "applyTransform", "clone", "union", "type"], "sources": ["F:/常规项目/华通云(V2)/adminweb/node_modules/zrender/lib/graphic/Group.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport * as zrUtil from '../core/util.js';\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nvar Group = (function (_super) {\n    __extends(Group, _super);\n    function Group(opts) {\n        var _this = _super.call(this) || this;\n        _this.isGroup = true;\n        _this._children = [];\n        _this.attr(opts);\n        return _this;\n    }\n    Group.prototype.childrenRef = function () {\n        return this._children;\n    };\n    Group.prototype.children = function () {\n        return this._children.slice();\n    };\n    Group.prototype.childAt = function (idx) {\n        return this._children[idx];\n    };\n    Group.prototype.childOfName = function (name) {\n        var children = this._children;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i].name === name) {\n                return children[i];\n            }\n        }\n    };\n    Group.prototype.childCount = function () {\n        return this._children.length;\n    };\n    Group.prototype.add = function (child) {\n        if (child) {\n            if (child !== this && child.parent !== this) {\n                this._children.push(child);\n                this._doAdd(child);\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (child.__hostTarget) {\n                    throw 'This elemenet has been used as an attachment';\n                }\n            }\n        }\n        return this;\n    };\n    Group.prototype.addBefore = function (child, nextSibling) {\n        if (child && child !== this && child.parent !== this\n            && nextSibling && nextSibling.parent === this) {\n            var children = this._children;\n            var idx = children.indexOf(nextSibling);\n            if (idx >= 0) {\n                children.splice(idx, 0, child);\n                this._doAdd(child);\n            }\n        }\n        return this;\n    };\n    Group.prototype.replace = function (oldChild, newChild) {\n        var idx = zrUtil.indexOf(this._children, oldChild);\n        if (idx >= 0) {\n            this.replaceAt(newChild, idx);\n        }\n        return this;\n    };\n    Group.prototype.replaceAt = function (child, index) {\n        var children = this._children;\n        var old = children[index];\n        if (child && child !== this && child.parent !== this && child !== old) {\n            children[index] = child;\n            old.parent = null;\n            var zr = this.__zr;\n            if (zr) {\n                old.removeSelfFromZr(zr);\n            }\n            this._doAdd(child);\n        }\n        return this;\n    };\n    Group.prototype._doAdd = function (child) {\n        if (child.parent) {\n            child.parent.remove(child);\n        }\n        child.parent = this;\n        var zr = this.__zr;\n        if (zr && zr !== child.__zr) {\n            child.addSelfToZr(zr);\n        }\n        zr && zr.refresh();\n    };\n    Group.prototype.remove = function (child) {\n        var zr = this.__zr;\n        var children = this._children;\n        var idx = zrUtil.indexOf(children, child);\n        if (idx < 0) {\n            return this;\n        }\n        children.splice(idx, 1);\n        child.parent = null;\n        if (zr) {\n            child.removeSelfFromZr(zr);\n        }\n        zr && zr.refresh();\n        return this;\n    };\n    Group.prototype.removeAll = function () {\n        var children = this._children;\n        var zr = this.__zr;\n        for (var i = 0; i < children.length; i++) {\n            var child = children[i];\n            if (zr) {\n                child.removeSelfFromZr(zr);\n            }\n            child.parent = null;\n        }\n        children.length = 0;\n        return this;\n    };\n    Group.prototype.eachChild = function (cb, context) {\n        var children = this._children;\n        for (var i = 0; i < children.length; i++) {\n            var child = children[i];\n            cb.call(context, child, i);\n        }\n        return this;\n    };\n    Group.prototype.traverse = function (cb, context) {\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            var stopped = cb.call(context, child);\n            if (child.isGroup && !stopped) {\n                child.traverse(cb, context);\n            }\n        }\n        return this;\n    };\n    Group.prototype.addSelfToZr = function (zr) {\n        _super.prototype.addSelfToZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            child.addSelfToZr(zr);\n        }\n    };\n    Group.prototype.removeSelfFromZr = function (zr) {\n        _super.prototype.removeSelfFromZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            child.removeSelfFromZr(zr);\n        }\n    };\n    Group.prototype.getBoundingRect = function (includeChildren) {\n        var tmpRect = new BoundingRect(0, 0, 0, 0);\n        var children = includeChildren || this._children;\n        var tmpMat = [];\n        var rect = null;\n        for (var i = 0; i < children.length; i++) {\n            var child = children[i];\n            if (child.ignore || child.invisible) {\n                continue;\n            }\n            var childRect = child.getBoundingRect();\n            var transform = child.getLocalTransform(tmpMat);\n            if (transform) {\n                BoundingRect.applyTransform(tmpRect, childRect, transform);\n                rect = rect || tmpRect.clone();\n                rect.union(tmpRect);\n            }\n            else {\n                rect = rect || childRect.clone();\n                rect.union(childRect);\n            }\n        }\n        return rect || tmpRect;\n    };\n    return Group;\n}(Element));\nGroup.prototype.type = 'group';\nexport default Group;\n"], "mappings": ";;;;;;;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,YAAY,MAAM,yBAAyB;AAClD,IAAIC,KAAK,GAAI,UAAUC,MAAM,EAAE;EAC3BL,SAAS,CAACI,KAAK,EAAEC,MAAM,CAAC;EACxB,SAASD,KAAKA,CAACE,IAAI,EAAE;IACjB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,OAAO,GAAG,IAAI;IACpBF,KAAK,CAACG,SAAS,GAAG,EAAE;IACpBH,KAAK,CAACI,IAAI,CAACL,IAAI,CAAC;IAChB,OAAOC,KAAK;EAChB;EACAH,KAAK,CAACQ,SAAS,CAACC,WAAW,GAAG,YAAY;IACtC,OAAO,IAAI,CAACH,SAAS;EACzB,CAAC;EACDN,KAAK,CAACQ,SAAS,CAACE,QAAQ,GAAG,YAAY;IACnC,OAAO,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,CAAC;EACjC,CAAC;EACDX,KAAK,CAACQ,SAAS,CAACI,OAAO,GAAG,UAAUC,GAAG,EAAE;IACrC,OAAO,IAAI,CAACP,SAAS,CAACO,GAAG,CAAC;EAC9B,CAAC;EACDb,KAAK,CAACQ,SAAS,CAACM,WAAW,GAAG,UAAUC,IAAI,EAAE;IAC1C,IAAIL,QAAQ,GAAG,IAAI,CAACJ,SAAS;IAC7B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIN,QAAQ,CAACM,CAAC,CAAC,CAACD,IAAI,KAAKA,IAAI,EAAE;QAC3B,OAAOL,QAAQ,CAACM,CAAC,CAAC;MACtB;IACJ;EACJ,CAAC;EACDhB,KAAK,CAACQ,SAAS,CAACU,UAAU,GAAG,YAAY;IACrC,OAAO,IAAI,CAACZ,SAAS,CAACW,MAAM;EAChC,CAAC;EACDjB,KAAK,CAACQ,SAAS,CAACW,GAAG,GAAG,UAAUC,KAAK,EAAE;IACnC,IAAIA,KAAK,EAAE;MACP,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,EAAE;QACzC,IAAI,CAACf,SAAS,CAACgB,IAAI,CAACF,KAAK,CAAC;QAC1B,IAAI,CAACG,MAAM,CAACH,KAAK,CAAC;MACtB;MACA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC,IAAIN,KAAK,CAACO,YAAY,EAAE;UACpB,MAAM,8CAA8C;QACxD;MACJ;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD3B,KAAK,CAACQ,SAAS,CAACoB,SAAS,GAAG,UAAUR,KAAK,EAAES,WAAW,EAAE;IACtD,IAAIT,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,IAC7CQ,WAAW,IAAIA,WAAW,CAACR,MAAM,KAAK,IAAI,EAAE;MAC/C,IAAIX,QAAQ,GAAG,IAAI,CAACJ,SAAS;MAC7B,IAAIO,GAAG,GAAGH,QAAQ,CAACoB,OAAO,CAACD,WAAW,CAAC;MACvC,IAAIhB,GAAG,IAAI,CAAC,EAAE;QACVH,QAAQ,CAACqB,MAAM,CAAClB,GAAG,EAAE,CAAC,EAAEO,KAAK,CAAC;QAC9B,IAAI,CAACG,MAAM,CAACH,KAAK,CAAC;MACtB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDpB,KAAK,CAACQ,SAAS,CAACwB,OAAO,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;IACpD,IAAIrB,GAAG,GAAGhB,MAAM,CAACiC,OAAO,CAAC,IAAI,CAACxB,SAAS,EAAE2B,QAAQ,CAAC;IAClD,IAAIpB,GAAG,IAAI,CAAC,EAAE;MACV,IAAI,CAACsB,SAAS,CAACD,QAAQ,EAAErB,GAAG,CAAC;IACjC;IACA,OAAO,IAAI;EACf,CAAC;EACDb,KAAK,CAACQ,SAAS,CAAC2B,SAAS,GAAG,UAAUf,KAAK,EAAEgB,KAAK,EAAE;IAChD,IAAI1B,QAAQ,GAAG,IAAI,CAACJ,SAAS;IAC7B,IAAI+B,GAAG,GAAG3B,QAAQ,CAAC0B,KAAK,CAAC;IACzB,IAAIhB,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,IAAID,KAAK,KAAKiB,GAAG,EAAE;MACnE3B,QAAQ,CAAC0B,KAAK,CAAC,GAAGhB,KAAK;MACvBiB,GAAG,CAAChB,MAAM,GAAG,IAAI;MACjB,IAAIiB,EAAE,GAAG,IAAI,CAACC,IAAI;MAClB,IAAID,EAAE,EAAE;QACJD,GAAG,CAACG,gBAAgB,CAACF,EAAE,CAAC;MAC5B;MACA,IAAI,CAACf,MAAM,CAACH,KAAK,CAAC;IACtB;IACA,OAAO,IAAI;EACf,CAAC;EACDpB,KAAK,CAACQ,SAAS,CAACe,MAAM,GAAG,UAAUH,KAAK,EAAE;IACtC,IAAIA,KAAK,CAACC,MAAM,EAAE;MACdD,KAAK,CAACC,MAAM,CAACoB,MAAM,CAACrB,KAAK,CAAC;IAC9B;IACAA,KAAK,CAACC,MAAM,GAAG,IAAI;IACnB,IAAIiB,EAAE,GAAG,IAAI,CAACC,IAAI;IAClB,IAAID,EAAE,IAAIA,EAAE,KAAKlB,KAAK,CAACmB,IAAI,EAAE;MACzBnB,KAAK,CAACsB,WAAW,CAACJ,EAAE,CAAC;IACzB;IACAA,EAAE,IAAIA,EAAE,CAACK,OAAO,CAAC,CAAC;EACtB,CAAC;EACD3C,KAAK,CAACQ,SAAS,CAACiC,MAAM,GAAG,UAAUrB,KAAK,EAAE;IACtC,IAAIkB,EAAE,GAAG,IAAI,CAACC,IAAI;IAClB,IAAI7B,QAAQ,GAAG,IAAI,CAACJ,SAAS;IAC7B,IAAIO,GAAG,GAAGhB,MAAM,CAACiC,OAAO,CAACpB,QAAQ,EAAEU,KAAK,CAAC;IACzC,IAAIP,GAAG,GAAG,CAAC,EAAE;MACT,OAAO,IAAI;IACf;IACAH,QAAQ,CAACqB,MAAM,CAAClB,GAAG,EAAE,CAAC,CAAC;IACvBO,KAAK,CAACC,MAAM,GAAG,IAAI;IACnB,IAAIiB,EAAE,EAAE;MACJlB,KAAK,CAACoB,gBAAgB,CAACF,EAAE,CAAC;IAC9B;IACAA,EAAE,IAAIA,EAAE,CAACK,OAAO,CAAC,CAAC;IAClB,OAAO,IAAI;EACf,CAAC;EACD3C,KAAK,CAACQ,SAAS,CAACoC,SAAS,GAAG,YAAY;IACpC,IAAIlC,QAAQ,GAAG,IAAI,CAACJ,SAAS;IAC7B,IAAIgC,EAAE,GAAG,IAAI,CAACC,IAAI;IAClB,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAII,KAAK,GAAGV,QAAQ,CAACM,CAAC,CAAC;MACvB,IAAIsB,EAAE,EAAE;QACJlB,KAAK,CAACoB,gBAAgB,CAACF,EAAE,CAAC;MAC9B;MACAlB,KAAK,CAACC,MAAM,GAAG,IAAI;IACvB;IACAX,QAAQ,CAACO,MAAM,GAAG,CAAC;IACnB,OAAO,IAAI;EACf,CAAC;EACDjB,KAAK,CAACQ,SAAS,CAACqC,SAAS,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;IAC/C,IAAIrC,QAAQ,GAAG,IAAI,CAACJ,SAAS;IAC7B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAII,KAAK,GAAGV,QAAQ,CAACM,CAAC,CAAC;MACvB8B,EAAE,CAAC1C,IAAI,CAAC2C,OAAO,EAAE3B,KAAK,EAAEJ,CAAC,CAAC;IAC9B;IACA,OAAO,IAAI;EACf,CAAC;EACDhB,KAAK,CAACQ,SAAS,CAACwC,QAAQ,GAAG,UAAUF,EAAE,EAAEC,OAAO,EAAE;IAC9C,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACV,SAAS,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAII,KAAK,GAAG,IAAI,CAACd,SAAS,CAACU,CAAC,CAAC;MAC7B,IAAIiC,OAAO,GAAGH,EAAE,CAAC1C,IAAI,CAAC2C,OAAO,EAAE3B,KAAK,CAAC;MACrC,IAAIA,KAAK,CAACf,OAAO,IAAI,CAAC4C,OAAO,EAAE;QAC3B7B,KAAK,CAAC4B,QAAQ,CAACF,EAAE,EAAEC,OAAO,CAAC;MAC/B;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD/C,KAAK,CAACQ,SAAS,CAACkC,WAAW,GAAG,UAAUJ,EAAE,EAAE;IACxCrC,MAAM,CAACO,SAAS,CAACkC,WAAW,CAACtC,IAAI,CAAC,IAAI,EAAEkC,EAAE,CAAC;IAC3C,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACV,SAAS,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAII,KAAK,GAAG,IAAI,CAACd,SAAS,CAACU,CAAC,CAAC;MAC7BI,KAAK,CAACsB,WAAW,CAACJ,EAAE,CAAC;IACzB;EACJ,CAAC;EACDtC,KAAK,CAACQ,SAAS,CAACgC,gBAAgB,GAAG,UAAUF,EAAE,EAAE;IAC7CrC,MAAM,CAACO,SAAS,CAACgC,gBAAgB,CAACpC,IAAI,CAAC,IAAI,EAAEkC,EAAE,CAAC;IAChD,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACV,SAAS,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAII,KAAK,GAAG,IAAI,CAACd,SAAS,CAACU,CAAC,CAAC;MAC7BI,KAAK,CAACoB,gBAAgB,CAACF,EAAE,CAAC;IAC9B;EACJ,CAAC;EACDtC,KAAK,CAACQ,SAAS,CAAC0C,eAAe,GAAG,UAAUC,eAAe,EAAE;IACzD,IAAIC,OAAO,GAAG,IAAIrD,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1C,IAAIW,QAAQ,GAAGyC,eAAe,IAAI,IAAI,CAAC7C,SAAS;IAChD,IAAI+C,MAAM,GAAG,EAAE;IACf,IAAIC,IAAI,GAAG,IAAI;IACf,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAII,KAAK,GAAGV,QAAQ,CAACM,CAAC,CAAC;MACvB,IAAII,KAAK,CAACmC,MAAM,IAAInC,KAAK,CAACoC,SAAS,EAAE;QACjC;MACJ;MACA,IAAIC,SAAS,GAAGrC,KAAK,CAAC8B,eAAe,CAAC,CAAC;MACvC,IAAIQ,SAAS,GAAGtC,KAAK,CAACuC,iBAAiB,CAACN,MAAM,CAAC;MAC/C,IAAIK,SAAS,EAAE;QACX3D,YAAY,CAAC6D,cAAc,CAACR,OAAO,EAAEK,SAAS,EAAEC,SAAS,CAAC;QAC1DJ,IAAI,GAAGA,IAAI,IAAIF,OAAO,CAACS,KAAK,CAAC,CAAC;QAC9BP,IAAI,CAACQ,KAAK,CAACV,OAAO,CAAC;MACvB,CAAC,MACI;QACDE,IAAI,GAAGA,IAAI,IAAIG,SAAS,CAACI,KAAK,CAAC,CAAC;QAChCP,IAAI,CAACQ,KAAK,CAACL,SAAS,CAAC;MACzB;IACJ;IACA,OAAOH,IAAI,IAAIF,OAAO;EAC1B,CAAC;EACD,OAAOpD,KAAK;AAChB,CAAC,CAACF,OAAO,CAAE;AACXE,KAAK,CAACQ,SAAS,CAACuD,IAAI,GAAG,OAAO;AAC9B,eAAe/D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}