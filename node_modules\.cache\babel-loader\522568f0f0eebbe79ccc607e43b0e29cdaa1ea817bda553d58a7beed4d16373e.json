{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport LRU from '../../core/LRU.js';\nimport { platformApi } from '../../core/platform.js';\nvar globalImageCache = new LRU(50);\nexport function findExistImage(newImageOrSrc) {\n  if (typeof newImageOrSrc === 'string') {\n    var cachedImgObj = globalImageCache.get(newImageOrSrc);\n    return cachedImgObj && cachedImgObj.image;\n  } else {\n    return newImageOrSrc;\n  }\n}\nexport function createOrUpdateImage(newImageOrSrc, image, hostEl, onload, cbPayload) {\n  if (!newImageOrSrc) {\n    return image;\n  } else if (typeof newImageOrSrc === 'string') {\n    if (image && image.__zrImageSrc === newImageOrSrc || !hostEl) {\n      return image;\n    }\n    var cachedImgObj = globalImageCache.get(newImageOrSrc);\n    var pendingWrap = {\n      hostEl: hostEl,\n      cb: onload,\n      cbPayload: cbPayload\n    };\n    if (cachedImgObj) {\n      image = cachedImgObj.image;\n      !isImageReady(image) && cachedImgObj.pending.push(pendingWrap);\n    } else {\n      image = platformApi.loadImage(newImageOrSrc, imageOnLoad, imageOnLoad);\n      image.__zrImageSrc = newImageOrSrc;\n      globalImageCache.put(newImageOrSrc, image.__cachedImgObj = {\n        image: image,\n        pending: [pendingWrap]\n      });\n    }\n    return image;\n  } else {\n    return newImageOrSrc;\n  }\n}\nfunction imageOnLoad() {\n  var cachedImgObj = this.__cachedImgObj;\n  this.onload = this.onerror = this.__cachedImgObj = null;\n  for (var i = 0; i < cachedImgObj.pending.length; i++) {\n    var pendingWrap = cachedImgObj.pending[i];\n    var cb = pendingWrap.cb;\n    cb && cb(this, pendingWrap.cbPayload);\n    pendingWrap.hostEl.dirty();\n  }\n  cachedImgObj.pending.length = 0;\n}\nexport function isImageReady(image) {\n  return image && image.width && image.height;\n}", "map": {"version": 3, "names": ["LRU", "platformApi", "globalImageCache", "findExistImage", "newImageOrSrc", "cachedImgObj", "get", "image", "createOrUpdateImage", "hostEl", "onload", "cbPayload", "__zrImageSrc", "pendingWrap", "cb", "isImageReady", "pending", "push", "loadImage", "imageOnLoad", "put", "__cachedImgObj", "onerror", "i", "length", "dirty", "width", "height"], "sources": ["E:/新项目/adminweb/node_modules/zrender/lib/graphic/helper/image.js"], "sourcesContent": ["import LRU from '../../core/LRU.js';\nimport { platformApi } from '../../core/platform.js';\nvar globalImageCache = new LRU(50);\nexport function findExistImage(newImageOrSrc) {\n    if (typeof newImageOrSrc === 'string') {\n        var cachedImgObj = globalImageCache.get(newImageOrSrc);\n        return cachedImgObj && cachedImgObj.image;\n    }\n    else {\n        return newImageOrSrc;\n    }\n}\nexport function createOrUpdateImage(newImageOrSrc, image, hostEl, onload, cbPayload) {\n    if (!newImageOrSrc) {\n        return image;\n    }\n    else if (typeof newImageOrSrc === 'string') {\n        if ((image && image.__zrImageSrc === newImageOrSrc) || !hostEl) {\n            return image;\n        }\n        var cachedImgObj = globalImageCache.get(newImageOrSrc);\n        var pendingWrap = { hostEl: hostEl, cb: onload, cbPayload: cbPayload };\n        if (cachedImgObj) {\n            image = cachedImgObj.image;\n            !isImageReady(image) && cachedImgObj.pending.push(pendingWrap);\n        }\n        else {\n            image = platformApi.loadImage(newImageOrSrc, imageOnLoad, imageOnLoad);\n            image.__zrImageSrc = newImageOrSrc;\n            globalImageCache.put(newImageOrSrc, image.__cachedImgObj = {\n                image: image,\n                pending: [pendingWrap]\n            });\n        }\n        return image;\n    }\n    else {\n        return newImageOrSrc;\n    }\n}\nfunction imageOnLoad() {\n    var cachedImgObj = this.__cachedImgObj;\n    this.onload = this.onerror = this.__cachedImgObj = null;\n    for (var i = 0; i < cachedImgObj.pending.length; i++) {\n        var pendingWrap = cachedImgObj.pending[i];\n        var cb = pendingWrap.cb;\n        cb && cb(this, pendingWrap.cbPayload);\n        pendingWrap.hostEl.dirty();\n    }\n    cachedImgObj.pending.length = 0;\n}\nexport function isImageReady(image) {\n    return image && image.width && image.height;\n}\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,mBAAmB;AACnC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,IAAIC,gBAAgB,GAAG,IAAIF,GAAG,CAAC,EAAE,CAAC;AAClC,OAAO,SAASG,cAAcA,CAACC,aAAa,EAAE;EAC1C,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IACnC,IAAIC,YAAY,GAAGH,gBAAgB,CAACI,GAAG,CAACF,aAAa,CAAC;IACtD,OAAOC,YAAY,IAAIA,YAAY,CAACE,KAAK;EAC7C,CAAC,MACI;IACD,OAAOH,aAAa;EACxB;AACJ;AACA,OAAO,SAASI,mBAAmBA,CAACJ,aAAa,EAAEG,KAAK,EAAEE,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAE;EACjF,IAAI,CAACP,aAAa,EAAE;IAChB,OAAOG,KAAK;EAChB,CAAC,MACI,IAAI,OAAOH,aAAa,KAAK,QAAQ,EAAE;IACxC,IAAKG,KAAK,IAAIA,KAAK,CAACK,YAAY,KAAKR,aAAa,IAAK,CAACK,MAAM,EAAE;MAC5D,OAAOF,KAAK;IAChB;IACA,IAAIF,YAAY,GAAGH,gBAAgB,CAACI,GAAG,CAACF,aAAa,CAAC;IACtD,IAAIS,WAAW,GAAG;MAAEJ,MAAM,EAAEA,MAAM;MAAEK,EAAE,EAAEJ,MAAM;MAAEC,SAAS,EAAEA;IAAU,CAAC;IACtE,IAAIN,YAAY,EAAE;MACdE,KAAK,GAAGF,YAAY,CAACE,KAAK;MAC1B,CAACQ,YAAY,CAACR,KAAK,CAAC,IAAIF,YAAY,CAACW,OAAO,CAACC,IAAI,CAACJ,WAAW,CAAC;IAClE,CAAC,MACI;MACDN,KAAK,GAAGN,WAAW,CAACiB,SAAS,CAACd,aAAa,EAAEe,WAAW,EAAEA,WAAW,CAAC;MACtEZ,KAAK,CAACK,YAAY,GAAGR,aAAa;MAClCF,gBAAgB,CAACkB,GAAG,CAAChB,aAAa,EAAEG,KAAK,CAACc,cAAc,GAAG;QACvDd,KAAK,EAAEA,KAAK;QACZS,OAAO,EAAE,CAACH,WAAW;MACzB,CAAC,CAAC;IACN;IACA,OAAON,KAAK;EAChB,CAAC,MACI;IACD,OAAOH,aAAa;EACxB;AACJ;AACA,SAASe,WAAWA,CAAA,EAAG;EACnB,IAAId,YAAY,GAAG,IAAI,CAACgB,cAAc;EACtC,IAAI,CAACX,MAAM,GAAG,IAAI,CAACY,OAAO,GAAG,IAAI,CAACD,cAAc,GAAG,IAAI;EACvD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,YAAY,CAACW,OAAO,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAClD,IAAIV,WAAW,GAAGR,YAAY,CAACW,OAAO,CAACO,CAAC,CAAC;IACzC,IAAIT,EAAE,GAAGD,WAAW,CAACC,EAAE;IACvBA,EAAE,IAAIA,EAAE,CAAC,IAAI,EAAED,WAAW,CAACF,SAAS,CAAC;IACrCE,WAAW,CAACJ,MAAM,CAACgB,KAAK,CAAC,CAAC;EAC9B;EACApB,YAAY,CAACW,OAAO,CAACQ,MAAM,GAAG,CAAC;AACnC;AACA,OAAO,SAAST,YAAYA,CAACR,KAAK,EAAE;EAChC,OAAOA,KAAK,IAAIA,KAAK,CAACmB,KAAK,IAAInB,KAAK,CAACoB,MAAM;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}