{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u4E91/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getParams, updateParams } from '@/api/system/params';\nexport default {\n  name: 'SystemParams',\n  data: function data() {\n    return {\n      form: {\n        minTransfer: 100,\n        maxTransfer: 50000,\n        transferFee: 1,\n        enableTransfer: 1,\n        enableInternalTransfer: 0,\n        minWithdraw: 100,\n        maxWithdraw: 50000,\n        withdrawFee: 1,\n        enableWithdraw: 1,\n        autoWithdraw: 0,\n        accountName: '',\n        accountNumber: '',\n        bankName: '',\n        bankBranch: '',\n        bankRemark: '',\n        autoExchange: 0,\n        // 新增管理奖参数\n        reward1: 10.00,\n        reward2: 5.00,\n        reward3: 1.00,\n        reward4: 5.00,\n        // 新增流量参数\n        sumRate: 0.00,\n        nowRate: 0.00\n      }\n    };\n  },\n  created: function created() {\n    this.getParamsData();\n  },\n  methods: {\n    // 获取参数数据\n    getParamsData: function getParamsData() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _context.next = 3;\n              return getParams();\n            case 3:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.form = res.data;\n              } else {\n                _this.$message.error(res.msg || '获取参数失败');\n              }\n              _context.next = 11;\n              break;\n            case 7:\n              _context.prev = 7;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('获取参数失败:', _context.t0);\n              _this.$message.error('获取参数失败');\n            case 11:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 7]]);\n      }))();\n    },\n    // 提交表单\n    handleSubmit: function handleSubmit() {\n      var _this2 = this;\n      this.$confirm('确认要保存修改吗？', '提示', {\n        type: 'warning'\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return updateParams(_this2.form);\n            case 3:\n              res = _context2.sent;\n              if (res.code === 0) {\n                _this2.$message.success('保存成功');\n              } else {\n                _this2.$message.error(res.msg || '保存失败');\n              }\n              _context2.next = 11;\n              break;\n            case 7:\n              _context2.prev = 7;\n              _context2.t0 = _context2[\"catch\"](0);\n              console.error('保存失败:', _context2.t0);\n              _this2.$message.error('保存失败');\n            case 11:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 7]]);\n      })))[\"catch\"](function () {});\n    }\n  }\n};", "map": {"version": 3, "names": ["getParams", "updateParams", "name", "data", "form", "minTransfer", "maxTransfer", "transferFee", "enableTransfer", "enableInternalTransfer", "minWithdraw", "max<PERSON><PERSON><PERSON><PERSON>", "withdrawFee", "enableWithdraw", "autoWithdraw", "accountName", "accountNumber", "bankName", "bankBranch", "bankRemark", "autoExchange", "reward1", "reward2", "reward3", "reward4", "sumRate", "nowRate", "created", "getParamsData", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "$message", "error", "msg", "t0", "console", "stop", "handleSubmit", "_this2", "$confirm", "type", "then", "_callee2", "_callee2$", "_context2", "success"], "sources": ["src/views/system/params/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <el-form :model=\"form\" ref=\"form\" label-width=\"150px\" class=\"params-form\">\r\n        <!-- 转账相关设置 -->\r\n        <div class=\"section-title\">转账设置</div>\r\n        <el-form-item label=\"最低转账限额\">\r\n          <el-input-number \r\n            v-model=\"form.minTransfer\" \r\n            :min=\"0\"\r\n            :precision=\"2\"\r\n            :step=\"100\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">元</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"最高转账限额\">\r\n          <el-input-number \r\n            v-model=\"form.maxTransfer\" \r\n            :min=\"form.minTransfer\"\r\n            :precision=\"2\"\r\n            :step=\"1000\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">元</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"转账手续费\">\r\n          <el-input-number \r\n            v-model=\"form.transferFee\" \r\n            :min=\"0\"\r\n            :max=\"100\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">%</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否允许转账\">\r\n          <el-switch\r\n            v-model=\"form.enableTransfer\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- <el-form-item label=\"是否允许内部转账\">\r\n          <el-switch\r\n            v-model=\"form.enableInternalTransfer\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item> -->\r\n\r\n        <!-- 提现相关设置 -->\r\n        <div class=\"section-title\">提现设置</div>\r\n        <el-form-item label=\"最低提现限额\">\r\n          <el-input-number \r\n            v-model=\"form.minWithdraw\" \r\n            :min=\"0\"\r\n            :precision=\"2\"\r\n            :step=\"100\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">元</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"最高提现限额\">\r\n          <el-input-number \r\n            v-model=\"form.maxWithdraw\" \r\n            :min=\"form.minWithdraw\"\r\n            :precision=\"2\"\r\n            :step=\"1000\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">元</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"提现手续费\">\r\n          <el-input-number \r\n            v-model=\"form.withdrawFee\" \r\n            :min=\"0\"\r\n            :max=\"100\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">%</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否允许提现\">\r\n          <el-switch\r\n            v-model=\"form.enableWithdraw\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否自动提现\">\r\n          <el-switch\r\n            v-model=\"form.autoWithdraw\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 打款信息设置 -->\r\n        <div class=\"section-title\">打款信息设置</div>\r\n        <el-form-item label=\"收款账户名称\">\r\n          <el-input \r\n            v-model=\"form.accountName\"\r\n            placeholder=\"请输入收款账户名称\"\r\n            style=\"width: 300px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"收款账号\">\r\n          <el-input\r\n            v-model=\"form.accountNumber\"\r\n            placeholder=\"请输入收款账号\"\r\n            style=\"width: 300px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"开户行\">\r\n          <el-input\r\n            v-model=\"form.bankName\"\r\n            placeholder=\"请输入开户行名称\"\r\n            style=\"width: 300px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"开户支行\">\r\n          <el-input\r\n            v-model=\"form.bankBranch\"\r\n            placeholder=\"请输入开户支行名称\"\r\n            style=\"width: 300px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"备注说明\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            v-model=\"form.bankRemark\"\r\n            placeholder=\"请输入打款相关说明信息\"\r\n            :rows=\"3\"\r\n            style=\"width: 500px\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 增加是否允许兑换和是否复投的开关 -->\r\n        <div class=\"section-title\">兑换设置</div>\r\n        <el-form-item label=\"是否允许兑换\">\r\n          <el-switch\r\n            v-model=\"form.autoExchange\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <!-- 管理奖参数设置 -->\r\n        <div class=\"section-title\">管理奖参数设置</div>\r\n        <el-form-item label=\"合伙人比例\">\r\n          <el-input-number \r\n            v-model=\"form.reward1\" \r\n            :min=\"0\"\r\n            :max=\"100\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">%</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"联创比例\">\r\n          <el-input-number \r\n            v-model=\"form.reward2\" \r\n            :min=\"0\"\r\n            :max=\"100\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">%</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"平级比例\">\r\n          <el-input-number \r\n            v-model=\"form.reward3\" \r\n            :min=\"0\"\r\n            :max=\"100\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">%</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"团队分红比例\">\r\n          <el-input-number \r\n            v-model=\"form.reward4\" \r\n            :min=\"0\"\r\n            :max=\"100\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n          />\r\n          <span class=\"unit\">%</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSubmit\">保存设置</el-button>\r\n        </el-form-item>\r\n\r\n        <!-- 流量管理 -->\r\n        <div class=\"section-title\">流量管理</div>\r\n        <el-form-item label=\"总的流量\">\r\n          <el-input-number \r\n            v-model=\"form.sumRate\" \r\n            :min=\"0\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n            :disabled=\"true\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"当前可用流量\">\r\n          <el-input-number \r\n            v-model=\"form.nowRate\" \r\n            :min=\"0\"\r\n            :precision=\"2\"\r\n            style=\"width: 200px\"\r\n            :disabled=\"true\"\r\n          />\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getParams, updateParams } from '@/api/system/params'\r\n\r\nexport default {\r\n  name: 'SystemParams',\r\n  data() {\r\n    return {\r\n      form: {\r\n        minTransfer: 100,\r\n        maxTransfer: 50000,\r\n        transferFee: 1,\r\n        enableTransfer: 1,\r\n        enableInternalTransfer: 0,\r\n        minWithdraw: 100,\r\n        maxWithdraw: 50000,\r\n        withdrawFee: 1,\r\n        enableWithdraw: 1,\r\n        autoWithdraw: 0,\r\n        accountName: '',\r\n        accountNumber: '',\r\n        bankName: '',\r\n        bankBranch: '',\r\n        bankRemark: '',\r\n        autoExchange: 0,\r\n        // 新增管理奖参数\r\n        reward1: 10.00,\r\n        reward2: 5.00,\r\n        reward3: 1.00,\r\n        reward4: 5.00,\r\n        // 新增流量参数\r\n        sumRate: 0.00,\r\n        nowRate: 0.00\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getParamsData()\r\n  },\r\n  methods: {\r\n    // 获取参数数据\r\n    async getParamsData() {\r\n      try {\r\n        const res = await getParams()\r\n        if (res.code === 0) {\r\n          this.form = res.data\r\n        } else {\r\n          this.$message.error(res.msg || '获取参数失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取参数失败:', error)\r\n        this.$message.error('获取参数失败')\r\n      }\r\n    },\r\n\r\n    // 提交表单\r\n    handleSubmit() {\r\n      this.$confirm('确认要保存修改吗？', '提示', {\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const res = await updateParams(this.form)\r\n          if (res.code === 0) {\r\n            this.$message.success('保存成功')\r\n          } else {\r\n            this.$message.error(res.msg || '保存失败')\r\n          }\r\n        } catch (error) {\r\n          console.error('保存失败:', error)\r\n          this.$message.error('保存失败')\r\n        }\r\n      }).catch(() => {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .params-form {\r\n    max-width: 800px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n\r\n    .section-title {\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      margin: 30px 0 20px;\r\n      padding-left: 10px;\r\n      border-left: 4px solid #409EFF;\r\n    }\r\n\r\n    .unit {\r\n      margin-left: 10px;\r\n      color: #909399;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 25px;\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";;AA8OA,SAAAA,SAAA,EAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,sBAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,aAAA;QACAC,QAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACA;QACAC,OAAA;QACAC,OAAA;QACAC,OAAA;QACAC,OAAA;QACA;QACAC,OAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAxC,SAAA;YAAA;cAAAmC,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAZ,KAAA,CAAA1B,IAAA,GAAA+B,GAAA,CAAAhC,IAAA;cACA;gBACA2B,KAAA,CAAAa,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAAP,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAS,OAAA,CAAAH,KAAA,YAAAN,QAAA,CAAAQ,EAAA;cACAhB,KAAA,CAAAa,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAN,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IAEA;IAEA;IACAe,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,IAAA;MACA,GAAAC,IAAA,cAAAtB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqB,SAAA;QAAA,IAAAnB,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAmB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjB,IAAA,GAAAiB,SAAA,CAAAhB,IAAA;YAAA;cAAAgB,SAAA,CAAAjB,IAAA;cAAAiB,SAAA,CAAAhB,IAAA;cAAA,OAEAvC,YAAA,CAAAiD,MAAA,CAAA9C,IAAA;YAAA;cAAA+B,GAAA,GAAAqB,SAAA,CAAAf,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAQ,MAAA,CAAAP,QAAA,CAAAc,OAAA;cACA;gBACAP,MAAA,CAAAP,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAAW,SAAA,CAAAhB,IAAA;cAAA;YAAA;cAAAgB,SAAA,CAAAjB,IAAA;cAAAiB,SAAA,CAAAV,EAAA,GAAAU,SAAA;cAEAT,OAAA,CAAAH,KAAA,UAAAY,SAAA,CAAAV,EAAA;cACAI,MAAA,CAAAP,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAY,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA,CAEA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}