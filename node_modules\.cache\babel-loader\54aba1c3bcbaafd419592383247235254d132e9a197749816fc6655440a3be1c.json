{"ast": null, "code": "import \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.reverse.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.sort.js\";\nimport \"core-js/modules/es.function.name.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getTooltipMarker, encodeHTML, makeValueReadable, convertToColorString } from '../../util/format.js';\nimport { isString, each, hasOwn, isArray, map, assert, extend } from 'zrender/lib/core/util.js';\nimport { SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nimport { getRandomIdBase } from '../../util/number.js';\nvar TOOLTIP_LINE_HEIGHT_CSS = 'line-height:1';\n// TODO: more textStyle option\nfunction getTooltipTextStyle(textStyle, renderMode) {\n  var nameFontColor = textStyle.color || '#6e7079';\n  var nameFontSize = textStyle.fontSize || 12;\n  var nameFontWeight = textStyle.fontWeight || '400';\n  var valueFontColor = textStyle.color || '#464646';\n  var valueFontSize = textStyle.fontSize || 14;\n  var valueFontWeight = textStyle.fontWeight || '900';\n  if (renderMode === 'html') {\n    // `textStyle` is probably from user input, should be encoded to reduce security risk.\n    return {\n      // eslint-disable-next-line max-len\n      nameStyle: \"font-size:\" + encodeHTML(nameFontSize + '') + \"px;color:\" + encodeHTML(nameFontColor) + \";font-weight:\" + encodeHTML(nameFontWeight + ''),\n      // eslint-disable-next-line max-len\n      valueStyle: \"font-size:\" + encodeHTML(valueFontSize + '') + \"px;color:\" + encodeHTML(valueFontColor) + \";font-weight:\" + encodeHTML(valueFontWeight + '')\n    };\n  } else {\n    return {\n      nameStyle: {\n        fontSize: nameFontSize,\n        fill: nameFontColor,\n        fontWeight: nameFontWeight\n      },\n      valueStyle: {\n        fontSize: valueFontSize,\n        fill: valueFontColor,\n        fontWeight: valueFontWeight\n      }\n    };\n  }\n}\n// See `TooltipMarkupLayoutIntent['innerGapLevel']`.\n// (value from UI design)\nvar HTML_GAPS = [0, 10, 20, 30];\nvar RICH_TEXT_GAPS = ['', '\\n', '\\n\\n', '\\n\\n\\n'];\n// eslint-disable-next-line max-len\nexport function createTooltipMarkup(type, option) {\n  option.type = type;\n  return option;\n}\nfunction isSectionFragment(frag) {\n  return frag.type === 'section';\n}\nfunction getBuilder(frag) {\n  return isSectionFragment(frag) ? buildSection : buildNameValue;\n}\nfunction getBlockGapLevel(frag) {\n  if (isSectionFragment(frag)) {\n    var gapLevel_1 = 0;\n    var subBlockLen = frag.blocks.length;\n    var hasInnerGap_1 = subBlockLen > 1 || subBlockLen > 0 && !frag.noHeader;\n    each(frag.blocks, function (subBlock) {\n      var subGapLevel = getBlockGapLevel(subBlock);\n      // If the some of the sub-blocks have some gaps (like 10px) inside, this block\n      // should use a larger gap (like 20px) to distinguish those sub-blocks.\n      if (subGapLevel >= gapLevel_1) {\n        gapLevel_1 = subGapLevel + +(hasInnerGap_1 && (\n        // 0 always can not be readable gap level.\n        !subGapLevel\n        // If no header, always keep the sub gap level. Otherwise\n        // look weird in case `multipleSeries`.\n        || isSectionFragment(subBlock) && !subBlock.noHeader));\n      }\n    });\n    return gapLevel_1;\n  }\n  return 0;\n}\nfunction buildSection(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var noHeader = fragment.noHeader;\n  var gaps = getGap(getBlockGapLevel(fragment));\n  var subMarkupTextList = [];\n  var subBlocks = fragment.blocks || [];\n  assert(!subBlocks || isArray(subBlocks));\n  subBlocks = subBlocks || [];\n  var orderMode = ctx.orderMode;\n  if (fragment.sortBlocks && orderMode) {\n    subBlocks = subBlocks.slice();\n    var orderMap = {\n      valueAsc: 'asc',\n      valueDesc: 'desc'\n    };\n    if (hasOwn(orderMap, orderMode)) {\n      var comparator_1 = new SortOrderComparator(orderMap[orderMode], null);\n      subBlocks.sort(function (a, b) {\n        return comparator_1.evaluate(a.sortParam, b.sortParam);\n      });\n    }\n    // FIXME 'seriesDesc' necessary?\n    else if (orderMode === 'seriesDesc') {\n      subBlocks.reverse();\n    }\n  }\n  each(subBlocks, function (subBlock, idx) {\n    var valueFormatter = fragment.valueFormatter;\n    var subMarkupText = getBuilder(subBlock)(\n    // Inherit valueFormatter\n    valueFormatter ? extend(extend({}, ctx), {\n      valueFormatter: valueFormatter\n    }) : ctx, subBlock, idx > 0 ? gaps.html : 0, toolTipTextStyle);\n    subMarkupText != null && subMarkupTextList.push(subMarkupText);\n  });\n  var subMarkupText = ctx.renderMode === 'richText' ? subMarkupTextList.join(gaps.richText) : wrapBlockHTML(subMarkupTextList.join(''), noHeader ? topMarginForOuterGap : gaps.html);\n  if (noHeader) {\n    return subMarkupText;\n  }\n  var displayableHeader = makeValueReadable(fragment.header, 'ordinal', ctx.useUTC);\n  var nameStyle = getTooltipTextStyle(toolTipTextStyle, ctx.renderMode).nameStyle;\n  if (ctx.renderMode === 'richText') {\n    return wrapInlineNameRichText(ctx, displayableHeader, nameStyle) + gaps.richText + subMarkupText;\n  } else {\n    return wrapBlockHTML(\"<div style=\\\"\" + nameStyle + \";\" + TOOLTIP_LINE_HEIGHT_CSS + \";\\\">\" + encodeHTML(displayableHeader) + '</div>' + subMarkupText, topMarginForOuterGap);\n  }\n}\nfunction buildNameValue(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var renderMode = ctx.renderMode;\n  var noName = fragment.noName;\n  var noValue = fragment.noValue;\n  var noMarker = !fragment.markerType;\n  var name = fragment.name;\n  var useUTC = ctx.useUTC;\n  var valueFormatter = fragment.valueFormatter || ctx.valueFormatter || function (value) {\n    value = isArray(value) ? value : [value];\n    return map(value, function (val, idx) {\n      return makeValueReadable(val, isArray(valueTypeOption) ? valueTypeOption[idx] : valueTypeOption, useUTC);\n    });\n  };\n  if (noName && noValue) {\n    return;\n  }\n  var markerStr = noMarker ? '' : ctx.markupStyleCreator.makeTooltipMarker(fragment.markerType, fragment.markerColor || '#333', renderMode);\n  var readableName = noName ? '' : makeValueReadable(name, 'ordinal', useUTC);\n  var valueTypeOption = fragment.valueType;\n  var readableValueList = noValue ? [] : valueFormatter(fragment.value, fragment.dataIndex);\n  var valueAlignRight = !noMarker || !noName;\n  // It little weird if only value next to marker but far from marker.\n  var valueCloseToMarker = !noMarker && noName;\n  var _a = getTooltipTextStyle(toolTipTextStyle, renderMode),\n    nameStyle = _a.nameStyle,\n    valueStyle = _a.valueStyle;\n  return renderMode === 'richText' ? (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameRichText(ctx, readableName, nameStyle))\n  // Value has commas inside, so use ' ' as delimiter for multiple values.\n  + (noValue ? '' : wrapInlineValueRichText(ctx, readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)) : wrapBlockHTML((noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameHTML(readableName, !noMarker, nameStyle)) + (noValue ? '' : wrapInlineValueHTML(readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)), topMarginForOuterGap);\n}\n/**\n * @return markupText. null/undefined means no content.\n */\nexport function buildTooltipMarkup(fragment, markupStyleCreator, renderMode, orderMode, useUTC, toolTipTextStyle) {\n  if (!fragment) {\n    return;\n  }\n  var builder = getBuilder(fragment);\n  var ctx = {\n    useUTC: useUTC,\n    renderMode: renderMode,\n    orderMode: orderMode,\n    markupStyleCreator: markupStyleCreator,\n    valueFormatter: fragment.valueFormatter\n  };\n  return builder(ctx, fragment, 0, toolTipTextStyle);\n}\nfunction getGap(gapLevel) {\n  return {\n    html: HTML_GAPS[gapLevel],\n    richText: RICH_TEXT_GAPS[gapLevel]\n  };\n}\nfunction wrapBlockHTML(encodedContent, topGap) {\n  var clearfix = '<div style=\"clear:both\"></div>';\n  var marginCSS = \"margin: \" + topGap + \"px 0 0\";\n  return \"<div style=\\\"\" + marginCSS + \";\" + TOOLTIP_LINE_HEIGHT_CSS + \";\\\">\" + encodedContent + clearfix + '</div>';\n}\nfunction wrapInlineNameHTML(name, leftHasMarker, style) {\n  var marginCss = leftHasMarker ? 'margin-left:2px' : '';\n  return \"<span style=\\\"\" + style + \";\" + marginCss + \"\\\">\" + encodeHTML(name) + '</span>';\n}\nfunction wrapInlineValueHTML(valueList, alignRight, valueCloseToMarker, style) {\n  // Do not too close to marker, considering there are multiple values separated by spaces.\n  var paddingStr = valueCloseToMarker ? '10px' : '20px';\n  var alignCSS = alignRight ? \"float:right;margin-left:\" + paddingStr : '';\n  valueList = isArray(valueList) ? valueList : [valueList];\n  return \"<span style=\\\"\" + alignCSS + \";\" + style + \"\\\">\"\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  + map(valueList, function (value) {\n    return encodeHTML(value);\n  }).join('&nbsp;&nbsp;') + '</span>';\n}\nfunction wrapInlineNameRichText(ctx, name, style) {\n  return ctx.markupStyleCreator.wrapRichTextStyle(name, style);\n}\nfunction wrapInlineValueRichText(ctx, values, alignRight, valueCloseToMarker, style) {\n  var styles = [style];\n  var paddingLeft = valueCloseToMarker ? 10 : 20;\n  alignRight && styles.push({\n    padding: [0, 0, 0, paddingLeft],\n    align: 'right'\n  });\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  return ctx.markupStyleCreator.wrapRichTextStyle(isArray(values) ? values.join('  ') : values, styles);\n}\nexport function retrieveVisualColorForTooltipMarker(series, dataIndex) {\n  var style = series.getData().getItemVisual(dataIndex, 'style');\n  var color = style[series.visualDrawType];\n  return convertToColorString(color);\n}\nexport function getPaddingFromTooltipModel(model, renderMode) {\n  var padding = model.get('padding');\n  return padding != null ? padding\n  // We give slightly different to look pretty.\n  : renderMode === 'richText' ? [8, 10] : 10;\n}\n/**\n * The major feature is generate styles for `renderMode: 'richText'`.\n * But it also serves `renderMode: 'html'` to provide\n * \"renderMode-independent\" API.\n */\nvar TooltipMarkupStyleCreator = /** @class */function () {\n  function TooltipMarkupStyleCreator() {\n    this.richTextStyles = {};\n    // Notice that \"generate a style name\" usually happens repeatedly when mouse is moving and\n    // a tooltip is displayed. So we put the `_nextStyleNameId` as a member of each creator\n    // rather than static shared by all creators (which will cause it increase to fast).\n    this._nextStyleNameId = getRandomIdBase();\n  }\n  TooltipMarkupStyleCreator.prototype._generateStyleName = function () {\n    return '__EC_aUTo_' + this._nextStyleNameId++;\n  };\n  TooltipMarkupStyleCreator.prototype.makeTooltipMarker = function (markerType, colorStr, renderMode) {\n    var markerId = renderMode === 'richText' ? this._generateStyleName() : null;\n    var marker = getTooltipMarker({\n      color: colorStr,\n      type: markerType,\n      renderMode: renderMode,\n      markerId: markerId\n    });\n    if (isString(marker)) {\n      return marker;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(markerId);\n      }\n      this.richTextStyles[markerId] = marker.style;\n      return marker.content;\n    }\n  };\n  /**\n   * @usage\n   * ```ts\n   * const styledText = markupStyleCreator.wrapRichTextStyle([\n   *     // The styles will be auto merged.\n   *     {\n   *         fontSize: 12,\n   *         color: 'blue'\n   *     },\n   *     {\n   *         padding: 20\n   *     }\n   * ]);\n   * ```\n   */\n  TooltipMarkupStyleCreator.prototype.wrapRichTextStyle = function (text, styles) {\n    var finalStl = {};\n    if (isArray(styles)) {\n      each(styles, function (stl) {\n        return extend(finalStl, stl);\n      });\n    } else {\n      extend(finalStl, styles);\n    }\n    var styleName = this._generateStyleName();\n    this.richTextStyles[styleName] = finalStl;\n    return \"{\" + styleName + \"|\" + text + \"}\";\n  };\n  return TooltipMarkupStyleCreator;\n}();\nexport { TooltipMarkupStyleCreator };", "map": {"version": 3, "names": ["getTooltipMarker", "encodeHTML", "makeValueReadable", "convertToColorString", "isString", "each", "hasOwn", "isArray", "map", "assert", "extend", "SortOrderComparator", "getRandomIdBase", "TOOLTIP_LINE_HEIGHT_CSS", "getTooltipTextStyle", "textStyle", "renderMode", "nameFontColor", "color", "nameFontSize", "fontSize", "nameFontWeight", "fontWeight", "valueFontColor", "valueFontSize", "valueFontWeight", "nameStyle", "valueStyle", "fill", "HTML_GAPS", "RICH_TEXT_GAPS", "createTooltipMarkup", "type", "option", "isSectionFragment", "frag", "getBuilder", "buildSection", "buildNameValue", "getBlockGapLevel", "gapLevel_1", "subBlockLen", "blocks", "length", "hasInnerGap_1", "<PERSON><PERSON><PERSON><PERSON>", "subBlock", "subGapLevel", "ctx", "fragment", "topMarginForOuterGap", "toolTipTextStyle", "gaps", "getGap", "subMarkupTextList", "subBlocks", "orderMode", "sortBlocks", "slice", "orderMap", "valueAsc", "valueDesc", "comparator_1", "sort", "a", "b", "evaluate", "sortParam", "reverse", "idx", "valueFormatter", "subMarkupText", "html", "push", "join", "richText", "wrapBlockHTML", "displayableHeader", "header", "useUTC", "wrapInlineNameRichText", "noName", "noValue", "<PERSON><PERSON><PERSON><PERSON>", "markerType", "name", "value", "val", "valueTypeOption", "markerStr", "markupStyleCreator", "makeTooltipMarker", "markerColor", "readableName", "valueType", "readableValueList", "dataIndex", "valueAlignRight", "valueCloseToMarker", "_a", "wrapInlineValueRichText", "wrapInlineNameHTML", "wrapInlineValueHTML", "buildTooltipMarkup", "builder", "gapLevel", "encodedContent", "topGap", "clearfix", "marginCSS", "leftHasMarker", "style", "marginCss", "valueList", "alignRight", "paddingStr", "alignCSS", "wrapRichTextStyle", "values", "styles", "paddingLeft", "padding", "align", "retrieveVisualColorForTooltipMarker", "series", "getData", "getItemVisual", "visualDrawType", "getPaddingFromTooltipModel", "model", "get", "TooltipMarkupStyleCreator", "richTextStyles", "_nextStyleNameId", "prototype", "_generateStyleName", "colorStr", "markerId", "marker", "process", "env", "NODE_ENV", "content", "text", "finalStl", "stl", "styleName"], "sources": ["F:/常规项目/adminweb/node_modules/echarts/lib/component/tooltip/tooltipMarkup.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getTooltipMarker, encodeHTML, makeValueReadable, convertToColorString } from '../../util/format.js';\nimport { isString, each, hasOwn, isArray, map, assert, extend } from 'zrender/lib/core/util.js';\nimport { SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nimport { getRandomIdBase } from '../../util/number.js';\nvar TOOLTIP_LINE_HEIGHT_CSS = 'line-height:1';\n// TODO: more textStyle option\nfunction getTooltipTextStyle(textStyle, renderMode) {\n  var nameFontColor = textStyle.color || '#6e7079';\n  var nameFontSize = textStyle.fontSize || 12;\n  var nameFontWeight = textStyle.fontWeight || '400';\n  var valueFontColor = textStyle.color || '#464646';\n  var valueFontSize = textStyle.fontSize || 14;\n  var valueFontWeight = textStyle.fontWeight || '900';\n  if (renderMode === 'html') {\n    // `textStyle` is probably from user input, should be encoded to reduce security risk.\n    return {\n      // eslint-disable-next-line max-len\n      nameStyle: \"font-size:\" + encodeHTML(nameFontSize + '') + \"px;color:\" + encodeHTML(nameFontColor) + \";font-weight:\" + encodeHTML(nameFontWeight + ''),\n      // eslint-disable-next-line max-len\n      valueStyle: \"font-size:\" + encodeHTML(valueFontSize + '') + \"px;color:\" + encodeHTML(valueFontColor) + \";font-weight:\" + encodeHTML(valueFontWeight + '')\n    };\n  } else {\n    return {\n      nameStyle: {\n        fontSize: nameFontSize,\n        fill: nameFontColor,\n        fontWeight: nameFontWeight\n      },\n      valueStyle: {\n        fontSize: valueFontSize,\n        fill: valueFontColor,\n        fontWeight: valueFontWeight\n      }\n    };\n  }\n}\n// See `TooltipMarkupLayoutIntent['innerGapLevel']`.\n// (value from UI design)\nvar HTML_GAPS = [0, 10, 20, 30];\nvar RICH_TEXT_GAPS = ['', '\\n', '\\n\\n', '\\n\\n\\n'];\n// eslint-disable-next-line max-len\nexport function createTooltipMarkup(type, option) {\n  option.type = type;\n  return option;\n}\nfunction isSectionFragment(frag) {\n  return frag.type === 'section';\n}\nfunction getBuilder(frag) {\n  return isSectionFragment(frag) ? buildSection : buildNameValue;\n}\nfunction getBlockGapLevel(frag) {\n  if (isSectionFragment(frag)) {\n    var gapLevel_1 = 0;\n    var subBlockLen = frag.blocks.length;\n    var hasInnerGap_1 = subBlockLen > 1 || subBlockLen > 0 && !frag.noHeader;\n    each(frag.blocks, function (subBlock) {\n      var subGapLevel = getBlockGapLevel(subBlock);\n      // If the some of the sub-blocks have some gaps (like 10px) inside, this block\n      // should use a larger gap (like 20px) to distinguish those sub-blocks.\n      if (subGapLevel >= gapLevel_1) {\n        gapLevel_1 = subGapLevel + +(hasInnerGap_1 && (\n        // 0 always can not be readable gap level.\n        !subGapLevel\n        // If no header, always keep the sub gap level. Otherwise\n        // look weird in case `multipleSeries`.\n        || isSectionFragment(subBlock) && !subBlock.noHeader));\n      }\n    });\n    return gapLevel_1;\n  }\n  return 0;\n}\nfunction buildSection(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var noHeader = fragment.noHeader;\n  var gaps = getGap(getBlockGapLevel(fragment));\n  var subMarkupTextList = [];\n  var subBlocks = fragment.blocks || [];\n  assert(!subBlocks || isArray(subBlocks));\n  subBlocks = subBlocks || [];\n  var orderMode = ctx.orderMode;\n  if (fragment.sortBlocks && orderMode) {\n    subBlocks = subBlocks.slice();\n    var orderMap = {\n      valueAsc: 'asc',\n      valueDesc: 'desc'\n    };\n    if (hasOwn(orderMap, orderMode)) {\n      var comparator_1 = new SortOrderComparator(orderMap[orderMode], null);\n      subBlocks.sort(function (a, b) {\n        return comparator_1.evaluate(a.sortParam, b.sortParam);\n      });\n    }\n    // FIXME 'seriesDesc' necessary?\n    else if (orderMode === 'seriesDesc') {\n      subBlocks.reverse();\n    }\n  }\n  each(subBlocks, function (subBlock, idx) {\n    var valueFormatter = fragment.valueFormatter;\n    var subMarkupText = getBuilder(subBlock)(\n    // Inherit valueFormatter\n    valueFormatter ? extend(extend({}, ctx), {\n      valueFormatter: valueFormatter\n    }) : ctx, subBlock, idx > 0 ? gaps.html : 0, toolTipTextStyle);\n    subMarkupText != null && subMarkupTextList.push(subMarkupText);\n  });\n  var subMarkupText = ctx.renderMode === 'richText' ? subMarkupTextList.join(gaps.richText) : wrapBlockHTML(subMarkupTextList.join(''), noHeader ? topMarginForOuterGap : gaps.html);\n  if (noHeader) {\n    return subMarkupText;\n  }\n  var displayableHeader = makeValueReadable(fragment.header, 'ordinal', ctx.useUTC);\n  var nameStyle = getTooltipTextStyle(toolTipTextStyle, ctx.renderMode).nameStyle;\n  if (ctx.renderMode === 'richText') {\n    return wrapInlineNameRichText(ctx, displayableHeader, nameStyle) + gaps.richText + subMarkupText;\n  } else {\n    return wrapBlockHTML(\"<div style=\\\"\" + nameStyle + \";\" + TOOLTIP_LINE_HEIGHT_CSS + \";\\\">\" + encodeHTML(displayableHeader) + '</div>' + subMarkupText, topMarginForOuterGap);\n  }\n}\nfunction buildNameValue(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var renderMode = ctx.renderMode;\n  var noName = fragment.noName;\n  var noValue = fragment.noValue;\n  var noMarker = !fragment.markerType;\n  var name = fragment.name;\n  var useUTC = ctx.useUTC;\n  var valueFormatter = fragment.valueFormatter || ctx.valueFormatter || function (value) {\n    value = isArray(value) ? value : [value];\n    return map(value, function (val, idx) {\n      return makeValueReadable(val, isArray(valueTypeOption) ? valueTypeOption[idx] : valueTypeOption, useUTC);\n    });\n  };\n  if (noName && noValue) {\n    return;\n  }\n  var markerStr = noMarker ? '' : ctx.markupStyleCreator.makeTooltipMarker(fragment.markerType, fragment.markerColor || '#333', renderMode);\n  var readableName = noName ? '' : makeValueReadable(name, 'ordinal', useUTC);\n  var valueTypeOption = fragment.valueType;\n  var readableValueList = noValue ? [] : valueFormatter(fragment.value, fragment.dataIndex);\n  var valueAlignRight = !noMarker || !noName;\n  // It little weird if only value next to marker but far from marker.\n  var valueCloseToMarker = !noMarker && noName;\n  var _a = getTooltipTextStyle(toolTipTextStyle, renderMode),\n    nameStyle = _a.nameStyle,\n    valueStyle = _a.valueStyle;\n  return renderMode === 'richText' ? (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameRichText(ctx, readableName, nameStyle))\n  // Value has commas inside, so use ' ' as delimiter for multiple values.\n  + (noValue ? '' : wrapInlineValueRichText(ctx, readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)) : wrapBlockHTML((noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameHTML(readableName, !noMarker, nameStyle)) + (noValue ? '' : wrapInlineValueHTML(readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)), topMarginForOuterGap);\n}\n/**\n * @return markupText. null/undefined means no content.\n */\nexport function buildTooltipMarkup(fragment, markupStyleCreator, renderMode, orderMode, useUTC, toolTipTextStyle) {\n  if (!fragment) {\n    return;\n  }\n  var builder = getBuilder(fragment);\n  var ctx = {\n    useUTC: useUTC,\n    renderMode: renderMode,\n    orderMode: orderMode,\n    markupStyleCreator: markupStyleCreator,\n    valueFormatter: fragment.valueFormatter\n  };\n  return builder(ctx, fragment, 0, toolTipTextStyle);\n}\nfunction getGap(gapLevel) {\n  return {\n    html: HTML_GAPS[gapLevel],\n    richText: RICH_TEXT_GAPS[gapLevel]\n  };\n}\nfunction wrapBlockHTML(encodedContent, topGap) {\n  var clearfix = '<div style=\"clear:both\"></div>';\n  var marginCSS = \"margin: \" + topGap + \"px 0 0\";\n  return \"<div style=\\\"\" + marginCSS + \";\" + TOOLTIP_LINE_HEIGHT_CSS + \";\\\">\" + encodedContent + clearfix + '</div>';\n}\nfunction wrapInlineNameHTML(name, leftHasMarker, style) {\n  var marginCss = leftHasMarker ? 'margin-left:2px' : '';\n  return \"<span style=\\\"\" + style + \";\" + marginCss + \"\\\">\" + encodeHTML(name) + '</span>';\n}\nfunction wrapInlineValueHTML(valueList, alignRight, valueCloseToMarker, style) {\n  // Do not too close to marker, considering there are multiple values separated by spaces.\n  var paddingStr = valueCloseToMarker ? '10px' : '20px';\n  var alignCSS = alignRight ? \"float:right;margin-left:\" + paddingStr : '';\n  valueList = isArray(valueList) ? valueList : [valueList];\n  return \"<span style=\\\"\" + alignCSS + \";\" + style + \"\\\">\"\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  + map(valueList, function (value) {\n    return encodeHTML(value);\n  }).join('&nbsp;&nbsp;') + '</span>';\n}\nfunction wrapInlineNameRichText(ctx, name, style) {\n  return ctx.markupStyleCreator.wrapRichTextStyle(name, style);\n}\nfunction wrapInlineValueRichText(ctx, values, alignRight, valueCloseToMarker, style) {\n  var styles = [style];\n  var paddingLeft = valueCloseToMarker ? 10 : 20;\n  alignRight && styles.push({\n    padding: [0, 0, 0, paddingLeft],\n    align: 'right'\n  });\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  return ctx.markupStyleCreator.wrapRichTextStyle(isArray(values) ? values.join('  ') : values, styles);\n}\nexport function retrieveVisualColorForTooltipMarker(series, dataIndex) {\n  var style = series.getData().getItemVisual(dataIndex, 'style');\n  var color = style[series.visualDrawType];\n  return convertToColorString(color);\n}\nexport function getPaddingFromTooltipModel(model, renderMode) {\n  var padding = model.get('padding');\n  return padding != null ? padding\n  // We give slightly different to look pretty.\n  : renderMode === 'richText' ? [8, 10] : 10;\n}\n/**\n * The major feature is generate styles for `renderMode: 'richText'`.\n * But it also serves `renderMode: 'html'` to provide\n * \"renderMode-independent\" API.\n */\nvar TooltipMarkupStyleCreator = /** @class */function () {\n  function TooltipMarkupStyleCreator() {\n    this.richTextStyles = {};\n    // Notice that \"generate a style name\" usually happens repeatedly when mouse is moving and\n    // a tooltip is displayed. So we put the `_nextStyleNameId` as a member of each creator\n    // rather than static shared by all creators (which will cause it increase to fast).\n    this._nextStyleNameId = getRandomIdBase();\n  }\n  TooltipMarkupStyleCreator.prototype._generateStyleName = function () {\n    return '__EC_aUTo_' + this._nextStyleNameId++;\n  };\n  TooltipMarkupStyleCreator.prototype.makeTooltipMarker = function (markerType, colorStr, renderMode) {\n    var markerId = renderMode === 'richText' ? this._generateStyleName() : null;\n    var marker = getTooltipMarker({\n      color: colorStr,\n      type: markerType,\n      renderMode: renderMode,\n      markerId: markerId\n    });\n    if (isString(marker)) {\n      return marker;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(markerId);\n      }\n      this.richTextStyles[markerId] = marker.style;\n      return marker.content;\n    }\n  };\n  /**\n   * @usage\n   * ```ts\n   * const styledText = markupStyleCreator.wrapRichTextStyle([\n   *     // The styles will be auto merged.\n   *     {\n   *         fontSize: 12,\n   *         color: 'blue'\n   *     },\n   *     {\n   *         padding: 20\n   *     }\n   * ]);\n   * ```\n   */\n  TooltipMarkupStyleCreator.prototype.wrapRichTextStyle = function (text, styles) {\n    var finalStl = {};\n    if (isArray(styles)) {\n      each(styles, function (stl) {\n        return extend(finalStl, stl);\n      });\n    } else {\n      extend(finalStl, styles);\n    }\n    var styleName = this._generateStyleName();\n    this.richTextStyles[styleName] = finalStl;\n    return \"{\" + styleName + \"|\" + text + \"}\";\n  };\n  return TooltipMarkupStyleCreator;\n}();\nexport { TooltipMarkupStyleCreator };"], "mappings": ";;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,oBAAoB,QAAQ,sBAAsB;AAC5G,SAASC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,QAAQ,0BAA0B;AAC/F,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,eAAe,QAAQ,sBAAsB;AACtD,IAAIC,uBAAuB,GAAG,eAAe;AAC7C;AACA,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,UAAU,EAAE;EAClD,IAAIC,aAAa,GAAGF,SAAS,CAACG,KAAK,IAAI,SAAS;EAChD,IAAIC,YAAY,GAAGJ,SAAS,CAACK,QAAQ,IAAI,EAAE;EAC3C,IAAIC,cAAc,GAAGN,SAAS,CAACO,UAAU,IAAI,KAAK;EAClD,IAAIC,cAAc,GAAGR,SAAS,CAACG,KAAK,IAAI,SAAS;EACjD,IAAIM,aAAa,GAAGT,SAAS,CAACK,QAAQ,IAAI,EAAE;EAC5C,IAAIK,eAAe,GAAGV,SAAS,CAACO,UAAU,IAAI,KAAK;EACnD,IAAIN,UAAU,KAAK,MAAM,EAAE;IACzB;IACA,OAAO;MACL;MACAU,SAAS,EAAE,YAAY,GAAGzB,UAAU,CAACkB,YAAY,GAAG,EAAE,CAAC,GAAG,WAAW,GAAGlB,UAAU,CAACgB,aAAa,CAAC,GAAG,eAAe,GAAGhB,UAAU,CAACoB,cAAc,GAAG,EAAE,CAAC;MACrJ;MACAM,UAAU,EAAE,YAAY,GAAG1B,UAAU,CAACuB,aAAa,GAAG,EAAE,CAAC,GAAG,WAAW,GAAGvB,UAAU,CAACsB,cAAc,CAAC,GAAG,eAAe,GAAGtB,UAAU,CAACwB,eAAe,GAAG,EAAE;IAC1J,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MACLC,SAAS,EAAE;QACTN,QAAQ,EAAED,YAAY;QACtBS,IAAI,EAAEX,aAAa;QACnBK,UAAU,EAAED;MACd,CAAC;MACDM,UAAU,EAAE;QACVP,QAAQ,EAAEI,aAAa;QACvBI,IAAI,EAAEL,cAAc;QACpBD,UAAU,EAAEG;MACd;IACF,CAAC;EACH;AACF;AACA;AACA;AACA,IAAII,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B,IAAIC,cAAc,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC;AACjD;AACA,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChDA,MAAM,CAACD,IAAI,GAAGA,IAAI;EAClB,OAAOC,MAAM;AACf;AACA,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,OAAOA,IAAI,CAACH,IAAI,KAAK,SAAS;AAChC;AACA,SAASI,UAAUA,CAACD,IAAI,EAAE;EACxB,OAAOD,iBAAiB,CAACC,IAAI,CAAC,GAAGE,YAAY,GAAGC,cAAc;AAChE;AACA,SAASC,gBAAgBA,CAACJ,IAAI,EAAE;EAC9B,IAAID,iBAAiB,CAACC,IAAI,CAAC,EAAE;IAC3B,IAAIK,UAAU,GAAG,CAAC;IAClB,IAAIC,WAAW,GAAGN,IAAI,CAACO,MAAM,CAACC,MAAM;IACpC,IAAIC,aAAa,GAAGH,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,CAAC,IAAI,CAACN,IAAI,CAACU,QAAQ;IACxExC,IAAI,CAAC8B,IAAI,CAACO,MAAM,EAAE,UAAUI,QAAQ,EAAE;MACpC,IAAIC,WAAW,GAAGR,gBAAgB,CAACO,QAAQ,CAAC;MAC5C;MACA;MACA,IAAIC,WAAW,IAAIP,UAAU,EAAE;QAC7BA,UAAU,GAAGO,WAAW,GAAG,EAAEH,aAAa;QAC1C;QACA,CAACG;QACD;QACA;QAAA,GACGb,iBAAiB,CAACY,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACD,QAAQ,CAAC,CAAC;MACxD;IACF,CAAC,CAAC;IACF,OAAOL,UAAU;EACnB;EACA,OAAO,CAAC;AACV;AACA,SAASH,YAAYA,CAACW,GAAG,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAE;EAC3E,IAAIN,QAAQ,GAAGI,QAAQ,CAACJ,QAAQ;EAChC,IAAIO,IAAI,GAAGC,MAAM,CAACd,gBAAgB,CAACU,QAAQ,CAAC,CAAC;EAC7C,IAAIK,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,SAAS,GAAGN,QAAQ,CAACP,MAAM,IAAI,EAAE;EACrCjC,MAAM,CAAC,CAAC8C,SAAS,IAAIhD,OAAO,CAACgD,SAAS,CAAC,CAAC;EACxCA,SAAS,GAAGA,SAAS,IAAI,EAAE;EAC3B,IAAIC,SAAS,GAAGR,GAAG,CAACQ,SAAS;EAC7B,IAAIP,QAAQ,CAACQ,UAAU,IAAID,SAAS,EAAE;IACpCD,SAAS,GAAGA,SAAS,CAACG,KAAK,CAAC,CAAC;IAC7B,IAAIC,QAAQ,GAAG;MACbC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE;IACb,CAAC;IACD,IAAIvD,MAAM,CAACqD,QAAQ,EAAEH,SAAS,CAAC,EAAE;MAC/B,IAAIM,YAAY,GAAG,IAAInD,mBAAmB,CAACgD,QAAQ,CAACH,SAAS,CAAC,EAAE,IAAI,CAAC;MACrED,SAAS,CAACQ,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;QAC7B,OAAOH,YAAY,CAACI,QAAQ,CAACF,CAAC,CAACG,SAAS,EAAEF,CAAC,CAACE,SAAS,CAAC;MACxD,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAIX,SAAS,KAAK,YAAY,EAAE;MACnCD,SAAS,CAACa,OAAO,CAAC,CAAC;IACrB;EACF;EACA/D,IAAI,CAACkD,SAAS,EAAE,UAAUT,QAAQ,EAAEuB,GAAG,EAAE;IACvC,IAAIC,cAAc,GAAGrB,QAAQ,CAACqB,cAAc;IAC5C,IAAIC,aAAa,GAAGnC,UAAU,CAACU,QAAQ,CAAC;IACxC;IACAwB,cAAc,GAAG5D,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEsC,GAAG,CAAC,EAAE;MACvCsB,cAAc,EAAEA;IAClB,CAAC,CAAC,GAAGtB,GAAG,EAAEF,QAAQ,EAAEuB,GAAG,GAAG,CAAC,GAAGjB,IAAI,CAACoB,IAAI,GAAG,CAAC,EAAErB,gBAAgB,CAAC;IAC9DoB,aAAa,IAAI,IAAI,IAAIjB,iBAAiB,CAACmB,IAAI,CAACF,aAAa,CAAC;EAChE,CAAC,CAAC;EACF,IAAIA,aAAa,GAAGvB,GAAG,CAAChC,UAAU,KAAK,UAAU,GAAGsC,iBAAiB,CAACoB,IAAI,CAACtB,IAAI,CAACuB,QAAQ,CAAC,GAAGC,aAAa,CAACtB,iBAAiB,CAACoB,IAAI,CAAC,EAAE,CAAC,EAAE7B,QAAQ,GAAGK,oBAAoB,GAAGE,IAAI,CAACoB,IAAI,CAAC;EAClL,IAAI3B,QAAQ,EAAE;IACZ,OAAO0B,aAAa;EACtB;EACA,IAAIM,iBAAiB,GAAG3E,iBAAiB,CAAC+C,QAAQ,CAAC6B,MAAM,EAAE,SAAS,EAAE9B,GAAG,CAAC+B,MAAM,CAAC;EACjF,IAAIrD,SAAS,GAAGZ,mBAAmB,CAACqC,gBAAgB,EAAEH,GAAG,CAAChC,UAAU,CAAC,CAACU,SAAS;EAC/E,IAAIsB,GAAG,CAAChC,UAAU,KAAK,UAAU,EAAE;IACjC,OAAOgE,sBAAsB,CAAChC,GAAG,EAAE6B,iBAAiB,EAAEnD,SAAS,CAAC,GAAG0B,IAAI,CAACuB,QAAQ,GAAGJ,aAAa;EAClG,CAAC,MAAM;IACL,OAAOK,aAAa,CAAC,eAAe,GAAGlD,SAAS,GAAG,GAAG,GAAGb,uBAAuB,GAAG,MAAM,GAAGZ,UAAU,CAAC4E,iBAAiB,CAAC,GAAG,QAAQ,GAAGN,aAAa,EAAErB,oBAAoB,CAAC;EAC7K;AACF;AACA,SAASZ,cAAcA,CAACU,GAAG,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAE;EAC7E,IAAInC,UAAU,GAAGgC,GAAG,CAAChC,UAAU;EAC/B,IAAIiE,MAAM,GAAGhC,QAAQ,CAACgC,MAAM;EAC5B,IAAIC,OAAO,GAAGjC,QAAQ,CAACiC,OAAO;EAC9B,IAAIC,QAAQ,GAAG,CAAClC,QAAQ,CAACmC,UAAU;EACnC,IAAIC,IAAI,GAAGpC,QAAQ,CAACoC,IAAI;EACxB,IAAIN,MAAM,GAAG/B,GAAG,CAAC+B,MAAM;EACvB,IAAIT,cAAc,GAAGrB,QAAQ,CAACqB,cAAc,IAAItB,GAAG,CAACsB,cAAc,IAAI,UAAUgB,KAAK,EAAE;IACrFA,KAAK,GAAG/E,OAAO,CAAC+E,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;IACxC,OAAO9E,GAAG,CAAC8E,KAAK,EAAE,UAAUC,GAAG,EAAElB,GAAG,EAAE;MACpC,OAAOnE,iBAAiB,CAACqF,GAAG,EAAEhF,OAAO,CAACiF,eAAe,CAAC,GAAGA,eAAe,CAACnB,GAAG,CAAC,GAAGmB,eAAe,EAAET,MAAM,CAAC;IAC1G,CAAC,CAAC;EACJ,CAAC;EACD,IAAIE,MAAM,IAAIC,OAAO,EAAE;IACrB;EACF;EACA,IAAIO,SAAS,GAAGN,QAAQ,GAAG,EAAE,GAAGnC,GAAG,CAAC0C,kBAAkB,CAACC,iBAAiB,CAAC1C,QAAQ,CAACmC,UAAU,EAAEnC,QAAQ,CAAC2C,WAAW,IAAI,MAAM,EAAE5E,UAAU,CAAC;EACzI,IAAI6E,YAAY,GAAGZ,MAAM,GAAG,EAAE,GAAG/E,iBAAiB,CAACmF,IAAI,EAAE,SAAS,EAAEN,MAAM,CAAC;EAC3E,IAAIS,eAAe,GAAGvC,QAAQ,CAAC6C,SAAS;EACxC,IAAIC,iBAAiB,GAAGb,OAAO,GAAG,EAAE,GAAGZ,cAAc,CAACrB,QAAQ,CAACqC,KAAK,EAAErC,QAAQ,CAAC+C,SAAS,CAAC;EACzF,IAAIC,eAAe,GAAG,CAACd,QAAQ,IAAI,CAACF,MAAM;EAC1C;EACA,IAAIiB,kBAAkB,GAAG,CAACf,QAAQ,IAAIF,MAAM;EAC5C,IAAIkB,EAAE,GAAGrF,mBAAmB,CAACqC,gBAAgB,EAAEnC,UAAU,CAAC;IACxDU,SAAS,GAAGyE,EAAE,CAACzE,SAAS;IACxBC,UAAU,GAAGwE,EAAE,CAACxE,UAAU;EAC5B,OAAOX,UAAU,KAAK,UAAU,GAAG,CAACmE,QAAQ,GAAG,EAAE,GAAGM,SAAS,KAAKR,MAAM,GAAG,EAAE,GAAGD,sBAAsB,CAAChC,GAAG,EAAE6C,YAAY,EAAEnE,SAAS,CAAC;EACpI;EAAA,GACGwD,OAAO,GAAG,EAAE,GAAGkB,uBAAuB,CAACpD,GAAG,EAAE+C,iBAAiB,EAAEE,eAAe,EAAEC,kBAAkB,EAAEvE,UAAU,CAAC,CAAC,GAAGiD,aAAa,CAAC,CAACO,QAAQ,GAAG,EAAE,GAAGM,SAAS,KAAKR,MAAM,GAAG,EAAE,GAAGoB,kBAAkB,CAACR,YAAY,EAAE,CAACV,QAAQ,EAAEzD,SAAS,CAAC,CAAC,IAAIwD,OAAO,GAAG,EAAE,GAAGoB,mBAAmB,CAACP,iBAAiB,EAAEE,eAAe,EAAEC,kBAAkB,EAAEvE,UAAU,CAAC,CAAC,EAAEuB,oBAAoB,CAAC;AAC5W;AACA;AACA;AACA;AACA,OAAO,SAASqD,kBAAkBA,CAACtD,QAAQ,EAAEyC,kBAAkB,EAAE1E,UAAU,EAAEwC,SAAS,EAAEuB,MAAM,EAAE5B,gBAAgB,EAAE;EAChH,IAAI,CAACF,QAAQ,EAAE;IACb;EACF;EACA,IAAIuD,OAAO,GAAGpE,UAAU,CAACa,QAAQ,CAAC;EAClC,IAAID,GAAG,GAAG;IACR+B,MAAM,EAAEA,MAAM;IACd/D,UAAU,EAAEA,UAAU;IACtBwC,SAAS,EAAEA,SAAS;IACpBkC,kBAAkB,EAAEA,kBAAkB;IACtCpB,cAAc,EAAErB,QAAQ,CAACqB;EAC3B,CAAC;EACD,OAAOkC,OAAO,CAACxD,GAAG,EAAEC,QAAQ,EAAE,CAAC,EAAEE,gBAAgB,CAAC;AACpD;AACA,SAASE,MAAMA,CAACoD,QAAQ,EAAE;EACxB,OAAO;IACLjC,IAAI,EAAE3C,SAAS,CAAC4E,QAAQ,CAAC;IACzB9B,QAAQ,EAAE7C,cAAc,CAAC2E,QAAQ;EACnC,CAAC;AACH;AACA,SAAS7B,aAAaA,CAAC8B,cAAc,EAAEC,MAAM,EAAE;EAC7C,IAAIC,QAAQ,GAAG,gCAAgC;EAC/C,IAAIC,SAAS,GAAG,UAAU,GAAGF,MAAM,GAAG,QAAQ;EAC9C,OAAO,eAAe,GAAGE,SAAS,GAAG,GAAG,GAAGhG,uBAAuB,GAAG,MAAM,GAAG6F,cAAc,GAAGE,QAAQ,GAAG,QAAQ;AACpH;AACA,SAASP,kBAAkBA,CAAChB,IAAI,EAAEyB,aAAa,EAAEC,KAAK,EAAE;EACtD,IAAIC,SAAS,GAAGF,aAAa,GAAG,iBAAiB,GAAG,EAAE;EACtD,OAAO,gBAAgB,GAAGC,KAAK,GAAG,GAAG,GAAGC,SAAS,GAAG,KAAK,GAAG/G,UAAU,CAACoF,IAAI,CAAC,GAAG,SAAS;AAC1F;AACA,SAASiB,mBAAmBA,CAACW,SAAS,EAAEC,UAAU,EAAEhB,kBAAkB,EAAEa,KAAK,EAAE;EAC7E;EACA,IAAII,UAAU,GAAGjB,kBAAkB,GAAG,MAAM,GAAG,MAAM;EACrD,IAAIkB,QAAQ,GAAGF,UAAU,GAAG,0BAA0B,GAAGC,UAAU,GAAG,EAAE;EACxEF,SAAS,GAAG1G,OAAO,CAAC0G,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;EACxD,OAAO,gBAAgB,GAAGG,QAAQ,GAAG,GAAG,GAAGL,KAAK,GAAG;EACnD;EAAA,EACEvG,GAAG,CAACyG,SAAS,EAAE,UAAU3B,KAAK,EAAE;IAChC,OAAOrF,UAAU,CAACqF,KAAK,CAAC;EAC1B,CAAC,CAAC,CAACZ,IAAI,CAAC,cAAc,CAAC,GAAG,SAAS;AACrC;AACA,SAASM,sBAAsBA,CAAChC,GAAG,EAAEqC,IAAI,EAAE0B,KAAK,EAAE;EAChD,OAAO/D,GAAG,CAAC0C,kBAAkB,CAAC2B,iBAAiB,CAAChC,IAAI,EAAE0B,KAAK,CAAC;AAC9D;AACA,SAASX,uBAAuBA,CAACpD,GAAG,EAAEsE,MAAM,EAAEJ,UAAU,EAAEhB,kBAAkB,EAAEa,KAAK,EAAE;EACnF,IAAIQ,MAAM,GAAG,CAACR,KAAK,CAAC;EACpB,IAAIS,WAAW,GAAGtB,kBAAkB,GAAG,EAAE,GAAG,EAAE;EAC9CgB,UAAU,IAAIK,MAAM,CAAC9C,IAAI,CAAC;IACxBgD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,WAAW,CAAC;IAC/BE,KAAK,EAAE;EACT,CAAC,CAAC;EACF;EACA,OAAO1E,GAAG,CAAC0C,kBAAkB,CAAC2B,iBAAiB,CAAC9G,OAAO,CAAC+G,MAAM,CAAC,GAAGA,MAAM,CAAC5C,IAAI,CAAC,IAAI,CAAC,GAAG4C,MAAM,EAAEC,MAAM,CAAC;AACvG;AACA,OAAO,SAASI,mCAAmCA,CAACC,MAAM,EAAE5B,SAAS,EAAE;EACrE,IAAIe,KAAK,GAAGa,MAAM,CAACC,OAAO,CAAC,CAAC,CAACC,aAAa,CAAC9B,SAAS,EAAE,OAAO,CAAC;EAC9D,IAAI9E,KAAK,GAAG6F,KAAK,CAACa,MAAM,CAACG,cAAc,CAAC;EACxC,OAAO5H,oBAAoB,CAACe,KAAK,CAAC;AACpC;AACA,OAAO,SAAS8G,0BAA0BA,CAACC,KAAK,EAAEjH,UAAU,EAAE;EAC5D,IAAIyG,OAAO,GAAGQ,KAAK,CAACC,GAAG,CAAC,SAAS,CAAC;EAClC,OAAOT,OAAO,IAAI,IAAI,GAAGA;EACzB;EAAA,EACEzG,UAAU,KAAK,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,IAAImH,yBAAyB,GAAG,aAAa,YAAY;EACvD,SAASA,yBAAyBA,CAAA,EAAG;IACnC,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAACC,gBAAgB,GAAGzH,eAAe,CAAC,CAAC;EAC3C;EACAuH,yBAAyB,CAACG,SAAS,CAACC,kBAAkB,GAAG,YAAY;IACnE,OAAO,YAAY,GAAG,IAAI,CAACF,gBAAgB,EAAE;EAC/C,CAAC;EACDF,yBAAyB,CAACG,SAAS,CAAC3C,iBAAiB,GAAG,UAAUP,UAAU,EAAEoD,QAAQ,EAAExH,UAAU,EAAE;IAClG,IAAIyH,QAAQ,GAAGzH,UAAU,KAAK,UAAU,GAAG,IAAI,CAACuH,kBAAkB,CAAC,CAAC,GAAG,IAAI;IAC3E,IAAIG,MAAM,GAAG1I,gBAAgB,CAAC;MAC5BkB,KAAK,EAAEsH,QAAQ;MACfxG,IAAI,EAAEoD,UAAU;MAChBpE,UAAU,EAAEA,UAAU;MACtByH,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF,IAAIrI,QAAQ,CAACsI,MAAM,CAAC,EAAE;MACpB,OAAOA,MAAM;IACf,CAAC,MAAM;MACL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCpI,MAAM,CAACgI,QAAQ,CAAC;MAClB;MACA,IAAI,CAACL,cAAc,CAACK,QAAQ,CAAC,GAAGC,MAAM,CAAC3B,KAAK;MAC5C,OAAO2B,MAAM,CAACI,OAAO;IACvB;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEX,yBAAyB,CAACG,SAAS,CAACjB,iBAAiB,GAAG,UAAU0B,IAAI,EAAExB,MAAM,EAAE;IAC9E,IAAIyB,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIzI,OAAO,CAACgH,MAAM,CAAC,EAAE;MACnBlH,IAAI,CAACkH,MAAM,EAAE,UAAU0B,GAAG,EAAE;QAC1B,OAAOvI,MAAM,CAACsI,QAAQ,EAAEC,GAAG,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLvI,MAAM,CAACsI,QAAQ,EAAEzB,MAAM,CAAC;IAC1B;IACA,IAAI2B,SAAS,GAAG,IAAI,CAACX,kBAAkB,CAAC,CAAC;IACzC,IAAI,CAACH,cAAc,CAACc,SAAS,CAAC,GAAGF,QAAQ;IACzC,OAAO,GAAG,GAAGE,SAAS,GAAG,GAAG,GAAGH,IAAI,GAAG,GAAG;EAC3C,CAAC;EACD,OAAOZ,yBAAyB;AAClC,CAAC,CAAC,CAAC;AACH,SAASA,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}