{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.array.for-each.js\");\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.array.splice.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nexports.__esModule = true;\nvar _vue = require('vue');\nvar _vue2 = _interopRequireDefault(_vue);\nvar _dom = require('element-ui/lib/utils/dom');\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar nodeList = [];\nvar ctx = '@@clickoutsideContext';\nvar startClick = void 0;\nvar seed = 0;\n!_vue2[\"default\"].prototype.$isServer && (0, _dom.on)(document, 'mousedown', function (e) {\n  return startClick = e;\n});\n!_vue2[\"default\"].prototype.$isServer && (0, _dom.on)(document, 'mouseup', function (e) {\n  nodeList.forEach(function (node) {\n    return node[ctx].documentHandler(e, startClick);\n  });\n});\nfunction createDocumentHandler(el, binding, vnode) {\n  return function () {\n    var mouseup = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var mousedown = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (!vnode || !vnode.context || !mouseup.target || !mousedown.target || el.contains(mouseup.target) || el.contains(mousedown.target) || el === mouseup.target || vnode.context.popperElm && (vnode.context.popperElm.contains(mouseup.target) || vnode.context.popperElm.contains(mousedown.target))) return;\n    if (binding.expression && el[ctx].methodName && vnode.context[el[ctx].methodName]) {\n      vnode.context[el[ctx].methodName]();\n    } else {\n      el[ctx].bindingFn && el[ctx].bindingFn();\n    }\n  };\n}\n\n/**\n * v-clickoutside\n * @desc 点击元素外面才会触发的事件\n * @example\n * ```vue\n * <div v-element-clickoutside=\"handleClose\">\n * ```\n */\nexports[\"default\"] = {\n  bind: function bind(el, binding, vnode) {\n    nodeList.push(el);\n    var id = seed++;\n    el[ctx] = {\n      id: id,\n      documentHandler: createDocumentHandler(el, binding, vnode),\n      methodName: binding.expression,\n      bindingFn: binding.value\n    };\n  },\n  update: function update(el, binding, vnode) {\n    el[ctx].documentHandler = createDocumentHandler(el, binding, vnode);\n    el[ctx].methodName = binding.expression;\n    el[ctx].bindingFn = binding.value;\n  },\n  unbind: function unbind(el) {\n    var len = nodeList.length;\n    for (var i = 0; i < len; i++) {\n      if (nodeList[i][ctx].id === el[ctx].id) {\n        nodeList.splice(i, 1);\n        break;\n      }\n    }\n    delete el[ctx];\n  }\n};", "map": {"version": 3, "names": ["require", "exports", "__esModule", "_vue", "_vue2", "_interopRequireDefault", "_dom", "obj", "nodeList", "ctx", "startClick", "seed", "prototype", "$isServer", "on", "document", "e", "for<PERSON>ach", "node", "documentHandler", "createDocumentHandler", "el", "binding", "vnode", "mouseup", "arguments", "length", "undefined", "mousedown", "context", "target", "contains", "<PERSON><PERSON><PERSON><PERSON>", "expression", "methodName", "bindingFn", "bind", "push", "id", "value", "update", "unbind", "len", "i", "splice"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/element-ui/lib/utils/clickoutside.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _vue = require('vue');\n\nvar _vue2 = _interopRequireDefault(_vue);\n\nvar _dom = require('element-ui/lib/utils/dom');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar nodeList = [];\nvar ctx = '@@clickoutsideContext';\n\nvar startClick = void 0;\nvar seed = 0;\n\n!_vue2.default.prototype.$isServer && (0, _dom.on)(document, 'mousedown', function (e) {\n  return startClick = e;\n});\n\n!_vue2.default.prototype.$isServer && (0, _dom.on)(document, 'mouseup', function (e) {\n  nodeList.forEach(function (node) {\n    return node[ctx].documentHandler(e, startClick);\n  });\n});\n\nfunction createDocumentHandler(el, binding, vnode) {\n  return function () {\n    var mouseup = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var mousedown = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    if (!vnode || !vnode.context || !mouseup.target || !mousedown.target || el.contains(mouseup.target) || el.contains(mousedown.target) || el === mouseup.target || vnode.context.popperElm && (vnode.context.popperElm.contains(mouseup.target) || vnode.context.popperElm.contains(mousedown.target))) return;\n\n    if (binding.expression && el[ctx].methodName && vnode.context[el[ctx].methodName]) {\n      vnode.context[el[ctx].methodName]();\n    } else {\n      el[ctx].bindingFn && el[ctx].bindingFn();\n    }\n  };\n}\n\n/**\n * v-clickoutside\n * @desc 点击元素外面才会触发的事件\n * @example\n * ```vue\n * <div v-element-clickoutside=\"handleClose\">\n * ```\n */\nexports.default = {\n  bind: function bind(el, binding, vnode) {\n    nodeList.push(el);\n    var id = seed++;\n    el[ctx] = {\n      id: id,\n      documentHandler: createDocumentHandler(el, binding, vnode),\n      methodName: binding.expression,\n      bindingFn: binding.value\n    };\n  },\n  update: function update(el, binding, vnode) {\n    el[ctx].documentHandler = createDocumentHandler(el, binding, vnode);\n    el[ctx].methodName = binding.expression;\n    el[ctx].bindingFn = binding.value;\n  },\n  unbind: function unbind(el) {\n    var len = nodeList.length;\n\n    for (var i = 0; i < len; i++) {\n      if (nodeList[i][ctx].id === el[ctx].id) {\n        nodeList.splice(i, 1);\n        break;\n      }\n    }\n    delete el[ctx];\n  }\n};"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,IAAI,GAAGH,OAAO,CAAC,KAAK,CAAC;AAEzB,IAAII,KAAK,GAAGC,sBAAsB,CAACF,IAAI,CAAC;AAExC,IAAIG,IAAI,GAAGN,OAAO,CAAC,0BAA0B,CAAC;AAE9C,SAASK,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACL,UAAU,GAAGK,GAAG,GAAG;IAAE,WAASA;EAAI,CAAC;AAAE;AAE9F,IAAIC,QAAQ,GAAG,EAAE;AACjB,IAAIC,GAAG,GAAG,uBAAuB;AAEjC,IAAIC,UAAU,GAAG,KAAK,CAAC;AACvB,IAAIC,IAAI,GAAG,CAAC;AAEZ,CAACP,KAAK,WAAQ,CAACQ,SAAS,CAACC,SAAS,IAAI,CAAC,CAAC,EAAEP,IAAI,CAACQ,EAAE,EAAEC,QAAQ,EAAE,WAAW,EAAE,UAAUC,CAAC,EAAE;EACrF,OAAON,UAAU,GAAGM,CAAC;AACvB,CAAC,CAAC;AAEF,CAACZ,KAAK,WAAQ,CAACQ,SAAS,CAACC,SAAS,IAAI,CAAC,CAAC,EAAEP,IAAI,CAACQ,EAAE,EAAEC,QAAQ,EAAE,SAAS,EAAE,UAAUC,CAAC,EAAE;EACnFR,QAAQ,CAACS,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC/B,OAAOA,IAAI,CAACT,GAAG,CAAC,CAACU,eAAe,CAACH,CAAC,EAAEN,UAAU,CAAC;EACjD,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,SAASU,qBAAqBA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjD,OAAO,YAAY;IACjB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIG,SAAS,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEtF,IAAI,CAACF,KAAK,IAAI,CAACA,KAAK,CAACM,OAAO,IAAI,CAACL,OAAO,CAACM,MAAM,IAAI,CAACF,SAAS,CAACE,MAAM,IAAIT,EAAE,CAACU,QAAQ,CAACP,OAAO,CAACM,MAAM,CAAC,IAAIT,EAAE,CAACU,QAAQ,CAACH,SAAS,CAACE,MAAM,CAAC,IAAIT,EAAE,KAAKG,OAAO,CAACM,MAAM,IAAIP,KAAK,CAACM,OAAO,CAACG,SAAS,KAAKT,KAAK,CAACM,OAAO,CAACG,SAAS,CAACD,QAAQ,CAACP,OAAO,CAACM,MAAM,CAAC,IAAIP,KAAK,CAACM,OAAO,CAACG,SAAS,CAACD,QAAQ,CAACH,SAAS,CAACE,MAAM,CAAC,CAAC,EAAE;IAEtS,IAAIR,OAAO,CAACW,UAAU,IAAIZ,EAAE,CAACZ,GAAG,CAAC,CAACyB,UAAU,IAAIX,KAAK,CAACM,OAAO,CAACR,EAAE,CAACZ,GAAG,CAAC,CAACyB,UAAU,CAAC,EAAE;MACjFX,KAAK,CAACM,OAAO,CAACR,EAAE,CAACZ,GAAG,CAAC,CAACyB,UAAU,CAAC,CAAC,CAAC;IACrC,CAAC,MAAM;MACLb,EAAE,CAACZ,GAAG,CAAC,CAAC0B,SAAS,IAAId,EAAE,CAACZ,GAAG,CAAC,CAAC0B,SAAS,CAAC,CAAC;IAC1C;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,OAAO,WAAQ,GAAG;EAChBmC,IAAI,EAAE,SAASA,IAAIA,CAACf,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACtCf,QAAQ,CAAC6B,IAAI,CAAChB,EAAE,CAAC;IACjB,IAAIiB,EAAE,GAAG3B,IAAI,EAAE;IACfU,EAAE,CAACZ,GAAG,CAAC,GAAG;MACR6B,EAAE,EAAEA,EAAE;MACNnB,eAAe,EAAEC,qBAAqB,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,CAAC;MAC1DW,UAAU,EAAEZ,OAAO,CAACW,UAAU;MAC9BE,SAAS,EAAEb,OAAO,CAACiB;IACrB,CAAC;EACH,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAACnB,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC1CF,EAAE,CAACZ,GAAG,CAAC,CAACU,eAAe,GAAGC,qBAAqB,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,CAAC;IACnEF,EAAE,CAACZ,GAAG,CAAC,CAACyB,UAAU,GAAGZ,OAAO,CAACW,UAAU;IACvCZ,EAAE,CAACZ,GAAG,CAAC,CAAC0B,SAAS,GAAGb,OAAO,CAACiB,KAAK;EACnC,CAAC;EACDE,MAAM,EAAE,SAASA,MAAMA,CAACpB,EAAE,EAAE;IAC1B,IAAIqB,GAAG,GAAGlC,QAAQ,CAACkB,MAAM;IAEzB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;MAC5B,IAAInC,QAAQ,CAACmC,CAAC,CAAC,CAAClC,GAAG,CAAC,CAAC6B,EAAE,KAAKjB,EAAE,CAACZ,GAAG,CAAC,CAAC6B,EAAE,EAAE;QACtC9B,QAAQ,CAACoC,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;QACrB;MACF;IACF;IACA,OAAOtB,EAAE,CAACZ,GAAG,CAAC;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}