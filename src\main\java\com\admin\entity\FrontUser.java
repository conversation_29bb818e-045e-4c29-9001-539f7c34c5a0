package com.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("front_user")
public class FrontUser {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userNo;
    private String username;
    private String avatar; // 新增头像字段
//    private String password;
    private String email;
    private String realName;
    private String phone;
    private String shareCode;
    private String referrerCode;
    private BigDecimal availableBalance;
    private BigDecimal copyTradeBalance;
    private Integer copyTradeFrozenStatus;
    private BigDecimal commissionBalance;

    private BigDecimal profitBalance;
    private BigDecimal frozenBalance;
    private BigDecimal catBalance;

    private Integer teamTotalCount;
    private Integer teamTodayCount;
    private Integer status;
    private BigDecimal commissionRate;
    private BigDecimal totalRecharge;
    private Integer isActivated;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    // 推荐人信息   
    @TableField(exist = false)
    private String referrerName;
    @TableField(exist = false)
    private String referrerShareCode;
    @TableField(exist = false)
    private String referrerEmail;

    // 跟单相关字段
    private Integer isLeader;
    private Integer isFollowing;
    private LocalDateTime followStartTime;
    private BigDecimal reserveAmount;
    private Long leaderId;
    
    // 跟单员信息
    @TableField(exist = false)
    private String leaderName;
    @TableField(exist = false)
    private String leaderEmail;

    // 利润划转开关
    private Integer profitTransferEnabled;

}
