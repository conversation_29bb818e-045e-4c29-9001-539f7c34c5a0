{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getFont } from '../../label/labelStyle.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nvar PATH_COLOR = ['textStyle', 'color'];\nvar textStyleParams = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'padding', 'lineHeight', 'rich', 'width', 'height', 'overflow'];\n// TODO Performance improvement?\nvar tmpText = new ZRText();\nvar TextStyleMixin = /** @class */function () {\n  function TextStyleMixin() {}\n  /**\n   * Get color property or get color from option.textStyle.color\n   */\n  // TODO Callback\n  TextStyleMixin.prototype.getTextColor = function (isEmphasis) {\n    var ecModel = this.ecModel;\n    return this.getShallow('color') || (!isEmphasis && ecModel ? ecModel.get(PATH_COLOR) : null);\n  };\n  /**\n   * Create font string from fontStyle, fontWeight, fontSize, fontFamily\n   * @return {string}\n   */\n  TextStyleMixin.prototype.getFont = function () {\n    return getFont({\n      fontStyle: this.getShallow('fontStyle'),\n      fontWeight: this.getShallow('fontWeight'),\n      fontSize: this.getShallow('fontSize'),\n      fontFamily: this.getShallow('fontFamily')\n    }, this.ecModel);\n  };\n  TextStyleMixin.prototype.getTextRect = function (text) {\n    var style = {\n      text: text,\n      verticalAlign: this.getShallow('verticalAlign') || this.getShallow('baseline')\n    };\n    for (var i = 0; i < textStyleParams.length; i++) {\n      style[textStyleParams[i]] = this.getShallow(textStyleParams[i]);\n    }\n    tmpText.useStyle(style);\n    tmpText.update();\n    return tmpText.getBoundingRect();\n  };\n  return TextStyleMixin;\n}();\n;\nexport default TextStyleMixin;", "map": {"version": 3, "names": ["getFont", "ZRText", "PATH_COLOR", "textStyleParams", "tmpText", "TextStyleMixin", "prototype", "getTextColor", "isEmphasis", "ecModel", "getShallow", "get", "fontStyle", "fontWeight", "fontSize", "fontFamily", "getTextRect", "text", "style", "verticalAlign", "i", "length", "useStyle", "update", "getBoundingRect"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/echarts/lib/model/mixin/textStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getFont } from '../../label/labelStyle.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nvar PATH_COLOR = ['textStyle', 'color'];\nvar textStyleParams = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'padding', 'lineHeight', 'rich', 'width', 'height', 'overflow'];\n// TODO Performance improvement?\nvar tmpText = new ZRText();\nvar TextStyleMixin = /** @class */function () {\n  function TextStyleMixin() {}\n  /**\n   * Get color property or get color from option.textStyle.color\n   */\n  // TODO Callback\n  TextStyleMixin.prototype.getTextColor = function (isEmphasis) {\n    var ecModel = this.ecModel;\n    return this.getShallow('color') || (!isEmphasis && ecModel ? ecModel.get(PATH_COLOR) : null);\n  };\n  /**\n   * Create font string from fontStyle, fontWeight, fontSize, fontFamily\n   * @return {string}\n   */\n  TextStyleMixin.prototype.getFont = function () {\n    return getFont({\n      fontStyle: this.getShallow('fontStyle'),\n      fontWeight: this.getShallow('fontWeight'),\n      fontSize: this.getShallow('fontSize'),\n      fontFamily: this.getShallow('fontFamily')\n    }, this.ecModel);\n  };\n  TextStyleMixin.prototype.getTextRect = function (text) {\n    var style = {\n      text: text,\n      verticalAlign: this.getShallow('verticalAlign') || this.getShallow('baseline')\n    };\n    for (var i = 0; i < textStyleParams.length; i++) {\n      style[textStyleParams[i]] = this.getShallow(textStyleParams[i]);\n    }\n    tmpText.useStyle(style);\n    tmpText.update();\n    return tmpText.getBoundingRect();\n  };\n  return TextStyleMixin;\n}();\n;\nexport default TextStyleMixin;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,IAAIC,UAAU,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;AACvC,IAAIC,eAAe,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC;AAC3I;AACA,IAAIC,OAAO,GAAG,IAAIH,MAAM,CAAC,CAAC;AAC1B,IAAII,cAAc,GAAG,aAAa,YAAY;EAC5C,SAASA,cAAcA,CAAA,EAAG,CAAC;EAC3B;AACF;AACA;EACE;EACAA,cAAc,CAACC,SAAS,CAACC,YAAY,GAAG,UAAUC,UAAU,EAAE;IAC5D,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,OAAO,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC,KAAK,CAACF,UAAU,IAAIC,OAAO,GAAGA,OAAO,CAACE,GAAG,CAACT,UAAU,CAAC,GAAG,IAAI,CAAC;EAC9F,CAAC;EACD;AACF;AACA;AACA;EACEG,cAAc,CAACC,SAAS,CAACN,OAAO,GAAG,YAAY;IAC7C,OAAOA,OAAO,CAAC;MACbY,SAAS,EAAE,IAAI,CAACF,UAAU,CAAC,WAAW,CAAC;MACvCG,UAAU,EAAE,IAAI,CAACH,UAAU,CAAC,YAAY,CAAC;MACzCI,QAAQ,EAAE,IAAI,CAACJ,UAAU,CAAC,UAAU,CAAC;MACrCK,UAAU,EAAE,IAAI,CAACL,UAAU,CAAC,YAAY;IAC1C,CAAC,EAAE,IAAI,CAACD,OAAO,CAAC;EAClB,CAAC;EACDJ,cAAc,CAACC,SAAS,CAACU,WAAW,GAAG,UAAUC,IAAI,EAAE;IACrD,IAAIC,KAAK,GAAG;MACVD,IAAI,EAAEA,IAAI;MACVE,aAAa,EAAE,IAAI,CAACT,UAAU,CAAC,eAAe,CAAC,IAAI,IAAI,CAACA,UAAU,CAAC,UAAU;IAC/E,CAAC;IACD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,eAAe,CAACkB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/CF,KAAK,CAACf,eAAe,CAACiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAACV,UAAU,CAACP,eAAe,CAACiB,CAAC,CAAC,CAAC;IACjE;IACAhB,OAAO,CAACkB,QAAQ,CAACJ,KAAK,CAAC;IACvBd,OAAO,CAACmB,MAAM,CAAC,CAAC;IAChB,OAAOnB,OAAO,CAACoB,eAAe,CAAC,CAAC;EAClC,CAAC;EACD,OAAOnB,cAAc;AACvB,CAAC,CAAC,CAAC;AACH;AACA,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}