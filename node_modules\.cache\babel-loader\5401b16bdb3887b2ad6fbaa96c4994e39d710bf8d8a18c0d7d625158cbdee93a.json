{"ast": null, "code": "import \"core-js/modules/es.array.for-each.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner } from '../util/model.js';\nimport LabelManager from './LabelManager.js';\nvar getLabelManager = makeInner();\nexport function installLabelLayout(registers) {\n  registers.registerUpdateLifecycle('series:beforeupdate', function (ecModel, api, params) {\n    // TODO api provide an namespace that can save stuff per instance\n    var labelManager = getLabelManager(api).labelManager;\n    if (!labelManager) {\n      labelManager = getLabelManager(api).labelManager = new LabelManager();\n    }\n    labelManager.clearLabels();\n  });\n  registers.registerUpdateLifecycle('series:layoutlabels', function (ecModel, api, params) {\n    var labelManager = getLabelManager(api).labelManager;\n    params.updatedSeries.forEach(function (series) {\n      labelManager.addLabelsOfSeries(api.getViewOfSeriesModel(series));\n    });\n    labelManager.updateLayoutConfig(api);\n    labelManager.layout(api);\n    labelManager.processLabelsOverall();\n  });\n}", "map": {"version": 3, "names": ["makeInner", "LabelManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "installLabelLayout", "registers", "registerUpdateLifecycle", "ecModel", "api", "params", "labelManager", "<PERSON><PERSON><PERSON><PERSON>", "updatedSeries", "for<PERSON>ach", "series", "addLabelsOfSeries", "getViewOfSeriesModel", "updateLayoutConfig", "layout", "processLabelsOverall"], "sources": ["F:/常规项目/华通宝/adminweb/node_modules/echarts/lib/label/installLabelLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner } from '../util/model.js';\nimport LabelManager from './LabelManager.js';\nvar getLabelManager = makeInner();\nexport function installLabelLayout(registers) {\n  registers.registerUpdateLifecycle('series:beforeupdate', function (ecModel, api, params) {\n    // TODO api provide an namespace that can save stuff per instance\n    var labelManager = getLabelManager(api).labelManager;\n    if (!labelManager) {\n      labelManager = getLabelManager(api).labelManager = new LabelManager();\n    }\n    labelManager.clearLabels();\n  });\n  registers.registerUpdateLifecycle('series:layoutlabels', function (ecModel, api, params) {\n    var labelManager = getLabelManager(api).labelManager;\n    params.updatedSeries.forEach(function (series) {\n      labelManager.addLabelsOfSeries(api.getViewOfSeriesModel(series));\n    });\n    labelManager.updateLayoutConfig(api);\n    labelManager.layout(api);\n    labelManager.processLabelsOverall();\n  });\n}"], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,IAAIC,eAAe,GAAGF,SAAS,CAAC,CAAC;AACjC,OAAO,SAASG,kBAAkBA,CAACC,SAAS,EAAE;EAC5CA,SAAS,CAACC,uBAAuB,CAAC,qBAAqB,EAAE,UAAUC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;IACvF;IACA,IAAIC,YAAY,GAAGP,eAAe,CAACK,GAAG,CAAC,CAACE,YAAY;IACpD,IAAI,CAACA,YAAY,EAAE;MACjBA,YAAY,GAAGP,eAAe,CAACK,GAAG,CAAC,CAACE,YAAY,GAAG,IAAIR,YAAY,CAAC,CAAC;IACvE;IACAQ,YAAY,CAACC,WAAW,CAAC,CAAC;EAC5B,CAAC,CAAC;EACFN,SAAS,CAACC,uBAAuB,CAAC,qBAAqB,EAAE,UAAUC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;IACvF,IAAIC,YAAY,GAAGP,eAAe,CAACK,GAAG,CAAC,CAACE,YAAY;IACpDD,MAAM,CAACG,aAAa,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;MAC7CJ,YAAY,CAACK,iBAAiB,CAACP,GAAG,CAACQ,oBAAoB,CAACF,MAAM,CAAC,CAAC;IAClE,CAAC,CAAC;IACFJ,YAAY,CAACO,kBAAkB,CAACT,GAAG,CAAC;IACpCE,YAAY,CAACQ,MAAM,CAACV,GAAG,CAAC;IACxBE,YAAY,CAACS,oBAAoB,CAAC,CAAC;EACrC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}