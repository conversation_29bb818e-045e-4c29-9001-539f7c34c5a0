{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar availableMethods = ['getDom', 'getZr', 'getWidth', 'getHeight', 'getDevicePixelRatio', 'dispatchAction', 'isSSR', 'isDisposed', 'on', 'off', 'getDataURL', 'getConnectedDataURL',\n// 'getModel',\n'getOption',\n// 'getViewOfComponentModel',\n// 'getViewOfSeriesModel',\n'getId', 'updateLabelLayout'];\nvar ExtensionAPI = /** @class */function () {\n  function ExtensionAPI(ecInstance) {\n    zrUtil.each(availableMethods, function (methodName) {\n      this[methodName] = zrUtil.bind(ecInstance[methodName], ecInstance);\n    }, this);\n  }\n  return ExtensionAPI;\n}();\nexport default ExtensionAPI;", "map": {"version": 3, "names": ["zrUtil", "availableMethods", "ExtensionAPI", "ecInstance", "each", "methodName", "bind"], "sources": ["E:/最新项目文件/交易所/adminweb/node_modules/echarts/lib/core/ExtensionAPI.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar availableMethods = ['getDom', 'getZr', 'getWidth', 'getHeight', 'getDevicePixelRatio', 'dispatchAction', 'isSSR', 'isDisposed', 'on', 'off', 'getDataURL', 'getConnectedDataURL',\n// 'getModel',\n'getOption',\n// 'getViewOfComponentModel',\n// 'getViewOfSeriesModel',\n'getId', 'updateLabelLayout'];\nvar ExtensionAPI = /** @class */function () {\n  function ExtensionAPI(ecInstance) {\n    zrUtil.each(availableMethods, function (methodName) {\n      this[methodName] = zrUtil.bind(ecInstance[methodName], ecInstance);\n    }, this);\n  }\n  return ExtensionAPI;\n}();\nexport default ExtensionAPI;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,gBAAgB,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,qBAAqB;AACpL;AACA,WAAW;AACX;AACA;AACA,OAAO,EAAE,mBAAmB,CAAC;AAC7B,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAACC,UAAU,EAAE;IAChCH,MAAM,CAACI,IAAI,CAACH,gBAAgB,EAAE,UAAUI,UAAU,EAAE;MAClD,IAAI,CAACA,UAAU,CAAC,GAAGL,MAAM,CAACM,IAAI,CAACH,UAAU,CAACE,UAAU,CAAC,EAAEF,UAAU,CAAC;IACpE,CAAC,EAAE,IAAI,CAAC;EACV;EACA,OAAOD,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}