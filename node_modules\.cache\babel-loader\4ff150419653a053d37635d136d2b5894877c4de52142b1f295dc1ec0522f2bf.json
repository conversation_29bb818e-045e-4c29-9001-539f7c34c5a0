{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isTypedArray, clone, createHashMap, isArray, isObject, isArrayLike, hasOwn, assert, each, map, isNumber, isString, keys } from 'zrender/lib/core/util.js';\nimport { SOURCE_FORMAT_ORIGINAL, SERIES_LAYOUT_BY_COLUMN, SOURCE_FORMAT_UNKNOWN, SOURCE_FORMAT_KEYED_COLUMNS, SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS, SERIES_LAYOUT_BY_ROW } from '../util/types.js';\nimport { getDataItemValue } from '../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './helper/sourceHelper.js';\n;\n// @inner\nvar SourceImpl = /** @class */function () {\n  function SourceImpl(fields) {\n    this.data = fields.data || (fields.sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS ? {} : []);\n    this.sourceFormat = fields.sourceFormat || SOURCE_FORMAT_UNKNOWN;\n    // Visit config\n    this.seriesLayoutBy = fields.seriesLayoutBy || SERIES_LAYOUT_BY_COLUMN;\n    this.startIndex = fields.startIndex || 0;\n    this.dimensionsDetectedCount = fields.dimensionsDetectedCount;\n    this.metaRawOption = fields.metaRawOption;\n    var dimensionsDefine = this.dimensionsDefine = fields.dimensionsDefine;\n    if (dimensionsDefine) {\n      for (var i = 0; i < dimensionsDefine.length; i++) {\n        var dim = dimensionsDefine[i];\n        if (dim.type == null) {\n          if (guessOrdinal(this, i) === BE_ORDINAL.Must) {\n            dim.type = 'ordinal';\n          }\n        }\n      }\n    }\n  }\n  return SourceImpl;\n}();\nexport function isSourceInstance(val) {\n  return val instanceof SourceImpl;\n}\n/**\n * Create a source from option.\n * NOTE: Created source is immutable. Don't change any properties in it.\n */\nexport function createSource(sourceData, thisMetaRawOption,\n// can be null. If not provided, auto detect it from `sourceData`.\nsourceFormat) {\n  sourceFormat = sourceFormat || detectSourceFormat(sourceData);\n  var seriesLayoutBy = thisMetaRawOption.seriesLayoutBy;\n  var determined = determineSourceDimensions(sourceData, sourceFormat, seriesLayoutBy, thisMetaRawOption.sourceHeader, thisMetaRawOption.dimensions);\n  var source = new SourceImpl({\n    data: sourceData,\n    sourceFormat: sourceFormat,\n    seriesLayoutBy: seriesLayoutBy,\n    dimensionsDefine: determined.dimensionsDefine,\n    startIndex: determined.startIndex,\n    dimensionsDetectedCount: determined.dimensionsDetectedCount,\n    metaRawOption: clone(thisMetaRawOption)\n  });\n  return source;\n}\n/**\n * Wrap original series data for some compatibility cases.\n */\nexport function createSourceFromSeriesDataOption(data) {\n  return new SourceImpl({\n    data: data,\n    sourceFormat: isTypedArray(data) ? SOURCE_FORMAT_TYPED_ARRAY : SOURCE_FORMAT_ORIGINAL\n  });\n}\n/**\n * Clone source but excludes source data.\n */\nexport function cloneSourceShallow(source) {\n  return new SourceImpl({\n    data: source.data,\n    sourceFormat: source.sourceFormat,\n    seriesLayoutBy: source.seriesLayoutBy,\n    dimensionsDefine: clone(source.dimensionsDefine),\n    startIndex: source.startIndex,\n    dimensionsDetectedCount: source.dimensionsDetectedCount\n  });\n}\n/**\n * Note: An empty array will be detected as `SOURCE_FORMAT_ARRAY_ROWS`.\n */\nexport function detectSourceFormat(data) {\n  var sourceFormat = SOURCE_FORMAT_UNKNOWN;\n  if (isTypedArray(data)) {\n    sourceFormat = SOURCE_FORMAT_TYPED_ARRAY;\n  } else if (isArray(data)) {\n    // FIXME Whether tolerate null in top level array?\n    if (data.length === 0) {\n      sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n    }\n    for (var i = 0, len = data.length; i < len; i++) {\n      var item = data[i];\n      if (item == null) {\n        continue;\n      } else if (isArray(item) || isTypedArray(item)) {\n        sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n        break;\n      } else if (isObject(item)) {\n        sourceFormat = SOURCE_FORMAT_OBJECT_ROWS;\n        break;\n      }\n    }\n  } else if (isObject(data)) {\n    for (var key in data) {\n      if (hasOwn(data, key) && isArrayLike(data[key])) {\n        sourceFormat = SOURCE_FORMAT_KEYED_COLUMNS;\n        break;\n      }\n    }\n  }\n  return sourceFormat;\n}\n/**\n * Determine the source definitions from data standalone dimensions definitions\n * are not specified.\n */\nfunction determineSourceDimensions(data, sourceFormat, seriesLayoutBy, sourceHeader,\n// standalone raw dimensions definition, like:\n// {\n//     dimensions: ['aa', 'bb', { name: 'cc', type: 'time' }]\n// }\n// in `dataset` or `series`\ndimensionsDefine) {\n  var dimensionsDetectedCount;\n  var startIndex;\n  // PENDING: Could data be null/undefined here?\n  // currently, if `dataset.source` not specified, error thrown.\n  // if `series.data` not specified, nothing rendered without error thrown.\n  // Should test these cases.\n  if (!data) {\n    return {\n      dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n      startIndex: startIndex,\n      dimensionsDetectedCount: dimensionsDetectedCount\n    };\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var dataArrayRows = data;\n    // Rule: Most of the first line are string: it is header.\n    // Caution: consider a line with 5 string and 1 number,\n    // it still can not be sure it is a head, because the\n    // 5 string may be 5 values of category columns.\n    if (sourceHeader === 'auto' || sourceHeader == null) {\n      arrayRowsTravelFirst(function (val) {\n        // '-' is regarded as null/undefined.\n        if (val != null && val !== '-') {\n          if (isString(val)) {\n            startIndex == null && (startIndex = 1);\n          } else {\n            startIndex = 0;\n          }\n        }\n        // 10 is an experience number, avoid long loop.\n      }, seriesLayoutBy, dataArrayRows, 10);\n    } else {\n      startIndex = isNumber(sourceHeader) ? sourceHeader : sourceHeader ? 1 : 0;\n    }\n    if (!dimensionsDefine && startIndex === 1) {\n      dimensionsDefine = [];\n      arrayRowsTravelFirst(function (val, index) {\n        dimensionsDefine[index] = val != null ? val + '' : '';\n      }, seriesLayoutBy, dataArrayRows, Infinity);\n    }\n    dimensionsDetectedCount = dimensionsDefine ? dimensionsDefine.length : seriesLayoutBy === SERIES_LAYOUT_BY_ROW ? dataArrayRows.length : dataArrayRows[0] ? dataArrayRows[0].length : null;\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = objectRowsCollectDimensions(data);\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = [];\n      each(data, function (colArr, key) {\n        dimensionsDefine.push(key);\n      });\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var value0 = getDataItemValue(data[0]);\n    dimensionsDetectedCount = isArray(value0) && value0.length || 1;\n  } else if (sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!!dimensionsDefine, 'dimensions must be given if data is TypedArray.');\n    }\n  }\n  return {\n    startIndex: startIndex,\n    dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n    dimensionsDetectedCount: dimensionsDetectedCount\n  };\n}\nfunction objectRowsCollectDimensions(data) {\n  var firstIndex = 0;\n  var obj;\n  while (firstIndex < data.length && !(obj = data[firstIndex++])) {} // jshint ignore: line\n  if (obj) {\n    return keys(obj);\n  }\n}\n// Consider dimensions defined like ['A', 'price', 'B', 'price', 'C', 'price'],\n// which is reasonable. But dimension name is duplicated.\n// Returns undefined or an array contains only object without null/undefined or string.\nfunction normalizeDimensionsOption(dimensionsDefine) {\n  if (!dimensionsDefine) {\n    // The meaning of null/undefined is different from empty array.\n    return;\n  }\n  var nameMap = createHashMap();\n  return map(dimensionsDefine, function (rawItem, index) {\n    rawItem = isObject(rawItem) ? rawItem : {\n      name: rawItem\n    };\n    // Other fields will be discarded.\n    var item = {\n      name: rawItem.name,\n      displayName: rawItem.displayName,\n      type: rawItem.type\n    };\n    // User can set null in dimensions.\n    // We don't auto specify name, otherwise a given name may\n    // cause it to be referred unexpectedly.\n    if (item.name == null) {\n      return item;\n    }\n    // Also consider number form like 2012.\n    item.name += '';\n    // User may also specify displayName.\n    // displayName will always exists except user not\n    // specified or dim name is not specified or detected.\n    // (A auto generated dim name will not be used as\n    // displayName).\n    if (item.displayName == null) {\n      item.displayName = item.name;\n    }\n    var exist = nameMap.get(item.name);\n    if (!exist) {\n      nameMap.set(item.name, {\n        count: 1\n      });\n    } else {\n      item.name += '-' + exist.count++;\n    }\n    return item;\n  });\n}\nfunction arrayRowsTravelFirst(cb, seriesLayoutBy, data, maxLoop) {\n  if (seriesLayoutBy === SERIES_LAYOUT_BY_ROW) {\n    for (var i = 0; i < data.length && i < maxLoop; i++) {\n      cb(data[i] ? data[i][0] : null, i);\n    }\n  } else {\n    var value0 = data[0] || [];\n    for (var i = 0; i < value0.length && i < maxLoop; i++) {\n      cb(value0[i], i);\n    }\n  }\n}\nexport function shouldRetrieveDataByName(source) {\n  var sourceFormat = source.sourceFormat;\n  return sourceFormat === SOURCE_FORMAT_OBJECT_ROWS || sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS;\n}", "map": {"version": 3, "names": ["isTypedArray", "clone", "createHashMap", "isArray", "isObject", "isArrayLike", "hasOwn", "assert", "each", "map", "isNumber", "isString", "keys", "SOURCE_FORMAT_ORIGINAL", "SERIES_LAYOUT_BY_COLUMN", "SOURCE_FORMAT_UNKNOWN", "SOURCE_FORMAT_KEYED_COLUMNS", "SOURCE_FORMAT_TYPED_ARRAY", "SOURCE_FORMAT_ARRAY_ROWS", "SOURCE_FORMAT_OBJECT_ROWS", "SERIES_LAYOUT_BY_ROW", "getDataItemValue", "BE_ORDINAL", "guessOrdinal", "SourceImpl", "fields", "data", "sourceFormat", "seriesLayoutBy", "startIndex", "dimensionsDetectedCount", "metaRawOption", "dimensionsDefine", "i", "length", "dim", "type", "Must", "isSourceInstance", "val", "createSource", "sourceData", "thisMetaRawOption", "detectSourceFormat", "determined", "determineSourceDimensions", "sourceHeader", "dimensions", "source", "createSourceFromSeriesDataOption", "cloneSourceShallow", "len", "item", "key", "normalizeDimensionsOption", "dataArrayRows", "arrayRowsTravelFirst", "index", "Infinity", "objectRowsCollectDimensions", "col<PERSON>rr", "push", "value0", "process", "env", "NODE_ENV", "firstIndex", "obj", "nameMap", "rawItem", "name", "displayName", "exist", "get", "set", "count", "cb", "maxL<PERSON>", "shouldRetrieveDataByName"], "sources": ["E:/新项目/整理6/adminweb/node_modules/echarts/lib/data/Source.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isTypedArray, clone, createHashMap, isArray, isObject, isArrayLike, hasOwn, assert, each, map, isNumber, isString, keys } from 'zrender/lib/core/util.js';\nimport { SOURCE_FORMAT_ORIGINAL, SERIES_LAYOUT_BY_COLUMN, SOURCE_FORMAT_UNKNOWN, SOURCE_FORMAT_KEYED_COLUMNS, SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS, SERIES_LAYOUT_BY_ROW } from '../util/types.js';\nimport { getDataItemValue } from '../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './helper/sourceHelper.js';\n;\n// @inner\nvar SourceImpl = /** @class */function () {\n  function SourceImpl(fields) {\n    this.data = fields.data || (fields.sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS ? {} : []);\n    this.sourceFormat = fields.sourceFormat || SOURCE_FORMAT_UNKNOWN;\n    // Visit config\n    this.seriesLayoutBy = fields.seriesLayoutBy || SERIES_LAYOUT_BY_COLUMN;\n    this.startIndex = fields.startIndex || 0;\n    this.dimensionsDetectedCount = fields.dimensionsDetectedCount;\n    this.metaRawOption = fields.metaRawOption;\n    var dimensionsDefine = this.dimensionsDefine = fields.dimensionsDefine;\n    if (dimensionsDefine) {\n      for (var i = 0; i < dimensionsDefine.length; i++) {\n        var dim = dimensionsDefine[i];\n        if (dim.type == null) {\n          if (guessOrdinal(this, i) === BE_ORDINAL.Must) {\n            dim.type = 'ordinal';\n          }\n        }\n      }\n    }\n  }\n  return SourceImpl;\n}();\nexport function isSourceInstance(val) {\n  return val instanceof SourceImpl;\n}\n/**\n * Create a source from option.\n * NOTE: Created source is immutable. Don't change any properties in it.\n */\nexport function createSource(sourceData, thisMetaRawOption,\n// can be null. If not provided, auto detect it from `sourceData`.\nsourceFormat) {\n  sourceFormat = sourceFormat || detectSourceFormat(sourceData);\n  var seriesLayoutBy = thisMetaRawOption.seriesLayoutBy;\n  var determined = determineSourceDimensions(sourceData, sourceFormat, seriesLayoutBy, thisMetaRawOption.sourceHeader, thisMetaRawOption.dimensions);\n  var source = new SourceImpl({\n    data: sourceData,\n    sourceFormat: sourceFormat,\n    seriesLayoutBy: seriesLayoutBy,\n    dimensionsDefine: determined.dimensionsDefine,\n    startIndex: determined.startIndex,\n    dimensionsDetectedCount: determined.dimensionsDetectedCount,\n    metaRawOption: clone(thisMetaRawOption)\n  });\n  return source;\n}\n/**\n * Wrap original series data for some compatibility cases.\n */\nexport function createSourceFromSeriesDataOption(data) {\n  return new SourceImpl({\n    data: data,\n    sourceFormat: isTypedArray(data) ? SOURCE_FORMAT_TYPED_ARRAY : SOURCE_FORMAT_ORIGINAL\n  });\n}\n/**\n * Clone source but excludes source data.\n */\nexport function cloneSourceShallow(source) {\n  return new SourceImpl({\n    data: source.data,\n    sourceFormat: source.sourceFormat,\n    seriesLayoutBy: source.seriesLayoutBy,\n    dimensionsDefine: clone(source.dimensionsDefine),\n    startIndex: source.startIndex,\n    dimensionsDetectedCount: source.dimensionsDetectedCount\n  });\n}\n/**\n * Note: An empty array will be detected as `SOURCE_FORMAT_ARRAY_ROWS`.\n */\nexport function detectSourceFormat(data) {\n  var sourceFormat = SOURCE_FORMAT_UNKNOWN;\n  if (isTypedArray(data)) {\n    sourceFormat = SOURCE_FORMAT_TYPED_ARRAY;\n  } else if (isArray(data)) {\n    // FIXME Whether tolerate null in top level array?\n    if (data.length === 0) {\n      sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n    }\n    for (var i = 0, len = data.length; i < len; i++) {\n      var item = data[i];\n      if (item == null) {\n        continue;\n      } else if (isArray(item) || isTypedArray(item)) {\n        sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n        break;\n      } else if (isObject(item)) {\n        sourceFormat = SOURCE_FORMAT_OBJECT_ROWS;\n        break;\n      }\n    }\n  } else if (isObject(data)) {\n    for (var key in data) {\n      if (hasOwn(data, key) && isArrayLike(data[key])) {\n        sourceFormat = SOURCE_FORMAT_KEYED_COLUMNS;\n        break;\n      }\n    }\n  }\n  return sourceFormat;\n}\n/**\n * Determine the source definitions from data standalone dimensions definitions\n * are not specified.\n */\nfunction determineSourceDimensions(data, sourceFormat, seriesLayoutBy, sourceHeader,\n// standalone raw dimensions definition, like:\n// {\n//     dimensions: ['aa', 'bb', { name: 'cc', type: 'time' }]\n// }\n// in `dataset` or `series`\ndimensionsDefine) {\n  var dimensionsDetectedCount;\n  var startIndex;\n  // PENDING: Could data be null/undefined here?\n  // currently, if `dataset.source` not specified, error thrown.\n  // if `series.data` not specified, nothing rendered without error thrown.\n  // Should test these cases.\n  if (!data) {\n    return {\n      dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n      startIndex: startIndex,\n      dimensionsDetectedCount: dimensionsDetectedCount\n    };\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var dataArrayRows = data;\n    // Rule: Most of the first line are string: it is header.\n    // Caution: consider a line with 5 string and 1 number,\n    // it still can not be sure it is a head, because the\n    // 5 string may be 5 values of category columns.\n    if (sourceHeader === 'auto' || sourceHeader == null) {\n      arrayRowsTravelFirst(function (val) {\n        // '-' is regarded as null/undefined.\n        if (val != null && val !== '-') {\n          if (isString(val)) {\n            startIndex == null && (startIndex = 1);\n          } else {\n            startIndex = 0;\n          }\n        }\n        // 10 is an experience number, avoid long loop.\n      }, seriesLayoutBy, dataArrayRows, 10);\n    } else {\n      startIndex = isNumber(sourceHeader) ? sourceHeader : sourceHeader ? 1 : 0;\n    }\n    if (!dimensionsDefine && startIndex === 1) {\n      dimensionsDefine = [];\n      arrayRowsTravelFirst(function (val, index) {\n        dimensionsDefine[index] = val != null ? val + '' : '';\n      }, seriesLayoutBy, dataArrayRows, Infinity);\n    }\n    dimensionsDetectedCount = dimensionsDefine ? dimensionsDefine.length : seriesLayoutBy === SERIES_LAYOUT_BY_ROW ? dataArrayRows.length : dataArrayRows[0] ? dataArrayRows[0].length : null;\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = objectRowsCollectDimensions(data);\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = [];\n      each(data, function (colArr, key) {\n        dimensionsDefine.push(key);\n      });\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var value0 = getDataItemValue(data[0]);\n    dimensionsDetectedCount = isArray(value0) && value0.length || 1;\n  } else if (sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!!dimensionsDefine, 'dimensions must be given if data is TypedArray.');\n    }\n  }\n  return {\n    startIndex: startIndex,\n    dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n    dimensionsDetectedCount: dimensionsDetectedCount\n  };\n}\nfunction objectRowsCollectDimensions(data) {\n  var firstIndex = 0;\n  var obj;\n  while (firstIndex < data.length && !(obj = data[firstIndex++])) {} // jshint ignore: line\n  if (obj) {\n    return keys(obj);\n  }\n}\n// Consider dimensions defined like ['A', 'price', 'B', 'price', 'C', 'price'],\n// which is reasonable. But dimension name is duplicated.\n// Returns undefined or an array contains only object without null/undefined or string.\nfunction normalizeDimensionsOption(dimensionsDefine) {\n  if (!dimensionsDefine) {\n    // The meaning of null/undefined is different from empty array.\n    return;\n  }\n  var nameMap = createHashMap();\n  return map(dimensionsDefine, function (rawItem, index) {\n    rawItem = isObject(rawItem) ? rawItem : {\n      name: rawItem\n    };\n    // Other fields will be discarded.\n    var item = {\n      name: rawItem.name,\n      displayName: rawItem.displayName,\n      type: rawItem.type\n    };\n    // User can set null in dimensions.\n    // We don't auto specify name, otherwise a given name may\n    // cause it to be referred unexpectedly.\n    if (item.name == null) {\n      return item;\n    }\n    // Also consider number form like 2012.\n    item.name += '';\n    // User may also specify displayName.\n    // displayName will always exists except user not\n    // specified or dim name is not specified or detected.\n    // (A auto generated dim name will not be used as\n    // displayName).\n    if (item.displayName == null) {\n      item.displayName = item.name;\n    }\n    var exist = nameMap.get(item.name);\n    if (!exist) {\n      nameMap.set(item.name, {\n        count: 1\n      });\n    } else {\n      item.name += '-' + exist.count++;\n    }\n    return item;\n  });\n}\nfunction arrayRowsTravelFirst(cb, seriesLayoutBy, data, maxLoop) {\n  if (seriesLayoutBy === SERIES_LAYOUT_BY_ROW) {\n    for (var i = 0; i < data.length && i < maxLoop; i++) {\n      cb(data[i] ? data[i][0] : null, i);\n    }\n  } else {\n    var value0 = data[0] || [];\n    for (var i = 0; i < value0.length && i < maxLoop; i++) {\n      cb(value0[i], i);\n    }\n  }\n}\nexport function shouldRetrieveDataByName(source) {\n  var sourceFormat = source.sourceFormat;\n  return sourceFormat === SOURCE_FORMAT_OBJECT_ROWS || sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS;\n}"], "mappings": ";;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,0BAA0B;AAClK,SAASC,sBAAsB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,2BAA2B,EAAEC,yBAAyB,EAAEC,wBAAwB,EAAEC,yBAAyB,EAAEC,oBAAoB,QAAQ,kBAAkB;AAC5O,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,UAAU,EAAEC,YAAY,QAAQ,0BAA0B;AACnE;AACA;AACA,IAAIC,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACC,IAAI,GAAGD,MAAM,CAACC,IAAI,KAAKD,MAAM,CAACE,YAAY,KAAKX,2BAA2B,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IAC1F,IAAI,CAACW,YAAY,GAAGF,MAAM,CAACE,YAAY,IAAIZ,qBAAqB;IAChE;IACA,IAAI,CAACa,cAAc,GAAGH,MAAM,CAACG,cAAc,IAAId,uBAAuB;IACtE,IAAI,CAACe,UAAU,GAAGJ,MAAM,CAACI,UAAU,IAAI,CAAC;IACxC,IAAI,CAACC,uBAAuB,GAAGL,MAAM,CAACK,uBAAuB;IAC7D,IAAI,CAACC,aAAa,GAAGN,MAAM,CAACM,aAAa;IACzC,IAAIC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,GAAGP,MAAM,CAACO,gBAAgB;IACtE,IAAIA,gBAAgB,EAAE;MACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAChD,IAAIE,GAAG,GAAGH,gBAAgB,CAACC,CAAC,CAAC;QAC7B,IAAIE,GAAG,CAACC,IAAI,IAAI,IAAI,EAAE;UACpB,IAAIb,YAAY,CAAC,IAAI,EAAEU,CAAC,CAAC,KAAKX,UAAU,CAACe,IAAI,EAAE;YAC7CF,GAAG,CAACC,IAAI,GAAG,SAAS;UACtB;QACF;MACF;IACF;EACF;EACA,OAAOZ,UAAU;AACnB,CAAC,CAAC,CAAC;AACH,OAAO,SAASc,gBAAgBA,CAACC,GAAG,EAAE;EACpC,OAAOA,GAAG,YAAYf,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgB,YAAYA,CAACC,UAAU,EAAEC,iBAAiB;AAC1D;AACAf,YAAY,EAAE;EACZA,YAAY,GAAGA,YAAY,IAAIgB,kBAAkB,CAACF,UAAU,CAAC;EAC7D,IAAIb,cAAc,GAAGc,iBAAiB,CAACd,cAAc;EACrD,IAAIgB,UAAU,GAAGC,yBAAyB,CAACJ,UAAU,EAAEd,YAAY,EAAEC,cAAc,EAAEc,iBAAiB,CAACI,YAAY,EAAEJ,iBAAiB,CAACK,UAAU,CAAC;EAClJ,IAAIC,MAAM,GAAG,IAAIxB,UAAU,CAAC;IAC1BE,IAAI,EAAEe,UAAU;IAChBd,YAAY,EAAEA,YAAY;IAC1BC,cAAc,EAAEA,cAAc;IAC9BI,gBAAgB,EAAEY,UAAU,CAACZ,gBAAgB;IAC7CH,UAAU,EAAEe,UAAU,CAACf,UAAU;IACjCC,uBAAuB,EAAEc,UAAU,CAACd,uBAAuB;IAC3DC,aAAa,EAAE9B,KAAK,CAACyC,iBAAiB;EACxC,CAAC,CAAC;EACF,OAAOM,MAAM;AACf;AACA;AACA;AACA;AACA,OAAO,SAASC,gCAAgCA,CAACvB,IAAI,EAAE;EACrD,OAAO,IAAIF,UAAU,CAAC;IACpBE,IAAI,EAAEA,IAAI;IACVC,YAAY,EAAE3B,YAAY,CAAC0B,IAAI,CAAC,GAAGT,yBAAyB,GAAGJ;EACjE,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,OAAO,SAASqC,kBAAkBA,CAACF,MAAM,EAAE;EACzC,OAAO,IAAIxB,UAAU,CAAC;IACpBE,IAAI,EAAEsB,MAAM,CAACtB,IAAI;IACjBC,YAAY,EAAEqB,MAAM,CAACrB,YAAY;IACjCC,cAAc,EAAEoB,MAAM,CAACpB,cAAc;IACrCI,gBAAgB,EAAE/B,KAAK,CAAC+C,MAAM,CAAChB,gBAAgB,CAAC;IAChDH,UAAU,EAAEmB,MAAM,CAACnB,UAAU;IAC7BC,uBAAuB,EAAEkB,MAAM,CAAClB;EAClC,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,OAAO,SAASa,kBAAkBA,CAACjB,IAAI,EAAE;EACvC,IAAIC,YAAY,GAAGZ,qBAAqB;EACxC,IAAIf,YAAY,CAAC0B,IAAI,CAAC,EAAE;IACtBC,YAAY,GAAGV,yBAAyB;EAC1C,CAAC,MAAM,IAAId,OAAO,CAACuB,IAAI,CAAC,EAAE;IACxB;IACA,IAAIA,IAAI,CAACQ,MAAM,KAAK,CAAC,EAAE;MACrBP,YAAY,GAAGT,wBAAwB;IACzC;IACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEkB,GAAG,GAAGzB,IAAI,CAACQ,MAAM,EAAED,CAAC,GAAGkB,GAAG,EAAElB,CAAC,EAAE,EAAE;MAC/C,IAAImB,IAAI,GAAG1B,IAAI,CAACO,CAAC,CAAC;MAClB,IAAImB,IAAI,IAAI,IAAI,EAAE;QAChB;MACF,CAAC,MAAM,IAAIjD,OAAO,CAACiD,IAAI,CAAC,IAAIpD,YAAY,CAACoD,IAAI,CAAC,EAAE;QAC9CzB,YAAY,GAAGT,wBAAwB;QACvC;MACF,CAAC,MAAM,IAAId,QAAQ,CAACgD,IAAI,CAAC,EAAE;QACzBzB,YAAY,GAAGR,yBAAyB;QACxC;MACF;IACF;EACF,CAAC,MAAM,IAAIf,QAAQ,CAACsB,IAAI,CAAC,EAAE;IACzB,KAAK,IAAI2B,GAAG,IAAI3B,IAAI,EAAE;MACpB,IAAIpB,MAAM,CAACoB,IAAI,EAAE2B,GAAG,CAAC,IAAIhD,WAAW,CAACqB,IAAI,CAAC2B,GAAG,CAAC,CAAC,EAAE;QAC/C1B,YAAY,GAAGX,2BAA2B;QAC1C;MACF;IACF;EACF;EACA,OAAOW,YAAY;AACrB;AACA;AACA;AACA;AACA;AACA,SAASkB,yBAAyBA,CAACnB,IAAI,EAAEC,YAAY,EAAEC,cAAc,EAAEkB,YAAY;AACnF;AACA;AACA;AACA;AACA;AACAd,gBAAgB,EAAE;EAChB,IAAIF,uBAAuB;EAC3B,IAAID,UAAU;EACd;EACA;EACA;EACA;EACA,IAAI,CAACH,IAAI,EAAE;IACT,OAAO;MACLM,gBAAgB,EAAEsB,yBAAyB,CAACtB,gBAAgB,CAAC;MAC7DH,UAAU,EAAEA,UAAU;MACtBC,uBAAuB,EAAEA;IAC3B,CAAC;EACH;EACA,IAAIH,YAAY,KAAKT,wBAAwB,EAAE;IAC7C,IAAIqC,aAAa,GAAG7B,IAAI;IACxB;IACA;IACA;IACA;IACA,IAAIoB,YAAY,KAAK,MAAM,IAAIA,YAAY,IAAI,IAAI,EAAE;MACnDU,oBAAoB,CAAC,UAAUjB,GAAG,EAAE;QAClC;QACA,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAK,GAAG,EAAE;UAC9B,IAAI5B,QAAQ,CAAC4B,GAAG,CAAC,EAAE;YACjBV,UAAU,IAAI,IAAI,KAAKA,UAAU,GAAG,CAAC,CAAC;UACxC,CAAC,MAAM;YACLA,UAAU,GAAG,CAAC;UAChB;QACF;QACA;MACF,CAAC,EAAED,cAAc,EAAE2B,aAAa,EAAE,EAAE,CAAC;IACvC,CAAC,MAAM;MACL1B,UAAU,GAAGnB,QAAQ,CAACoC,YAAY,CAAC,GAAGA,YAAY,GAAGA,YAAY,GAAG,CAAC,GAAG,CAAC;IAC3E;IACA,IAAI,CAACd,gBAAgB,IAAIH,UAAU,KAAK,CAAC,EAAE;MACzCG,gBAAgB,GAAG,EAAE;MACrBwB,oBAAoB,CAAC,UAAUjB,GAAG,EAAEkB,KAAK,EAAE;QACzCzB,gBAAgB,CAACyB,KAAK,CAAC,GAAGlB,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,EAAE,GAAG,EAAE;MACvD,CAAC,EAAEX,cAAc,EAAE2B,aAAa,EAAEG,QAAQ,CAAC;IAC7C;IACA5B,uBAAuB,GAAGE,gBAAgB,GAAGA,gBAAgB,CAACE,MAAM,GAAGN,cAAc,KAAKR,oBAAoB,GAAGmC,aAAa,CAACrB,MAAM,GAAGqB,aAAa,CAAC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,CAACrB,MAAM,GAAG,IAAI;EAC3L,CAAC,MAAM,IAAIP,YAAY,KAAKR,yBAAyB,EAAE;IACrD,IAAI,CAACa,gBAAgB,EAAE;MACrBA,gBAAgB,GAAG2B,2BAA2B,CAACjC,IAAI,CAAC;IACtD;EACF,CAAC,MAAM,IAAIC,YAAY,KAAKX,2BAA2B,EAAE;IACvD,IAAI,CAACgB,gBAAgB,EAAE;MACrBA,gBAAgB,GAAG,EAAE;MACrBxB,IAAI,CAACkB,IAAI,EAAE,UAAUkC,MAAM,EAAEP,GAAG,EAAE;QAChCrB,gBAAgB,CAAC6B,IAAI,CAACR,GAAG,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF,CAAC,MAAM,IAAI1B,YAAY,KAAKd,sBAAsB,EAAE;IAClD,IAAIiD,MAAM,GAAGzC,gBAAgB,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;IACtCI,uBAAuB,GAAG3B,OAAO,CAAC2D,MAAM,CAAC,IAAIA,MAAM,CAAC5B,MAAM,IAAI,CAAC;EACjE,CAAC,MAAM,IAAIP,YAAY,KAAKV,yBAAyB,EAAE;IACrD,IAAI8C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC1D,MAAM,CAAC,CAAC,CAACyB,gBAAgB,EAAE,iDAAiD,CAAC;IAC/E;EACF;EACA,OAAO;IACLH,UAAU,EAAEA,UAAU;IACtBG,gBAAgB,EAAEsB,yBAAyB,CAACtB,gBAAgB,CAAC;IAC7DF,uBAAuB,EAAEA;EAC3B,CAAC;AACH;AACA,SAAS6B,2BAA2BA,CAACjC,IAAI,EAAE;EACzC,IAAIwC,UAAU,GAAG,CAAC;EAClB,IAAIC,GAAG;EACP,OAAOD,UAAU,GAAGxC,IAAI,CAACQ,MAAM,IAAI,EAAEiC,GAAG,GAAGzC,IAAI,CAACwC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE,IAAIC,GAAG,EAAE;IACP,OAAOvD,IAAI,CAACuD,GAAG,CAAC;EAClB;AACF;AACA;AACA;AACA;AACA,SAASb,yBAAyBA,CAACtB,gBAAgB,EAAE;EACnD,IAAI,CAACA,gBAAgB,EAAE;IACrB;IACA;EACF;EACA,IAAIoC,OAAO,GAAGlE,aAAa,CAAC,CAAC;EAC7B,OAAOO,GAAG,CAACuB,gBAAgB,EAAE,UAAUqC,OAAO,EAAEZ,KAAK,EAAE;IACrDY,OAAO,GAAGjE,QAAQ,CAACiE,OAAO,CAAC,GAAGA,OAAO,GAAG;MACtCC,IAAI,EAAED;IACR,CAAC;IACD;IACA,IAAIjB,IAAI,GAAG;MACTkB,IAAI,EAAED,OAAO,CAACC,IAAI;MAClBC,WAAW,EAAEF,OAAO,CAACE,WAAW;MAChCnC,IAAI,EAAEiC,OAAO,CAACjC;IAChB,CAAC;IACD;IACA;IACA;IACA,IAAIgB,IAAI,CAACkB,IAAI,IAAI,IAAI,EAAE;MACrB,OAAOlB,IAAI;IACb;IACA;IACAA,IAAI,CAACkB,IAAI,IAAI,EAAE;IACf;IACA;IACA;IACA;IACA;IACA,IAAIlB,IAAI,CAACmB,WAAW,IAAI,IAAI,EAAE;MAC5BnB,IAAI,CAACmB,WAAW,GAAGnB,IAAI,CAACkB,IAAI;IAC9B;IACA,IAAIE,KAAK,GAAGJ,OAAO,CAACK,GAAG,CAACrB,IAAI,CAACkB,IAAI,CAAC;IAClC,IAAI,CAACE,KAAK,EAAE;MACVJ,OAAO,CAACM,GAAG,CAACtB,IAAI,CAACkB,IAAI,EAAE;QACrBK,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLvB,IAAI,CAACkB,IAAI,IAAI,GAAG,GAAGE,KAAK,CAACG,KAAK,EAAE;IAClC;IACA,OAAOvB,IAAI;EACb,CAAC,CAAC;AACJ;AACA,SAASI,oBAAoBA,CAACoB,EAAE,EAAEhD,cAAc,EAAEF,IAAI,EAAEmD,OAAO,EAAE;EAC/D,IAAIjD,cAAc,KAAKR,oBAAoB,EAAE;IAC3C,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAACQ,MAAM,IAAID,CAAC,GAAG4C,OAAO,EAAE5C,CAAC,EAAE,EAAE;MACnD2C,EAAE,CAAClD,IAAI,CAACO,CAAC,CAAC,GAAGP,IAAI,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEA,CAAC,CAAC;IACpC;EACF,CAAC,MAAM;IACL,IAAI6B,MAAM,GAAGpC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;IAC1B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,MAAM,CAAC5B,MAAM,IAAID,CAAC,GAAG4C,OAAO,EAAE5C,CAAC,EAAE,EAAE;MACrD2C,EAAE,CAACd,MAAM,CAAC7B,CAAC,CAAC,EAAEA,CAAC,CAAC;IAClB;EACF;AACF;AACA,OAAO,SAAS6C,wBAAwBA,CAAC9B,MAAM,EAAE;EAC/C,IAAIrB,YAAY,GAAGqB,MAAM,CAACrB,YAAY;EACtC,OAAOA,YAAY,KAAKR,yBAAyB,IAAIQ,YAAY,KAAKX,2BAA2B;AACnG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}