{"ast": null, "code": "import _objectSpread from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _regeneratorRuntime from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"G:/\\u5907\\u4EFD9/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.string.pad-start.js\";\nimport { getBannerList, addBanner, updateBanner, toggleBannerStatus, deleteBanner } from '@/api/carousel/banner';\nexport default {\n  name: 'CarouselList',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        title: '',\n        status: ''\n      },\n      total: 0,\n      loading: false,\n      submitLoading: false,\n      // 表格数据\n      tableData: [],\n      // 弹窗相关\n      dialogVisible: false,\n      dialogTitle: '',\n      form: {\n        title: '',\n        imageUrl: '',\n        jumpUrl: '',\n        sort: 0,\n        status: 1\n      },\n      rules: {\n        title: [{\n          required: true,\n          message: '请输入轮播标题',\n          trigger: 'blur'\n        }],\n        imageUrl: [{\n          required: true,\n          message: '请上传轮播图片',\n          trigger: 'change'\n        }]\n      },\n      uploadHeaders: {\n        Authorization: 'Bearer ' + localStorage.getItem('token')\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    // 获取列表数据\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _this.loading = true;\n              _context.next = 4;\n              return getBannerList(_this.listQuery);\n            case 4:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.tableData = res.data;\n                _this.total = res.total;\n              } else {\n                _this.$message.error(res.msg || '获取列表失败');\n              }\n              _context.next = 11;\n              break;\n            case 8:\n              _context.prev = 8;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('获取列表失败:', _context.t0);\n            case 11:\n              _context.prev = 11;\n              _this.loading = false;\n              return _context.finish(11);\n            case 14:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 8, 11, 14]]);\n      }))();\n    },\n    // 重置查询\n    handleReset: function handleReset() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        title: '',\n        status: ''\n      };\n      this.getList();\n    },\n    // 分页相关\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    // 新增\n    handleAdd: function handleAdd() {\n      var _this2 = this;\n      this.dialogTitle = '新增轮播';\n      this.form = {\n        title: '',\n        imageUrl: '',\n        jumpUrl: '',\n        sort: 0,\n        status: 1\n      };\n      this.dialogVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.form && _this2.$refs.form.clearValidate();\n      });\n    },\n    // 修改\n    handleEdit: function handleEdit(row) {\n      var _this3 = this;\n      this.dialogTitle = '修改轮播';\n      this.form = _objectSpread({}, row);\n      this.dialogVisible = true;\n      this.$nextTick(function () {\n        _this3.$refs.form && _this3.$refs.form.clearValidate();\n      });\n    },\n    // 提交表单\n    submitForm: function submitForm() {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _this4.$refs.form.validate(/*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(valid) {\n                  var api, res;\n                  return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n                    while (1) switch (_context2.prev = _context2.next) {\n                      case 0:\n                        if (!valid) {\n                          _context2.next = 16;\n                          break;\n                        }\n                        _context2.prev = 1;\n                        _this4.submitLoading = true;\n                        api = _this4.form.id ? updateBanner : addBanner;\n                        _context2.next = 6;\n                        return api(_this4.form);\n                      case 6:\n                        res = _context2.sent;\n                        if (res.code === 0) {\n                          _this4.$message.success(_this4.form.id ? '修改成功' : '新增成功');\n                          _this4.dialogVisible = false;\n                          _this4.getList();\n                        } else {\n                          _this4.$message.error(res.msg || '操作失败');\n                        }\n                        _context2.next = 13;\n                        break;\n                      case 10:\n                        _context2.prev = 10;\n                        _context2.t0 = _context2[\"catch\"](1);\n                        console.error('保存失败:', _context2.t0);\n                      case 13:\n                        _context2.prev = 13;\n                        _this4.submitLoading = false;\n                        return _context2.finish(13);\n                      case 16:\n                      case \"end\":\n                        return _context2.stop();\n                    }\n                  }, _callee2, null, [[1, 10, 13, 16]]);\n                }));\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }());\n            case 1:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }))();\n    },\n    // 切换状态\n    handleToggleStatus: function handleToggleStatus(row) {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var newStatus, res;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.prev = 0;\n              newStatus = row.status === 1 ? 0 : 1;\n              _context4.next = 4;\n              return toggleBannerStatus(row.id, newStatus);\n            case 4:\n              res = _context4.sent;\n              if (res.code === 0) {\n                _this5.$message.success(\"\".concat(newStatus === 1 ? '启用' : '禁用', \"\\u6210\\u529F\"));\n                row.status = newStatus;\n              } else {\n                _this5.$message.error(res.msg || '操作失败');\n              }\n              _context4.next = 11;\n              break;\n            case 8:\n              _context4.prev = 8;\n              _context4.t0 = _context4[\"catch\"](0);\n              console.error('修改状态失败:', _context4.t0);\n            case 11:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[0, 8]]);\n      }))();\n    },\n    // 删除\n    handleDelete: function handleDelete(row) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return _this6.$confirm('确认要删除该轮播图吗？', '警告', {\n                type: 'warning'\n              });\n            case 3:\n              _context5.next = 5;\n              return deleteBanner(row.id);\n            case 5:\n              res = _context5.sent;\n              if (res.code === 0) {\n                _this6.$message.success('删除成功');\n                _this6.getList();\n              } else {\n                _this6.$message.error(res.msg || '删除失败');\n              }\n              _context5.next = 12;\n              break;\n            case 9:\n              _context5.prev = 9;\n              _context5.t0 = _context5[\"catch\"](0);\n              if (_context5.t0 !== 'cancel') {\n                console.error('删除失败:', _context5.t0);\n              }\n            case 12:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, null, [[0, 9]]);\n      }))();\n    },\n    // 上传相关\n    beforeUpload: function beforeUpload(file) {\n      var isJPG = file.type === 'image/jpeg';\n      var isPNG = file.type === 'image/png';\n      var isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isJPG && !isPNG) {\n        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');\n        return false;\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!');\n        return false;\n      }\n      return true;\n    },\n    handleUploadSuccess: function handleUploadSuccess(res) {\n      if (res.code === 0) {\n        this.form.imageUrl = 'https://frontapi.huatongyun666.com' + res.data;\n        this.$message.success('上传成功');\n      } else {\n        this.$message.error(res.msg || '上传失败');\n      }\n    },\n    convertImage: function convertImage(src) {\n      return 'https://frontapi.huatongyun666.com' + src;\n    },\n    handleUploadError: function handleUploadError(err) {\n      console.error('上传失败:', err);\n      this.$message.error('上传失败，请重试');\n    },\n    formatDateTime: function formatDateTime(time) {\n      if (!time) return '-';\n      var date = new Date(time);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, '0');\n      var day = String(date.getDate()).padStart(2, '0');\n      var hours = String(date.getHours()).padStart(2, '0');\n      var minutes = String(date.getMinutes()).padStart(2, '0');\n      var seconds = String(date.getSeconds()).padStart(2, '0');\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    }\n  }\n};", "map": {"version": 3, "names": ["getBannerList", "addBanner", "updateBanner", "toggleBannerStatus", "deleteBanner", "name", "data", "list<PERSON>uery", "page", "limit", "title", "status", "total", "loading", "submitLoading", "tableData", "dialogVisible", "dialogTitle", "form", "imageUrl", "jumpUrl", "sort", "rules", "required", "message", "trigger", "uploadHeaders", "Authorization", "localStorage", "getItem", "created", "getList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleReset", "handleSizeChange", "val", "handleCurrentChange", "handleAdd", "_this2", "$nextTick", "$refs", "clearValidate", "handleEdit", "row", "_this3", "_objectSpread", "submitForm", "_this4", "_callee3", "_callee3$", "_context3", "validate", "_ref", "_callee2", "valid", "api", "_callee2$", "_context2", "id", "success", "_x", "apply", "arguments", "handleToggleStatus", "_this5", "_callee4", "newStatus", "_callee4$", "_context4", "concat", "handleDelete", "_this6", "_callee5", "_callee5$", "_context5", "$confirm", "type", "beforeUpload", "file", "isJPG", "isPNG", "isLt2M", "size", "handleUploadSuccess", "convertImage", "src", "handleUploadError", "err", "formatDateTime", "time", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["src/views/carousel/list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索和操作区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-input\r\n          v-model=\"listQuery.title\"\r\n          placeholder=\"轮播标题\"\r\n          style=\"width: 200px;\"\r\n          class=\"filter-item\"\r\n          @keyup.enter.native=\"getList\"\r\n          clearable\r\n        />\r\n        <el-select\r\n          v-model=\"listQuery.status\"\r\n          placeholder=\"状态\"\r\n          clearable\r\n          class=\"filter-item\"\r\n          style=\"width: 130px\"\r\n        >\r\n          <el-option label=\"启用\" :value=\"1\" />\r\n          <el-option label=\"禁用\" :value=\"0\" />\r\n        </el-select>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getList\">搜索</el-button>\r\n        <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"handleReset\">重置</el-button>\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增轮播</el-button>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"80\" align=\"center\" />\r\n        <el-table-column label=\"轮播图片\" align=\"center\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <el-image\r\n              style=\"width: 100px; height: 60px\"\r\n              :src=\"convertImage(scope.row.imageUrl)\"\r\n              :preview-src-list=\"[scope.row.imageUrl]\"\r\n              fit=\"cover\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"轮播标题\" prop=\"title\" align=\"center\" />\r\n        <el-table-column label=\"跳转链接\" prop=\"jumpUrl\" align=\"center\" show-overflow-tooltip />\r\n        <el-table-column label=\"排序\" prop=\"sort\" align=\"center\" width=\"80\" />\r\n        <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.status === 1 ? 'success' : 'info'\">\r\n              {{ scope.row.status === 1 ? '启用' : '禁用' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.createTime) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" @click=\"handleEdit(scope.row)\">修改</el-button>\r\n            <el-button \r\n              type=\"text\" \r\n              :style=\"{ color: scope.row.status === 1 ? '#F56C6C' : '#67C23A' }\"\r\n              @click=\"handleToggleStatus(scope.row)\"\r\n            >\r\n              {{ scope.row.status === 1 ? '禁用' : '启用' }}\r\n            </el-button>\r\n            <el-button type=\"text\" style=\"color: #F56C6C\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 新增/修改对话框 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"轮播标题\" prop=\"title\">\r\n          <el-input v-model=\"form.title\" placeholder=\"请输入轮播标题\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"轮播图片\" prop=\"imageUrl\">\r\n          <el-upload\r\n            class=\"upload-container\"\r\n            action=\"/upload/image\"\r\n            :headers=\"uploadHeaders\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :on-error=\"handleUploadError\"\r\n            :before-upload=\"beforeUpload\"\r\n            :drag=\"true\"\r\n          >\r\n            <template v-if=\"form.imageUrl\">\r\n              <img :src=\"form.imageUrl\" class=\"preview-image\">\r\n            </template>\r\n            <template v-else>\r\n              <i class=\"el-icon-upload\"></i>\r\n              <div class=\"el-upload__text\">\r\n                将文件拖到此处，或<em>点击上传</em>\r\n              </div>\r\n            </template>\r\n          </el-upload>\r\n          <div class=\"upload-tip\">建议尺寸：750x350像素，支持jpg、png格式，大小不超过2MB</div>\r\n        </el-form-item>\r\n        <el-form-item label=\"跳转链接\" prop=\"jumpUrl\">\r\n          <el-input v-model=\"form.jumpUrl\" placeholder=\"请输入跳转链接\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"sort\">\r\n          <el-input-number v-model=\"form.sort\" :min=\"0\" :max=\"99\" controls-position=\"right\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio :label=\"1\">启用</el-radio>\r\n            <el-radio :label=\"0\">禁用</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getBannerList, addBanner, updateBanner, toggleBannerStatus, deleteBanner } from '@/api/carousel/banner'\r\n\r\nexport default {\r\n  name: 'CarouselList',\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        title: '',\r\n        status: ''\r\n      },\r\n      total: 0,\r\n      loading: false,\r\n      submitLoading: false,\r\n      // 表格数据\r\n      tableData: [],\r\n      // 弹窗相关\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      form: {\r\n        title: '',\r\n        imageUrl: '',\r\n        jumpUrl: '',\r\n        sort: 0,\r\n        status: 1\r\n      },\r\n      rules: {\r\n        title: [\r\n          { required: true, message: '请输入轮播标题', trigger: 'blur' }\r\n        ],\r\n        imageUrl: [\r\n          { required: true, message: '请上传轮播图片', trigger: 'change' }\r\n        ]\r\n      },\r\n      uploadHeaders: {\r\n        Authorization: 'Bearer ' + localStorage.getItem('token')\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 获取列表数据\r\n    async getList() {\r\n      try {\r\n        this.loading = true\r\n        const res = await getBannerList(this.listQuery)\r\n        if (res.code === 0) {\r\n          this.tableData = res.data\r\n          this.total = res.total\r\n        } else {\r\n          this.$message.error(res.msg || '获取列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取列表失败:', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 重置查询\r\n    handleReset() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        title: '',\r\n        status: ''\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    // 分页相关\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n\r\n    // 新增\r\n    handleAdd() {\r\n      this.dialogTitle = '新增轮播'\r\n      this.form = {\r\n        title: '',\r\n        imageUrl: '',\r\n        jumpUrl: '',\r\n        sort: 0,\r\n        status: 1\r\n      }\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.form && this.$refs.form.clearValidate()\r\n      })\r\n    },\r\n\r\n    // 修改\r\n    handleEdit(row) {\r\n      this.dialogTitle = '修改轮播'\r\n      this.form = { ...row }\r\n      this.dialogVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.form && this.$refs.form.clearValidate()\r\n      })\r\n    },\r\n\r\n    // 提交表单\r\n    async submitForm() {\r\n      this.$refs.form.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            this.submitLoading = true\r\n            const api = this.form.id ? updateBanner : addBanner\r\n            const res = await api(this.form)\r\n            if (res.code === 0) {\r\n              this.$message.success(this.form.id ? '修改成功' : '新增成功')\r\n              this.dialogVisible = false\r\n              this.getList()\r\n            } else {\r\n              this.$message.error(res.msg || '操作失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('保存失败:', error)\r\n          } finally {\r\n            this.submitLoading = false\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 切换状态\r\n    async handleToggleStatus(row) {\r\n      try {\r\n        const newStatus = row.status === 1 ? 0 : 1\r\n        const res = await toggleBannerStatus(row.id, newStatus)\r\n        if (res.code === 0) {\r\n          this.$message.success(`${newStatus === 1 ? '启用' : '禁用'}成功`)\r\n          row.status = newStatus\r\n        } else {\r\n          this.$message.error(res.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('修改状态失败:', error)\r\n      }\r\n    },\r\n\r\n    // 删除\r\n    async handleDelete(row) {\r\n      try {\r\n        await this.$confirm('确认要删除该轮播图吗？', '警告', {\r\n          type: 'warning'\r\n        })\r\n        const res = await deleteBanner(row.id)\r\n        if (res.code === 0) {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        } else {\r\n          this.$message.error(res.msg || '删除失败')\r\n        }\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          console.error('删除失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 上传相关\r\n    beforeUpload(file) {\r\n      const isJPG = file.type === 'image/jpeg'\r\n      const isPNG = file.type === 'image/png'\r\n      const isLt2M = file.size / 1024 / 1024 < 2\r\n\r\n      if (!isJPG && !isPNG) {\r\n        this.$message.error('上传图片只能是 JPG 或 PNG 格式!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 2MB!')\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    handleUploadSuccess(res) {\r\n      if (res.code === 0) {\r\n        this.form.imageUrl ='https://frontapi.huatongyun666.com'+res.data\r\n        this.$message.success('上传成功')\r\n      } else {\r\n        this.$message.error(res.msg || '上传失败')\r\n      }\r\n    },\r\n    convertImage(src){\r\n      return 'https://frontapi.huatongyun666.com'+src;\r\n    } \r\n    ,\r\n    handleUploadError(err) {\r\n      console.error('上传失败:', err)\r\n      this.$message.error('上传失败，请重试')\r\n    },\r\n    formatDateTime(time) {\r\n      if (!time) return '-'\r\n      const date = new Date(time)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    .filter-item {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n\r\n  .upload-container {\r\n    width: 360px;\r\n    height: 180px;\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    cursor: pointer;\r\n    position: relative;\r\n    overflow: hidden;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n   background-color: #fafafa;\r\n\r\n    &:hover {\r\n      border-color: #409EFF;\r\n      background-color: #f5f7fa;\r\n    }\r\n\r\n    .preview-image {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n    }\r\n\r\n    .el-icon-upload {\r\n      font-size: 67px;\r\n      color: #c0c4cc;\r\n      margin-bottom: 10px;\r\n      line-height: 50px;\r\n    }\r\n\r\n    .el-upload__text {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      text-align: center;\r\n\r\n      em {\r\n        color: #409EFF;\r\n        font-style: normal;\r\n      }\r\n    }\r\n  }\r\n\r\n  .upload-tip {\r\n    font-size: 12px;\r\n    color: #909399;\r\n    margin-top: 5px;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;AA8IA,SAAAA,aAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,kBAAA,EAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,aAAA;MACA;MACAC,SAAA;MACA;MACAC,aAAA;MACAC,WAAA;MACAC,IAAA;QACAR,KAAA;QACAS,QAAA;QACAC,OAAA;QACAC,IAAA;QACAV,MAAA;MACA;MACAW,KAAA;QACAZ,KAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,QAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,aAAA;QACAC,aAAA,cAAAC,YAAA,CAAAC,OAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAApB,OAAA;cAAA4B,QAAA,CAAAE,IAAA;cAAA,OACA3C,aAAA,CAAAiC,KAAA,CAAA1B,SAAA;YAAA;cAAA+B,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAZ,KAAA,CAAAlB,SAAA,GAAAuB,GAAA,CAAAhC,IAAA;gBACA2B,KAAA,CAAArB,KAAA,GAAA0B,GAAA,CAAA1B,KAAA;cACA;gBACAqB,KAAA,CAAAa,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAAP,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEAS,OAAA,CAAAH,KAAA,YAAAN,QAAA,CAAAQ,EAAA;YAAA;cAAAR,QAAA,CAAAC,IAAA;cAEAT,KAAA,CAAApB,OAAA;cAAA,OAAA4B,QAAA,CAAAU,MAAA;YAAA;YAAA;cAAA,OAAAV,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IAEA;IAEA;IACAgB,WAAA,WAAAA,YAAA;MACA,KAAA9C,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACA,KAAAoB,OAAA;IACA;IAEA;IACAuB,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAhD,SAAA,CAAAE,KAAA,GAAA8C,GAAA;MACA,KAAAxB,OAAA;IACA;IACAyB,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAhD,SAAA,CAAAC,IAAA,GAAA+C,GAAA;MACA,KAAAxB,OAAA;IACA;IAEA;IACA0B,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAzC,WAAA;MACA,KAAAC,IAAA;QACAR,KAAA;QACAS,QAAA;QACAC,OAAA;QACAC,IAAA;QACAV,MAAA;MACA;MACA,KAAAK,aAAA;MACA,KAAA2C,SAAA;QACAD,MAAA,CAAAE,KAAA,CAAA1C,IAAA,IAAAwC,MAAA,CAAAE,KAAA,CAAA1C,IAAA,CAAA2C,aAAA;MACA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA/C,WAAA;MACA,KAAAC,IAAA,GAAA+C,aAAA,KAAAF,GAAA;MACA,KAAA/C,aAAA;MACA,KAAA2C,SAAA;QACAK,MAAA,CAAAJ,KAAA,CAAA1C,IAAA,IAAA8C,MAAA,CAAAJ,KAAA,CAAA1C,IAAA,CAAA2C,aAAA;MACA;IACA;IAEA;IACAK,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,OAAAjC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAgC,SAAA;QAAA,OAAAjC,mBAAA,GAAAI,IAAA,UAAA8B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,IAAA,GAAA4B,SAAA,CAAA3B,IAAA;YAAA;cACAwB,MAAA,CAAAP,KAAA,CAAA1C,IAAA,CAAAqD,QAAA;gBAAA,IAAAC,IAAA,GAAAtC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAqC,SAAAC,KAAA;kBAAA,IAAAC,GAAA,EAAArC,GAAA;kBAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAqC,UAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;sBAAA;wBAAA,KACA+B,KAAA;0BAAAG,SAAA,CAAAlC,IAAA;0BAAA;wBAAA;wBAAAkC,SAAA,CAAAnC,IAAA;wBAEAyB,MAAA,CAAArD,aAAA;wBACA6D,GAAA,GAAAR,MAAA,CAAAjD,IAAA,CAAA4D,EAAA,GAAA5E,YAAA,GAAAD,SAAA;wBAAA4E,SAAA,CAAAlC,IAAA;wBAAA,OACAgC,GAAA,CAAAR,MAAA,CAAAjD,IAAA;sBAAA;wBAAAoB,GAAA,GAAAuC,SAAA,CAAAjC,IAAA;wBACA,IAAAN,GAAA,CAAAO,IAAA;0BACAsB,MAAA,CAAArB,QAAA,CAAAiC,OAAA,CAAAZ,MAAA,CAAAjD,IAAA,CAAA4D,EAAA;0BACAX,MAAA,CAAAnD,aAAA;0BACAmD,MAAA,CAAApC,OAAA;wBACA;0BACAoC,MAAA,CAAArB,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;wBACA;wBAAA6B,SAAA,CAAAlC,IAAA;wBAAA;sBAAA;wBAAAkC,SAAA,CAAAnC,IAAA;wBAAAmC,SAAA,CAAA5B,EAAA,GAAA4B,SAAA;wBAEA3B,OAAA,CAAAH,KAAA,UAAA8B,SAAA,CAAA5B,EAAA;sBAAA;wBAAA4B,SAAA,CAAAnC,IAAA;wBAEAyB,MAAA,CAAArD,aAAA;wBAAA,OAAA+D,SAAA,CAAA1B,MAAA;sBAAA;sBAAA;wBAAA,OAAA0B,SAAA,CAAAzB,IAAA;oBAAA;kBAAA,GAAAqB,QAAA;gBAAA,CAGA;gBAAA,iBAAAO,EAAA;kBAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;gBAAA;cAAA;YAAA;YAAA;cAAA,OAAAZ,SAAA,CAAAlB,IAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IACA;IAEA;IACAe,kBAAA,WAAAA,mBAAApB,GAAA;MAAA,IAAAqB,MAAA;MAAA,OAAAlD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAiD,SAAA;QAAA,IAAAC,SAAA,EAAAhD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAgD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9C,IAAA,GAAA8C,SAAA,CAAA7C,IAAA;YAAA;cAAA6C,SAAA,CAAA9C,IAAA;cAEA4C,SAAA,GAAAvB,GAAA,CAAApD,MAAA;cAAA6E,SAAA,CAAA7C,IAAA;cAAA,OACAxC,kBAAA,CAAA4D,GAAA,CAAAe,EAAA,EAAAQ,SAAA;YAAA;cAAAhD,GAAA,GAAAkD,SAAA,CAAA5C,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAuC,MAAA,CAAAtC,QAAA,CAAAiC,OAAA,IAAAU,MAAA,CAAAH,SAAA;gBACAvB,GAAA,CAAApD,MAAA,GAAA2E,SAAA;cACA;gBACAF,MAAA,CAAAtC,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAAwC,SAAA,CAAA7C,IAAA;cAAA;YAAA;cAAA6C,SAAA,CAAA9C,IAAA;cAAA8C,SAAA,CAAAvC,EAAA,GAAAuC,SAAA;cAEAtC,OAAA,CAAAH,KAAA,YAAAyC,SAAA,CAAAvC,EAAA;YAAA;YAAA;cAAA,OAAAuC,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAiC,QAAA;MAAA;IAEA;IAEA;IACAK,YAAA,WAAAA,aAAA3B,GAAA;MAAA,IAAA4B,MAAA;MAAA,OAAAzD,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAwD,SAAA;QAAA,IAAAtD,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAsD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApD,IAAA,GAAAoD,SAAA,CAAAnD,IAAA;YAAA;cAAAmD,SAAA,CAAApD,IAAA;cAAAoD,SAAA,CAAAnD,IAAA;cAAA,OAEAgD,MAAA,CAAAI,QAAA;gBACAC,IAAA;cACA;YAAA;cAAAF,SAAA,CAAAnD,IAAA;cAAA,OACAvC,YAAA,CAAA2D,GAAA,CAAAe,EAAA;YAAA;cAAAxC,GAAA,GAAAwD,SAAA,CAAAlD,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACA8C,MAAA,CAAA7C,QAAA,CAAAiC,OAAA;gBACAY,MAAA,CAAA5D,OAAA;cACA;gBACA4D,MAAA,CAAA7C,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;cACA;cAAA8C,SAAA,CAAAnD,IAAA;cAAA;YAAA;cAAAmD,SAAA,CAAApD,IAAA;cAAAoD,SAAA,CAAA7C,EAAA,GAAA6C,SAAA;cAEA,IAAAA,SAAA,CAAA7C,EAAA;gBACAC,OAAA,CAAAH,KAAA,UAAA+C,SAAA,CAAA7C,EAAA;cACA;YAAA;YAAA;cAAA,OAAA6C,SAAA,CAAA1C,IAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IAEA;IACAK,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,KAAA,GAAAD,IAAA,CAAAF,IAAA;MACA,IAAAI,KAAA,GAAAF,IAAA,CAAAF,IAAA;MACA,IAAAK,MAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,KAAA,KAAAC,KAAA;QACA,KAAAtD,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAsD,MAAA;QACA,KAAAvD,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IACAwD,mBAAA,WAAAA,oBAAAjE,GAAA;MACA,IAAAA,GAAA,CAAAO,IAAA;QACA,KAAA3B,IAAA,CAAAC,QAAA,0CAAAmB,GAAA,CAAAhC,IAAA;QACA,KAAAwC,QAAA,CAAAiC,OAAA;MACA;QACA,KAAAjC,QAAA,CAAAC,KAAA,CAAAT,GAAA,CAAAU,GAAA;MACA;IACA;IACAwD,YAAA,WAAAA,aAAAC,GAAA;MACA,8CAAAA,GAAA;IACA;IAEAC,iBAAA,WAAAA,kBAAAC,GAAA;MACAzD,OAAA,CAAAH,KAAA,UAAA4D,GAAA;MACA,KAAA7D,QAAA,CAAAC,KAAA;IACA;IACA6D,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAA5B,MAAA,CAAAuB,IAAA,OAAAvB,MAAA,CAAAyB,KAAA,OAAAzB,MAAA,CAAA6B,GAAA,OAAA7B,MAAA,CAAA+B,KAAA,OAAA/B,MAAA,CAAAiC,OAAA,OAAAjC,MAAA,CAAAmC,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}