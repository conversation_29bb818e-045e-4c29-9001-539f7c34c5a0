{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u5B9D/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _objectSpread from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u5B9D/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u5B9D/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getLoginLogList, cleanLoginLog } from '@/api/log/login';\nimport moment from 'moment';\nexport default {\n  name: 'LoginLog',\n  data: function data() {\n    return {\n      loading: false,\n      listQuery: {\n        page: 1,\n        limit: 10,\n        username: '',\n        ip: '',\n        status: '',\n        dateRange: []\n      },\n      total: 0,\n      tableData: []\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var params, res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _this.loading = true;\n              params = _objectSpread({}, _this.listQuery);\n              if (params.dateRange && params.dateRange.length === 2) {\n                params.startDate = moment(params.dateRange[0]).format('YYYY-MM-DD');\n                params.endDate = moment(params.dateRange[1]).format('YYYY-MM-DD');\n              }\n              delete params.dateRange;\n              _context.next = 7;\n              return getLoginLogList(params);\n            case 7:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.tableData = res.data;\n                _this.total = res.total;\n              } else {\n                _this.$message.error(res.msg || '获取登录日志失败');\n              }\n              _context.next = 15;\n              break;\n            case 11:\n              _context.prev = 11;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('获取登录日志失败:', _context.t0);\n              _this.$message.error('获取登录日志失败');\n            case 15:\n              _context.prev = 15;\n              _this.loading = false;\n              return _context.finish(15);\n            case 18:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 11, 15, 18]]);\n      }))();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      this.getList();\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      this.getList();\n    },\n    // 清空日志\n    handleClean: function handleClean() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return _this2.$confirm('是否确认清空所有登录日志数据?', '警告', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n              });\n            case 3:\n              _context2.next = 5;\n              return cleanLoginLog();\n            case 5:\n              res = _context2.sent;\n              if (res.code === 0) {\n                _this2.$message.success('清空成功');\n                _this2.getList();\n              } else {\n                _this2.$message.error(res.msg || '清空失败');\n              }\n              _context2.next = 12;\n              break;\n            case 9:\n              _context2.prev = 9;\n              _context2.t0 = _context2[\"catch\"](0);\n              if (_context2.t0 !== 'cancel') {\n                console.error('清空登录日志失败:', _context2.t0);\n                _this2.$message.error('清空登录日志失败');\n              }\n            case 12:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 9]]);\n      }))();\n    },\n    // 重置查询\n    handleReset: function handleReset() {\n      this.listQuery = {\n        page: 1,\n        limit: 10,\n        username: '',\n        ip: '',\n        status: '',\n        dateRange: []\n      };\n      this.getList();\n    },\n    formatDateTime: function formatDateTime(time) {\n      if (!time) {\n        return '-';\n      }\n      return moment(time).format('YYYY-MM-DD HH:mm:ss');\n    }\n  }\n};", "map": {"version": 3, "names": ["getLoginLogList", "cleanLoginLog", "moment", "name", "data", "loading", "list<PERSON>uery", "page", "limit", "username", "ip", "status", "date<PERSON><PERSON><PERSON>", "total", "tableData", "created", "getList", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "params", "res", "wrap", "_callee$", "_context", "prev", "next", "_objectSpread", "length", "startDate", "format", "endDate", "sent", "code", "$message", "error", "msg", "t0", "console", "finish", "stop", "handleSizeChange", "val", "handleCurrentChange", "handleClean", "_this2", "_callee2", "_callee2$", "_context2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "success", "handleReset", "formatDateTime", "time"], "sources": ["src/views/log/login/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <div class=\"filter-line\">\r\n          <el-input\r\n            v-model=\"listQuery.username\"\r\n            placeholder=\"用户名\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 180px\"\r\n            class=\"filter-item\"\r\n          />\r\n          <el-input\r\n            v-model=\"listQuery.ip\"\r\n            placeholder=\"登录地址\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 180px\"\r\n            class=\"filter-item\"\r\n          />\r\n          <el-select\r\n            v-model=\"listQuery.status\"\r\n            placeholder=\"登录状态\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 120px\"\r\n            class=\"filter-item\"\r\n          >\r\n            <el-option label=\"成功\" value=\"1\" />\r\n            <el-option label=\"失败\" value=\"0\" />\r\n          </el-select>\r\n          <el-date-picker\r\n            v-model=\"listQuery.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"YYYY-MM-DD\"\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            class=\"filter-item\"\r\n          />\r\n          <div class=\"filter-buttons\">\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\" @click=\"getList\" :loading=\"loading\">搜索</el-button>\r\n            <el-button type=\"success\" icon=\"el-icon-refresh\" size=\"small\" @click=\"handleReset\">重置</el-button>\r\n            <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"small\" @click=\"handleClean\">清空</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"序号\" type=\"index\" width=\"80\" align=\"center\" />\r\n        <el-table-column label=\"用户名称\" prop=\"username\" align=\"center\" width=\"120\" />\r\n        <el-table-column label=\"登录地址\" prop=\"ipaddr\" align=\"center\" width=\"220\" />\r\n        <el-table-column label=\"登录地点\" prop=\"loginLocation\" align=\"center\" width=\"150\" />\r\n        <el-table-column label=\"浏览器\" prop=\"browser\" align=\"center\" width=\"150\" />\r\n        <el-table-column label=\"操作系统\" prop=\"os\" align=\"center\" width=\"150\" />\r\n        <el-table-column label=\"登录状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.status === 1? 'success' : 'danger'\">\r\n              {{ scope.row.status === 1 ? '成功' : '失败' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"登录信息\" prop=\"msg\" align=\"center\" show-overflow-tooltip />\r\n        <el-table-column label=\"登录时间\" align=\"center\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ formatDateTime(scope.row.loginTime) }}\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getLoginLogList, cleanLoginLog } from '@/api/log/login'\r\nimport moment from 'moment'\r\n\r\nexport default {\r\n  name: 'LoginLog',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        ip: '',\r\n        status: '',\r\n        dateRange: []\r\n      },\r\n      total: 0,\r\n      tableData: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    async getList() {\r\n      try {\r\n        this.loading = true\r\n        const params = { ...this.listQuery }\r\n        if (params.dateRange && params.dateRange.length === 2) {\r\n          params.startDate = moment(params.dateRange[0]).format('YYYY-MM-DD')\r\n          params.endDate = moment(params.dateRange[1]).format('YYYY-MM-DD')\r\n        }\r\n        delete params.dateRange\r\n\r\n        const res = await getLoginLogList(params)\r\n        if (res.code === 0) {\r\n          this.tableData = res.data\r\n          this.total = res.total\r\n        } else {\r\n          this.$message.error(res.msg || '获取登录日志失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取登录日志失败:', error)\r\n        this.$message.error('获取登录日志失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      this.getList()\r\n    },\r\n\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      this.getList()\r\n    },\r\n\r\n    // 清空日志\r\n    async handleClean() {\r\n      try {\r\n        await this.$confirm('是否确认清空所有登录日志数据?', '警告', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        const res = await cleanLoginLog()\r\n        if (res.code === 0) {\r\n          this.$message.success('清空成功')\r\n          this.getList()\r\n        } else {\r\n          this.$message.error(res.msg || '清空失败')\r\n        }\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          console.error('清空登录日志失败:', error)\r\n          this.$message.error('清空登录日志失败')\r\n        }\r\n      }\r\n    },\r\n\r\n    // 重置查询\r\n    handleReset() {\r\n      this.listQuery = {\r\n        page: 1,\r\n        limit: 10,\r\n        username: '',\r\n        ip: '',\r\n        status: '',\r\n        dateRange: []\r\n      }\r\n      this.getList()\r\n    },\r\n\r\n    formatDateTime(time) {\r\n      if (!time) {\r\n        return '-'\r\n      }\r\n      return moment(time).format('YYYY-MM-DD HH:mm:ss')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    \r\n    .filter-line {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 15px 10px;  // 设置行间距15px，列间距10px\r\n      align-items: center;\r\n    }\r\n\r\n    .filter-item {\r\n      margin: 0;  // 移除原有的margin，使用gap控制间距\r\n    }\r\n\r\n    .filter-buttons {\r\n      white-space: nowrap;  // 按钮组不换行\r\n      \r\n      .el-button {\r\n        margin-left: 0;\r\n        margin-right: 10px;\r\n        \r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;AAmGA,SAAAA,eAAA,EAAAC,aAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,EAAA;QACAC,MAAA;QACAC,SAAA;MACA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAV,KAAA,CAAAb,OAAA;cACAkB,MAAA,GAAAO,aAAA,KAAAZ,KAAA,CAAAZ,SAAA;cACA,IAAAiB,MAAA,CAAAX,SAAA,IAAAW,MAAA,CAAAX,SAAA,CAAAmB,MAAA;gBACAR,MAAA,CAAAS,SAAA,GAAA9B,MAAA,CAAAqB,MAAA,CAAAX,SAAA,KAAAqB,MAAA;gBACAV,MAAA,CAAAW,OAAA,GAAAhC,MAAA,CAAAqB,MAAA,CAAAX,SAAA,KAAAqB,MAAA;cACA;cACA,OAAAV,MAAA,CAAAX,SAAA;cAAAe,QAAA,CAAAE,IAAA;cAAA,OAEA7B,eAAA,CAAAuB,MAAA;YAAA;cAAAC,GAAA,GAAAG,QAAA,CAAAQ,IAAA;cACA,IAAAX,GAAA,CAAAY,IAAA;gBACAlB,KAAA,CAAAJ,SAAA,GAAAU,GAAA,CAAApB,IAAA;gBACAc,KAAA,CAAAL,KAAA,GAAAW,GAAA,CAAAX,KAAA;cACA;gBACAK,KAAA,CAAAmB,QAAA,CAAAC,KAAA,CAAAd,GAAA,CAAAe,GAAA;cACA;cAAAZ,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAa,EAAA,GAAAb,QAAA;cAEAc,OAAA,CAAAH,KAAA,cAAAX,QAAA,CAAAa,EAAA;cACAtB,KAAA,CAAAmB,QAAA,CAAAC,KAAA;YAAA;cAAAX,QAAA,CAAAC,IAAA;cAEAV,KAAA,CAAAb,OAAA;cAAA,OAAAsB,QAAA,CAAAe,MAAA;YAAA;YAAA;cAAA,OAAAf,QAAA,CAAAgB,IAAA;UAAA;QAAA,GAAArB,OAAA;MAAA;IAEA;IAEAsB,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAvC,SAAA,CAAAE,KAAA,GAAAqC,GAAA;MACA,KAAA7B,OAAA;IACA;IAEA8B,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAvC,SAAA,CAAAC,IAAA,GAAAsC,GAAA;MACA,KAAA7B,OAAA;IACA;IAEA;IACA+B,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,OAAA7B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA4B,SAAA;QAAA,IAAAzB,GAAA;QAAA,OAAAJ,mBAAA,GAAAK,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cAAAsB,SAAA,CAAAvB,IAAA;cAAAuB,SAAA,CAAAtB,IAAA;cAAA,OAEAmB,MAAA,CAAAI,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAAJ,SAAA,CAAAtB,IAAA;cAAA,OACA5B,aAAA;YAAA;cAAAuB,GAAA,GAAA2B,SAAA,CAAAhB,IAAA;cACA,IAAAX,GAAA,CAAAY,IAAA;gBACAY,MAAA,CAAAX,QAAA,CAAAmB,OAAA;gBACAR,MAAA,CAAAhC,OAAA;cACA;gBACAgC,MAAA,CAAAX,QAAA,CAAAC,KAAA,CAAAd,GAAA,CAAAe,GAAA;cACA;cAAAY,SAAA,CAAAtB,IAAA;cAAA;YAAA;cAAAsB,SAAA,CAAAvB,IAAA;cAAAuB,SAAA,CAAAX,EAAA,GAAAW,SAAA;cAEA,IAAAA,SAAA,CAAAX,EAAA;gBACAC,OAAA,CAAAH,KAAA,cAAAa,SAAA,CAAAX,EAAA;gBACAQ,MAAA,CAAAX,QAAA,CAAAC,KAAA;cACA;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAAR,IAAA;UAAA;QAAA,GAAAM,QAAA;MAAA;IAEA;IAEA;IACAQ,WAAA,WAAAA,YAAA;MACA,KAAAnD,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,EAAA;QACAC,MAAA;QACAC,SAAA;MACA;MACA,KAAAI,OAAA;IACA;IAEA0C,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAzD,MAAA,CAAAyD,IAAA,EAAA1B,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}