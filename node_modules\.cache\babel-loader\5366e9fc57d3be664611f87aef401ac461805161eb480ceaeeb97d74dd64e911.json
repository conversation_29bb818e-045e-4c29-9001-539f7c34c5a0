{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { subPixelOptimize } from '../../util/graphic.js';\nimport createRenderPlanner from '../helper/createRenderPlanner.js';\nimport { parsePercent } from '../../util/number.js';\nimport { map, retrieve2 } from 'zrender/lib/core/util.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nvar candlestickLayout = {\n  seriesType: 'candlestick',\n  plan: createRenderPlanner(),\n  reset: function reset(seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    var data = seriesModel.getData();\n    var candleWidth = calculateCandleWidth(seriesModel, data);\n    var cDimIdx = 0;\n    var vDimIdx = 1;\n    var coordDims = ['x', 'y'];\n    var cDimI = data.getDimensionIndex(data.mapDimension(coordDims[cDimIdx]));\n    var vDimsI = map(data.mapDimensionsAll(coordDims[vDimIdx]), data.getDimensionIndex, data);\n    var openDimI = vDimsI[0];\n    var closeDimI = vDimsI[1];\n    var lowestDimI = vDimsI[2];\n    var highestDimI = vDimsI[3];\n    data.setLayout({\n      candleWidth: candleWidth,\n      // The value is experimented visually.\n      isSimpleBox: candleWidth <= 1.3\n    });\n    if (cDimI < 0 || vDimsI.length < 4) {\n      return;\n    }\n    return {\n      progress: seriesModel.pipelineContext.large ? largeProgress : normalProgress\n    };\n    function normalProgress(params, data) {\n      var dataIndex;\n      var store = data.getStore();\n      while ((dataIndex = params.next()) != null) {\n        var axisDimVal = store.get(cDimI, dataIndex);\n        var openVal = store.get(openDimI, dataIndex);\n        var closeVal = store.get(closeDimI, dataIndex);\n        var lowestVal = store.get(lowestDimI, dataIndex);\n        var highestVal = store.get(highestDimI, dataIndex);\n        var ocLow = Math.min(openVal, closeVal);\n        var ocHigh = Math.max(openVal, closeVal);\n        var ocLowPoint = getPoint(ocLow, axisDimVal);\n        var ocHighPoint = getPoint(ocHigh, axisDimVal);\n        var lowestPoint = getPoint(lowestVal, axisDimVal);\n        var highestPoint = getPoint(highestVal, axisDimVal);\n        var ends = [];\n        addBodyEnd(ends, ocHighPoint, 0);\n        addBodyEnd(ends, ocLowPoint, 1);\n        ends.push(subPixelOptimizePoint(highestPoint), subPixelOptimizePoint(ocHighPoint), subPixelOptimizePoint(lowestPoint), subPixelOptimizePoint(ocLowPoint));\n        var itemModel = data.getItemModel(dataIndex);\n        var hasDojiColor = !!itemModel.get(['itemStyle', 'borderColorDoji']);\n        data.setItemLayout(dataIndex, {\n          sign: getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor),\n          initBaseline: openVal > closeVal ? ocHighPoint[vDimIdx] : ocLowPoint[vDimIdx],\n          ends: ends,\n          brushRect: makeBrushRect(lowestVal, highestVal, axisDimVal)\n        });\n      }\n      function getPoint(val, axisDimVal) {\n        var p = [];\n        p[cDimIdx] = axisDimVal;\n        p[vDimIdx] = val;\n        return isNaN(axisDimVal) || isNaN(val) ? [NaN, NaN] : coordSys.dataToPoint(p);\n      }\n      function addBodyEnd(ends, point, start) {\n        var point1 = point.slice();\n        var point2 = point.slice();\n        point1[cDimIdx] = subPixelOptimize(point1[cDimIdx] + candleWidth / 2, 1, false);\n        point2[cDimIdx] = subPixelOptimize(point2[cDimIdx] - candleWidth / 2, 1, true);\n        start ? ends.push(point1, point2) : ends.push(point2, point1);\n      }\n      function makeBrushRect(lowestVal, highestVal, axisDimVal) {\n        var pmin = getPoint(lowestVal, axisDimVal);\n        var pmax = getPoint(highestVal, axisDimVal);\n        pmin[cDimIdx] -= candleWidth / 2;\n        pmax[cDimIdx] -= candleWidth / 2;\n        return {\n          x: pmin[0],\n          y: pmin[1],\n          width: vDimIdx ? candleWidth : pmax[0] - pmin[0],\n          height: vDimIdx ? pmax[1] - pmin[1] : candleWidth\n        };\n      }\n      function subPixelOptimizePoint(point) {\n        point[cDimIdx] = subPixelOptimize(point[cDimIdx], 1);\n        return point;\n      }\n    }\n    function largeProgress(params, data) {\n      // Structure: [sign, x, yhigh, ylow, sign, x, yhigh, ylow, ...]\n      var points = createFloat32Array(params.count * 4);\n      var offset = 0;\n      var point;\n      var tmpIn = [];\n      var tmpOut = [];\n      var dataIndex;\n      var store = data.getStore();\n      var hasDojiColor = !!seriesModel.get(['itemStyle', 'borderColorDoji']);\n      while ((dataIndex = params.next()) != null) {\n        var axisDimVal = store.get(cDimI, dataIndex);\n        var openVal = store.get(openDimI, dataIndex);\n        var closeVal = store.get(closeDimI, dataIndex);\n        var lowestVal = store.get(lowestDimI, dataIndex);\n        var highestVal = store.get(highestDimI, dataIndex);\n        if (isNaN(axisDimVal) || isNaN(lowestVal) || isNaN(highestVal)) {\n          points[offset++] = NaN;\n          offset += 3;\n          continue;\n        }\n        points[offset++] = getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor);\n        tmpIn[cDimIdx] = axisDimVal;\n        tmpIn[vDimIdx] = lowestVal;\n        point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n        points[offset++] = point ? point[0] : NaN;\n        points[offset++] = point ? point[1] : NaN;\n        tmpIn[vDimIdx] = highestVal;\n        point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n        points[offset++] = point ? point[1] : NaN;\n      }\n      data.setLayout('largePoints', points);\n    }\n  }\n};\n/**\n * Get the sign of a single data.\n *\n * @returns 0 for doji with hasDojiColor: true,\n *          1 for positive,\n *          -1 for negative.\n */\nfunction getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor) {\n  var sign;\n  if (openVal > closeVal) {\n    sign = -1;\n  } else if (openVal < closeVal) {\n    sign = 1;\n  } else {\n    sign = hasDojiColor\n    // When doji color is set, use it instead of color/color0.\n    ? 0 : dataIndex > 0\n    // If close === open, compare with close of last record\n    ? store.get(closeDimI, dataIndex - 1) <= closeVal ? 1 : -1\n    // No record of previous, set to be positive\n    : 1;\n  }\n  return sign;\n}\nfunction calculateCandleWidth(seriesModel, data) {\n  var baseAxis = seriesModel.getBaseAxis();\n  var extent;\n  var bandWidth = baseAxis.type === 'category' ? baseAxis.getBandWidth() : (extent = baseAxis.getExtent(), Math.abs(extent[1] - extent[0]) / data.count());\n  var barMaxWidth = parsePercent(retrieve2(seriesModel.get('barMaxWidth'), bandWidth), bandWidth);\n  var barMinWidth = parsePercent(retrieve2(seriesModel.get('barMinWidth'), 1), bandWidth);\n  var barWidth = seriesModel.get('barWidth');\n  return barWidth != null ? parsePercent(barWidth, bandWidth)\n  // Put max outer to ensure bar visible in spite of overlap.\n  : Math.max(Math.min(bandWidth / 2, barMaxWidth), barMinWidth);\n}\nexport default candlestickLayout;", "map": {"version": 3, "names": ["subPixelOptimize", "createRenderPlanner", "parsePercent", "map", "retrieve2", "createFloat32Array", "candlestickLayout", "seriesType", "plan", "reset", "seriesModel", "coordSys", "coordinateSystem", "data", "getData", "candleWidth", "calculateCandleWidth", "cDimIdx", "vDimIdx", "coordDims", "cDimI", "getDimensionIndex", "mapDimension", "vDimsI", "mapDimensionsAll", "openDimI", "closeDimI", "lowestDimI", "highestDimI", "setLayout", "isSimpleBox", "length", "progress", "pipelineContext", "large", "largeProgress", "normalProgress", "params", "dataIndex", "store", "getStore", "next", "axisDimVal", "get", "openVal", "closeVal", "lowestVal", "highestVal", "ocLow", "Math", "min", "ocHigh", "max", "ocLowPoint", "getPoint", "ocHighPoint", "lowestPoint", "highestPoint", "ends", "addBodyEnd", "push", "subPixelOptimizePoint", "itemModel", "getItemModel", "hasDojiColor", "setItemLayout", "sign", "getSign", "initBaseline", "brushRect", "makeBrushRect", "val", "p", "isNaN", "NaN", "dataToPoint", "point", "start", "point1", "slice", "point2", "pmin", "pmax", "x", "y", "width", "height", "points", "count", "offset", "tmpIn", "tmpOut", "baseAxis", "getBaseAxis", "extent", "bandWidth", "type", "getBandWidth", "getExtent", "abs", "barMaxWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:/新项目/adminweb/node_modules/echarts/lib/chart/candlestick/candlestickLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { subPixelOptimize } from '../../util/graphic.js';\nimport createRenderPlanner from '../helper/createRenderPlanner.js';\nimport { parsePercent } from '../../util/number.js';\nimport { map, retrieve2 } from 'zrender/lib/core/util.js';\nimport { createFloat32Array } from '../../util/vendor.js';\nvar candlestickLayout = {\n  seriesType: 'candlestick',\n  plan: createRenderPlanner(),\n  reset: function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    var data = seriesModel.getData();\n    var candleWidth = calculateCandleWidth(seriesModel, data);\n    var cDimIdx = 0;\n    var vDimIdx = 1;\n    var coordDims = ['x', 'y'];\n    var cDimI = data.getDimensionIndex(data.mapDimension(coordDims[cDimIdx]));\n    var vDimsI = map(data.mapDimensionsAll(coordDims[vDimIdx]), data.getDimensionIndex, data);\n    var openDimI = vDimsI[0];\n    var closeDimI = vDimsI[1];\n    var lowestDimI = vDimsI[2];\n    var highestDimI = vDimsI[3];\n    data.setLayout({\n      candleWidth: candleWidth,\n      // The value is experimented visually.\n      isSimpleBox: candleWidth <= 1.3\n    });\n    if (cDimI < 0 || vDimsI.length < 4) {\n      return;\n    }\n    return {\n      progress: seriesModel.pipelineContext.large ? largeProgress : normalProgress\n    };\n    function normalProgress(params, data) {\n      var dataIndex;\n      var store = data.getStore();\n      while ((dataIndex = params.next()) != null) {\n        var axisDimVal = store.get(cDimI, dataIndex);\n        var openVal = store.get(openDimI, dataIndex);\n        var closeVal = store.get(closeDimI, dataIndex);\n        var lowestVal = store.get(lowestDimI, dataIndex);\n        var highestVal = store.get(highestDimI, dataIndex);\n        var ocLow = Math.min(openVal, closeVal);\n        var ocHigh = Math.max(openVal, closeVal);\n        var ocLowPoint = getPoint(ocLow, axisDimVal);\n        var ocHighPoint = getPoint(ocHigh, axisDimVal);\n        var lowestPoint = getPoint(lowestVal, axisDimVal);\n        var highestPoint = getPoint(highestVal, axisDimVal);\n        var ends = [];\n        addBodyEnd(ends, ocHighPoint, 0);\n        addBodyEnd(ends, ocLowPoint, 1);\n        ends.push(subPixelOptimizePoint(highestPoint), subPixelOptimizePoint(ocHighPoint), subPixelOptimizePoint(lowestPoint), subPixelOptimizePoint(ocLowPoint));\n        var itemModel = data.getItemModel(dataIndex);\n        var hasDojiColor = !!itemModel.get(['itemStyle', 'borderColorDoji']);\n        data.setItemLayout(dataIndex, {\n          sign: getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor),\n          initBaseline: openVal > closeVal ? ocHighPoint[vDimIdx] : ocLowPoint[vDimIdx],\n          ends: ends,\n          brushRect: makeBrushRect(lowestVal, highestVal, axisDimVal)\n        });\n      }\n      function getPoint(val, axisDimVal) {\n        var p = [];\n        p[cDimIdx] = axisDimVal;\n        p[vDimIdx] = val;\n        return isNaN(axisDimVal) || isNaN(val) ? [NaN, NaN] : coordSys.dataToPoint(p);\n      }\n      function addBodyEnd(ends, point, start) {\n        var point1 = point.slice();\n        var point2 = point.slice();\n        point1[cDimIdx] = subPixelOptimize(point1[cDimIdx] + candleWidth / 2, 1, false);\n        point2[cDimIdx] = subPixelOptimize(point2[cDimIdx] - candleWidth / 2, 1, true);\n        start ? ends.push(point1, point2) : ends.push(point2, point1);\n      }\n      function makeBrushRect(lowestVal, highestVal, axisDimVal) {\n        var pmin = getPoint(lowestVal, axisDimVal);\n        var pmax = getPoint(highestVal, axisDimVal);\n        pmin[cDimIdx] -= candleWidth / 2;\n        pmax[cDimIdx] -= candleWidth / 2;\n        return {\n          x: pmin[0],\n          y: pmin[1],\n          width: vDimIdx ? candleWidth : pmax[0] - pmin[0],\n          height: vDimIdx ? pmax[1] - pmin[1] : candleWidth\n        };\n      }\n      function subPixelOptimizePoint(point) {\n        point[cDimIdx] = subPixelOptimize(point[cDimIdx], 1);\n        return point;\n      }\n    }\n    function largeProgress(params, data) {\n      // Structure: [sign, x, yhigh, ylow, sign, x, yhigh, ylow, ...]\n      var points = createFloat32Array(params.count * 4);\n      var offset = 0;\n      var point;\n      var tmpIn = [];\n      var tmpOut = [];\n      var dataIndex;\n      var store = data.getStore();\n      var hasDojiColor = !!seriesModel.get(['itemStyle', 'borderColorDoji']);\n      while ((dataIndex = params.next()) != null) {\n        var axisDimVal = store.get(cDimI, dataIndex);\n        var openVal = store.get(openDimI, dataIndex);\n        var closeVal = store.get(closeDimI, dataIndex);\n        var lowestVal = store.get(lowestDimI, dataIndex);\n        var highestVal = store.get(highestDimI, dataIndex);\n        if (isNaN(axisDimVal) || isNaN(lowestVal) || isNaN(highestVal)) {\n          points[offset++] = NaN;\n          offset += 3;\n          continue;\n        }\n        points[offset++] = getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor);\n        tmpIn[cDimIdx] = axisDimVal;\n        tmpIn[vDimIdx] = lowestVal;\n        point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n        points[offset++] = point ? point[0] : NaN;\n        points[offset++] = point ? point[1] : NaN;\n        tmpIn[vDimIdx] = highestVal;\n        point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n        points[offset++] = point ? point[1] : NaN;\n      }\n      data.setLayout('largePoints', points);\n    }\n  }\n};\n/**\n * Get the sign of a single data.\n *\n * @returns 0 for doji with hasDojiColor: true,\n *          1 for positive,\n *          -1 for negative.\n */\nfunction getSign(store, dataIndex, openVal, closeVal, closeDimI, hasDojiColor) {\n  var sign;\n  if (openVal > closeVal) {\n    sign = -1;\n  } else if (openVal < closeVal) {\n    sign = 1;\n  } else {\n    sign = hasDojiColor\n    // When doji color is set, use it instead of color/color0.\n    ? 0 : dataIndex > 0\n    // If close === open, compare with close of last record\n    ? store.get(closeDimI, dataIndex - 1) <= closeVal ? 1 : -1\n    // No record of previous, set to be positive\n    : 1;\n  }\n  return sign;\n}\nfunction calculateCandleWidth(seriesModel, data) {\n  var baseAxis = seriesModel.getBaseAxis();\n  var extent;\n  var bandWidth = baseAxis.type === 'category' ? baseAxis.getBandWidth() : (extent = baseAxis.getExtent(), Math.abs(extent[1] - extent[0]) / data.count());\n  var barMaxWidth = parsePercent(retrieve2(seriesModel.get('barMaxWidth'), bandWidth), bandWidth);\n  var barMinWidth = parsePercent(retrieve2(seriesModel.get('barMinWidth'), 1), bandWidth);\n  var barWidth = seriesModel.get('barWidth');\n  return barWidth != null ? parsePercent(barWidth, bandWidth)\n  // Put max outer to ensure bar visible in spite of overlap.\n  : Math.max(Math.min(bandWidth / 2, barMaxWidth), barMinWidth);\n}\nexport default candlestickLayout;"], "mappings": ";;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,GAAG,EAAEC,SAAS,QAAQ,0BAA0B;AACzD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,IAAIC,iBAAiB,GAAG;EACtBC,UAAU,EAAE,aAAa;EACzBC,IAAI,EAAEP,mBAAmB,CAAC,CAAC;EAC3BQ,KAAK,EAAE,SAAPA,KAAKA,CAAYC,WAAW,EAAE;IAC5B,IAAIC,QAAQ,GAAGD,WAAW,CAACE,gBAAgB;IAC3C,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,WAAW,GAAGC,oBAAoB,CAACN,WAAW,EAAEG,IAAI,CAAC;IACzD,IAAII,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC1B,IAAIC,KAAK,GAAGP,IAAI,CAACQ,iBAAiB,CAACR,IAAI,CAACS,YAAY,CAACH,SAAS,CAACF,OAAO,CAAC,CAAC,CAAC;IACzE,IAAIM,MAAM,GAAGpB,GAAG,CAACU,IAAI,CAACW,gBAAgB,CAACL,SAAS,CAACD,OAAO,CAAC,CAAC,EAAEL,IAAI,CAACQ,iBAAiB,EAAER,IAAI,CAAC;IACzF,IAAIY,QAAQ,GAAGF,MAAM,CAAC,CAAC,CAAC;IACxB,IAAIG,SAAS,GAAGH,MAAM,CAAC,CAAC,CAAC;IACzB,IAAII,UAAU,GAAGJ,MAAM,CAAC,CAAC,CAAC;IAC1B,IAAIK,WAAW,GAAGL,MAAM,CAAC,CAAC,CAAC;IAC3BV,IAAI,CAACgB,SAAS,CAAC;MACbd,WAAW,EAAEA,WAAW;MACxB;MACAe,WAAW,EAAEf,WAAW,IAAI;IAC9B,CAAC,CAAC;IACF,IAAIK,KAAK,GAAG,CAAC,IAAIG,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;MAClC;IACF;IACA,OAAO;MACLC,QAAQ,EAAEtB,WAAW,CAACuB,eAAe,CAACC,KAAK,GAAGC,aAAa,GAAGC;IAChE,CAAC;IACD,SAASA,cAAcA,CAACC,MAAM,EAAExB,IAAI,EAAE;MACpC,IAAIyB,SAAS;MACb,IAAIC,KAAK,GAAG1B,IAAI,CAAC2B,QAAQ,CAAC,CAAC;MAC3B,OAAO,CAACF,SAAS,GAAGD,MAAM,CAACI,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;QAC1C,IAAIC,UAAU,GAAGH,KAAK,CAACI,GAAG,CAACvB,KAAK,EAAEkB,SAAS,CAAC;QAC5C,IAAIM,OAAO,GAAGL,KAAK,CAACI,GAAG,CAAClB,QAAQ,EAAEa,SAAS,CAAC;QAC5C,IAAIO,QAAQ,GAAGN,KAAK,CAACI,GAAG,CAACjB,SAAS,EAAEY,SAAS,CAAC;QAC9C,IAAIQ,SAAS,GAAGP,KAAK,CAACI,GAAG,CAAChB,UAAU,EAAEW,SAAS,CAAC;QAChD,IAAIS,UAAU,GAAGR,KAAK,CAACI,GAAG,CAACf,WAAW,EAAEU,SAAS,CAAC;QAClD,IAAIU,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACN,OAAO,EAAEC,QAAQ,CAAC;QACvC,IAAIM,MAAM,GAAGF,IAAI,CAACG,GAAG,CAACR,OAAO,EAAEC,QAAQ,CAAC;QACxC,IAAIQ,UAAU,GAAGC,QAAQ,CAACN,KAAK,EAAEN,UAAU,CAAC;QAC5C,IAAIa,WAAW,GAAGD,QAAQ,CAACH,MAAM,EAAET,UAAU,CAAC;QAC9C,IAAIc,WAAW,GAAGF,QAAQ,CAACR,SAAS,EAAEJ,UAAU,CAAC;QACjD,IAAIe,YAAY,GAAGH,QAAQ,CAACP,UAAU,EAAEL,UAAU,CAAC;QACnD,IAAIgB,IAAI,GAAG,EAAE;QACbC,UAAU,CAACD,IAAI,EAAEH,WAAW,EAAE,CAAC,CAAC;QAChCI,UAAU,CAACD,IAAI,EAAEL,UAAU,EAAE,CAAC,CAAC;QAC/BK,IAAI,CAACE,IAAI,CAACC,qBAAqB,CAACJ,YAAY,CAAC,EAAEI,qBAAqB,CAACN,WAAW,CAAC,EAAEM,qBAAqB,CAACL,WAAW,CAAC,EAAEK,qBAAqB,CAACR,UAAU,CAAC,CAAC;QACzJ,IAAIS,SAAS,GAAGjD,IAAI,CAACkD,YAAY,CAACzB,SAAS,CAAC;QAC5C,IAAI0B,YAAY,GAAG,CAAC,CAACF,SAAS,CAACnB,GAAG,CAAC,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;QACpE9B,IAAI,CAACoD,aAAa,CAAC3B,SAAS,EAAE;UAC5B4B,IAAI,EAAEC,OAAO,CAAC5B,KAAK,EAAED,SAAS,EAAEM,OAAO,EAAEC,QAAQ,EAAEnB,SAAS,EAAEsC,YAAY,CAAC;UAC3EI,YAAY,EAAExB,OAAO,GAAGC,QAAQ,GAAGU,WAAW,CAACrC,OAAO,CAAC,GAAGmC,UAAU,CAACnC,OAAO,CAAC;UAC7EwC,IAAI,EAAEA,IAAI;UACVW,SAAS,EAAEC,aAAa,CAACxB,SAAS,EAAEC,UAAU,EAAEL,UAAU;QAC5D,CAAC,CAAC;MACJ;MACA,SAASY,QAAQA,CAACiB,GAAG,EAAE7B,UAAU,EAAE;QACjC,IAAI8B,CAAC,GAAG,EAAE;QACVA,CAAC,CAACvD,OAAO,CAAC,GAAGyB,UAAU;QACvB8B,CAAC,CAACtD,OAAO,CAAC,GAAGqD,GAAG;QAChB,OAAOE,KAAK,CAAC/B,UAAU,CAAC,IAAI+B,KAAK,CAACF,GAAG,CAAC,GAAG,CAACG,GAAG,EAAEA,GAAG,CAAC,GAAG/D,QAAQ,CAACgE,WAAW,CAACH,CAAC,CAAC;MAC/E;MACA,SAASb,UAAUA,CAACD,IAAI,EAAEkB,KAAK,EAAEC,KAAK,EAAE;QACtC,IAAIC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC;QAC1B,IAAIC,MAAM,GAAGJ,KAAK,CAACG,KAAK,CAAC,CAAC;QAC1BD,MAAM,CAAC7D,OAAO,CAAC,GAAGjB,gBAAgB,CAAC8E,MAAM,CAAC7D,OAAO,CAAC,GAAGF,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;QAC/EiE,MAAM,CAAC/D,OAAO,CAAC,GAAGjB,gBAAgB,CAACgF,MAAM,CAAC/D,OAAO,CAAC,GAAGF,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QAC9E8D,KAAK,GAAGnB,IAAI,CAACE,IAAI,CAACkB,MAAM,EAAEE,MAAM,CAAC,GAAGtB,IAAI,CAACE,IAAI,CAACoB,MAAM,EAAEF,MAAM,CAAC;MAC/D;MACA,SAASR,aAAaA,CAACxB,SAAS,EAAEC,UAAU,EAAEL,UAAU,EAAE;QACxD,IAAIuC,IAAI,GAAG3B,QAAQ,CAACR,SAAS,EAAEJ,UAAU,CAAC;QAC1C,IAAIwC,IAAI,GAAG5B,QAAQ,CAACP,UAAU,EAAEL,UAAU,CAAC;QAC3CuC,IAAI,CAAChE,OAAO,CAAC,IAAIF,WAAW,GAAG,CAAC;QAChCmE,IAAI,CAACjE,OAAO,CAAC,IAAIF,WAAW,GAAG,CAAC;QAChC,OAAO;UACLoE,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAC;UACVG,CAAC,EAAEH,IAAI,CAAC,CAAC,CAAC;UACVI,KAAK,EAAEnE,OAAO,GAAGH,WAAW,GAAGmE,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC;UAChDK,MAAM,EAAEpE,OAAO,GAAGgE,IAAI,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,GAAGlE;QACxC,CAAC;MACH;MACA,SAAS8C,qBAAqBA,CAACe,KAAK,EAAE;QACpCA,KAAK,CAAC3D,OAAO,CAAC,GAAGjB,gBAAgB,CAAC4E,KAAK,CAAC3D,OAAO,CAAC,EAAE,CAAC,CAAC;QACpD,OAAO2D,KAAK;MACd;IACF;IACA,SAASzC,aAAaA,CAACE,MAAM,EAAExB,IAAI,EAAE;MACnC;MACA,IAAI0E,MAAM,GAAGlF,kBAAkB,CAACgC,MAAM,CAACmD,KAAK,GAAG,CAAC,CAAC;MACjD,IAAIC,MAAM,GAAG,CAAC;MACd,IAAIb,KAAK;MACT,IAAIc,KAAK,GAAG,EAAE;MACd,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIrD,SAAS;MACb,IAAIC,KAAK,GAAG1B,IAAI,CAAC2B,QAAQ,CAAC,CAAC;MAC3B,IAAIwB,YAAY,GAAG,CAAC,CAACtD,WAAW,CAACiC,GAAG,CAAC,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;MACtE,OAAO,CAACL,SAAS,GAAGD,MAAM,CAACI,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;QAC1C,IAAIC,UAAU,GAAGH,KAAK,CAACI,GAAG,CAACvB,KAAK,EAAEkB,SAAS,CAAC;QAC5C,IAAIM,OAAO,GAAGL,KAAK,CAACI,GAAG,CAAClB,QAAQ,EAAEa,SAAS,CAAC;QAC5C,IAAIO,QAAQ,GAAGN,KAAK,CAACI,GAAG,CAACjB,SAAS,EAAEY,SAAS,CAAC;QAC9C,IAAIQ,SAAS,GAAGP,KAAK,CAACI,GAAG,CAAChB,UAAU,EAAEW,SAAS,CAAC;QAChD,IAAIS,UAAU,GAAGR,KAAK,CAACI,GAAG,CAACf,WAAW,EAAEU,SAAS,CAAC;QAClD,IAAImC,KAAK,CAAC/B,UAAU,CAAC,IAAI+B,KAAK,CAAC3B,SAAS,CAAC,IAAI2B,KAAK,CAAC1B,UAAU,CAAC,EAAE;UAC9DwC,MAAM,CAACE,MAAM,EAAE,CAAC,GAAGf,GAAG;UACtBe,MAAM,IAAI,CAAC;UACX;QACF;QACAF,MAAM,CAACE,MAAM,EAAE,CAAC,GAAGtB,OAAO,CAAC5B,KAAK,EAAED,SAAS,EAAEM,OAAO,EAAEC,QAAQ,EAAEnB,SAAS,EAAEsC,YAAY,CAAC;QACxF0B,KAAK,CAACzE,OAAO,CAAC,GAAGyB,UAAU;QAC3BgD,KAAK,CAACxE,OAAO,CAAC,GAAG4B,SAAS;QAC1B8B,KAAK,GAAGjE,QAAQ,CAACgE,WAAW,CAACe,KAAK,EAAE,IAAI,EAAEC,MAAM,CAAC;QACjDJ,MAAM,CAACE,MAAM,EAAE,CAAC,GAAGb,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGF,GAAG;QACzCa,MAAM,CAACE,MAAM,EAAE,CAAC,GAAGb,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGF,GAAG;QACzCgB,KAAK,CAACxE,OAAO,CAAC,GAAG6B,UAAU;QAC3B6B,KAAK,GAAGjE,QAAQ,CAACgE,WAAW,CAACe,KAAK,EAAE,IAAI,EAAEC,MAAM,CAAC;QACjDJ,MAAM,CAACE,MAAM,EAAE,CAAC,GAAGb,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGF,GAAG;MAC3C;MACA7D,IAAI,CAACgB,SAAS,CAAC,aAAa,EAAE0D,MAAM,CAAC;IACvC;EACF;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASpB,OAAOA,CAAC5B,KAAK,EAAED,SAAS,EAAEM,OAAO,EAAEC,QAAQ,EAAEnB,SAAS,EAAEsC,YAAY,EAAE;EAC7E,IAAIE,IAAI;EACR,IAAItB,OAAO,GAAGC,QAAQ,EAAE;IACtBqB,IAAI,GAAG,CAAC,CAAC;EACX,CAAC,MAAM,IAAItB,OAAO,GAAGC,QAAQ,EAAE;IAC7BqB,IAAI,GAAG,CAAC;EACV,CAAC,MAAM;IACLA,IAAI,GAAGF;IACP;IAAA,EACE,CAAC,GAAG1B,SAAS,GAAG;IAClB;IAAA,EACEC,KAAK,CAACI,GAAG,CAACjB,SAAS,EAAEY,SAAS,GAAG,CAAC,CAAC,IAAIO,QAAQ,GAAG,CAAC,GAAG,CAAC;IACzD;IAAA,EACE,CAAC;EACL;EACA,OAAOqB,IAAI;AACb;AACA,SAASlD,oBAAoBA,CAACN,WAAW,EAAEG,IAAI,EAAE;EAC/C,IAAI+E,QAAQ,GAAGlF,WAAW,CAACmF,WAAW,CAAC,CAAC;EACxC,IAAIC,MAAM;EACV,IAAIC,SAAS,GAAGH,QAAQ,CAACI,IAAI,KAAK,UAAU,GAAGJ,QAAQ,CAACK,YAAY,CAAC,CAAC,IAAIH,MAAM,GAAGF,QAAQ,CAACM,SAAS,CAAC,CAAC,EAAEjD,IAAI,CAACkD,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGjF,IAAI,CAAC2E,KAAK,CAAC,CAAC,CAAC;EACxJ,IAAIY,WAAW,GAAGlG,YAAY,CAACE,SAAS,CAACM,WAAW,CAACiC,GAAG,CAAC,aAAa,CAAC,EAAEoD,SAAS,CAAC,EAAEA,SAAS,CAAC;EAC/F,IAAIM,WAAW,GAAGnG,YAAY,CAACE,SAAS,CAACM,WAAW,CAACiC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,EAAEoD,SAAS,CAAC;EACvF,IAAIO,QAAQ,GAAG5F,WAAW,CAACiC,GAAG,CAAC,UAAU,CAAC;EAC1C,OAAO2D,QAAQ,IAAI,IAAI,GAAGpG,YAAY,CAACoG,QAAQ,EAAEP,SAAS;EAC1D;EAAA,EACE9C,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,GAAG,CAAC6C,SAAS,GAAG,CAAC,EAAEK,WAAW,CAAC,EAAEC,WAAW,CAAC;AAC/D;AACA,eAAe/F,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}