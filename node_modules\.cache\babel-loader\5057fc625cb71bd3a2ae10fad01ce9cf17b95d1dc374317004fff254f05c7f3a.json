{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { extend } from 'zrender/lib/core/util.js';\nexport default function treeVisual(ecModel) {\n  ecModel.eachSeriesByType('tree', function (seriesModel) {\n    var data = seriesModel.getData();\n    var tree = data.tree;\n    tree.eachNode(function (node) {\n      var model = node.getModel();\n      // TODO Optimize\n      var style = model.getModel('itemStyle').getItemStyle();\n      var existsStyle = data.ensureUniqueItemVisual(node.dataIndex, 'style');\n      extend(existsStyle, style);\n    });\n  });\n}", "map": {"version": 3, "names": ["extend", "treeVisual", "ecModel", "eachSeriesByType", "seriesModel", "data", "getData", "tree", "eachNode", "node", "model", "getModel", "style", "getItemStyle", "existsStyle", "ensureUniqueItemVisual", "dataIndex"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/echarts/lib/chart/tree/treeVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { extend } from 'zrender/lib/core/util.js';\nexport default function treeVisual(ecModel) {\n  ecModel.eachSeriesByType('tree', function (seriesModel) {\n    var data = seriesModel.getData();\n    var tree = data.tree;\n    tree.eachNode(function (node) {\n      var model = node.getModel();\n      // TODO Optimize\n      var style = model.getModel('itemStyle').getItemStyle();\n      var existsStyle = data.ensureUniqueItemVisual(node.dataIndex, 'style');\n      extend(existsStyle, style);\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,0BAA0B;AACjD,eAAe,SAASC,UAAUA,CAACC,OAAO,EAAE;EAC1CA,OAAO,CAACC,gBAAgB,CAAC,MAAM,EAAE,UAAUC,WAAW,EAAE;IACtD,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;IAChC,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;IACpBA,IAAI,CAACC,QAAQ,CAAC,UAAUC,IAAI,EAAE;MAC5B,IAAIC,KAAK,GAAGD,IAAI,CAACE,QAAQ,CAAC,CAAC;MAC3B;MACA,IAAIC,KAAK,GAAGF,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACE,YAAY,CAAC,CAAC;MACtD,IAAIC,WAAW,GAAGT,IAAI,CAACU,sBAAsB,CAACN,IAAI,CAACO,SAAS,EAAE,OAAO,CAAC;MACtEhB,MAAM,CAACc,WAAW,EAAEF,KAAK,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}