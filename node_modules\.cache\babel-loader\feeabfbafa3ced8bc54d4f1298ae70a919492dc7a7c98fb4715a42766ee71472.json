{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/es.number.is-finite.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner, getDataItemValue, queryReferringComponents, SINGLE_REFERRING } from '../../util/model.js';\nimport { createHashMap, each, isArray, isString, isObject, isTypedArray } from 'zrender/lib/core/util.js';\nimport { SOURCE_FORMAT_ORIGINAL, SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS, SERIES_LAYOUT_BY_ROW, SOURCE_FORMAT_KEYED_COLUMNS } from '../../util/types.js';\n// The result of `guessOrdinal`.\nexport var BE_ORDINAL = {\n  Must: 1,\n  Might: 2,\n  Not: 3 // Other cases\n};\nvar innerGlobalModel = makeInner();\n/**\n * MUST be called before mergeOption of all series.\n */\nexport function resetSourceDefaulter(ecModel) {\n  // `datasetMap` is used to make default encode.\n  innerGlobalModel(ecModel).datasetMap = createHashMap();\n}\n/**\n * [The strategy of the arrengment of data dimensions for dataset]:\n * \"value way\": all axes are non-category axes. So series one by one take\n *     several (the number is coordSysDims.length) dimensions from dataset.\n *     The result of data arrengment of data dimensions like:\n *     | ser0_x | ser0_y | ser1_x | ser1_y | ser2_x | ser2_y |\n * \"category way\": at least one axis is category axis. So the the first data\n *     dimension is always mapped to the first category axis and shared by\n *     all of the series. The other data dimensions are taken by series like\n *     \"value way\" does.\n *     The result of data arrengment of data dimensions like:\n *     | ser_shared_x | ser0_y | ser1_y | ser2_y |\n *\n * @return encode Never be `null/undefined`.\n */\nexport function makeSeriesEncodeForAxisCoordSys(coordDimensions, seriesModel, source) {\n  var encode = {};\n  var datasetModel = querySeriesUpstreamDatasetModel(seriesModel);\n  // Currently only make default when using dataset, util more reqirements occur.\n  if (!datasetModel || !coordDimensions) {\n    return encode;\n  }\n  var encodeItemName = [];\n  var encodeSeriesName = [];\n  var ecModel = seriesModel.ecModel;\n  var datasetMap = innerGlobalModel(ecModel).datasetMap;\n  var key = datasetModel.uid + '_' + source.seriesLayoutBy;\n  var baseCategoryDimIndex;\n  var categoryWayValueDimStart;\n  coordDimensions = coordDimensions.slice();\n  each(coordDimensions, function (coordDimInfoLoose, coordDimIdx) {\n    var coordDimInfo = isObject(coordDimInfoLoose) ? coordDimInfoLoose : coordDimensions[coordDimIdx] = {\n      name: coordDimInfoLoose\n    };\n    if (coordDimInfo.type === 'ordinal' && baseCategoryDimIndex == null) {\n      baseCategoryDimIndex = coordDimIdx;\n      categoryWayValueDimStart = getDataDimCountOnCoordDim(coordDimInfo);\n    }\n    encode[coordDimInfo.name] = [];\n  });\n  var datasetRecord = datasetMap.get(key) || datasetMap.set(key, {\n    categoryWayDim: categoryWayValueDimStart,\n    valueWayDim: 0\n  });\n  // TODO\n  // Auto detect first time axis and do arrangement.\n  each(coordDimensions, function (coordDimInfo, coordDimIdx) {\n    var coordDimName = coordDimInfo.name;\n    var count = getDataDimCountOnCoordDim(coordDimInfo);\n    // In value way.\n    if (baseCategoryDimIndex == null) {\n      var start = datasetRecord.valueWayDim;\n      pushDim(encode[coordDimName], start, count);\n      pushDim(encodeSeriesName, start, count);\n      datasetRecord.valueWayDim += count;\n      // ??? TODO give a better default series name rule?\n      // especially when encode x y specified.\n      // consider: when multiple series share one dimension\n      // category axis, series name should better use\n      // the other dimension name. On the other hand, use\n      // both dimensions name.\n    }\n    // In category way, the first category axis.\n    else if (baseCategoryDimIndex === coordDimIdx) {\n      pushDim(encode[coordDimName], 0, count);\n      pushDim(encodeItemName, 0, count);\n    }\n    // In category way, the other axis.\n    else {\n      var start = datasetRecord.categoryWayDim;\n      pushDim(encode[coordDimName], start, count);\n      pushDim(encodeSeriesName, start, count);\n      datasetRecord.categoryWayDim += count;\n    }\n  });\n  function pushDim(dimIdxArr, idxFrom, idxCount) {\n    for (var i = 0; i < idxCount; i++) {\n      dimIdxArr.push(idxFrom + i);\n    }\n  }\n  function getDataDimCountOnCoordDim(coordDimInfo) {\n    var dimsDef = coordDimInfo.dimsDef;\n    return dimsDef ? dimsDef.length : 1;\n  }\n  encodeItemName.length && (encode.itemName = encodeItemName);\n  encodeSeriesName.length && (encode.seriesName = encodeSeriesName);\n  return encode;\n}\n/**\n * Work for data like [{name: ..., value: ...}, ...].\n *\n * @return encode Never be `null/undefined`.\n */\nexport function makeSeriesEncodeForNameBased(seriesModel, source, dimCount) {\n  var encode = {};\n  var datasetModel = querySeriesUpstreamDatasetModel(seriesModel);\n  // Currently only make default when using dataset, util more reqirements occur.\n  if (!datasetModel) {\n    return encode;\n  }\n  var sourceFormat = source.sourceFormat;\n  var dimensionsDefine = source.dimensionsDefine;\n  var potentialNameDimIndex;\n  if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS || sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    each(dimensionsDefine, function (dim, idx) {\n      if ((isObject(dim) ? dim.name : dim) === 'name') {\n        potentialNameDimIndex = idx;\n      }\n    });\n  }\n  var idxResult = function () {\n    var idxRes0 = {};\n    var idxRes1 = {};\n    var guessRecords = [];\n    // 5 is an experience value.\n    for (var i = 0, len = Math.min(5, dimCount); i < len; i++) {\n      var guessResult = doGuessOrdinal(source.data, sourceFormat, source.seriesLayoutBy, dimensionsDefine, source.startIndex, i);\n      guessRecords.push(guessResult);\n      var isPureNumber = guessResult === BE_ORDINAL.Not;\n      // [Strategy of idxRes0]: find the first BE_ORDINAL.Not as the value dim,\n      // and then find a name dim with the priority:\n      // \"BE_ORDINAL.Might|BE_ORDINAL.Must\" > \"other dim\" > \"the value dim itself\".\n      if (isPureNumber && idxRes0.v == null && i !== potentialNameDimIndex) {\n        idxRes0.v = i;\n      }\n      if (idxRes0.n == null || idxRes0.n === idxRes0.v || !isPureNumber && guessRecords[idxRes0.n] === BE_ORDINAL.Not) {\n        idxRes0.n = i;\n      }\n      if (fulfilled(idxRes0) && guessRecords[idxRes0.n] !== BE_ORDINAL.Not) {\n        return idxRes0;\n      }\n      // [Strategy of idxRes1]: if idxRes0 not satisfied (that is, no BE_ORDINAL.Not),\n      // find the first BE_ORDINAL.Might as the value dim,\n      // and then find a name dim with the priority:\n      // \"other dim\" > \"the value dim itself\".\n      // That is for backward compat: number-like (e.g., `'3'`, `'55'`) can be\n      // treated as number.\n      if (!isPureNumber) {\n        if (guessResult === BE_ORDINAL.Might && idxRes1.v == null && i !== potentialNameDimIndex) {\n          idxRes1.v = i;\n        }\n        if (idxRes1.n == null || idxRes1.n === idxRes1.v) {\n          idxRes1.n = i;\n        }\n      }\n    }\n    function fulfilled(idxResult) {\n      return idxResult.v != null && idxResult.n != null;\n    }\n    return fulfilled(idxRes0) ? idxRes0 : fulfilled(idxRes1) ? idxRes1 : null;\n  }();\n  if (idxResult) {\n    encode.value = [idxResult.v];\n    // `potentialNameDimIndex` has highest priority.\n    var nameDimIndex = potentialNameDimIndex != null ? potentialNameDimIndex : idxResult.n;\n    // By default, label uses itemName in charts.\n    // So we don't set encodeLabel here.\n    encode.itemName = [nameDimIndex];\n    encode.seriesName = [nameDimIndex];\n  }\n  return encode;\n}\n/**\n * @return If return null/undefined, indicate that should not use datasetModel.\n */\nexport function querySeriesUpstreamDatasetModel(seriesModel) {\n  // Caution: consider the scenario:\n  // A dataset is declared and a series is not expected to use the dataset,\n  // and at the beginning `setOption({series: { noData })` (just prepare other\n  // option but no data), then `setOption({series: {data: [...]}); In this case,\n  // the user should set an empty array to avoid that dataset is used by default.\n  var thisData = seriesModel.get('data', true);\n  if (!thisData) {\n    return queryReferringComponents(seriesModel.ecModel, 'dataset', {\n      index: seriesModel.get('datasetIndex', true),\n      id: seriesModel.get('datasetId', true)\n    }, SINGLE_REFERRING).models[0];\n  }\n}\n/**\n * @return Always return an array event empty.\n */\nexport function queryDatasetUpstreamDatasetModels(datasetModel) {\n  // Only these attributes declared, we by default reference to `datasetIndex: 0`.\n  // Otherwise, no reference.\n  if (!datasetModel.get('transform', true) && !datasetModel.get('fromTransformResult', true)) {\n    return [];\n  }\n  return queryReferringComponents(datasetModel.ecModel, 'dataset', {\n    index: datasetModel.get('fromDatasetIndex', true),\n    id: datasetModel.get('fromDatasetId', true)\n  }, SINGLE_REFERRING).models;\n}\n/**\n * The rule should not be complex, otherwise user might not\n * be able to known where the data is wrong.\n * The code is ugly, but how to make it neat?\n */\nexport function guessOrdinal(source, dimIndex) {\n  return doGuessOrdinal(source.data, source.sourceFormat, source.seriesLayoutBy, source.dimensionsDefine, source.startIndex, dimIndex);\n}\n// dimIndex may be overflow source data.\n// return {BE_ORDINAL}\nfunction doGuessOrdinal(data, sourceFormat, seriesLayoutBy, dimensionsDefine, startIndex, dimIndex) {\n  var result;\n  // Experience value.\n  var maxLoop = 5;\n  if (isTypedArray(data)) {\n    return BE_ORDINAL.Not;\n  }\n  // When sourceType is 'objectRows' or 'keyedColumns', dimensionsDefine\n  // always exists in source.\n  var dimName;\n  var dimType;\n  if (dimensionsDefine) {\n    var dimDefItem = dimensionsDefine[dimIndex];\n    if (isObject(dimDefItem)) {\n      dimName = dimDefItem.name;\n      dimType = dimDefItem.type;\n    } else if (isString(dimDefItem)) {\n      dimName = dimDefItem;\n    }\n  }\n  if (dimType != null) {\n    return dimType === 'ordinal' ? BE_ORDINAL.Must : BE_ORDINAL.Not;\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var dataArrayRows = data;\n    if (seriesLayoutBy === SERIES_LAYOUT_BY_ROW) {\n      var sample = dataArrayRows[dimIndex];\n      for (var i = 0; i < (sample || []).length && i < maxLoop; i++) {\n        if ((result = detectValue(sample[startIndex + i])) != null) {\n          return result;\n        }\n      }\n    } else {\n      for (var i = 0; i < dataArrayRows.length && i < maxLoop; i++) {\n        var row = dataArrayRows[startIndex + i];\n        if (row && (result = detectValue(row[dimIndex])) != null) {\n          return result;\n        }\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    var dataObjectRows = data;\n    if (!dimName) {\n      return BE_ORDINAL.Not;\n    }\n    for (var i = 0; i < dataObjectRows.length && i < maxLoop; i++) {\n      var item = dataObjectRows[i];\n      if (item && (result = detectValue(item[dimName])) != null) {\n        return result;\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    var dataKeyedColumns = data;\n    if (!dimName) {\n      return BE_ORDINAL.Not;\n    }\n    var sample = dataKeyedColumns[dimName];\n    if (!sample || isTypedArray(sample)) {\n      return BE_ORDINAL.Not;\n    }\n    for (var i = 0; i < sample.length && i < maxLoop; i++) {\n      if ((result = detectValue(sample[i])) != null) {\n        return result;\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var dataOriginal = data;\n    for (var i = 0; i < dataOriginal.length && i < maxLoop; i++) {\n      var item = dataOriginal[i];\n      var val = getDataItemValue(item);\n      if (!isArray(val)) {\n        return BE_ORDINAL.Not;\n      }\n      if ((result = detectValue(val[dimIndex])) != null) {\n        return result;\n      }\n    }\n  }\n  function detectValue(val) {\n    var beStr = isString(val);\n    // Consider usage convenience, '1', '2' will be treated as \"number\".\n    // `Number('')` (or any whitespace) is `0`.\n    if (val != null && Number.isFinite(Number(val)) && val !== '') {\n      return beStr ? BE_ORDINAL.Might : BE_ORDINAL.Not;\n    } else if (beStr && val !== '-') {\n      return BE_ORDINAL.Must;\n    }\n  }\n  return BE_ORDINAL.Not;\n}", "map": {"version": 3, "names": ["makeInner", "getDataItemValue", "queryReferringComponents", "SINGLE_REFERRING", "createHashMap", "each", "isArray", "isString", "isObject", "isTypedArray", "SOURCE_FORMAT_ORIGINAL", "SOURCE_FORMAT_ARRAY_ROWS", "SOURCE_FORMAT_OBJECT_ROWS", "SERIES_LAYOUT_BY_ROW", "SOURCE_FORMAT_KEYED_COLUMNS", "BE_ORDINAL", "Must", "Might", "Not", "innerGlobalModel", "resetSourceDefaulter", "ecModel", "datasetMap", "makeSeriesEncodeForAxisCoordSys", "coordDimensions", "seriesModel", "source", "encode", "datasetModel", "querySeriesUpstreamDatasetModel", "encodeItemName", "encodeSeriesName", "key", "uid", "seriesLayoutBy", "baseCategoryDimIndex", "categoryWayValueDimStart", "slice", "coordDimInfoLoose", "coordDimIdx", "coordDimInfo", "name", "type", "getDataDimCountOnCoordDim", "datasetRecord", "get", "set", "categoryWayDim", "valueWayDim", "coordDimName", "count", "start", "pushDim", "dimIdxArr", "idxFrom", "idxCount", "i", "push", "dimsDef", "length", "itemName", "seriesName", "makeSeriesEncodeForNameBased", "dimCount", "sourceFormat", "dimensionsDefine", "potentialNameDimIndex", "dim", "idx", "idxResult", "idxRes0", "idxRes1", "guessRecords", "len", "Math", "min", "guessResult", "doGuessOrdinal", "data", "startIndex", "isPureNumber", "v", "n", "fulfilled", "value", "nameDimIndex", "thisData", "index", "id", "models", "queryDatasetUpstreamDatasetModels", "guessOrdinal", "dimIndex", "result", "maxL<PERSON>", "dimName", "dimType", "dimDefItem", "dataArrayRows", "sample", "detectValue", "row", "dataObjectRows", "item", "dataKeyedColumns", "dataOriginal", "val", "beStr", "Number", "isFinite"], "sources": ["G:/备份9/adminweb/node_modules/echarts/lib/data/helper/sourceHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner, getDataItemValue, queryReferringComponents, SINGLE_REFERRING } from '../../util/model.js';\nimport { createHashMap, each, isArray, isString, isObject, isTypedArray } from 'zrender/lib/core/util.js';\nimport { SOURCE_FORMAT_ORIGINAL, SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS, SERIES_LAYOUT_BY_ROW, SOURCE_FORMAT_KEYED_COLUMNS } from '../../util/types.js';\n// The result of `guessOrdinal`.\nexport var BE_ORDINAL = {\n  Must: 1,\n  Might: 2,\n  Not: 3 // Other cases\n};\n\nvar innerGlobalModel = makeInner();\n/**\n * MUST be called before mergeOption of all series.\n */\nexport function resetSourceDefaulter(ecModel) {\n  // `datasetMap` is used to make default encode.\n  innerGlobalModel(ecModel).datasetMap = createHashMap();\n}\n/**\n * [The strategy of the arrengment of data dimensions for dataset]:\n * \"value way\": all axes are non-category axes. So series one by one take\n *     several (the number is coordSysDims.length) dimensions from dataset.\n *     The result of data arrengment of data dimensions like:\n *     | ser0_x | ser0_y | ser1_x | ser1_y | ser2_x | ser2_y |\n * \"category way\": at least one axis is category axis. So the the first data\n *     dimension is always mapped to the first category axis and shared by\n *     all of the series. The other data dimensions are taken by series like\n *     \"value way\" does.\n *     The result of data arrengment of data dimensions like:\n *     | ser_shared_x | ser0_y | ser1_y | ser2_y |\n *\n * @return encode Never be `null/undefined`.\n */\nexport function makeSeriesEncodeForAxisCoordSys(coordDimensions, seriesModel, source) {\n  var encode = {};\n  var datasetModel = querySeriesUpstreamDatasetModel(seriesModel);\n  // Currently only make default when using dataset, util more reqirements occur.\n  if (!datasetModel || !coordDimensions) {\n    return encode;\n  }\n  var encodeItemName = [];\n  var encodeSeriesName = [];\n  var ecModel = seriesModel.ecModel;\n  var datasetMap = innerGlobalModel(ecModel).datasetMap;\n  var key = datasetModel.uid + '_' + source.seriesLayoutBy;\n  var baseCategoryDimIndex;\n  var categoryWayValueDimStart;\n  coordDimensions = coordDimensions.slice();\n  each(coordDimensions, function (coordDimInfoLoose, coordDimIdx) {\n    var coordDimInfo = isObject(coordDimInfoLoose) ? coordDimInfoLoose : coordDimensions[coordDimIdx] = {\n      name: coordDimInfoLoose\n    };\n    if (coordDimInfo.type === 'ordinal' && baseCategoryDimIndex == null) {\n      baseCategoryDimIndex = coordDimIdx;\n      categoryWayValueDimStart = getDataDimCountOnCoordDim(coordDimInfo);\n    }\n    encode[coordDimInfo.name] = [];\n  });\n  var datasetRecord = datasetMap.get(key) || datasetMap.set(key, {\n    categoryWayDim: categoryWayValueDimStart,\n    valueWayDim: 0\n  });\n  // TODO\n  // Auto detect first time axis and do arrangement.\n  each(coordDimensions, function (coordDimInfo, coordDimIdx) {\n    var coordDimName = coordDimInfo.name;\n    var count = getDataDimCountOnCoordDim(coordDimInfo);\n    // In value way.\n    if (baseCategoryDimIndex == null) {\n      var start = datasetRecord.valueWayDim;\n      pushDim(encode[coordDimName], start, count);\n      pushDim(encodeSeriesName, start, count);\n      datasetRecord.valueWayDim += count;\n      // ??? TODO give a better default series name rule?\n      // especially when encode x y specified.\n      // consider: when multiple series share one dimension\n      // category axis, series name should better use\n      // the other dimension name. On the other hand, use\n      // both dimensions name.\n    }\n    // In category way, the first category axis.\n    else if (baseCategoryDimIndex === coordDimIdx) {\n      pushDim(encode[coordDimName], 0, count);\n      pushDim(encodeItemName, 0, count);\n    }\n    // In category way, the other axis.\n    else {\n      var start = datasetRecord.categoryWayDim;\n      pushDim(encode[coordDimName], start, count);\n      pushDim(encodeSeriesName, start, count);\n      datasetRecord.categoryWayDim += count;\n    }\n  });\n  function pushDim(dimIdxArr, idxFrom, idxCount) {\n    for (var i = 0; i < idxCount; i++) {\n      dimIdxArr.push(idxFrom + i);\n    }\n  }\n  function getDataDimCountOnCoordDim(coordDimInfo) {\n    var dimsDef = coordDimInfo.dimsDef;\n    return dimsDef ? dimsDef.length : 1;\n  }\n  encodeItemName.length && (encode.itemName = encodeItemName);\n  encodeSeriesName.length && (encode.seriesName = encodeSeriesName);\n  return encode;\n}\n/**\n * Work for data like [{name: ..., value: ...}, ...].\n *\n * @return encode Never be `null/undefined`.\n */\nexport function makeSeriesEncodeForNameBased(seriesModel, source, dimCount) {\n  var encode = {};\n  var datasetModel = querySeriesUpstreamDatasetModel(seriesModel);\n  // Currently only make default when using dataset, util more reqirements occur.\n  if (!datasetModel) {\n    return encode;\n  }\n  var sourceFormat = source.sourceFormat;\n  var dimensionsDefine = source.dimensionsDefine;\n  var potentialNameDimIndex;\n  if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS || sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    each(dimensionsDefine, function (dim, idx) {\n      if ((isObject(dim) ? dim.name : dim) === 'name') {\n        potentialNameDimIndex = idx;\n      }\n    });\n  }\n  var idxResult = function () {\n    var idxRes0 = {};\n    var idxRes1 = {};\n    var guessRecords = [];\n    // 5 is an experience value.\n    for (var i = 0, len = Math.min(5, dimCount); i < len; i++) {\n      var guessResult = doGuessOrdinal(source.data, sourceFormat, source.seriesLayoutBy, dimensionsDefine, source.startIndex, i);\n      guessRecords.push(guessResult);\n      var isPureNumber = guessResult === BE_ORDINAL.Not;\n      // [Strategy of idxRes0]: find the first BE_ORDINAL.Not as the value dim,\n      // and then find a name dim with the priority:\n      // \"BE_ORDINAL.Might|BE_ORDINAL.Must\" > \"other dim\" > \"the value dim itself\".\n      if (isPureNumber && idxRes0.v == null && i !== potentialNameDimIndex) {\n        idxRes0.v = i;\n      }\n      if (idxRes0.n == null || idxRes0.n === idxRes0.v || !isPureNumber && guessRecords[idxRes0.n] === BE_ORDINAL.Not) {\n        idxRes0.n = i;\n      }\n      if (fulfilled(idxRes0) && guessRecords[idxRes0.n] !== BE_ORDINAL.Not) {\n        return idxRes0;\n      }\n      // [Strategy of idxRes1]: if idxRes0 not satisfied (that is, no BE_ORDINAL.Not),\n      // find the first BE_ORDINAL.Might as the value dim,\n      // and then find a name dim with the priority:\n      // \"other dim\" > \"the value dim itself\".\n      // That is for backward compat: number-like (e.g., `'3'`, `'55'`) can be\n      // treated as number.\n      if (!isPureNumber) {\n        if (guessResult === BE_ORDINAL.Might && idxRes1.v == null && i !== potentialNameDimIndex) {\n          idxRes1.v = i;\n        }\n        if (idxRes1.n == null || idxRes1.n === idxRes1.v) {\n          idxRes1.n = i;\n        }\n      }\n    }\n    function fulfilled(idxResult) {\n      return idxResult.v != null && idxResult.n != null;\n    }\n    return fulfilled(idxRes0) ? idxRes0 : fulfilled(idxRes1) ? idxRes1 : null;\n  }();\n  if (idxResult) {\n    encode.value = [idxResult.v];\n    // `potentialNameDimIndex` has highest priority.\n    var nameDimIndex = potentialNameDimIndex != null ? potentialNameDimIndex : idxResult.n;\n    // By default, label uses itemName in charts.\n    // So we don't set encodeLabel here.\n    encode.itemName = [nameDimIndex];\n    encode.seriesName = [nameDimIndex];\n  }\n  return encode;\n}\n/**\n * @return If return null/undefined, indicate that should not use datasetModel.\n */\nexport function querySeriesUpstreamDatasetModel(seriesModel) {\n  // Caution: consider the scenario:\n  // A dataset is declared and a series is not expected to use the dataset,\n  // and at the beginning `setOption({series: { noData })` (just prepare other\n  // option but no data), then `setOption({series: {data: [...]}); In this case,\n  // the user should set an empty array to avoid that dataset is used by default.\n  var thisData = seriesModel.get('data', true);\n  if (!thisData) {\n    return queryReferringComponents(seriesModel.ecModel, 'dataset', {\n      index: seriesModel.get('datasetIndex', true),\n      id: seriesModel.get('datasetId', true)\n    }, SINGLE_REFERRING).models[0];\n  }\n}\n/**\n * @return Always return an array event empty.\n */\nexport function queryDatasetUpstreamDatasetModels(datasetModel) {\n  // Only these attributes declared, we by default reference to `datasetIndex: 0`.\n  // Otherwise, no reference.\n  if (!datasetModel.get('transform', true) && !datasetModel.get('fromTransformResult', true)) {\n    return [];\n  }\n  return queryReferringComponents(datasetModel.ecModel, 'dataset', {\n    index: datasetModel.get('fromDatasetIndex', true),\n    id: datasetModel.get('fromDatasetId', true)\n  }, SINGLE_REFERRING).models;\n}\n/**\n * The rule should not be complex, otherwise user might not\n * be able to known where the data is wrong.\n * The code is ugly, but how to make it neat?\n */\nexport function guessOrdinal(source, dimIndex) {\n  return doGuessOrdinal(source.data, source.sourceFormat, source.seriesLayoutBy, source.dimensionsDefine, source.startIndex, dimIndex);\n}\n// dimIndex may be overflow source data.\n// return {BE_ORDINAL}\nfunction doGuessOrdinal(data, sourceFormat, seriesLayoutBy, dimensionsDefine, startIndex, dimIndex) {\n  var result;\n  // Experience value.\n  var maxLoop = 5;\n  if (isTypedArray(data)) {\n    return BE_ORDINAL.Not;\n  }\n  // When sourceType is 'objectRows' or 'keyedColumns', dimensionsDefine\n  // always exists in source.\n  var dimName;\n  var dimType;\n  if (dimensionsDefine) {\n    var dimDefItem = dimensionsDefine[dimIndex];\n    if (isObject(dimDefItem)) {\n      dimName = dimDefItem.name;\n      dimType = dimDefItem.type;\n    } else if (isString(dimDefItem)) {\n      dimName = dimDefItem;\n    }\n  }\n  if (dimType != null) {\n    return dimType === 'ordinal' ? BE_ORDINAL.Must : BE_ORDINAL.Not;\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var dataArrayRows = data;\n    if (seriesLayoutBy === SERIES_LAYOUT_BY_ROW) {\n      var sample = dataArrayRows[dimIndex];\n      for (var i = 0; i < (sample || []).length && i < maxLoop; i++) {\n        if ((result = detectValue(sample[startIndex + i])) != null) {\n          return result;\n        }\n      }\n    } else {\n      for (var i = 0; i < dataArrayRows.length && i < maxLoop; i++) {\n        var row = dataArrayRows[startIndex + i];\n        if (row && (result = detectValue(row[dimIndex])) != null) {\n          return result;\n        }\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    var dataObjectRows = data;\n    if (!dimName) {\n      return BE_ORDINAL.Not;\n    }\n    for (var i = 0; i < dataObjectRows.length && i < maxLoop; i++) {\n      var item = dataObjectRows[i];\n      if (item && (result = detectValue(item[dimName])) != null) {\n        return result;\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    var dataKeyedColumns = data;\n    if (!dimName) {\n      return BE_ORDINAL.Not;\n    }\n    var sample = dataKeyedColumns[dimName];\n    if (!sample || isTypedArray(sample)) {\n      return BE_ORDINAL.Not;\n    }\n    for (var i = 0; i < sample.length && i < maxLoop; i++) {\n      if ((result = detectValue(sample[i])) != null) {\n        return result;\n      }\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var dataOriginal = data;\n    for (var i = 0; i < dataOriginal.length && i < maxLoop; i++) {\n      var item = dataOriginal[i];\n      var val = getDataItemValue(item);\n      if (!isArray(val)) {\n        return BE_ORDINAL.Not;\n      }\n      if ((result = detectValue(val[dimIndex])) != null) {\n        return result;\n      }\n    }\n  }\n  function detectValue(val) {\n    var beStr = isString(val);\n    // Consider usage convenience, '1', '2' will be treated as \"number\".\n    // `Number('')` (or any whitespace) is `0`.\n    if (val != null && Number.isFinite(Number(val)) && val !== '') {\n      return beStr ? BE_ORDINAL.Might : BE_ORDINAL.Not;\n    } else if (beStr && val !== '-') {\n      return BE_ORDINAL.Must;\n    }\n  }\n  return BE_ORDINAL.Not;\n}"], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,gBAAgB,EAAEC,wBAAwB,EAAEC,gBAAgB,QAAQ,qBAAqB;AAC7G,SAASC,aAAa,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,0BAA0B;AACzG,SAASC,sBAAsB,EAAEC,wBAAwB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAEC,2BAA2B,QAAQ,qBAAqB;AACpK;AACA,OAAO,IAAIC,UAAU,GAAG;EACtBC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC,CAAC;AACT,CAAC;AAED,IAAIC,gBAAgB,GAAGnB,SAAS,CAAC,CAAC;AAClC;AACA;AACA;AACA,OAAO,SAASoB,oBAAoBA,CAACC,OAAO,EAAE;EAC5C;EACAF,gBAAgB,CAACE,OAAO,CAAC,CAACC,UAAU,GAAGlB,aAAa,CAAC,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmB,+BAA+BA,CAACC,eAAe,EAAEC,WAAW,EAAEC,MAAM,EAAE;EACpF,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,YAAY,GAAGC,+BAA+B,CAACJ,WAAW,CAAC;EAC/D;EACA,IAAI,CAACG,YAAY,IAAI,CAACJ,eAAe,EAAE;IACrC,OAAOG,MAAM;EACf;EACA,IAAIG,cAAc,GAAG,EAAE;EACvB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIV,OAAO,GAAGI,WAAW,CAACJ,OAAO;EACjC,IAAIC,UAAU,GAAGH,gBAAgB,CAACE,OAAO,CAAC,CAACC,UAAU;EACrD,IAAIU,GAAG,GAAGJ,YAAY,CAACK,GAAG,GAAG,GAAG,GAAGP,MAAM,CAACQ,cAAc;EACxD,IAAIC,oBAAoB;EACxB,IAAIC,wBAAwB;EAC5BZ,eAAe,GAAGA,eAAe,CAACa,KAAK,CAAC,CAAC;EACzChC,IAAI,CAACmB,eAAe,EAAE,UAAUc,iBAAiB,EAAEC,WAAW,EAAE;IAC9D,IAAIC,YAAY,GAAGhC,QAAQ,CAAC8B,iBAAiB,CAAC,GAAGA,iBAAiB,GAAGd,eAAe,CAACe,WAAW,CAAC,GAAG;MAClGE,IAAI,EAAEH;IACR,CAAC;IACD,IAAIE,YAAY,CAACE,IAAI,KAAK,SAAS,IAAIP,oBAAoB,IAAI,IAAI,EAAE;MACnEA,oBAAoB,GAAGI,WAAW;MAClCH,wBAAwB,GAAGO,yBAAyB,CAACH,YAAY,CAAC;IACpE;IACAb,MAAM,CAACa,YAAY,CAACC,IAAI,CAAC,GAAG,EAAE;EAChC,CAAC,CAAC;EACF,IAAIG,aAAa,GAAGtB,UAAU,CAACuB,GAAG,CAACb,GAAG,CAAC,IAAIV,UAAU,CAACwB,GAAG,CAACd,GAAG,EAAE;IAC7De,cAAc,EAAEX,wBAAwB;IACxCY,WAAW,EAAE;EACf,CAAC,CAAC;EACF;EACA;EACA3C,IAAI,CAACmB,eAAe,EAAE,UAAUgB,YAAY,EAAED,WAAW,EAAE;IACzD,IAAIU,YAAY,GAAGT,YAAY,CAACC,IAAI;IACpC,IAAIS,KAAK,GAAGP,yBAAyB,CAACH,YAAY,CAAC;IACnD;IACA,IAAIL,oBAAoB,IAAI,IAAI,EAAE;MAChC,IAAIgB,KAAK,GAAGP,aAAa,CAACI,WAAW;MACrCI,OAAO,CAACzB,MAAM,CAACsB,YAAY,CAAC,EAAEE,KAAK,EAAED,KAAK,CAAC;MAC3CE,OAAO,CAACrB,gBAAgB,EAAEoB,KAAK,EAAED,KAAK,CAAC;MACvCN,aAAa,CAACI,WAAW,IAAIE,KAAK;MAClC;MACA;MACA;MACA;MACA;MACA;IACF;IACA;IAAA,KACK,IAAIf,oBAAoB,KAAKI,WAAW,EAAE;MAC7Ca,OAAO,CAACzB,MAAM,CAACsB,YAAY,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC;MACvCE,OAAO,CAACtB,cAAc,EAAE,CAAC,EAAEoB,KAAK,CAAC;IACnC;IACA;IAAA,KACK;MACH,IAAIC,KAAK,GAAGP,aAAa,CAACG,cAAc;MACxCK,OAAO,CAACzB,MAAM,CAACsB,YAAY,CAAC,EAAEE,KAAK,EAAED,KAAK,CAAC;MAC3CE,OAAO,CAACrB,gBAAgB,EAAEoB,KAAK,EAAED,KAAK,CAAC;MACvCN,aAAa,CAACG,cAAc,IAAIG,KAAK;IACvC;EACF,CAAC,CAAC;EACF,SAASE,OAAOA,CAACC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;MACjCH,SAAS,CAACI,IAAI,CAACH,OAAO,GAAGE,CAAC,CAAC;IAC7B;EACF;EACA,SAASb,yBAAyBA,CAACH,YAAY,EAAE;IAC/C,IAAIkB,OAAO,GAAGlB,YAAY,CAACkB,OAAO;IAClC,OAAOA,OAAO,GAAGA,OAAO,CAACC,MAAM,GAAG,CAAC;EACrC;EACA7B,cAAc,CAAC6B,MAAM,KAAKhC,MAAM,CAACiC,QAAQ,GAAG9B,cAAc,CAAC;EAC3DC,gBAAgB,CAAC4B,MAAM,KAAKhC,MAAM,CAACkC,UAAU,GAAG9B,gBAAgB,CAAC;EACjE,OAAOJ,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmC,4BAA4BA,CAACrC,WAAW,EAAEC,MAAM,EAAEqC,QAAQ,EAAE;EAC1E,IAAIpC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,YAAY,GAAGC,+BAA+B,CAACJ,WAAW,CAAC;EAC/D;EACA,IAAI,CAACG,YAAY,EAAE;IACjB,OAAOD,MAAM;EACf;EACA,IAAIqC,YAAY,GAAGtC,MAAM,CAACsC,YAAY;EACtC,IAAIC,gBAAgB,GAAGvC,MAAM,CAACuC,gBAAgB;EAC9C,IAAIC,qBAAqB;EACzB,IAAIF,YAAY,KAAKpD,yBAAyB,IAAIoD,YAAY,KAAKlD,2BAA2B,EAAE;IAC9FT,IAAI,CAAC4D,gBAAgB,EAAE,UAAUE,GAAG,EAAEC,GAAG,EAAE;MACzC,IAAI,CAAC5D,QAAQ,CAAC2D,GAAG,CAAC,GAAGA,GAAG,CAAC1B,IAAI,GAAG0B,GAAG,MAAM,MAAM,EAAE;QAC/CD,qBAAqB,GAAGE,GAAG;MAC7B;IACF,CAAC,CAAC;EACJ;EACA,IAAIC,SAAS,GAAG,YAAY;IAC1B,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,YAAY,GAAG,EAAE;IACrB;IACA,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEiB,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEZ,QAAQ,CAAC,EAAEP,CAAC,GAAGiB,GAAG,EAAEjB,CAAC,EAAE,EAAE;MACzD,IAAIoB,WAAW,GAAGC,cAAc,CAACnD,MAAM,CAACoD,IAAI,EAAEd,YAAY,EAAEtC,MAAM,CAACQ,cAAc,EAAE+B,gBAAgB,EAAEvC,MAAM,CAACqD,UAAU,EAAEvB,CAAC,CAAC;MAC1HgB,YAAY,CAACf,IAAI,CAACmB,WAAW,CAAC;MAC9B,IAAII,YAAY,GAAGJ,WAAW,KAAK7D,UAAU,CAACG,GAAG;MACjD;MACA;MACA;MACA,IAAI8D,YAAY,IAAIV,OAAO,CAACW,CAAC,IAAI,IAAI,IAAIzB,CAAC,KAAKU,qBAAqB,EAAE;QACpEI,OAAO,CAACW,CAAC,GAAGzB,CAAC;MACf;MACA,IAAIc,OAAO,CAACY,CAAC,IAAI,IAAI,IAAIZ,OAAO,CAACY,CAAC,KAAKZ,OAAO,CAACW,CAAC,IAAI,CAACD,YAAY,IAAIR,YAAY,CAACF,OAAO,CAACY,CAAC,CAAC,KAAKnE,UAAU,CAACG,GAAG,EAAE;QAC/GoD,OAAO,CAACY,CAAC,GAAG1B,CAAC;MACf;MACA,IAAI2B,SAAS,CAACb,OAAO,CAAC,IAAIE,YAAY,CAACF,OAAO,CAACY,CAAC,CAAC,KAAKnE,UAAU,CAACG,GAAG,EAAE;QACpE,OAAOoD,OAAO;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACU,YAAY,EAAE;QACjB,IAAIJ,WAAW,KAAK7D,UAAU,CAACE,KAAK,IAAIsD,OAAO,CAACU,CAAC,IAAI,IAAI,IAAIzB,CAAC,KAAKU,qBAAqB,EAAE;UACxFK,OAAO,CAACU,CAAC,GAAGzB,CAAC;QACf;QACA,IAAIe,OAAO,CAACW,CAAC,IAAI,IAAI,IAAIX,OAAO,CAACW,CAAC,KAAKX,OAAO,CAACU,CAAC,EAAE;UAChDV,OAAO,CAACW,CAAC,GAAG1B,CAAC;QACf;MACF;IACF;IACA,SAAS2B,SAASA,CAACd,SAAS,EAAE;MAC5B,OAAOA,SAAS,CAACY,CAAC,IAAI,IAAI,IAAIZ,SAAS,CAACa,CAAC,IAAI,IAAI;IACnD;IACA,OAAOC,SAAS,CAACb,OAAO,CAAC,GAAGA,OAAO,GAAGa,SAAS,CAACZ,OAAO,CAAC,GAAGA,OAAO,GAAG,IAAI;EAC3E,CAAC,CAAC,CAAC;EACH,IAAIF,SAAS,EAAE;IACb1C,MAAM,CAACyD,KAAK,GAAG,CAACf,SAAS,CAACY,CAAC,CAAC;IAC5B;IACA,IAAII,YAAY,GAAGnB,qBAAqB,IAAI,IAAI,GAAGA,qBAAqB,GAAGG,SAAS,CAACa,CAAC;IACtF;IACA;IACAvD,MAAM,CAACiC,QAAQ,GAAG,CAACyB,YAAY,CAAC;IAChC1D,MAAM,CAACkC,UAAU,GAAG,CAACwB,YAAY,CAAC;EACpC;EACA,OAAO1D,MAAM;AACf;AACA;AACA;AACA;AACA,OAAO,SAASE,+BAA+BA,CAACJ,WAAW,EAAE;EAC3D;EACA;EACA;EACA;EACA;EACA,IAAI6D,QAAQ,GAAG7D,WAAW,CAACoB,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;EAC5C,IAAI,CAACyC,QAAQ,EAAE;IACb,OAAOpF,wBAAwB,CAACuB,WAAW,CAACJ,OAAO,EAAE,SAAS,EAAE;MAC9DkE,KAAK,EAAE9D,WAAW,CAACoB,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;MAC5C2C,EAAE,EAAE/D,WAAW,CAACoB,GAAG,CAAC,WAAW,EAAE,IAAI;IACvC,CAAC,EAAE1C,gBAAgB,CAAC,CAACsF,MAAM,CAAC,CAAC,CAAC;EAChC;AACF;AACA;AACA;AACA;AACA,OAAO,SAASC,iCAAiCA,CAAC9D,YAAY,EAAE;EAC9D;EACA;EACA,IAAI,CAACA,YAAY,CAACiB,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAACjB,YAAY,CAACiB,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,EAAE;IAC1F,OAAO,EAAE;EACX;EACA,OAAO3C,wBAAwB,CAAC0B,YAAY,CAACP,OAAO,EAAE,SAAS,EAAE;IAC/DkE,KAAK,EAAE3D,YAAY,CAACiB,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC;IACjD2C,EAAE,EAAE5D,YAAY,CAACiB,GAAG,CAAC,eAAe,EAAE,IAAI;EAC5C,CAAC,EAAE1C,gBAAgB,CAAC,CAACsF,MAAM;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAYA,CAACjE,MAAM,EAAEkE,QAAQ,EAAE;EAC7C,OAAOf,cAAc,CAACnD,MAAM,CAACoD,IAAI,EAAEpD,MAAM,CAACsC,YAAY,EAAEtC,MAAM,CAACQ,cAAc,EAAER,MAAM,CAACuC,gBAAgB,EAAEvC,MAAM,CAACqD,UAAU,EAAEa,QAAQ,CAAC;AACtI;AACA;AACA;AACA,SAASf,cAAcA,CAACC,IAAI,EAAEd,YAAY,EAAE9B,cAAc,EAAE+B,gBAAgB,EAAEc,UAAU,EAAEa,QAAQ,EAAE;EAClG,IAAIC,MAAM;EACV;EACA,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIrF,YAAY,CAACqE,IAAI,CAAC,EAAE;IACtB,OAAO/D,UAAU,CAACG,GAAG;EACvB;EACA;EACA;EACA,IAAI6E,OAAO;EACX,IAAIC,OAAO;EACX,IAAI/B,gBAAgB,EAAE;IACpB,IAAIgC,UAAU,GAAGhC,gBAAgB,CAAC2B,QAAQ,CAAC;IAC3C,IAAIpF,QAAQ,CAACyF,UAAU,CAAC,EAAE;MACxBF,OAAO,GAAGE,UAAU,CAACxD,IAAI;MACzBuD,OAAO,GAAGC,UAAU,CAACvD,IAAI;IAC3B,CAAC,MAAM,IAAInC,QAAQ,CAAC0F,UAAU,CAAC,EAAE;MAC/BF,OAAO,GAAGE,UAAU;IACtB;EACF;EACA,IAAID,OAAO,IAAI,IAAI,EAAE;IACnB,OAAOA,OAAO,KAAK,SAAS,GAAGjF,UAAU,CAACC,IAAI,GAAGD,UAAU,CAACG,GAAG;EACjE;EACA,IAAI8C,YAAY,KAAKrD,wBAAwB,EAAE;IAC7C,IAAIuF,aAAa,GAAGpB,IAAI;IACxB,IAAI5C,cAAc,KAAKrB,oBAAoB,EAAE;MAC3C,IAAIsF,MAAM,GAAGD,aAAa,CAACN,QAAQ,CAAC;MACpC,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC2C,MAAM,IAAI,EAAE,EAAExC,MAAM,IAAIH,CAAC,GAAGsC,OAAO,EAAEtC,CAAC,EAAE,EAAE;QAC7D,IAAI,CAACqC,MAAM,GAAGO,WAAW,CAACD,MAAM,CAACpB,UAAU,GAAGvB,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;UAC1D,OAAOqC,MAAM;QACf;MACF;IACF,CAAC,MAAM;MACL,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,aAAa,CAACvC,MAAM,IAAIH,CAAC,GAAGsC,OAAO,EAAEtC,CAAC,EAAE,EAAE;QAC5D,IAAI6C,GAAG,GAAGH,aAAa,CAACnB,UAAU,GAAGvB,CAAC,CAAC;QACvC,IAAI6C,GAAG,IAAI,CAACR,MAAM,GAAGO,WAAW,CAACC,GAAG,CAACT,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE;UACxD,OAAOC,MAAM;QACf;MACF;IACF;EACF,CAAC,MAAM,IAAI7B,YAAY,KAAKpD,yBAAyB,EAAE;IACrD,IAAI0F,cAAc,GAAGxB,IAAI;IACzB,IAAI,CAACiB,OAAO,EAAE;MACZ,OAAOhF,UAAU,CAACG,GAAG;IACvB;IACA,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,cAAc,CAAC3C,MAAM,IAAIH,CAAC,GAAGsC,OAAO,EAAEtC,CAAC,EAAE,EAAE;MAC7D,IAAI+C,IAAI,GAAGD,cAAc,CAAC9C,CAAC,CAAC;MAC5B,IAAI+C,IAAI,IAAI,CAACV,MAAM,GAAGO,WAAW,CAACG,IAAI,CAACR,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE;QACzD,OAAOF,MAAM;MACf;IACF;EACF,CAAC,MAAM,IAAI7B,YAAY,KAAKlD,2BAA2B,EAAE;IACvD,IAAI0F,gBAAgB,GAAG1B,IAAI;IAC3B,IAAI,CAACiB,OAAO,EAAE;MACZ,OAAOhF,UAAU,CAACG,GAAG;IACvB;IACA,IAAIiF,MAAM,GAAGK,gBAAgB,CAACT,OAAO,CAAC;IACtC,IAAI,CAACI,MAAM,IAAI1F,YAAY,CAAC0F,MAAM,CAAC,EAAE;MACnC,OAAOpF,UAAU,CAACG,GAAG;IACvB;IACA,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,MAAM,CAACxC,MAAM,IAAIH,CAAC,GAAGsC,OAAO,EAAEtC,CAAC,EAAE,EAAE;MACrD,IAAI,CAACqC,MAAM,GAAGO,WAAW,CAACD,MAAM,CAAC3C,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAC7C,OAAOqC,MAAM;MACf;IACF;EACF,CAAC,MAAM,IAAI7B,YAAY,KAAKtD,sBAAsB,EAAE;IAClD,IAAI+F,YAAY,GAAG3B,IAAI;IACvB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,YAAY,CAAC9C,MAAM,IAAIH,CAAC,GAAGsC,OAAO,EAAEtC,CAAC,EAAE,EAAE;MAC3D,IAAI+C,IAAI,GAAGE,YAAY,CAACjD,CAAC,CAAC;MAC1B,IAAIkD,GAAG,GAAGzG,gBAAgB,CAACsG,IAAI,CAAC;MAChC,IAAI,CAACjG,OAAO,CAACoG,GAAG,CAAC,EAAE;QACjB,OAAO3F,UAAU,CAACG,GAAG;MACvB;MACA,IAAI,CAAC2E,MAAM,GAAGO,WAAW,CAACM,GAAG,CAACd,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE;QACjD,OAAOC,MAAM;MACf;IACF;EACF;EACA,SAASO,WAAWA,CAACM,GAAG,EAAE;IACxB,IAAIC,KAAK,GAAGpG,QAAQ,CAACmG,GAAG,CAAC;IACzB;IACA;IACA,IAAIA,GAAG,IAAI,IAAI,IAAIE,MAAM,CAACC,QAAQ,CAACD,MAAM,CAACF,GAAG,CAAC,CAAC,IAAIA,GAAG,KAAK,EAAE,EAAE;MAC7D,OAAOC,KAAK,GAAG5F,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACG,GAAG;IAClD,CAAC,MAAM,IAAIyF,KAAK,IAAID,GAAG,KAAK,GAAG,EAAE;MAC/B,OAAO3F,UAAU,CAACC,IAAI;IACxB;EACF;EACA,OAAOD,UAAU,CAACG,GAAG;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}