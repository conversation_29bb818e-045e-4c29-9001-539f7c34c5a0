{"ast": null, "code": "import _regeneratorRuntime from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.replace.js\";\nexport default {\n  name: 'UserBankCards',\n  props: {\n    visible: {\n      type: Boolean,\n      \"default\": false\n    },\n    userId: {\n      type: [String, Number],\n      \"default\": ''\n    }\n  },\n  data: function data() {\n    return {\n      loading: false,\n      cardList: []\n    };\n  },\n  watch: {\n    visible: function visible(val) {\n      if (val) {\n        this.getList();\n      }\n    }\n  },\n  methods: {\n    formatBankCard: function formatBankCard(card) {\n      return card ? card.replace(/^(\\d{4})\\d+(\\d{4})$/, '$1 **** **** $2') : '';\n    },\n    formatPhone: function formatPhone(phone) {\n      return phone ? phone.replace(/^(\\d{3})\\d{4}(\\d{4})$/, '$1****$2') : '';\n    },\n    formatIdCard: function formatIdCard(idCard) {\n      return idCard ? idCard.replace(/^(\\d{6})\\d+(\\d{4})$/, '$1********$2') : '';\n    },\n    getList: function getList() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this.loading = true;\n              try {\n                // TODO: 调用获取银行卡列表接口\n                _this.cardList = [{\n                  accountName: '张三',\n                  cardNo: '6222021234567890123',\n                  phone: '***********',\n                  idCard: '110101199001011234',\n                  bankName: '工商银行',\n                  branchName: '���京市朝阳支行'\n                }];\n              } catch (error) {\n                console.error('获取银行卡列表失败:', error);\n                _this.$message.error('获取银行卡列表失败');\n              } finally {\n                _this.loading = false;\n              }\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))();\n    },\n    handleDelete: function handleDelete(row) {\n      var _this2 = this;\n      this.$confirm('确认要删除该银行卡吗？', '警告', {\n        type: 'warning'\n      }).then(function () {\n        // TODO: 调用删除银行卡接口\n        _this2.$message.success('删除成功');\n        _this2.getList();\n      })[\"catch\"](function () {});\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "userId", "String", "Number", "data", "loading", "cardList", "watch", "val", "getList", "methods", "formatBankCard", "card", "replace", "formatPhone", "phone", "formatIdCard", "idCard", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "accountName", "cardNo", "bankName", "branchName", "error", "console", "$message", "stop", "handleDelete", "row", "_this2", "$confirm", "then", "success"], "sources": ["src/views/user/components/BankCards.vue"], "sourcesContent": ["<template>\r\n  <el-dialog title=\"银行卡管理\" :visible.sync=\"visible\" width=\"800px\">\r\n    <el-table :data=\"cardList\" border v-loading=\"loading\">\r\n      <el-table-column label=\"持卡人\" prop=\"accountName\" align=\"center\" />\r\n      <el-table-column label=\"银行卡号\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatBankCard(scope.row.cardNo) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预留手机号\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatPhone(scope.row.phone) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"身份证号\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatIdCard(scope.row.idCard) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"开户行\" prop=\"bankName\" align=\"center\" />\r\n      <el-table-column label=\"支行\" prop=\"branchName\" align=\"center\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <div slot=\"footer\">\r\n      <el-button @click=\"visible = false\">关 闭</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'UserBankCards',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    userId: {\r\n      type: [String, Number],\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      cardList: []\r\n    }\r\n  },\r\n  watch: {\r\n    visible(val) {\r\n      if (val) {\r\n        this.getList()\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    formatBankCard(card) {\r\n      return card ? card.replace(/^(\\d{4})\\d+(\\d{4})$/, '$1 **** **** $2') : ''\r\n    },\r\n    formatPhone(phone) {\r\n      return phone ? phone.replace(/^(\\d{3})\\d{4}(\\d{4})$/, '$1****$2') : ''\r\n    },\r\n    formatIdCard(idCard) {\r\n      return idCard ? idCard.replace(/^(\\d{6})\\d+(\\d{4})$/, '$1********$2') : ''\r\n    },\r\n    async getList() {\r\n      this.loading = true\r\n      try {\r\n        // TODO: 调用获取银行卡列表接口\r\n        this.cardList = [\r\n          {\r\n            accountName: '张三',\r\n            cardNo: '6222021234567890123',\r\n            phone: '***********',\r\n            idCard: '110101199001011234',\r\n            bankName: '工商银行',\r\n            branchName: '���京市朝阳支行'\r\n          }\r\n        ]\r\n      } catch (error) {\r\n        console.error('获取银行卡列表失败:', error)\r\n        this.$message.error('获取银行卡列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('确认要删除该银行卡吗？', '警告', {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // TODO: 调用删除银行卡接口\r\n        this.$message.success('删除成功')\r\n        this.getList()\r\n      }).catch(() => {})\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": ";;;;;AAkCA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACA;IACA;IACAC,MAAA;MACAF,IAAA,GAAAG,MAAA,EAAAC,MAAA;MACA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAT,OAAA,WAAAA,QAAAU,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,OAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,IAAA;MACA,OAAAA,IAAA,GAAAA,IAAA,CAAAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,OAAAA,KAAA,GAAAA,KAAA,CAAAF,OAAA;IACA;IACAG,YAAA,WAAAA,aAAAC,MAAA;MACA,OAAAA,MAAA,GAAAA,MAAA,CAAAJ,OAAA;IACA;IACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAS,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,OAAAF,mBAAA,GAAAG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,KAAA,CAAAb,OAAA;cACA;gBACA;gBACAa,KAAA,CAAAZ,QAAA,IACA;kBACAsB,WAAA;kBACAC,MAAA;kBACAd,KAAA;kBACAE,MAAA;kBACAa,QAAA;kBACAC,UAAA;gBACA,EACA;cACA,SAAAC,KAAA;gBACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;gBACAd,KAAA,CAAAgB,QAAA,CAAAF,KAAA;cACA;gBACAd,KAAA,CAAAb,OAAA;cACA;YAAA;YAAA;cAAA,OAAAoB,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IACA;IACAc,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAxC,IAAA;MACA,GAAAyC,IAAA;QACA;QACAF,MAAA,CAAAJ,QAAA,CAAAO,OAAA;QACAH,MAAA,CAAA7B,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}