{"ast": null, "code": "import { isArray, isNumber, map } from '../core/util.js';\nexport function normalizeLineDash(lineType, lineWidth) {\n  if (!lineType || lineType === 'solid' || !(lineWidth > 0)) {\n    return null;\n  }\n  return lineType === 'dashed' ? [4 * lineWidth, 2 * lineWidth] : lineType === 'dotted' ? [lineWidth] : isNumber(lineType) ? [lineType] : isArray(lineType) ? lineType : null;\n}\nexport function getLineDash(el) {\n  var style = el.style;\n  var lineDash = style.lineDash && style.lineWidth > 0 && normalizeLineDash(style.lineDash, style.lineWidth);\n  var lineDashOffset = style.lineDashOffset;\n  if (lineDash) {\n    var lineScale_1 = style.strokeNoScale && el.getLineScale ? el.getLineScale() : 1;\n    if (lineScale_1 && lineScale_1 !== 1) {\n      lineDash = map(lineDash, function (rawVal) {\n        return rawVal / lineScale_1;\n      });\n      lineDashOffset /= lineScale_1;\n    }\n  }\n  return [lineDash, lineDashOffset];\n}", "map": {"version": 3, "names": ["isArray", "isNumber", "map", "normalizeLineDash", "lineType", "lineWidth", "getLineDash", "el", "style", "lineDash", "lineDashOffset", "lineScale_1", "strokeNoScale", "getLineScale", "rawVal"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/zrender/lib/canvas/dashStyle.js"], "sourcesContent": ["import { isArray, isNumber, map } from '../core/util.js';\nexport function normalizeLineDash(lineType, lineWidth) {\n    if (!lineType || lineType === 'solid' || !(lineWidth > 0)) {\n        return null;\n    }\n    return lineType === 'dashed'\n        ? [4 * lineWidth, 2 * lineWidth]\n        : lineType === 'dotted'\n            ? [lineWidth]\n            : isNumber(lineType)\n                ? [lineType] : isArray(lineType) ? lineType : null;\n}\nexport function getLineDash(el) {\n    var style = el.style;\n    var lineDash = style.lineDash && style.lineWidth > 0 && normalizeLineDash(style.lineDash, style.lineWidth);\n    var lineDashOffset = style.lineDashOffset;\n    if (lineDash) {\n        var lineScale_1 = (style.strokeNoScale && el.getLineScale) ? el.getLineScale() : 1;\n        if (lineScale_1 && lineScale_1 !== 1) {\n            lineDash = map(lineDash, function (rawVal) {\n                return rawVal / lineScale_1;\n            });\n            lineDashOffset /= lineScale_1;\n        }\n    }\n    return [lineDash, lineDashOffset];\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,iBAAiB;AACxD,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACnD,IAAI,CAACD,QAAQ,IAAIA,QAAQ,KAAK,OAAO,IAAI,EAAEC,SAAS,GAAG,CAAC,CAAC,EAAE;IACvD,OAAO,IAAI;EACf;EACA,OAAOD,QAAQ,KAAK,QAAQ,GACtB,CAAC,CAAC,GAAGC,SAAS,EAAE,CAAC,GAAGA,SAAS,CAAC,GAC9BD,QAAQ,KAAK,QAAQ,GACjB,CAACC,SAAS,CAAC,GACXJ,QAAQ,CAACG,QAAQ,CAAC,GACd,CAACA,QAAQ,CAAC,GAAGJ,OAAO,CAACI,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI;AAClE;AACA,OAAO,SAASE,WAAWA,CAACC,EAAE,EAAE;EAC5B,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;EACpB,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACH,SAAS,GAAG,CAAC,IAAIF,iBAAiB,CAACK,KAAK,CAACC,QAAQ,EAAED,KAAK,CAACH,SAAS,CAAC;EAC1G,IAAIK,cAAc,GAAGF,KAAK,CAACE,cAAc;EACzC,IAAID,QAAQ,EAAE;IACV,IAAIE,WAAW,GAAIH,KAAK,CAACI,aAAa,IAAIL,EAAE,CAACM,YAAY,GAAIN,EAAE,CAACM,YAAY,CAAC,CAAC,GAAG,CAAC;IAClF,IAAIF,WAAW,IAAIA,WAAW,KAAK,CAAC,EAAE;MAClCF,QAAQ,GAAGP,GAAG,CAACO,QAAQ,EAAE,UAAUK,MAAM,EAAE;QACvC,OAAOA,MAAM,GAAGH,WAAW;MAC/B,CAAC,CAAC;MACFD,cAAc,IAAIC,WAAW;IACjC;EACJ;EACA,OAAO,CAACF,QAAQ,EAAEC,cAAc,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}