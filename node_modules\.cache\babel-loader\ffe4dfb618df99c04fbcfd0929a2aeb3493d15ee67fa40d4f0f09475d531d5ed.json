{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { calculateTextPosition } from 'zrender/lib/contain/text.js';\nimport { isArray, isNumber } from 'zrender/lib/core/util.js';\nexport function createSectorCalculateTextPosition(positionMapping, opts) {\n  opts = opts || {};\n  var isRoundCap = opts.isRoundCap;\n  return function (out, opts, boundingRect) {\n    var textPosition = opts.position;\n    if (!textPosition || textPosition instanceof Array) {\n      return calculateTextPosition(out, opts, boundingRect);\n    }\n    var mappedSectorPosition = positionMapping(textPosition);\n    var distance = opts.distance != null ? opts.distance : 5;\n    var sector = this.shape;\n    var cx = sector.cx;\n    var cy = sector.cy;\n    var r = sector.r;\n    var r0 = sector.r0;\n    var middleR = (r + r0) / 2;\n    var startAngle = sector.startAngle;\n    var endAngle = sector.endAngle;\n    var middleAngle = (startAngle + endAngle) / 2;\n    var extraDist = isRoundCap ? Math.abs(r - r0) / 2 : 0;\n    var mathCos = Math.cos;\n    var mathSin = Math.sin;\n    // base position: top-left\n    var x = cx + r * mathCos(startAngle);\n    var y = cy + r * mathSin(startAngle);\n    var textAlign = 'left';\n    var textVerticalAlign = 'top';\n    switch (mappedSectorPosition) {\n      case 'startArc':\n        x = cx + (r0 - distance) * mathCos(middleAngle);\n        y = cy + (r0 - distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'top';\n        break;\n      case 'insideStartArc':\n        x = cx + (r0 + distance) * mathCos(middleAngle);\n        y = cy + (r0 + distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'startAngle':\n        x = cx + middleR * mathCos(startAngle) + adjustAngleDistanceX(startAngle, distance + extraDist, false);\n        y = cy + middleR * mathSin(startAngle) + adjustAngleDistanceY(startAngle, distance + extraDist, false);\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideStartAngle':\n        x = cx + middleR * mathCos(startAngle) + adjustAngleDistanceX(startAngle, -distance + extraDist, false);\n        y = cy + middleR * mathSin(startAngle) + adjustAngleDistanceY(startAngle, -distance + extraDist, false);\n        textAlign = 'left';\n        textVerticalAlign = 'middle';\n        break;\n      case 'middle':\n        x = cx + middleR * mathCos(middleAngle);\n        y = cy + middleR * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'middle';\n        break;\n      case 'endArc':\n        x = cx + (r + distance) * mathCos(middleAngle);\n        y = cy + (r + distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideEndArc':\n        x = cx + (r - distance) * mathCos(middleAngle);\n        y = cy + (r - distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'top';\n        break;\n      case 'endAngle':\n        x = cx + middleR * mathCos(endAngle) + adjustAngleDistanceX(endAngle, distance + extraDist, true);\n        y = cy + middleR * mathSin(endAngle) + adjustAngleDistanceY(endAngle, distance + extraDist, true);\n        textAlign = 'left';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideEndAngle':\n        x = cx + middleR * mathCos(endAngle) + adjustAngleDistanceX(endAngle, -distance + extraDist, true);\n        y = cy + middleR * mathSin(endAngle) + adjustAngleDistanceY(endAngle, -distance + extraDist, true);\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      default:\n        return calculateTextPosition(out, opts, boundingRect);\n    }\n    out = out || {};\n    out.x = x;\n    out.y = y;\n    out.align = textAlign;\n    out.verticalAlign = textVerticalAlign;\n    return out;\n  };\n}\nexport function setSectorTextRotation(sector, textPosition, positionMapping, rotateType) {\n  if (isNumber(rotateType)) {\n    // user-set rotation\n    sector.setTextConfig({\n      rotation: rotateType\n    });\n    return;\n  } else if (isArray(textPosition)) {\n    // user-set position, use 0 as auto rotation\n    sector.setTextConfig({\n      rotation: 0\n    });\n    return;\n  }\n  var shape = sector.shape;\n  var startAngle = shape.clockwise ? shape.startAngle : shape.endAngle;\n  var endAngle = shape.clockwise ? shape.endAngle : shape.startAngle;\n  var middleAngle = (startAngle + endAngle) / 2;\n  var anchorAngle;\n  var mappedSectorPosition = positionMapping(textPosition);\n  switch (mappedSectorPosition) {\n    case 'startArc':\n    case 'insideStartArc':\n    case 'middle':\n    case 'insideEndArc':\n    case 'endArc':\n      anchorAngle = middleAngle;\n      break;\n    case 'startAngle':\n    case 'insideStartAngle':\n      anchorAngle = startAngle;\n      break;\n    case 'endAngle':\n    case 'insideEndAngle':\n      anchorAngle = endAngle;\n      break;\n    default:\n      sector.setTextConfig({\n        rotation: 0\n      });\n      return;\n  }\n  var rotate = Math.PI * 1.5 - anchorAngle;\n  /**\n   * TODO: labels with rotate > Math.PI / 2 should be rotate another\n   * half round flipped to increase readability. However, only middle\n   * position supports this for now, because in other positions, the\n   * anchor point is not at the center of the text, so the positions\n   * after rotating is not as expected.\n   */\n  if (mappedSectorPosition === 'middle' && rotate > Math.PI / 2 && rotate < Math.PI * 1.5) {\n    rotate -= Math.PI;\n  }\n  sector.setTextConfig({\n    rotation: rotate\n  });\n}\nfunction adjustAngleDistanceX(angle, distance, isEnd) {\n  return distance * Math.sin(angle) * (isEnd ? -1 : 1);\n}\nfunction adjustAngleDistanceY(angle, distance, isEnd) {\n  return distance * Math.cos(angle) * (isEnd ? 1 : -1);\n}", "map": {"version": 3, "names": ["calculateTextPosition", "isArray", "isNumber", "createSectorCalculateTextPosition", "positionMapping", "opts", "isRoundCap", "out", "boundingRect", "textPosition", "position", "Array", "mappedSectorPosition", "distance", "sector", "shape", "cx", "cy", "r", "r0", "middleR", "startAngle", "endAngle", "middleAngle", "extraDist", "Math", "abs", "mathCos", "cos", "mathSin", "sin", "x", "y", "textAlign", "textVerticalAlign", "adjustAngleDistanceX", "adjustAngleDistanceY", "align", "verticalAlign", "setSectorTextRotation", "rotateType", "setTextConfig", "rotation", "clockwise", "anchorAngle", "rotate", "PI", "angle", "isEnd"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/node_modules/echarts/lib/label/sectorLabel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { calculateTextPosition } from 'zrender/lib/contain/text.js';\nimport { isArray, isNumber } from 'zrender/lib/core/util.js';\nexport function createSectorCalculateTextPosition(positionMapping, opts) {\n  opts = opts || {};\n  var isRoundCap = opts.isRoundCap;\n  return function (out, opts, boundingRect) {\n    var textPosition = opts.position;\n    if (!textPosition || textPosition instanceof Array) {\n      return calculateTextPosition(out, opts, boundingRect);\n    }\n    var mappedSectorPosition = positionMapping(textPosition);\n    var distance = opts.distance != null ? opts.distance : 5;\n    var sector = this.shape;\n    var cx = sector.cx;\n    var cy = sector.cy;\n    var r = sector.r;\n    var r0 = sector.r0;\n    var middleR = (r + r0) / 2;\n    var startAngle = sector.startAngle;\n    var endAngle = sector.endAngle;\n    var middleAngle = (startAngle + endAngle) / 2;\n    var extraDist = isRoundCap ? Math.abs(r - r0) / 2 : 0;\n    var mathCos = Math.cos;\n    var mathSin = Math.sin;\n    // base position: top-left\n    var x = cx + r * mathCos(startAngle);\n    var y = cy + r * mathSin(startAngle);\n    var textAlign = 'left';\n    var textVerticalAlign = 'top';\n    switch (mappedSectorPosition) {\n      case 'startArc':\n        x = cx + (r0 - distance) * mathCos(middleAngle);\n        y = cy + (r0 - distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'top';\n        break;\n      case 'insideStartArc':\n        x = cx + (r0 + distance) * mathCos(middleAngle);\n        y = cy + (r0 + distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'startAngle':\n        x = cx + middleR * mathCos(startAngle) + adjustAngleDistanceX(startAngle, distance + extraDist, false);\n        y = cy + middleR * mathSin(startAngle) + adjustAngleDistanceY(startAngle, distance + extraDist, false);\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideStartAngle':\n        x = cx + middleR * mathCos(startAngle) + adjustAngleDistanceX(startAngle, -distance + extraDist, false);\n        y = cy + middleR * mathSin(startAngle) + adjustAngleDistanceY(startAngle, -distance + extraDist, false);\n        textAlign = 'left';\n        textVerticalAlign = 'middle';\n        break;\n      case 'middle':\n        x = cx + middleR * mathCos(middleAngle);\n        y = cy + middleR * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'middle';\n        break;\n      case 'endArc':\n        x = cx + (r + distance) * mathCos(middleAngle);\n        y = cy + (r + distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideEndArc':\n        x = cx + (r - distance) * mathCos(middleAngle);\n        y = cy + (r - distance) * mathSin(middleAngle);\n        textAlign = 'center';\n        textVerticalAlign = 'top';\n        break;\n      case 'endAngle':\n        x = cx + middleR * mathCos(endAngle) + adjustAngleDistanceX(endAngle, distance + extraDist, true);\n        y = cy + middleR * mathSin(endAngle) + adjustAngleDistanceY(endAngle, distance + extraDist, true);\n        textAlign = 'left';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideEndAngle':\n        x = cx + middleR * mathCos(endAngle) + adjustAngleDistanceX(endAngle, -distance + extraDist, true);\n        y = cy + middleR * mathSin(endAngle) + adjustAngleDistanceY(endAngle, -distance + extraDist, true);\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      default:\n        return calculateTextPosition(out, opts, boundingRect);\n    }\n    out = out || {};\n    out.x = x;\n    out.y = y;\n    out.align = textAlign;\n    out.verticalAlign = textVerticalAlign;\n    return out;\n  };\n}\nexport function setSectorTextRotation(sector, textPosition, positionMapping, rotateType) {\n  if (isNumber(rotateType)) {\n    // user-set rotation\n    sector.setTextConfig({\n      rotation: rotateType\n    });\n    return;\n  } else if (isArray(textPosition)) {\n    // user-set position, use 0 as auto rotation\n    sector.setTextConfig({\n      rotation: 0\n    });\n    return;\n  }\n  var shape = sector.shape;\n  var startAngle = shape.clockwise ? shape.startAngle : shape.endAngle;\n  var endAngle = shape.clockwise ? shape.endAngle : shape.startAngle;\n  var middleAngle = (startAngle + endAngle) / 2;\n  var anchorAngle;\n  var mappedSectorPosition = positionMapping(textPosition);\n  switch (mappedSectorPosition) {\n    case 'startArc':\n    case 'insideStartArc':\n    case 'middle':\n    case 'insideEndArc':\n    case 'endArc':\n      anchorAngle = middleAngle;\n      break;\n    case 'startAngle':\n    case 'insideStartAngle':\n      anchorAngle = startAngle;\n      break;\n    case 'endAngle':\n    case 'insideEndAngle':\n      anchorAngle = endAngle;\n      break;\n    default:\n      sector.setTextConfig({\n        rotation: 0\n      });\n      return;\n  }\n  var rotate = Math.PI * 1.5 - anchorAngle;\n  /**\n   * TODO: labels with rotate > Math.PI / 2 should be rotate another\n   * half round flipped to increase readability. However, only middle\n   * position supports this for now, because in other positions, the\n   * anchor point is not at the center of the text, so the positions\n   * after rotating is not as expected.\n   */\n  if (mappedSectorPosition === 'middle' && rotate > Math.PI / 2 && rotate < Math.PI * 1.5) {\n    rotate -= Math.PI;\n  }\n  sector.setTextConfig({\n    rotation: rotate\n  });\n}\nfunction adjustAngleDistanceX(angle, distance, isEnd) {\n  return distance * Math.sin(angle) * (isEnd ? -1 : 1);\n}\nfunction adjustAngleDistanceY(angle, distance, isEnd) {\n  return distance * Math.cos(angle) * (isEnd ? 1 : -1);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,OAAO,EAAEC,QAAQ,QAAQ,0BAA0B;AAC5D,OAAO,SAASC,iCAAiCA,CAACC,eAAe,EAAEC,IAAI,EAAE;EACvEA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjB,IAAIC,UAAU,GAAGD,IAAI,CAACC,UAAU;EAChC,OAAO,UAAUC,GAAG,EAAEF,IAAI,EAAEG,YAAY,EAAE;IACxC,IAAIC,YAAY,GAAGJ,IAAI,CAACK,QAAQ;IAChC,IAAI,CAACD,YAAY,IAAIA,YAAY,YAAYE,KAAK,EAAE;MAClD,OAAOX,qBAAqB,CAACO,GAAG,EAAEF,IAAI,EAAEG,YAAY,CAAC;IACvD;IACA,IAAII,oBAAoB,GAAGR,eAAe,CAACK,YAAY,CAAC;IACxD,IAAII,QAAQ,GAAGR,IAAI,CAACQ,QAAQ,IAAI,IAAI,GAAGR,IAAI,CAACQ,QAAQ,GAAG,CAAC;IACxD,IAAIC,MAAM,GAAG,IAAI,CAACC,KAAK;IACvB,IAAIC,EAAE,GAAGF,MAAM,CAACE,EAAE;IAClB,IAAIC,EAAE,GAAGH,MAAM,CAACG,EAAE;IAClB,IAAIC,CAAC,GAAGJ,MAAM,CAACI,CAAC;IAChB,IAAIC,EAAE,GAAGL,MAAM,CAACK,EAAE;IAClB,IAAIC,OAAO,GAAG,CAACF,CAAC,GAAGC,EAAE,IAAI,CAAC;IAC1B,IAAIE,UAAU,GAAGP,MAAM,CAACO,UAAU;IAClC,IAAIC,QAAQ,GAAGR,MAAM,CAACQ,QAAQ;IAC9B,IAAIC,WAAW,GAAG,CAACF,UAAU,GAAGC,QAAQ,IAAI,CAAC;IAC7C,IAAIE,SAAS,GAAGlB,UAAU,GAAGmB,IAAI,CAACC,GAAG,CAACR,CAAC,GAAGC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;IACrD,IAAIQ,OAAO,GAAGF,IAAI,CAACG,GAAG;IACtB,IAAIC,OAAO,GAAGJ,IAAI,CAACK,GAAG;IACtB;IACA,IAAIC,CAAC,GAAGf,EAAE,GAAGE,CAAC,GAAGS,OAAO,CAACN,UAAU,CAAC;IACpC,IAAIW,CAAC,GAAGf,EAAE,GAAGC,CAAC,GAAGW,OAAO,CAACR,UAAU,CAAC;IACpC,IAAIY,SAAS,GAAG,MAAM;IACtB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,QAAQtB,oBAAoB;MAC1B,KAAK,UAAU;QACbmB,CAAC,GAAGf,EAAE,GAAG,CAACG,EAAE,GAAGN,QAAQ,IAAIc,OAAO,CAACJ,WAAW,CAAC;QAC/CS,CAAC,GAAGf,EAAE,GAAG,CAACE,EAAE,GAAGN,QAAQ,IAAIgB,OAAO,CAACN,WAAW,CAAC;QAC/CU,SAAS,GAAG,QAAQ;QACpBC,iBAAiB,GAAG,KAAK;QACzB;MACF,KAAK,gBAAgB;QACnBH,CAAC,GAAGf,EAAE,GAAG,CAACG,EAAE,GAAGN,QAAQ,IAAIc,OAAO,CAACJ,WAAW,CAAC;QAC/CS,CAAC,GAAGf,EAAE,GAAG,CAACE,EAAE,GAAGN,QAAQ,IAAIgB,OAAO,CAACN,WAAW,CAAC;QAC/CU,SAAS,GAAG,QAAQ;QACpBC,iBAAiB,GAAG,QAAQ;QAC5B;MACF,KAAK,YAAY;QACfH,CAAC,GAAGf,EAAE,GAAGI,OAAO,GAAGO,OAAO,CAACN,UAAU,CAAC,GAAGc,oBAAoB,CAACd,UAAU,EAAER,QAAQ,GAAGW,SAAS,EAAE,KAAK,CAAC;QACtGQ,CAAC,GAAGf,EAAE,GAAGG,OAAO,GAAGS,OAAO,CAACR,UAAU,CAAC,GAAGe,oBAAoB,CAACf,UAAU,EAAER,QAAQ,GAAGW,SAAS,EAAE,KAAK,CAAC;QACtGS,SAAS,GAAG,OAAO;QACnBC,iBAAiB,GAAG,QAAQ;QAC5B;MACF,KAAK,kBAAkB;QACrBH,CAAC,GAAGf,EAAE,GAAGI,OAAO,GAAGO,OAAO,CAACN,UAAU,CAAC,GAAGc,oBAAoB,CAACd,UAAU,EAAE,CAACR,QAAQ,GAAGW,SAAS,EAAE,KAAK,CAAC;QACvGQ,CAAC,GAAGf,EAAE,GAAGG,OAAO,GAAGS,OAAO,CAACR,UAAU,CAAC,GAAGe,oBAAoB,CAACf,UAAU,EAAE,CAACR,QAAQ,GAAGW,SAAS,EAAE,KAAK,CAAC;QACvGS,SAAS,GAAG,MAAM;QAClBC,iBAAiB,GAAG,QAAQ;QAC5B;MACF,KAAK,QAAQ;QACXH,CAAC,GAAGf,EAAE,GAAGI,OAAO,GAAGO,OAAO,CAACJ,WAAW,CAAC;QACvCS,CAAC,GAAGf,EAAE,GAAGG,OAAO,GAAGS,OAAO,CAACN,WAAW,CAAC;QACvCU,SAAS,GAAG,QAAQ;QACpBC,iBAAiB,GAAG,QAAQ;QAC5B;MACF,KAAK,QAAQ;QACXH,CAAC,GAAGf,EAAE,GAAG,CAACE,CAAC,GAAGL,QAAQ,IAAIc,OAAO,CAACJ,WAAW,CAAC;QAC9CS,CAAC,GAAGf,EAAE,GAAG,CAACC,CAAC,GAAGL,QAAQ,IAAIgB,OAAO,CAACN,WAAW,CAAC;QAC9CU,SAAS,GAAG,QAAQ;QACpBC,iBAAiB,GAAG,QAAQ;QAC5B;MACF,KAAK,cAAc;QACjBH,CAAC,GAAGf,EAAE,GAAG,CAACE,CAAC,GAAGL,QAAQ,IAAIc,OAAO,CAACJ,WAAW,CAAC;QAC9CS,CAAC,GAAGf,EAAE,GAAG,CAACC,CAAC,GAAGL,QAAQ,IAAIgB,OAAO,CAACN,WAAW,CAAC;QAC9CU,SAAS,GAAG,QAAQ;QACpBC,iBAAiB,GAAG,KAAK;QACzB;MACF,KAAK,UAAU;QACbH,CAAC,GAAGf,EAAE,GAAGI,OAAO,GAAGO,OAAO,CAACL,QAAQ,CAAC,GAAGa,oBAAoB,CAACb,QAAQ,EAAET,QAAQ,GAAGW,SAAS,EAAE,IAAI,CAAC;QACjGQ,CAAC,GAAGf,EAAE,GAAGG,OAAO,GAAGS,OAAO,CAACP,QAAQ,CAAC,GAAGc,oBAAoB,CAACd,QAAQ,EAAET,QAAQ,GAAGW,SAAS,EAAE,IAAI,CAAC;QACjGS,SAAS,GAAG,MAAM;QAClBC,iBAAiB,GAAG,QAAQ;QAC5B;MACF,KAAK,gBAAgB;QACnBH,CAAC,GAAGf,EAAE,GAAGI,OAAO,GAAGO,OAAO,CAACL,QAAQ,CAAC,GAAGa,oBAAoB,CAACb,QAAQ,EAAE,CAACT,QAAQ,GAAGW,SAAS,EAAE,IAAI,CAAC;QAClGQ,CAAC,GAAGf,EAAE,GAAGG,OAAO,GAAGS,OAAO,CAACP,QAAQ,CAAC,GAAGc,oBAAoB,CAACd,QAAQ,EAAE,CAACT,QAAQ,GAAGW,SAAS,EAAE,IAAI,CAAC;QAClGS,SAAS,GAAG,OAAO;QACnBC,iBAAiB,GAAG,QAAQ;QAC5B;MACF;QACE,OAAOlC,qBAAqB,CAACO,GAAG,EAAEF,IAAI,EAAEG,YAAY,CAAC;IACzD;IACAD,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACfA,GAAG,CAACwB,CAAC,GAAGA,CAAC;IACTxB,GAAG,CAACyB,CAAC,GAAGA,CAAC;IACTzB,GAAG,CAAC8B,KAAK,GAAGJ,SAAS;IACrB1B,GAAG,CAAC+B,aAAa,GAAGJ,iBAAiB;IACrC,OAAO3B,GAAG;EACZ,CAAC;AACH;AACA,OAAO,SAASgC,qBAAqBA,CAACzB,MAAM,EAAEL,YAAY,EAAEL,eAAe,EAAEoC,UAAU,EAAE;EACvF,IAAItC,QAAQ,CAACsC,UAAU,CAAC,EAAE;IACxB;IACA1B,MAAM,CAAC2B,aAAa,CAAC;MACnBC,QAAQ,EAAEF;IACZ,CAAC,CAAC;IACF;EACF,CAAC,MAAM,IAAIvC,OAAO,CAACQ,YAAY,CAAC,EAAE;IAChC;IACAK,MAAM,CAAC2B,aAAa,CAAC;MACnBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF;EACF;EACA,IAAI3B,KAAK,GAAGD,MAAM,CAACC,KAAK;EACxB,IAAIM,UAAU,GAAGN,KAAK,CAAC4B,SAAS,GAAG5B,KAAK,CAACM,UAAU,GAAGN,KAAK,CAACO,QAAQ;EACpE,IAAIA,QAAQ,GAAGP,KAAK,CAAC4B,SAAS,GAAG5B,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACM,UAAU;EAClE,IAAIE,WAAW,GAAG,CAACF,UAAU,GAAGC,QAAQ,IAAI,CAAC;EAC7C,IAAIsB,WAAW;EACf,IAAIhC,oBAAoB,GAAGR,eAAe,CAACK,YAAY,CAAC;EACxD,QAAQG,oBAAoB;IAC1B,KAAK,UAAU;IACf,KAAK,gBAAgB;IACrB,KAAK,QAAQ;IACb,KAAK,cAAc;IACnB,KAAK,QAAQ;MACXgC,WAAW,GAAGrB,WAAW;MACzB;IACF,KAAK,YAAY;IACjB,KAAK,kBAAkB;MACrBqB,WAAW,GAAGvB,UAAU;MACxB;IACF,KAAK,UAAU;IACf,KAAK,gBAAgB;MACnBuB,WAAW,GAAGtB,QAAQ;MACtB;IACF;MACER,MAAM,CAAC2B,aAAa,CAAC;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;EACJ;EACA,IAAIG,MAAM,GAAGpB,IAAI,CAACqB,EAAE,GAAG,GAAG,GAAGF,WAAW;EACxC;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIhC,oBAAoB,KAAK,QAAQ,IAAIiC,MAAM,GAAGpB,IAAI,CAACqB,EAAE,GAAG,CAAC,IAAID,MAAM,GAAGpB,IAAI,CAACqB,EAAE,GAAG,GAAG,EAAE;IACvFD,MAAM,IAAIpB,IAAI,CAACqB,EAAE;EACnB;EACAhC,MAAM,CAAC2B,aAAa,CAAC;IACnBC,QAAQ,EAAEG;EACZ,CAAC,CAAC;AACJ;AACA,SAASV,oBAAoBA,CAACY,KAAK,EAAElC,QAAQ,EAAEmC,KAAK,EAAE;EACpD,OAAOnC,QAAQ,GAAGY,IAAI,CAACK,GAAG,CAACiB,KAAK,CAAC,IAAIC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACtD;AACA,SAASZ,oBAAoBA,CAACW,KAAK,EAAElC,QAAQ,EAAEmC,KAAK,EAAE;EACpD,OAAOnC,QAAQ,GAAGY,IAAI,CAACG,GAAG,CAACmB,KAAK,CAAC,IAAIC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}