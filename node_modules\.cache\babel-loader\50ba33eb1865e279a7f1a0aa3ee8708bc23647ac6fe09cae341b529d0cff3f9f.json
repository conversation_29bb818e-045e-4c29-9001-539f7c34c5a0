{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"app-container\"\n  }, [_c('el-card', {\n    staticClass: \"box-card\"\n  }, [_c('div', {\n    staticClass: \"header\"\n  }, [_c('h3', [_vm._v(\"首页通知设置\")]), _c('div', {\n    staticClass: \"header-right\"\n  }, [_c('el-switch', {\n    attrs: {\n      \"active-value\": 1,\n      \"inactive-value\": 0,\n      \"active-text\": \"开启\",\n      \"inactive-text\": \"关闭\"\n    },\n    on: {\n      \"change\": _vm.handleStatusChange\n    },\n    model: {\n      value: _vm.notice.status,\n      callback: function callback($$v) {\n        _vm.$set(_vm.notice, \"status\", $$v);\n      },\n      expression: \"notice.status\"\n    }\n  }), _c('el-button', {\n    attrs: {\n      \"type\": \"primary\",\n      \"loading\": _vm.loading\n    },\n    on: {\n      \"click\": _vm.handleSave\n    }\n  }, [_vm._v(\"保存修改\")])], 1)]), _c('div', {\n    staticClass: \"content-container\"\n  }, [_c('el-input', {\n    attrs: {\n      \"type\": \"textarea\",\n      \"rows\": 15,\n      \"placeholder\": \"请输入首页通知内容\",\n      \"resize\": \"none\"\n    },\n    model: {\n      value: _vm.notice.content,\n      callback: function callback($$v) {\n        _vm.$set(_vm.notice, \"content\", $$v);\n      },\n      expression: \"notice.content\"\n    }\n  }), _c('div', {\n    staticClass: \"tips\"\n  }, [_c('p', [_vm._v(\"提示：\")]), _c('p', [_vm._v(\"1. 支持换行\")]), _c('p', [_vm._v(\"2. 建议控制文本长度，以确保良好的显示效果\")])])], 1)])], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "attrs", "on", "handleStatusChange", "model", "value", "notice", "status", "callback", "$$v", "$set", "expression", "loading", "handleSave", "content", "staticRenderFns"], "sources": ["E:/最新项目文件/交易所/adminweb/src/views/notice/home-notice/index.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"app-container\"},[_c('el-card',{staticClass:\"box-card\"},[_c('div',{staticClass:\"header\"},[_c('h3',[_vm._v(\"首页通知设置\")]),_c('div',{staticClass:\"header-right\"},[_c('el-switch',{attrs:{\"active-value\":1,\"inactive-value\":0,\"active-text\":\"开启\",\"inactive-text\":\"关闭\"},on:{\"change\":_vm.handleStatusChange},model:{value:(_vm.notice.status),callback:function ($$v) {_vm.$set(_vm.notice, \"status\", $$v)},expression:\"notice.status\"}}),_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.loading},on:{\"click\":_vm.handleSave}},[_vm._v(\"保存修改\")])],1)]),_c('div',{staticClass:\"content-container\"},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":15,\"placeholder\":\"请输入首页通知内容\",\"resize\":\"none\"},model:{value:(_vm.notice.content),callback:function ($$v) {_vm.$set(_vm.notice, \"content\", $$v)},expression:\"notice.content\"}}),_c('div',{staticClass:\"tips\"},[_c('p',[_vm._v(\"提示：\")]),_c('p',[_vm._v(\"1. 支持换行\")]),_c('p',[_vm._v(\"2. 建议控制文本长度，以确保良好的显示效果\")])])],1)])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAe,CAAC,EAAC,CAACF,EAAE,CAAC,SAAS,EAAC;IAACE,WAAW,EAAC;EAAU,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAQ,CAAC,EAAC,CAACF,EAAE,CAAC,IAAI,EAAC,CAACD,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAc,CAAC,EAAC,CAACF,EAAE,CAAC,WAAW,EAAC;IAACI,KAAK,EAAC;MAAC,cAAc,EAAC,CAAC;MAAC,gBAAgB,EAAC,CAAC;MAAC,aAAa,EAAC,IAAI;MAAC,eAAe,EAAC;IAAI,CAAC;IAACC,EAAE,EAAC;MAAC,QAAQ,EAACN,GAAG,CAACO;IAAkB,CAAC;IAACC,KAAK,EAAC;MAACC,KAAK,EAAET,GAAG,CAACU,MAAM,CAACC,MAAO;MAACC,QAAQ,EAAC,SAATA,QAAQA,CAAWC,GAAG,EAAE;QAACb,GAAG,CAACc,IAAI,CAACd,GAAG,CAACU,MAAM,EAAE,QAAQ,EAAEG,GAAG,CAAC;MAAA,CAAC;MAACE,UAAU,EAAC;IAAe;EAAC,CAAC,CAAC,EAACd,EAAE,CAAC,WAAW,EAAC;IAACI,KAAK,EAAC;MAAC,MAAM,EAAC,SAAS;MAAC,SAAS,EAACL,GAAG,CAACgB;IAAO,CAAC;IAACV,EAAE,EAAC;MAAC,OAAO,EAACN,GAAG,CAACiB;IAAU;EAAC,CAAC,EAAC,CAACjB,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACF,EAAE,CAAC,UAAU,EAAC;IAACI,KAAK,EAAC;MAAC,MAAM,EAAC,UAAU;MAAC,MAAM,EAAC,EAAE;MAAC,aAAa,EAAC,WAAW;MAAC,QAAQ,EAAC;IAAM,CAAC;IAACG,KAAK,EAAC;MAACC,KAAK,EAAET,GAAG,CAACU,MAAM,CAACQ,OAAQ;MAACN,QAAQ,EAAC,SAATA,QAAQA,CAAWC,GAAG,EAAE;QAACb,GAAG,CAACc,IAAI,CAACd,GAAG,CAACU,MAAM,EAAE,SAAS,EAAEG,GAAG,CAAC;MAAA,CAAC;MAACE,UAAU,EAAC;IAAgB;EAAC,CAAC,CAAC,EAACd,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC;EAAM,CAAC,EAAC,CAACF,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAACH,EAAE,CAAC,GAAG,EAAC,CAACD,GAAG,CAACI,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AAClgC,CAAC;AACD,IAAIe,eAAe,GAAG,EAAE;AAExB,SAASpB,MAAM,EAAEoB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}