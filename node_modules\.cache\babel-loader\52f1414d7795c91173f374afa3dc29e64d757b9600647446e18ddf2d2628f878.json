{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { use } from '../../extension.js';\nimport MapView from './MapView.js';\nimport MapSeries from './MapSeries.js';\nimport mapDataStatistic from './mapDataStatistic.js';\nimport mapSymbolLayout from './mapSymbolLayout.js';\nimport { createLegacyDataSelectAction } from '../../legacy/dataSelectAction.js';\nimport { install as installGeo } from '../../component/geo/install.js';\nexport function install(registers) {\n  use(installGeo);\n  registers.registerChartView(MapView);\n  registers.registerSeriesModel(MapSeries);\n  registers.registerLayout(mapSymbolLayout);\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, mapDataStatistic);\n  createLegacyDataSelectAction('map', registers.registerAction);\n}", "map": {"version": 3, "names": ["use", "MapView", "MapSeries", "mapDataStatistic", "mapSymbolLayout", "createLegacyDataSelectAction", "install", "installGeo", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "registerProcessor", "PRIORITY", "PROCESSOR", "STATISTIC", "registerAction"], "sources": ["E:/新项目/整理6/adminweb/node_modules/echarts/lib/chart/map/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { use } from '../../extension.js';\nimport MapView from './MapView.js';\nimport MapSeries from './MapSeries.js';\nimport mapDataStatistic from './mapDataStatistic.js';\nimport mapSymbolLayout from './mapSymbolLayout.js';\nimport { createLegacyDataSelectAction } from '../../legacy/dataSelectAction.js';\nimport { install as installGeo } from '../../component/geo/install.js';\nexport function install(registers) {\n  use(installGeo);\n  registers.registerChartView(MapView);\n  registers.registerSeriesModel(MapSeries);\n  registers.registerLayout(mapSymbolLayout);\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, mapDataStatistic);\n  createLegacyDataSelectAction('map', registers.registerAction);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,oBAAoB;AACxC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,SAASC,4BAA4B,QAAQ,kCAAkC;AAC/E,SAASC,OAAO,IAAIC,UAAU,QAAQ,gCAAgC;AACtE,OAAO,SAASD,OAAOA,CAACE,SAAS,EAAE;EACjCR,GAAG,CAACO,UAAU,CAAC;EACfC,SAAS,CAACC,iBAAiB,CAACR,OAAO,CAAC;EACpCO,SAAS,CAACE,mBAAmB,CAACR,SAAS,CAAC;EACxCM,SAAS,CAACG,cAAc,CAACP,eAAe,CAAC;EACzCI,SAAS,CAACI,iBAAiB,CAACJ,SAAS,CAACK,QAAQ,CAACC,SAAS,CAACC,SAAS,EAAEZ,gBAAgB,CAAC;EACrFE,4BAA4B,CAAC,KAAK,EAAEG,SAAS,CAACQ,cAAc,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}