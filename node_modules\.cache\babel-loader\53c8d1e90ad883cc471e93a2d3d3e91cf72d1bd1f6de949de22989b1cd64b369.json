{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'NoticeList',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        page: 1,\n        limit: 10,\n        title: '',\n        type: '',\n        status: '',\n        dateRange: []\n      },\n      total: 0,\n      // 表格数据\n      tableData: [{\n        id: 1,\n        title: '关于系统升级维护的通知',\n        type: '1',\n        sort: 1,\n        status: 1,\n        publisher: '管理员',\n        publishTime: '2024-01-20 10:00:00',\n        createTime: '2024-01-19 15:00:00',\n        content: '<p>尊敬的用户：</p><p>为了给您提供更好的服务，系统将于2024年1月21日凌晨2:00-4:00进行升级维护。</p><p>给您带来的不便敬请谅解。</p>',\n        views: 1250,\n        attachments: [{\n          name: '维护说明.pdf',\n          url: ''\n        }]\n      }, {\n        id: 2,\n        title: '元旦活动公告',\n        type: '2',\n        sort: 2,\n        status: 1,\n        publisher: '管理员',\n        publishTime: '2024-01-19 10:00:00',\n        createTime: '2024-01-18 15:00:00',\n        content: '<p>尊敬的用户：</p><p>元旦期间，平台将举办多项优惠活动，具体详情如下：</p><p>1. 充值送红包</p><p>2. 交易返现</p>',\n        views: 2680,\n        attachments: []\n      }],\n      // 预览相关\n      previewVisible: false,\n      previewData: {}\n    };\n  },\n  methods: {\n    handleSizeChange: function handleSizeChange(val) {\n      this.listQuery.limit = val;\n      // 重新加载数据\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listQuery.page = val;\n      // 重新加载数据\n    },\n    handleAdd: function handleAdd() {\n      this.$router.push('/dashboard/notice/publish');\n    },\n    handleEdit: function handleEdit(row) {\n      this.$router.push({\n        path: '/dashboard/notice/publish',\n        query: {\n          id: row.id\n        }\n      });\n    },\n    handlePreview: function handlePreview(row) {\n      this.previewData = row;\n      this.previewVisible = true;\n    },\n    handleDelete: function handleDelete(row) {\n      var _this = this;\n      this.$confirm('是否确认删除该公告？', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this.$message.success('删除成功');\n      });\n    },\n    handleStatusChange: function handleStatusChange(row) {\n      var _this2 = this;\n      var text = row.status === 1 ? '发布' : '下线';\n      this.$confirm(\"\\u786E\\u8BA4\\u8981\".concat(text, \"\\u8BE5\\u516C\\u544A\\u5417\\uFF1F\"), '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this2.$message.success(\"\".concat(text, \"\\u6210\\u529F\"));\n      })[\"catch\"](function () {\n        row.status = row.status === 1 ? 0 : 1;\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "list<PERSON>uery", "page", "limit", "title", "type", "status", "date<PERSON><PERSON><PERSON>", "total", "tableData", "id", "sort", "publisher", "publishTime", "createTime", "content", "views", "attachments", "url", "previewVisible", "previewData", "methods", "handleSizeChange", "val", "handleCurrentChange", "handleAdd", "$router", "push", "handleEdit", "row", "path", "query", "handlePreview", "handleDelete", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "then", "$message", "success", "handleStatusChange", "_this2", "text", "concat"], "sources": ["src/views/notice/list/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-input\r\n          v-model=\"listQuery.title\"\r\n          placeholder=\"公告标题\"\r\n          style=\"width: 200px;\"\r\n          class=\"filter-item\"\r\n        />\r\n        <el-select\r\n          v-model=\"listQuery.type\"\r\n          placeholder=\"公告类型\"\r\n          clearable\r\n          class=\"filter-item\"\r\n          style=\"width: 130px\"\r\n        >\r\n          <el-option label=\"系统公告\" value=\"1\" />\r\n          <el-option label=\"活动公告\" value=\"2\" />\r\n          <el-option label=\"其他公告\" value=\"3\" />\r\n        </el-select>\r\n        <el-select\r\n          v-model=\"listQuery.status\"\r\n          placeholder=\"状态\"\r\n          clearable\r\n          class=\"filter-item\"\r\n          style=\"width: 130px\"\r\n        >\r\n          <el-option label=\"已发布\" value=\"1\" />\r\n          <el-option label=\"未发布\" value=\"0\" />\r\n        </el-select>\r\n        <el-date-picker\r\n          v-model=\"listQuery.dateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          class=\"filter-item\"\r\n        />\r\n        <el-button type=\"primary\" icon=\"el-icon-search\">搜索</el-button>\r\n        <el-button type=\"success\" icon=\"el-icon-refresh\">重置</el-button>\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增公告</el-button>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"公告标题\" prop=\"title\" min-width=\"200\" show-overflow-tooltip>\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"primary\" :underline=\"false\" @click=\"handlePreview(scope.row)\">\r\n              {{ scope.row.title }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"公告类型\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.type === '1' ? 'danger' : scope.row.type === '2' ? 'success' : 'info'\">\r\n              {{ scope.row.type === '1' ? '系统公告' : scope.row.type === '2' ? '活动公告' : '其他公告' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"排序\" prop=\"sort\" align=\"center\" width=\"80\" />\r\n        <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"发布人\" prop=\"publisher\" align=\"center\" width=\"120\" />\r\n        <el-table-column label=\"发布时间\" prop=\"publishTime\" align=\"center\" width=\"160\" />\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\" align=\"center\" width=\"160\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"200\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" @click=\"handleEdit(scope.row)\">修改</el-button>\r\n            <el-button type=\"text\" @click=\"handlePreview(scope.row)\">预览</el-button>\r\n            <el-button \r\n              type=\"text\" \r\n              style=\"color: #f56c6c\"\r\n              @click=\"handleDelete(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页区域 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"listQuery.page\"\r\n          :page-sizes=\"[10, 20, 30, 50]\"\r\n          :page-size=\"listQuery.limit\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\">\r\n        </el-pagination>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 预览对话框 -->\r\n    <el-dialog \r\n      :title=\"previewData.title\" \r\n      :visible.sync=\"previewVisible\" \r\n      width=\"800px\"\r\n      class=\"preview-dialog\"\r\n    >\r\n      <div class=\"notice-info\">\r\n        <span class=\"info-item\">\r\n          <i class=\"el-icon-user\"></i>\r\n          发布人：{{ previewData.publisher }}\r\n        </span>\r\n        <span class=\"info-item\">\r\n          <i class=\"el-icon-time\"></i>\r\n          发布时间：{{ previewData.publishTime }}\r\n        </span>\r\n        <span class=\"info-item\">\r\n          <i class=\"el-icon-view\"></i>\r\n          浏览量：{{ previewData.views }}\r\n        </span>\r\n      </div>\r\n      <div class=\"notice-content\" v-html=\"previewData.content\"></div>\r\n      <div class=\"notice-attachments\" v-if=\"previewData.attachments && previewData.attachments.length\">\r\n        <div class=\"attachment-title\">附件列表：</div>\r\n        <div class=\"attachment-list\">\r\n          <div v-for=\"(item, index) in previewData.attachments\" :key=\"index\" class=\"attachment-item\">\r\n            <i class=\"el-icon-document\"></i>\r\n            <span class=\"attachment-name\">{{ item.name }}</span>\r\n            <el-button type=\"text\" size=\"small\">下载</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'NoticeList',\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      listQuery: {\r\n        page: 1,\r\n        limit: 10,\r\n        title: '',\r\n        type: '',\r\n        status: '',\r\n        dateRange: []\r\n      },\r\n      total: 0,\r\n      // 表格数据\r\n      tableData: [\r\n        {\r\n          id: 1,\r\n          title: '关于系统升级维护的通知',\r\n          type: '1',\r\n          sort: 1,\r\n          status: 1,\r\n          publisher: '管理员',\r\n          publishTime: '2024-01-20 10:00:00',\r\n          createTime: '2024-01-19 15:00:00',\r\n          content: '<p>尊敬的用户：</p><p>为了给您提供更好的服务，系统将于2024年1月21日凌晨2:00-4:00进行升级维护。</p><p>给您带来的不便敬请谅解。</p>',\r\n          views: 1250,\r\n          attachments: [\r\n            { name: '维护说明.pdf', url: '' }\r\n          ]\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '元旦活动公告',\r\n          type: '2',\r\n          sort: 2,\r\n          status: 1,\r\n          publisher: '管理员',\r\n          publishTime: '2024-01-19 10:00:00',\r\n          createTime: '2024-01-18 15:00:00',\r\n          content: '<p>尊敬的用户：</p><p>元旦期间，平台将举办多项优惠活动，具体详情如下：</p><p>1. 充值送红包</p><p>2. 交易返现</p>',\r\n          views: 2680,\r\n          attachments: []\r\n        }\r\n      ],\r\n      // 预览相关\r\n      previewVisible: false,\r\n      previewData: {}\r\n    }\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      this.listQuery.limit = val\r\n      // 重新加载数据\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listQuery.page = val\r\n      // 重新加载数据\r\n    },\r\n    handleAdd() {\r\n      this.$router.push('/dashboard/notice/publish')\r\n    },\r\n    handleEdit(row) {\r\n      this.$router.push({\r\n        path: '/dashboard/notice/publish',\r\n        query: { id: row.id }\r\n      })\r\n    },\r\n    handlePreview(row) {\r\n      this.previewData = row\r\n      this.previewVisible = true\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm('是否确认删除该公告？', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success('删除成功')\r\n      })\r\n    },\r\n    handleStatusChange(row) {\r\n      const text = row.status === 1 ? '发布' : '下线'\r\n      this.$confirm(`确认要${text}该公告吗？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success(`${text}成功`)\r\n      }).catch(() => {\r\n        row.status = row.status === 1 ? 0 : 1\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    .filter-item {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .operation-container {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .pagination-container {\r\n    padding: 20px 0;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n.preview-dialog {\r\n  ::v-deep .el-dialog__body {\r\n    padding: 20px 30px;\r\n  }\r\n\r\n  .notice-info {\r\n    margin-bottom: 20px;\r\n    padding-bottom: 15px;\r\n    border-bottom: 1px solid #eee;\r\n    color: #666;\r\n    font-size: 14px;\r\n\r\n    .info-item {\r\n      margin-right: 20px;\r\n      \r\n      i {\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .notice-content {\r\n    min-height: 200px;\r\n    line-height: 1.8;\r\n  }\r\n\r\n  .notice-attachments {\r\n    margin-top: 20px;\r\n    padding-top: 15px;\r\n    border-top: 1px solid #eee;\r\n\r\n    .attachment-title {\r\n      font-size: 14px;\r\n      color: #666;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .attachment-list {\r\n      .attachment-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 8px;\r\n\r\n        i {\r\n          color: #909399;\r\n          margin-right: 5px;\r\n        }\r\n\r\n        .attachment-name {\r\n          flex: 1;\r\n          margin-right: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";AAmJA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,IAAA;QACAC,MAAA;QACAC,SAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA,GACA;QACAC,EAAA;QACAN,KAAA;QACAC,IAAA;QACAM,IAAA;QACAL,MAAA;QACAM,SAAA;QACAC,WAAA;QACAC,UAAA;QACAC,OAAA;QACAC,KAAA;QACAC,WAAA,GACA;UAAAlB,IAAA;UAAAmB,GAAA;QAAA;MAEA,GACA;QACAR,EAAA;QACAN,KAAA;QACAC,IAAA;QACAM,IAAA;QACAL,MAAA;QACAM,SAAA;QACAC,WAAA;QACAC,UAAA;QACAC,OAAA;QACAC,KAAA;QACAC,WAAA;MACA,EACA;MACA;MACAE,cAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAtB,SAAA,CAAAE,KAAA,GAAAoB,GAAA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAtB,SAAA,CAAAC,IAAA,GAAAqB,GAAA;MACA;IACA;IACAE,SAAA,WAAAA,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAH,OAAA,CAAAC,IAAA;QACAG,IAAA;QACAC,KAAA;UAAArB,EAAA,EAAAmB,GAAA,CAAAnB;QAAA;MACA;IACA;IACAsB,aAAA,WAAAA,cAAAH,GAAA;MACA,KAAAT,WAAA,GAAAS,GAAA;MACA,KAAAV,cAAA;IACA;IACAc,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,KAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAhC,IAAA;MACA,GAAAiC,IAAA;QACAJ,KAAA,CAAAK,QAAA,CAAAC,OAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,IAAA,GAAAd,GAAA,CAAAvB,MAAA;MACA,KAAA6B,QAAA,sBAAAS,MAAA,CAAAD,IAAA;QACAP,iBAAA;QACAC,gBAAA;QACAhC,IAAA;MACA,GAAAiC,IAAA;QACAI,MAAA,CAAAH,QAAA,CAAAC,OAAA,IAAAI,MAAA,CAAAD,IAAA;MACA;QACAd,GAAA,CAAAvB,MAAA,GAAAuB,GAAA,CAAAvB,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}