{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { use } from '../../extension.js';\nimport ScatterSeriesModel from './ScatterSeries.js';\nimport ScatterView from './ScatterView.js';\nimport { install as installGridSimple } from '../../component/grid/installSimple.js';\nimport layoutPoints from '../../layout/points.js';\nexport function install(registers) {\n  // In case developer forget to include grid component\n  use(installGridSimple);\n  registers.registerSeriesModel(ScatterSeriesModel);\n  registers.registerChartView(ScatterView);\n  registers.registerLayout(layoutPoints('scatter'));\n}", "map": {"version": 3, "names": ["use", "ScatterSeriesModel", "ScatterView", "install", "installGridSimple", "layoutPoints", "registers", "registerSeriesModel", "registerChartView", "registerLayout"], "sources": ["F:/常规项目/adminweb/node_modules/echarts/lib/chart/scatter/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { use } from '../../extension.js';\nimport ScatterSeriesModel from './ScatterSeries.js';\nimport ScatterView from './ScatterView.js';\nimport { install as installGridSimple } from '../../component/grid/installSimple.js';\nimport layoutPoints from '../../layout/points.js';\nexport function install(registers) {\n  // In case developer forget to include grid component\n  use(installGridSimple);\n  registers.registerSeriesModel(ScatterSeriesModel);\n  registers.registerChartView(ScatterView);\n  registers.registerLayout(layoutPoints('scatter'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,oBAAoB;AACxC,OAAOC,kBAAkB,MAAM,oBAAoB;AACnD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,OAAO,IAAIC,iBAAiB,QAAQ,uCAAuC;AACpF,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAO,SAASF,OAAOA,CAACG,SAAS,EAAE;EACjC;EACAN,GAAG,CAACI,iBAAiB,CAAC;EACtBE,SAAS,CAACC,mBAAmB,CAACN,kBAAkB,CAAC;EACjDK,SAAS,CAACE,iBAAiB,CAACN,WAAW,CAAC;EACxCI,SAAS,CAACG,cAAc,CAACJ,YAAY,CAAC,SAAS,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}