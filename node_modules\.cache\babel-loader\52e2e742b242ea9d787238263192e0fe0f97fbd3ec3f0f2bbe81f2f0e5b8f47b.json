{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport DataZoomModel from './DataZoomModel.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar InsideZoomModel = /** @class */function (_super) {\n  __extends(InsideZoomModel, _super);\n  function InsideZoomModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = InsideZoomModel.type;\n    return _this;\n  }\n  InsideZoomModel.type = 'dataZoom.inside';\n  InsideZoomModel.defaultOption = inheritDefaultOption(DataZoomModel.defaultOption, {\n    disabled: false,\n    zoomLock: false,\n    zoomOnMouseWheel: true,\n    moveOnMouseMove: true,\n    moveOnMouseWheel: false,\n    preventDefaultMouseMove: true\n  });\n  return InsideZoomModel;\n}(DataZoomModel);\nexport default InsideZoomModel;", "map": {"version": 3, "names": ["__extends", "DataZoomModel", "inheritDefaultOption", "InsideZoomModel", "_super", "_this", "apply", "arguments", "type", "defaultOption", "disabled", "zoomLock", "zoomOnMouseWheel", "moveOnMouseMove", "moveOnMouseWheel", "preventDefaultMouseMove"], "sources": ["G:/备份9/adminweb/node_modules/echarts/lib/component/dataZoom/InsideZoomModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport DataZoomModel from './DataZoomModel.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar InsideZoomModel = /** @class */function (_super) {\n  __extends(InsideZoomModel, _super);\n  function InsideZoomModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = InsideZoomModel.type;\n    return _this;\n  }\n  InsideZoomModel.type = 'dataZoom.inside';\n  InsideZoomModel.defaultOption = inheritDefaultOption(DataZoomModel.defaultOption, {\n    disabled: false,\n    zoomLock: false,\n    zoomOnMouseWheel: true,\n    moveOnMouseMove: true,\n    moveOnMouseWheel: false,\n    preventDefaultMouseMove: true\n  });\n  return InsideZoomModel;\n}(DataZoomModel);\nexport default InsideZoomModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDJ,SAAS,CAACG,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjC,OAAOH,KAAK;EACd;EACAF,eAAe,CAACK,IAAI,GAAG,iBAAiB;EACxCL,eAAe,CAACM,aAAa,GAAGP,oBAAoB,CAACD,aAAa,CAACQ,aAAa,EAAE;IAChFC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,KAAK;IACfC,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE,IAAI;IACrBC,gBAAgB,EAAE,KAAK;IACvBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,OAAOZ,eAAe;AACxB,CAAC,CAACF,aAAa,CAAC;AAChB,eAAeE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}