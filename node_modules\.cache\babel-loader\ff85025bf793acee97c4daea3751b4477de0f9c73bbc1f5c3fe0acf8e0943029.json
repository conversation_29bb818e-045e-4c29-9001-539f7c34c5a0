{"ast": null, "code": "import _objectSpread from \"E:/\\u65B0\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nexport default {\n  name: 'SystemMenu',\n  data: function data() {\n    return {\n      // 查询参数\n      listQuery: {\n        menuName: '',\n        status: ''\n      },\n      // 表格数据\n      tableData: [{\n        id: '1',\n        menuName: '系统管理',\n        icon: 'el-icon-setting',\n        sort: 1,\n        perms: '',\n        path: '/system',\n        component: '',\n        status: '1',\n        children: [{\n          id: '1-1',\n          menuName: '用户管理',\n          icon: 'el-icon-user',\n          sort: 1,\n          perms: 'system:user:list',\n          path: 'user',\n          component: 'system/user/index',\n          status: '1'\n        }, {\n          id: '1-2',\n          menuName: '角色管理',\n          icon: 'el-icon-s-custom',\n          sort: 2,\n          perms: 'system:role:list',\n          path: 'role',\n          component: 'system/role/index',\n          status: '1'\n        }]\n      }],\n      // 菜单树选项\n      menuOptions: [],\n      defaultProps: {\n        children: 'children',\n        label: 'menuName',\n        value: 'id'\n      },\n      // 弹窗表单\n      dialogVisible: false,\n      dialogTitle: '',\n      form: {\n        parentId: '',\n        menuType: 'M',\n        menuName: '',\n        icon: '',\n        sort: 0,\n        path: '',\n        component: '',\n        perms: '',\n        status: '1'\n      },\n      // 表单校验规则\n      rules: {\n        menuName: [{\n          required: true,\n          message: '请输入菜单名称',\n          trigger: 'blur'\n        }],\n        path: [{\n          required: true,\n          message: '请输入路由地址',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.menuOptions = this.tableData;\n  },\n  methods: {\n    // 新增菜单\n    handleAdd: function handleAdd(row) {\n      this.dialogTitle = '添加菜单';\n      this.form = {\n        parentId: row ? row.id : '',\n        menuType: 'M',\n        menuName: '',\n        icon: '',\n        sort: 0,\n        path: '',\n        component: '',\n        perms: '',\n        status: '1'\n      };\n      this.dialogVisible = true;\n    },\n    // 修改菜单\n    handleEdit: function handleEdit(row) {\n      this.dialogTitle = '修改菜单';\n      this.form = _objectSpread({}, row);\n      this.dialogVisible = true;\n    },\n    // 删除菜单\n    handleDelete: function handleDelete(row) {\n      var _this = this;\n      this.$confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项?', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this.$message.success('删除成功');\n      })[\"catch\"](function () {});\n    },\n    // 表单提交\n    submitForm: function submitForm() {\n      var _this2 = this;\n      this.$refs.form.validate(function (valid) {\n        if (valid) {\n          _this2.$message.success('操作成功');\n          _this2.dialogVisible = false;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "list<PERSON>uery", "menuName", "status", "tableData", "id", "icon", "sort", "perms", "path", "component", "children", "menuOptions", "defaultProps", "label", "value", "dialogVisible", "dialogTitle", "form", "parentId", "menuType", "rules", "required", "message", "trigger", "created", "methods", "handleAdd", "row", "handleEdit", "_objectSpread", "handleDelete", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "$message", "success", "submitForm", "_this2", "$refs", "validate", "valid"], "sources": ["src/views/system/menu/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 搜索和操作区域 -->\r\n      <div class=\"filter-container\">\r\n        <el-input\r\n          v-model=\"listQuery.menuName\"\r\n          placeholder=\"菜单名称\"\r\n          style=\"width: 200px;\"\r\n          class=\"filter-item\"\r\n        />\r\n        <el-select\r\n          v-model=\"listQuery.status\"\r\n          placeholder=\"状态\"\r\n          clearable\r\n          class=\"filter-item\"\r\n          style=\"width: 130px\"\r\n        >\r\n          <el-option label=\"显示\" value=\"1\" />\r\n          <el-option label=\"隐藏\" value=\"0\" />\r\n        </el-select>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\">搜索</el-button>\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n        <el-button type=\"info\" icon=\"el-icon-refresh\">展开/折叠</el-button>\r\n      </div>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        :data=\"tableData\"\r\n        row-key=\"id\"\r\n        border\r\n        default-expand-all\r\n        :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n      >\r\n        <el-table-column prop=\"menuName\" label=\"菜单名称\" />\r\n        <el-table-column prop=\"icon\" label=\"图标\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <i :class=\"scope.row.icon\"></i>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"sort\" label=\"排序\" width=\"80\" />\r\n        <el-table-column prop=\"perms\" label=\"权限标识\" />\r\n        <el-table-column prop=\"path\" label=\"路由地址\" />\r\n        <el-table-column prop=\"component\" label=\"组件路径\" />\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.status === '1' ? 'success' : 'info'\">\r\n              {{ scope.row.status === '1' ? '显示' : '隐藏' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button type=\"text\" size=\"small\" @click=\"handleAdd(scope.row)\">新增</el-button>\r\n            <el-button type=\"text\" size=\"small\" @click=\"handleEdit(scope.row)\">修改</el-button>\r\n            <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-card>\r\n\r\n    <!-- 添加或修改菜单对话框 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\">\r\n      <el-form :model=\"form\" :rules=\"rules\" ref=\"form\" label-width=\"100px\">\r\n        <el-form-item label=\"上级菜单\">\r\n          <el-tree-select\r\n            v-model=\"form.parentId\"\r\n            :data=\"menuOptions\"\r\n            :props=\"defaultProps\"\r\n            placeholder=\"选择上级菜单\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"菜单类型\">\r\n          <el-radio-group v-model=\"form.menuType\">\r\n            <el-radio label=\"M\">目录</el-radio>\r\n            <el-radio label=\"C\">菜单</el-radio>\r\n            <el-radio label=\"F\">按钮</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n          <el-input v-model=\"form.menuName\" placeholder=\"请输入菜单名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"图标\" v-if=\"form.menuType !== 'F'\">\r\n          <el-input v-model=\"form.icon\" placeholder=\"请选择图标\">\r\n            <el-button slot=\"append\" icon=\"el-icon-plus\">选择</el-button>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"sort\">\r\n          <el-input-number v-model=\"form.sort\" :min=\"0\" :max=\"999\" controls-position=\"right\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"路由地址\" v-if=\"form.menuType !== 'F'\" prop=\"path\">\r\n          <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"组件路径\" v-if=\"form.menuType === 'C'\" prop=\"component\">\r\n          <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"权限标识\" v-if=\"form.menuType === 'F'\" prop=\"perms\">\r\n          <el-input v-model=\"form.perms\" placeholder=\"请输入权限标识\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"显示状态\" v-if=\"form.menuType !== 'F'\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio label=\"1\">显示</el-radio>\r\n            <el-radio label=\"0\">隐藏</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SystemMenu',\r\n  data() {\r\n    return {\r\n      // 查询参数\r\n      listQuery: {\r\n        menuName: '',\r\n        status: ''\r\n      },\r\n      // 表格数据\r\n      tableData: [\r\n        {\r\n          id: '1',\r\n          menuName: '系统管理',\r\n          icon: 'el-icon-setting',\r\n          sort: 1,\r\n          perms: '',\r\n          path: '/system',\r\n          component: '',\r\n          status: '1',\r\n          children: [\r\n            {\r\n              id: '1-1',\r\n              menuName: '用户管理',\r\n              icon: 'el-icon-user',\r\n              sort: 1,\r\n              perms: 'system:user:list',\r\n              path: 'user',\r\n              component: 'system/user/index',\r\n              status: '1'\r\n            },\r\n            {\r\n              id: '1-2',\r\n              menuName: '角色管理',\r\n              icon: 'el-icon-s-custom',\r\n              sort: 2,\r\n              perms: 'system:role:list',\r\n              path: 'role',\r\n              component: 'system/role/index',\r\n              status: '1'\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'menuName',\r\n        value: 'id'\r\n      },\r\n      // 弹窗表单\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      form: {\r\n        parentId: '',\r\n        menuType: 'M',\r\n        menuName: '',\r\n        icon: '',\r\n        sort: 0,\r\n        path: '',\r\n        component: '',\r\n        perms: '',\r\n        status: '1'\r\n      },\r\n      // 表单校验规则\r\n      rules: {\r\n        menuName: [\r\n          { required: true, message: '请输入菜单名称', trigger: 'blur' }\r\n        ],\r\n        path: [\r\n          { required: true, message: '请输入路由地址', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.menuOptions = this.tableData\r\n  },\r\n  methods: {\r\n    // 新增菜单\r\n    handleAdd(row) {\r\n      this.dialogTitle = '添加菜单'\r\n      this.form = {\r\n        parentId: row ? row.id : '',\r\n        menuType: 'M',\r\n        menuName: '',\r\n        icon: '',\r\n        sort: 0,\r\n        path: '',\r\n        component: '',\r\n        perms: '',\r\n        status: '1'\r\n      }\r\n      this.dialogVisible = true\r\n    },\r\n    // 修改菜单\r\n    handleEdit(row) {\r\n      this.dialogTitle = '修改菜单'\r\n      this.form = { ...row }\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除菜单\r\n    handleDelete(row) {\r\n      this.$confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    // 表单提交\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.$message.success('操作成功')\r\n          this.dialogVisible = false\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .filter-container {\r\n    padding-bottom: 20px;\r\n    .filter-item {\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";AAoHA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA;MACAC,SAAA,GACA;QACAC,EAAA;QACAH,QAAA;QACAI,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;QACAC,SAAA;QACAP,MAAA;QACAQ,QAAA,GACA;UACAN,EAAA;UACAH,QAAA;UACAI,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,IAAA;UACAC,SAAA;UACAP,MAAA;QACA,GACA;UACAE,EAAA;UACAH,QAAA;UACAI,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,IAAA;UACAC,SAAA;UACAP,MAAA;QACA;MAEA,EACA;MACA;MACAS,WAAA;MACAC,YAAA;QACAF,QAAA;QACAG,KAAA;QACAC,KAAA;MACA;MACA;MACAC,aAAA;MACAC,WAAA;MACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAlB,QAAA;QACAI,IAAA;QACAC,IAAA;QACAE,IAAA;QACAC,SAAA;QACAF,KAAA;QACAL,MAAA;MACA;MACA;MACAkB,KAAA;QACAnB,QAAA,GACA;UAAAoB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,IAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAb,WAAA,QAAAR,SAAA;EACA;EACAsB,OAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAX,WAAA;MACA,KAAAC,IAAA;QACAC,QAAA,EAAAS,GAAA,GAAAA,GAAA,CAAAvB,EAAA;QACAe,QAAA;QACAlB,QAAA;QACAI,IAAA;QACAC,IAAA;QACAE,IAAA;QACAC,SAAA;QACAF,KAAA;QACAL,MAAA;MACA;MACA,KAAAa,aAAA;IACA;IACA;IACAa,UAAA,WAAAA,WAAAD,GAAA;MACA,KAAAX,WAAA;MACA,KAAAC,IAAA,GAAAY,aAAA,KAAAF,GAAA;MACA,KAAAZ,aAAA;IACA;IACA;IACAe,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,KAAA;MACA,KAAAC,QAAA,gBAAAL,GAAA,CAAA1B,QAAA;QACAgC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAL,KAAA,CAAAM,QAAA,CAAAC,OAAA;MACA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAxB,IAAA,CAAAyB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAH,QAAA,CAAAC,OAAA;UACAE,MAAA,CAAAzB,aAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}