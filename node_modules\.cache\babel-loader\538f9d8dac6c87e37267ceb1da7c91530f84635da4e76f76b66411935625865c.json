{"ast": null, "code": "'use strict';\n\nvar _typeof2 = require(\"E:/\\u65B0\\u9879\\u76EE/\\u6574\\u74066/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\nrequire(\"core-js/modules/es.symbol.js\");\nrequire(\"core-js/modules/es.symbol.description.js\");\nrequire(\"core-js/modules/es.symbol.iterator.js\");\nrequire(\"core-js/modules/es.error.cause.js\");\nrequire(\"core-js/modules/es.error.to-string.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.string.iterator.js\");\nrequire(\"core-js/modules/web.dom-collections.iterator.js\");\nrequire(\"core-js/modules/web.timers.js\");\nexports.__esModule = true;\nvar _typeof = typeof Symbol === \"function\" && _typeof2(Symbol.iterator) === \"symbol\" ? function (obj) {\n  return _typeof2(obj);\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : _typeof2(obj);\n};\nvar _ariaUtils = require('./aria-utils');\nvar _ariaUtils2 = _interopRequireDefault(_ariaUtils);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n\n/**\n * @constructor\n * @desc Dialog object providing modal focus management.\n *\n * Assumptions: The element serving as the dialog container is present in the\n * DOM and hidden. The dialog container has role='dialog'.\n *\n * @param dialogId\n *          The ID of the element serving as the dialog container.\n * @param focusAfterClosed\n *          Either the DOM node or the ID of the DOM node to focus when the\n *          dialog closes.\n * @param focusFirst\n *          Optional parameter containing either the DOM node or the ID of the\n *          DOM node to focus when the dialog opens. If not specified, the\n *          first focusable element in the dialog will receive focus.\n */\nvar aria = aria || {};\nvar tabEvent;\naria.Dialog = function (dialog, focusAfterClosed, focusFirst) {\n  var _this = this;\n  this.dialogNode = dialog;\n  if (this.dialogNode === null || this.dialogNode.getAttribute('role') !== 'dialog') {\n    throw new Error('Dialog() requires a DOM element with ARIA role of dialog.');\n  }\n  if (typeof focusAfterClosed === 'string') {\n    this.focusAfterClosed = document.getElementById(focusAfterClosed);\n  } else if ((typeof focusAfterClosed === 'undefined' ? 'undefined' : _typeof(focusAfterClosed)) === 'object') {\n    this.focusAfterClosed = focusAfterClosed;\n  } else {\n    this.focusAfterClosed = null;\n  }\n  if (typeof focusFirst === 'string') {\n    this.focusFirst = document.getElementById(focusFirst);\n  } else if ((typeof focusFirst === 'undefined' ? 'undefined' : _typeof(focusFirst)) === 'object') {\n    this.focusFirst = focusFirst;\n  } else {\n    this.focusFirst = null;\n  }\n  if (this.focusFirst) {\n    this.focusFirst.focus();\n  } else {\n    _ariaUtils2[\"default\"].focusFirstDescendant(this.dialogNode);\n  }\n  this.lastFocus = document.activeElement;\n  tabEvent = function tabEvent(e) {\n    _this.trapFocus(e);\n  };\n  this.addListeners();\n};\naria.Dialog.prototype.addListeners = function () {\n  document.addEventListener('focus', tabEvent, true);\n};\naria.Dialog.prototype.removeListeners = function () {\n  document.removeEventListener('focus', tabEvent, true);\n};\naria.Dialog.prototype.closeDialog = function () {\n  var _this2 = this;\n  this.removeListeners();\n  if (this.focusAfterClosed) {\n    setTimeout(function () {\n      _this2.focusAfterClosed.focus();\n    });\n  }\n};\naria.Dialog.prototype.trapFocus = function (event) {\n  if (_ariaUtils2[\"default\"].IgnoreUtilFocusChanges) {\n    return;\n  }\n  if (this.dialogNode.contains(event.target)) {\n    this.lastFocus = event.target;\n  } else {\n    _ariaUtils2[\"default\"].focusFirstDescendant(this.dialogNode);\n    if (this.lastFocus === document.activeElement) {\n      _ariaUtils2[\"default\"].focusLastDescendant(this.dialogNode);\n    }\n    this.lastFocus = document.activeElement;\n  }\n};\nexports[\"default\"] = aria.Dialog;", "map": {"version": 3, "names": ["_typeof2", "require", "exports", "__esModule", "_typeof", "Symbol", "iterator", "obj", "constructor", "prototype", "_ariaUtils", "_ariaUtils2", "_interopRequireDefault", "aria", "tabEvent", "Dialog", "dialog", "focusAfterClosed", "focusFirst", "_this", "dialogNode", "getAttribute", "Error", "document", "getElementById", "focus", "focusFirstDescendant", "lastFocus", "activeElement", "e", "trapFocus", "addListeners", "addEventListener", "removeListeners", "removeEventListener", "closeDialog", "_this2", "setTimeout", "event", "IgnoreUtilFocusChanges", "contains", "target", "focusLastDescendant"], "sources": ["E:/新项目/整理6/adminweb/node_modules/element-ui/lib/utils/aria-dialog.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _ariaUtils = require('./aria-utils');\n\nvar _ariaUtils2 = _interopRequireDefault(_ariaUtils);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @constructor\n * @desc Dialog object providing modal focus management.\n *\n * Assumptions: The element serving as the dialog container is present in the\n * DOM and hidden. The dialog container has role='dialog'.\n *\n * @param dialogId\n *          The ID of the element serving as the dialog container.\n * @param focusAfterClosed\n *          Either the DOM node or the ID of the DOM node to focus when the\n *          dialog closes.\n * @param focusFirst\n *          Optional parameter containing either the DOM node or the ID of the\n *          DOM node to focus when the dialog opens. If not specified, the\n *          first focusable element in the dialog will receive focus.\n */\nvar aria = aria || {};\nvar tabEvent;\n\naria.Dialog = function (dialog, focusAfterClosed, focusFirst) {\n  var _this = this;\n\n  this.dialogNode = dialog;\n  if (this.dialogNode === null || this.dialogNode.getAttribute('role') !== 'dialog') {\n    throw new Error('Dialog() requires a DOM element with ARIA role of dialog.');\n  }\n\n  if (typeof focusAfterClosed === 'string') {\n    this.focusAfterClosed = document.getElementById(focusAfterClosed);\n  } else if ((typeof focusAfterClosed === 'undefined' ? 'undefined' : _typeof(focusAfterClosed)) === 'object') {\n    this.focusAfterClosed = focusAfterClosed;\n  } else {\n    this.focusAfterClosed = null;\n  }\n\n  if (typeof focusFirst === 'string') {\n    this.focusFirst = document.getElementById(focusFirst);\n  } else if ((typeof focusFirst === 'undefined' ? 'undefined' : _typeof(focusFirst)) === 'object') {\n    this.focusFirst = focusFirst;\n  } else {\n    this.focusFirst = null;\n  }\n\n  if (this.focusFirst) {\n    this.focusFirst.focus();\n  } else {\n    _ariaUtils2.default.focusFirstDescendant(this.dialogNode);\n  }\n\n  this.lastFocus = document.activeElement;\n  tabEvent = function tabEvent(e) {\n    _this.trapFocus(e);\n  };\n  this.addListeners();\n};\n\naria.Dialog.prototype.addListeners = function () {\n  document.addEventListener('focus', tabEvent, true);\n};\n\naria.Dialog.prototype.removeListeners = function () {\n  document.removeEventListener('focus', tabEvent, true);\n};\n\naria.Dialog.prototype.closeDialog = function () {\n  var _this2 = this;\n\n  this.removeListeners();\n  if (this.focusAfterClosed) {\n    setTimeout(function () {\n      _this2.focusAfterClosed.focus();\n    });\n  }\n};\n\naria.Dialog.prototype.trapFocus = function (event) {\n  if (_ariaUtils2.default.IgnoreUtilFocusChanges) {\n    return;\n  }\n  if (this.dialogNode.contains(event.target)) {\n    this.lastFocus = event.target;\n  } else {\n    _ariaUtils2.default.focusFirstDescendant(this.dialogNode);\n    if (this.lastFocus === document.activeElement) {\n      _ariaUtils2.default.focusLastDescendant(this.dialogNode);\n    }\n    this.lastFocus = document.activeElement;\n  }\n};\n\nexports.default = aria.Dialog;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,QAAA,GAAAC,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIL,QAAA,CAAOK,MAAM,CAACC,QAAQ,MAAK,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAAP,QAAA,CAAcO,GAAG;AAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAI,OAAOF,MAAM,KAAK,UAAU,IAAIE,GAAG,CAACC,WAAW,KAAKH,MAAM,IAAIE,GAAG,KAAKF,MAAM,CAACI,SAAS,GAAG,QAAQ,GAAAT,QAAA,CAAUO,GAAG;AAAE,CAAC;AAE5Q,IAAIG,UAAU,GAAGT,OAAO,CAAC,cAAc,CAAC;AAExC,IAAIU,WAAW,GAAGC,sBAAsB,CAACF,UAAU,CAAC;AAEpD,SAASE,sBAAsBA,CAACL,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACJ,UAAU,GAAGI,GAAG,GAAG;IAAE,WAASA;EAAI,CAAC;AAAE;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIM,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;AACrB,IAAIC,QAAQ;AAEZD,IAAI,CAACE,MAAM,GAAG,UAAUC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAE;EAC5D,IAAIC,KAAK,GAAG,IAAI;EAEhB,IAAI,CAACC,UAAU,GAAGJ,MAAM;EACxB,IAAI,IAAI,CAACI,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,CAACC,YAAY,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;IACjF,MAAM,IAAIC,KAAK,CAAC,2DAA2D,CAAC;EAC9E;EAEA,IAAI,OAAOL,gBAAgB,KAAK,QAAQ,EAAE;IACxC,IAAI,CAACA,gBAAgB,GAAGM,QAAQ,CAACC,cAAc,CAACP,gBAAgB,CAAC;EACnE,CAAC,MAAM,IAAI,CAAC,OAAOA,gBAAgB,KAAK,WAAW,GAAG,WAAW,GAAGb,OAAO,CAACa,gBAAgB,CAAC,MAAM,QAAQ,EAAE;IAC3G,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;EAC1C,CAAC,MAAM;IACL,IAAI,CAACA,gBAAgB,GAAG,IAAI;EAC9B;EAEA,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAAE;IAClC,IAAI,CAACA,UAAU,GAAGK,QAAQ,CAACC,cAAc,CAACN,UAAU,CAAC;EACvD,CAAC,MAAM,IAAI,CAAC,OAAOA,UAAU,KAAK,WAAW,GAAG,WAAW,GAAGd,OAAO,CAACc,UAAU,CAAC,MAAM,QAAQ,EAAE;IAC/F,IAAI,CAACA,UAAU,GAAGA,UAAU;EAC9B,CAAC,MAAM;IACL,IAAI,CAACA,UAAU,GAAG,IAAI;EACxB;EAEA,IAAI,IAAI,CAACA,UAAU,EAAE;IACnB,IAAI,CAACA,UAAU,CAACO,KAAK,CAAC,CAAC;EACzB,CAAC,MAAM;IACLd,WAAW,WAAQ,CAACe,oBAAoB,CAAC,IAAI,CAACN,UAAU,CAAC;EAC3D;EAEA,IAAI,CAACO,SAAS,GAAGJ,QAAQ,CAACK,aAAa;EACvCd,QAAQ,GAAG,SAASA,QAAQA,CAACe,CAAC,EAAE;IAC9BV,KAAK,CAACW,SAAS,CAACD,CAAC,CAAC;EACpB,CAAC;EACD,IAAI,CAACE,YAAY,CAAC,CAAC;AACrB,CAAC;AAEDlB,IAAI,CAACE,MAAM,CAACN,SAAS,CAACsB,YAAY,GAAG,YAAY;EAC/CR,QAAQ,CAACS,gBAAgB,CAAC,OAAO,EAAElB,QAAQ,EAAE,IAAI,CAAC;AACpD,CAAC;AAEDD,IAAI,CAACE,MAAM,CAACN,SAAS,CAACwB,eAAe,GAAG,YAAY;EAClDV,QAAQ,CAACW,mBAAmB,CAAC,OAAO,EAAEpB,QAAQ,EAAE,IAAI,CAAC;AACvD,CAAC;AAEDD,IAAI,CAACE,MAAM,CAACN,SAAS,CAAC0B,WAAW,GAAG,YAAY;EAC9C,IAAIC,MAAM,GAAG,IAAI;EAEjB,IAAI,CAACH,eAAe,CAAC,CAAC;EACtB,IAAI,IAAI,CAAChB,gBAAgB,EAAE;IACzBoB,UAAU,CAAC,YAAY;MACrBD,MAAM,CAACnB,gBAAgB,CAACQ,KAAK,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ;AACF,CAAC;AAEDZ,IAAI,CAACE,MAAM,CAACN,SAAS,CAACqB,SAAS,GAAG,UAAUQ,KAAK,EAAE;EACjD,IAAI3B,WAAW,WAAQ,CAAC4B,sBAAsB,EAAE;IAC9C;EACF;EACA,IAAI,IAAI,CAACnB,UAAU,CAACoB,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;IAC1C,IAAI,CAACd,SAAS,GAAGW,KAAK,CAACG,MAAM;EAC/B,CAAC,MAAM;IACL9B,WAAW,WAAQ,CAACe,oBAAoB,CAAC,IAAI,CAACN,UAAU,CAAC;IACzD,IAAI,IAAI,CAACO,SAAS,KAAKJ,QAAQ,CAACK,aAAa,EAAE;MAC7CjB,WAAW,WAAQ,CAAC+B,mBAAmB,CAAC,IAAI,CAACtB,UAAU,CAAC;IAC1D;IACA,IAAI,CAACO,SAAS,GAAGJ,QAAQ,CAACK,aAAa;EACzC;AACF,CAAC;AAED1B,OAAO,WAAQ,GAAGW,IAAI,CAACE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}