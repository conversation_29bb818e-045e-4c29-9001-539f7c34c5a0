{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport request from '@/utils/request';\nexport default {\n  name: 'CopyTrade',\n  data: function data() {\n    return {\n      // 当前激活的标签页\n      activeTab: 'leader',\n      // 带单管理相关\n      leaderLoading: false,\n      leaderList: [],\n      leaderTotal: 0,\n      leaderOpen: false,\n      leaderTitle: '',\n      leaderForm: {},\n      leaderDateRange: [],\n      leaderQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        leaderNickname: null,\n        status: null\n      },\n      leaderRules: {\n        leaderNickname: [{\n          required: true,\n          message: '带单人昵称不能为空',\n          trigger: 'blur'\n        }],\n        symbol: [{\n          required: true,\n          message: '交易对不能为空',\n          trigger: 'blur'\n        }],\n        periodNo: [{\n          required: true,\n          message: '期号不能为空',\n          trigger: 'blur'\n        }]\n      },\n      // 跟单管理明细相关\n      detailLoading: false,\n      detailList: [],\n      detailTotal: 0,\n      detailQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        followerUsername: '',\n        followerUid: '',\n        followerEmail: '',\n        status: '',\n        isFollowing: '',\n        resultStatus: '',\n        isReturned: '',\n        isSettled: ''\n      },\n      detailDetailDialogVisible: false,\n      detailDetailRow: {},\n      // 跟单明细相关\n      historyLoading: false,\n      historyList: [],\n      historyTotal: 0,\n      historyDateRange: [],\n      historyQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        followerUsername: '',\n        followerEmail: '',\n        leaderNickname: '',\n        isReturned: '',\n        resultStatus: ''\n      },\n      historyDetailDialogVisible: false,\n      historyDetailRow: {},\n      // 选中数组\n      ids: [],\n      single: true,\n      multiple: true,\n      uploadHeaders: {\n        Authorization: 'Bearer ' + localStorage.getItem('token')\n      },\n      pairNameList: []\n    };\n  },\n  created: function created() {\n    this.getLeaderList();\n  },\n  mounted: function mounted() {\n    this.getLeaderInfo();\n    this.getPairNameList();\n  },\n  methods: {\n    // 标签页切换\n    handleTabClick: function handleTabClick(tab) {\n      if (tab.name === 'leader') {\n        this.getLeaderInfo();\n      } else if (tab.name === 'follow') {\n        this.getDetailList();\n      } else if (tab.name === 'history') {\n        this.getHistoryList();\n      }\n    },\n    // 带单管理相关方法\n    getLeaderList: function getLeaderList() {\n      this.leaderLoading = true;\n      // TODO: 调用API获取带单管理数据\n      this.leaderLoading = false;\n    },\n    handleLeaderQuery: function handleLeaderQuery() {\n      this.leaderQueryParams.pageNum = 1;\n      this.getLeaderList();\n    },\n    resetLeaderQuery: function resetLeaderQuery() {\n      this.leaderDateRange = [];\n      this.resetForm('leaderQueryForm');\n      this.handleLeaderQuery();\n    },\n    handleAddLeader: function handleAddLeader() {\n      this.resetLeaderForm();\n      this.leaderOpen = true;\n      this.leaderTitle = '添加带单管理';\n    },\n    handleUpdateLeader: function handleUpdateLeader(row) {\n      this.resetLeaderForm();\n      // TODO: 获取详情数据\n      this.leaderOpen = true;\n      this.leaderTitle = '修改带单管理';\n    },\n    handleViewLeader: function handleViewLeader(row) {\n      // TODO: 实现查看详情\n    },\n    handleDeleteLeader: function handleDeleteLeader(row) {\n      // TODO: 实现删除功能\n    },\n    handleLeaderSelectionChange: function handleLeaderSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    handleLeaderSizeChange: function handleLeaderSizeChange(val) {\n      this.leaderQueryParams.pageSize = val;\n      this.getLeaderList();\n    },\n    handleLeaderCurrentChange: function handleLeaderCurrentChange(val) {\n      this.leaderQueryParams.pageNum = val;\n      this.getLeaderList();\n    },\n    resetLeaderForm: function resetLeaderForm() {\n      this.leaderForm = {\n        id: null,\n        leaderNickname: null,\n        symbol: null,\n        periodNo: null,\n        marginBalance: 900000,\n        remark: null,\n        status: 0\n      };\n    },\n    submitLeaderForm: function submitLeaderForm() {\n      var _this = this;\n      this.$refs['leaderForm'].validate(function (valid) {\n        if (valid) {\n          // TODO: 提交表单\n          _this.leaderOpen = false;\n          _this.getLeaderList();\n        }\n      });\n    },\n    // 跟单管理明细相关方法\n    getDetailList: function getDetailList() {\n      var _this2 = this;\n      this.detailLoading = true;\n      request({\n        url: '/api/copyFollowDetail/list',\n        method: 'get',\n        params: this.detailQueryParams\n      }).then(function (res) {\n        _this2.detailList = res.data && res.data.records || res.records || [];\n        _this2.detailTotal = res.data && res.data.total || res.total || 0;\n      })[\"finally\"](function () {\n        _this2.detailLoading = false;\n      });\n    },\n    handleDetailQuery: function handleDetailQuery() {\n      this.detailQueryParams.pageNum = 1;\n      this.getDetailList();\n    },\n    resetDetailQuery: function resetDetailQuery() {\n      this.detailQueryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        followerUsername: '',\n        followerUid: '',\n        followerEmail: '',\n        status: '',\n        isFollowing: '',\n        resultStatus: '',\n        isReturned: '',\n        isSettled: ''\n      };\n      this.getDetailList();\n    },\n    handleDetailSizeChange: function handleDetailSizeChange(val) {\n      this.detailQueryParams.pageSize = val;\n      this.getDetailList();\n    },\n    handleDetailCurrentChange: function handleDetailCurrentChange(val) {\n      this.detailQueryParams.pageNum = val;\n      this.getDetailList();\n    },\n    showDetailDetail: function showDetailDetail(row) {\n      this.detailDetailRow = row;\n      this.detailDetailDialogVisible = true;\n    },\n    // 跟单明细相关方法\n    getHistoryList: function getHistoryList() {\n      var _this3 = this;\n      this.historyLoading = true;\n      request({\n        url: '/api/copyFollowHistory/list',\n        method: 'get',\n        params: this.historyQueryParams\n      }).then(function (res) {\n        _this3.historyList = res.data && res.data.records || res.records || [];\n        _this3.historyTotal = res.data && res.data.total || res.total || 0;\n      })[\"finally\"](function () {\n        _this3.historyLoading = false;\n      });\n    },\n    handleHistoryQuery: function handleHistoryQuery() {\n      this.historyQueryParams.pageNum = 1;\n      this.getHistoryList();\n    },\n    resetHistoryQuery: function resetHistoryQuery() {\n      this.historyQueryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        followerUsername: '',\n        followerEmail: '',\n        leaderNickname: '',\n        isReturned: '',\n        resultStatus: ''\n      };\n      this.getHistoryList();\n    },\n    handleHistorySizeChange: function handleHistorySizeChange(val) {\n      this.historyQueryParams.pageSize = val;\n      this.getHistoryList();\n    },\n    handleHistoryCurrentChange: function handleHistoryCurrentChange(val) {\n      this.historyQueryParams.pageNum = val;\n      this.getHistoryList();\n    },\n    showHistoryDetail: function showHistoryDetail(row) {\n      this.historyDetailRow = row;\n      this.historyDetailDialogVisible = true;\n    },\n    // 状态相关方法\n    getLeaderStatusText: function getLeaderStatusText(status) {\n      var statusMap = {\n        0: '未开始',\n        1: '准备中',\n        2: '已开始',\n        3: '结算中',\n        4: '已结束'\n      };\n      return statusMap[status] || '未知';\n    },\n    getLeaderStatusType: function getLeaderStatusType(status) {\n      var typeMap = {\n        0: 'info',\n        1: 'warning',\n        2: 'success',\n        3: 'primary',\n        4: 'danger'\n      };\n      return typeMap[status] || 'info';\n    },\n    getHistoryResultText: function getHistoryResultText(status) {\n      var resultMap = {\n        0: '未结算',\n        1: '盈利',\n        2: '亏损'\n      };\n      return resultMap[status] || '未知';\n    },\n    getHistoryResultType: function getHistoryResultType(status) {\n      var typeMap = {\n        0: 'info',\n        1: 'success',\n        2: 'danger'\n      };\n      return typeMap[status] || 'info';\n    },\n    // 通用方法\n    formatDateTime: function formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      var date = new Date(dateTime);\n      var pad = function pad(n) {\n        return n < 10 ? '0' + n : n;\n      };\n      return \"\".concat(date.getFullYear(), \"-\").concat(pad(date.getMonth() + 1), \"-\").concat(pad(date.getDate()), \" \") + \"\".concat(pad(date.getHours()), \":\").concat(pad(date.getMinutes()), \":\").concat(pad(date.getSeconds()));\n    },\n    getLeaderInfo: function getLeaderInfo() {\n      var _this4 = this;\n      request({\n        url: '/api/copyLeader/info',\n        method: 'get'\n      }).then(function (res) {\n        _this4.leaderForm = res.data || res;\n      });\n    },\n    saveLeaderInfo: function saveLeaderInfo() {\n      var _this5 = this;\n      request({\n        url: '/api/copyLeader/update',\n        method: 'post',\n        data: this.leaderForm\n      }).then(function () {\n        _this5.$message.success('保存成功');\n        _this5.getLeaderInfo();\n      });\n    },\n    beforeAvatarUpload: function beforeAvatarUpload(file) {\n      var isJPG = file.type === 'image/jpeg';\n      var isPNG = file.type === 'image/png';\n      var isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isJPG && !isPNG) {\n        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');\n        return false;\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!');\n        return false;\n      }\n      return true;\n    },\n    handleAvatarSuccess: function handleAvatarSuccess(res) {\n      if (res.code === 0) {\n        this.leaderForm.leaderAvatar = res.data;\n        this.$message.success('上传成功');\n      } else {\n        this.$message.error(res.msg || '上传失败');\n      }\n    },\n    handleAvatarError: function handleAvatarError(err) {\n      this.$message.error('上传失败，请重试');\n    },\n    getPairNameList: function getPairNameList() {\n      var _this6 = this;\n      request({\n        url: '/exchange/pair/enabledList',\n        method: 'get'\n      }).then(function (res) {\n        _this6.pairNameList = res.data || [];\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "activeTab", "leader<PERSON><PERSON><PERSON>", "leaderList", "leader<PERSON><PERSON><PERSON>", "leader<PERSON><PERSON>", "leader<PERSON><PERSON><PERSON>", "leader<PERSON><PERSON>", "leader<PERSON><PERSON><PERSON><PERSON><PERSON>", "leader<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageNum", "pageSize", "leader<PERSON><PERSON><PERSON>", "status", "leader<PERSON><PERSON>", "required", "message", "trigger", "symbol", "periodNo", "detailLoading", "detailList", "detailTotal", "detailQueryParams", "followerUsername", "followerUid", "followerEmail", "isFollowing", "resultStatus", "isReturned", "isSettled", "detailDetailDialogVisible", "detailDetailRow", "historyLoading", "historyList", "historyTotal", "historyDateRange", "historyQueryParams", "historyDetailDialogVisible", "historyDetailRow", "ids", "single", "multiple", "uploadHeaders", "Authorization", "localStorage", "getItem", "pairNameList", "created", "getLeaderList", "mounted", "getLeaderInfo", "getPairNameList", "methods", "handleTabClick", "tab", "getDetailList", "getHistoryList", "handleLeaderQuery", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetForm", "handleAddLeader", "resetLeaderForm", "handleUpdateLeader", "row", "handleViewLeader", "handleDeleteLeader", "handleLeaderSelectionChange", "selection", "map", "item", "id", "length", "handleLeaderSizeChange", "val", "handleLeaderCurrentChange", "marginBalance", "remark", "submitLeaderForm", "_this", "$refs", "validate", "valid", "_this2", "url", "method", "params", "then", "res", "records", "total", "handleDetailQuery", "resetDetail<PERSON><PERSON>y", "handleDetailSizeChange", "handleDetailCurrentChange", "showDetailDetail", "_this3", "handleHist<PERSON><PERSON><PERSON>y", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleHistorySizeChange", "handleHistoryCurrentChange", "showHistoryDetail", "getLeaderStatusText", "statusMap", "getLeaderStatusType", "typeMap", "getHistoryResultText", "resultMap", "getHistoryResultType", "formatDateTime", "dateTime", "date", "Date", "pad", "n", "concat", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "_this4", "saveLeaderInfo", "_this5", "$message", "success", "beforeAvatarUpload", "file", "isJPG", "type", "isPNG", "isLt2M", "size", "error", "handleAvatarSuccess", "code", "<PERSON><PERSON><PERSON><PERSON>", "msg", "handleAvatarError", "err", "_this6"], "sources": ["src/views/exchange/copy-trade/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <!-- 标签页 -->\r\n      <el-tabs class=\"deal-tabs\" v-model=\"activeTab\" @tab-click=\"handleTabClick\">\r\n        <el-tab-pane label=\"带单管理\" name=\"leader\">\r\n          <div class=\"tab-content\">\r\n            <el-form :model=\"leaderForm\" label-width=\"120px\" style=\"max-width: 900px;\">\r\n              <el-row :gutter=\"24\">\r\n                <!-- 左侧头像 -->\r\n                <el-col :span=\"6\">\r\n                  <el-form-item label=\"头像\" label-width=\"60px\">\r\n                    <el-upload\r\n                      class=\"avatar-uploader\"\r\n                      action=\"/upload/avatar\"\r\n                      :headers=\"uploadHeaders\"\r\n                      :show-file-list=\"false\"\r\n                      :on-success=\"handleAvatarSuccess\"\r\n                      :on-error=\"handleAvatarError\"\r\n                      :before-upload=\"beforeAvatarUpload\"\r\n                      :drag=\"false\"\r\n                    >\r\n                      <img v-if=\"leaderForm.leaderAvatar\" :src=\"leaderForm.leaderAvatar\" class=\"avatar\" />\r\n                      <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\r\n                    </el-upload>\r\n                    <div class=\"upload-tip\">建议尺寸：80x80像素，支持jpg、png格式，大小不超过2MB</div>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <!-- 右侧表单内容 -->\r\n                <el-col :span=\"18\">\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"带单人昵称\">\r\n                        <el-input v-model=\"leaderForm.leaderNickname\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-col :span=\"12\">\r\n                      <el-form-item label=\"期号\">\r\n                        <el-input v-model=\"leaderForm.periodNo\" disabled />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                      \r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"当前价格\">\r\n                        <el-input v-model=\"leaderForm.currentPrice\" disabled />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-form-item label=\"当前交易对\">\r\n                        <el-select v-model=\"leaderForm.symbol\" placeholder=\"请选择交易对\" filterable>\r\n                          <el-option\r\n                            v-for=\"item in pairNameList\"\r\n                            :key=\"item\"\r\n                            :label=\"item\"\r\n                            :value=\"item\"\r\n                          />\r\n                        </el-select>\r\n                      </el-form-item>\r\n                  </el-row>\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"开始时间\">\r\n                        <el-date-picker v-model=\"leaderForm.startTime\" type=\"datetime\" style=\"width: 100%;\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"结束时间\">\r\n                        <el-date-picker v-model=\"leaderForm.endTime\" type=\"datetime\" style=\"width: 100%;\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"状态\">\r\n                        <el-select v-model=\"leaderForm.status\" style=\"width: 100%;\" disabled>\r\n                          <el-option label=\"未开始\" :value=\"0\" />\r\n                          <el-option label=\"准备中\" :value=\"1\" />\r\n                          <el-option label=\"已开始\" :value=\"2\" />\r\n                          <el-option label=\"结算中\" :value=\"3\" />\r\n                          <el-option label=\"已结束\" :value=\"4\" />\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"累计收益\">\r\n                        <el-input v-model=\"leaderForm.totalProfit\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"胜率\">\r\n                        <el-input v-model=\"leaderForm.winRate\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"累计跟单人数\">\r\n                        <el-input v-model=\"leaderForm.followerCount\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"本次带单总收益\">\r\n                        <el-input v-model=\"leaderForm.currentProfit\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"本次带单收益率\">\r\n                        <el-input v-model=\"leaderForm.profitRate\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"保证金余额\">\r\n                        <el-input v-model=\"leaderForm.marginBalance\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                      <el-form-item label=\"做多/做空\">\r\n                        <el-select v-model=\"leaderForm.winOrLose\" style=\"width: 100%;\">\r\n                          <el-option label=\"做多\" :value=\"0\" />\r\n                          <el-option label=\"做空\" :value=\"1\" />\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"24\">\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"策略说明\">\r\n                        <el-input v-model=\"leaderForm.remark\" type=\"textarea\" />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\" style=\"text-align: center;\">\r\n                  <el-form-item>\r\n                    <el-button type=\"primary\" @click=\"saveLeaderInfo\">保存</el-button>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"跟单管理\" name=\"follow\">\r\n          <!-- 跟单管理明细内容 -->\r\n          <div class=\"tab-content\">\r\n            <!-- 筛选区域 -->\r\n            <div class=\"filter-container\">\r\n              <el-row :gutter=\"8\" class=\"filter-row\" type=\"flex\" align=\"middle\">\r\n                <el-col :span=\"3\">\r\n                  <el-input v-model.trim=\"detailQueryParams.followerUsername\" placeholder=\"用户名\" clearable class=\"filter-item\" />\r\n                </el-col>\r\n                <el-col :span=\"3\">\r\n                  <el-input v-model.trim=\"detailQueryParams.followerUid\" placeholder=\"UID\" clearable class=\"filter-item\" />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-input v-model.trim=\"detailQueryParams.followerEmail\" placeholder=\"邮箱\" clearable class=\"filter-item\" />\r\n                </el-col>\r\n                <el-col :span=\"3\">\r\n                  <el-select v-model=\"detailQueryParams.status\" placeholder=\"跟单状态\" clearable class=\"filter-item\">\r\n                    <el-option label=\"全部\" value=\"\" />\r\n                    <el-option label=\"未开始\" :value=\"0\" />\r\n                    <el-option label=\"准备中\" :value=\"1\" />\r\n                    <el-option label=\"已开始\" :value=\"2\" />\r\n                    <el-option label=\"结算中\" :value=\"3\" />\r\n                    <el-option label=\"已结束\" :value=\"4\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"3\">\r\n                  <el-select v-model=\"detailQueryParams.isFollowing\" placeholder=\"是否一键跟单\" clearable class=\"filter-item\">\r\n                    <el-option label=\"全部\" value=\"\" />\r\n                    <el-option label=\"否\" :value=\"0\" />\r\n                    <el-option label=\"是\" :value=\"1\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"3\">\r\n                  <el-select v-model=\"detailQueryParams.resultStatus\" placeholder=\"结算结果\" clearable class=\"filter-item\">\r\n                    <el-option label=\"全部\" value=\"\" />\r\n                    <el-option label=\"未结算\" :value=\"0\" />\r\n                    <el-option label=\"盈利\" :value=\"1\" />\r\n                    <el-option label=\"亏损\" :value=\"2\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"2\">\r\n                  <el-select v-model=\"detailQueryParams.isReturned\" placeholder=\"是否返本\" clearable class=\"filter-item\">\r\n                    <el-option label=\"全部\" value=\"\" />\r\n                    <el-option label=\"否\" :value=\"0\" />\r\n                    <el-option label=\"是\" :value=\"1\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"2\">\r\n                  <el-select v-model=\"detailQueryParams.isSettled\" placeholder=\"是否已结算\" clearable class=\"filter-item\">\r\n                    <el-option label=\"全部\" value=\"\" />\r\n                    <el-option label=\"否\" :value=\"0\" />\r\n                    <el-option label=\"是\" :value=\"1\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"4\" style=\"display: flex; gap: 8px;\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleDetailQuery\">搜索</el-button>\r\n                  <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetDetailQuery\">重置</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <!-- 表格 -->\r\n            <el-table :data=\"detailList\" v-loading=\"detailLoading\" border style=\"width: 100%; margin-top: 16px;\">\r\n              <el-table-column type=\"index\" label=\"序号\" align=\"center\" width=\"60\" />\r\n              <el-table-column prop=\"periodNo\" label=\"期号\" align=\"center\" min-width=\"120\" />\r\n              <el-table-column prop=\"followerNickname\" label=\"跟单人昵称\" align=\"center\" min-width=\"100\" />\r\n              <el-table-column prop=\"followerUsername\" label=\"用户名\" align=\"center\" min-width=\"100\" />\r\n              <el-table-column prop=\"userNo\" label=\"UID\" align=\"center\" min-width=\"100\" />\r\n              <el-table-column prop=\"followerEmail\" label=\"邮箱\" align=\"center\" min-width=\"160\" />\r\n              <el-table-column prop=\"followAmount\" label=\"跟单金额\" align=\"center\" min-width=\"120\" />\r\n              <el-table-column prop=\"status\" label=\"跟单状态\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getLeaderStatusType(scope.row.status)\">\r\n                    {{ getLeaderStatusText(scope.row.status) }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"isFollowing\" label=\"是否一键跟单\" align=\"center\" min-width=\"130\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.isFollowing === 1 ? 'success' : 'info'\">\r\n                    {{ scope.row.isFollowing === 1 ? '是' : '否' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"resultStatus\" label=\"结算结果\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getHistoryResultType(scope.row.resultStatus)\">\r\n                    {{ getHistoryResultText(scope.row.resultStatus) }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"isReturned\" label=\"是否返本\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.isReturned === 1 ? 'success' : 'info'\">\r\n                    {{ scope.row.isReturned === 1 ? '是' : '否' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"isSettled\" label=\"是否已结算\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.isSettled === 1 ? 'success' : 'info'\">\r\n                    {{ scope.row.isSettled === 1 ? '是' : '否' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"followTime\" label=\"跟单时间\" align=\"center\" min-width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.followTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"settleTime\" label=\"结算时间\" align=\"center\" min-width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.settleTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"leaderNickname\" label=\"带单人昵称\" align=\"center\" min-width=\"100\" />\r\n              <el-table-column label=\"操作\" align=\"center\" width=\"80\" fixed=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"mini\" @click=\"showDetailDetail(scope.row)\">详情</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-container\">\r\n              <el-pagination\r\n                background\r\n                @size-change=\"handleDetailSizeChange\"\r\n                @current-change=\"handleDetailCurrentChange\"\r\n                :current-page=\"detailQueryParams.pageNum\"\r\n                :page-sizes=\"[10, 20, 30, 50]\"\r\n                :page-size=\"detailQueryParams.pageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                :total=\"detailTotal\">\r\n              </el-pagination>\r\n            </div>\r\n            <!-- 详情弹窗 -->\r\n            <el-dialog :visible.sync=\"detailDetailDialogVisible\" title=\"跟单明细详情\" width=\"800px\">\r\n              <el-descriptions :column=\"2\" border>\r\n                <el-descriptions-item label=\"期号\">{{ detailDetailRow.periodNo }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"跟单人昵称\">{{ detailDetailRow.followerNickname }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"用户名\">{{ detailDetailRow.followerUsername }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"UID\">{{ detailDetailRow.userNo }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"邮箱\">{{ detailDetailRow.followerEmail }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"跟单金额\">{{ detailDetailRow.followAmount }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"跟单状态\">{{ getLeaderStatusText(detailDetailRow.status) }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"是否一键跟单\">{{ detailDetailRow.isFollowing === 1 ? '是' : '否' }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"结算结果\">{{ getHistoryResultText(detailDetailRow.resultStatus) }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"是否返本\">{{ detailDetailRow.isReturned === 1 ? '是' : '否' }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"是否已结算\">{{ detailDetailRow.isSettled === 1 ? '是' : '否' }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"跟单时间\">{{ formatDateTime(detailDetailRow.followTime) }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"结算时间\">{{ formatDateTime(detailDetailRow.settleTime) }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"带单人昵称\">{{ detailDetailRow.leaderNickname }}</el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-dialog>\r\n          </div>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"跟单明细\" name=\"history\">\r\n          <!-- 跟单明细内容 -->\r\n          <div class=\"tab-content\">\r\n            <!-- 搜索区域 -->\r\n            <div class=\"filter-container\">\r\n              <el-row :gutter=\"8\" class=\"filter-row\" type=\"flex\" align=\"middle\">\r\n                <el-col :span=\"4\">\r\n                  <el-input v-model.trim=\"historyQueryParams.followerUsername\" placeholder=\"跟单人用户名\" clearable class=\"filter-item\" />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-input v-model.trim=\"historyQueryParams.followerEmail\" placeholder=\"跟单人邮箱\" clearable class=\"filter-item\" />\r\n                </el-col>\r\n                <!-- <el-col :span=\"4\">\r\n                  <el-input v-model.trim=\"historyQueryParams.leaderNickname\" placeholder=\"带单人昵称\" clearable class=\"filter-item\" />\r\n                </el-col> -->\r\n                <el-col :span=\"4\">\r\n                  <el-select v-model=\"historyQueryParams.isReturned\" placeholder=\"是否返本\" clearable class=\"filter-item\">\r\n                    <el-option label=\"全部\" value=\"\" />\r\n                    <el-option label=\"否\" :value=\"0\" />\r\n                    <el-option label=\"是\" :value=\"1\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-select v-model=\"historyQueryParams.resultStatus\" placeholder=\"结算结果\" clearable class=\"filter-item\">\r\n                    <el-option label=\"全部\" value=\"\" />\r\n                    <el-option label=\"未结算\" :value=\"0\" />\r\n                    <el-option label=\"盈利\" :value=\"1\" />\r\n                    <el-option label=\"亏损\" :value=\"2\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"4\" style=\"display: flex; gap: 8px;\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleHistoryQuery\">搜索</el-button>\r\n                  <el-button type=\"success\" icon=\"el-icon-refresh\" @click=\"resetHistoryQuery\">重置</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n            <!-- 跟单明细表格 -->\r\n            <el-table :data=\"historyList\" v-loading=\"historyLoading\" border style=\"width: 100%; margin-top: 16px;\">\r\n              <el-table-column type=\"index\" label=\"序号\" align=\"center\" width=\"60\" />\r\n              <el-table-column prop=\"periodNo\" label=\"期号\" align=\"center\" min-width=\"120\" />\r\n              <el-table-column prop=\"leaderNickname\" label=\"带单人\" align=\"center\" min-width=\"100\" />\r\n              <el-table-column prop=\"followerUsername\" label=\"跟单人\" align=\"center\" min-width=\"100\" />\r\n              <el-table-column prop=\"followerEmail\" label=\"跟单人邮箱\" align=\"center\" min-width=\"160\" />\r\n              <el-table-column prop=\"symbol\" label=\"交易对\" align=\"center\" min-width=\"100\" />\r\n              <el-table-column prop=\"profit\" label=\"盈亏\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.profit >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.profit }}\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"profitRate\" label=\"收益率\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :class=\"scope.row.profitRate >= 0 ? 'text-success' : 'text-danger'\">\r\n                    {{ scope.row.profitRate }}%\r\n                  </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"resultStatus\" label=\"结算结果\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"getHistoryResultType(scope.row.resultStatus)\">\r\n                    {{ getHistoryResultText(scope.row.resultStatus) }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"isReturned\" label=\"是否返本\" align=\"center\" min-width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.isReturned === 1 ? 'success' : 'info'\">\r\n                    {{ scope.row.isReturned === 1 ? '是' : '否' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"followTime\" label=\"跟单时间\" align=\"center\" min-width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.followTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column prop=\"settleTime\" label=\"结算时间\" align=\"center\" min-width=\"160\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ formatDateTime(scope.row.settleTime) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" align=\"center\" width=\"80\" fixed=\"right\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"mini\" @click=\"showHistoryDetail(scope.row)\">详情</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <!-- 分页 -->\r\n            <div class=\"pagination-container\">\r\n              <el-pagination\r\n                background\r\n                @size-change=\"handleHistorySizeChange\"\r\n                @current-change=\"handleHistoryCurrentChange\"\r\n                :current-page=\"historyQueryParams.pageNum\"\r\n                :page-sizes=\"[10, 20, 30, 50]\"\r\n                :page-size=\"historyQueryParams.pageSize\"\r\n                layout=\"total, sizes, prev, pager, next, jumper\"\r\n                :total=\"historyTotal\">\r\n              </el-pagination>\r\n            </div>\r\n            <!-- 详情弹窗 -->\r\n            <el-dialog :visible.sync=\"historyDetailDialogVisible\" title=\"跟单明细详情\" width=\"800px\">\r\n              <el-descriptions :column=\"2\" border>\r\n                <el-descriptions-item label=\"期号\">{{ historyDetailRow.periodNo }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"带单人\">{{ historyDetailRow.leaderNickname }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"跟单人\">{{ historyDetailRow.followerUsername }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"跟单人邮箱\">{{ historyDetailRow.followerEmail }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"交易对\">{{ historyDetailRow.symbol }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"盈亏\">{{ historyDetailRow.profit }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"收益率\">{{ historyDetailRow.profitRate }}%</el-descriptions-item>\r\n                <el-descriptions-item label=\"结算结果\">\r\n                  <el-tag :type=\"getHistoryResultType(historyDetailRow.resultStatus)\">\r\n                    {{ getHistoryResultText(historyDetailRow.resultStatus) }}\r\n                  </el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"是否返本\">\r\n                  <el-tag :type=\"historyDetailRow.isReturned === 1 ? 'success' : 'info'\">\r\n                    {{ historyDetailRow.isReturned === 1 ? '是' : '否' }}\r\n                  </el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"跟单时间\">{{ formatDateTime(historyDetailRow.followTime) }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"结算时间\">{{ formatDateTime(historyDetailRow.settleTime) }}</el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-dialog>\r\n          </div>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <!-- 带单管理对话框 -->\r\n      <el-dialog :title=\"leaderTitle\" :visible.sync=\"leaderOpen\" width=\"600px\" append-to-body>\r\n        <el-form ref=\"leaderForm\" :model=\"leaderForm\" :rules=\"leaderRules\" label-width=\"100px\">\r\n          <el-form-item label=\"带单人昵称\" prop=\"leaderNickname\">\r\n            <el-input v-model=\"leaderForm.leaderNickname\" placeholder=\"请输入带单人昵称\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"交易对\" prop=\"symbol\">\r\n            <el-input v-model=\"leaderForm.symbol\" placeholder=\"请输入交易对\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"期号\" prop=\"periodNo\">\r\n            <el-input v-model=\"leaderForm.periodNo\" placeholder=\"请输入期号\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"保证金\" prop=\"marginBalance\">\r\n            <el-input-number v-model=\"leaderForm.marginBalance\" :precision=\"8\" :step=\"0.00000001\" :min=\"0\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"策略说明\" prop=\"remark\">\r\n            <el-input v-model=\"leaderForm.remark\" type=\"textarea\" placeholder=\"请输入策略说明\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-radio-group v-model=\"leaderForm.status\">\r\n              <el-radio :label=\"0\">未开始</el-radio>\r\n              <el-radio :label=\"1\">准备中</el-radio>\r\n              <el-radio :label=\"2\">已开始</el-radio>\r\n              <el-radio :label=\"3\">结算中</el-radio>\r\n              <el-radio :label=\"4\">已结束</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitLeaderForm\">确 定</el-button>\r\n          <el-button @click=\"leaderOpen = false\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request'\r\nexport default {\r\n  name: 'CopyTrade',\r\n  data() {\r\n    return {\r\n      // 当前激活的标签页\r\n      activeTab: 'leader',\r\n      \r\n      // 带单管理相关\r\n      leaderLoading: false,\r\n      leaderList: [],\r\n      leaderTotal: 0,\r\n      leaderOpen: false,\r\n      leaderTitle: '',\r\n      leaderForm: {},\r\n      leaderDateRange: [],\r\n      leaderQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        leaderNickname: null,\r\n        status: null\r\n      },\r\n      leaderRules: {\r\n        leaderNickname: [\r\n          { required: true, message: '带单人昵称不能为空', trigger: 'blur' }\r\n        ],\r\n        symbol: [\r\n          { required: true, message: '交易对不能为空', trigger: 'blur' }\r\n        ],\r\n        periodNo: [\r\n          { required: true, message: '期号不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 跟单管理明细相关\r\n      detailLoading: false,\r\n      detailList: [],\r\n      detailTotal: 0,\r\n      detailQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        followerUsername: '',\r\n        followerUid: '',\r\n        followerEmail: '',\r\n        status: '',\r\n        isFollowing: '',\r\n        resultStatus: '',\r\n        isReturned: '',\r\n        isSettled: ''\r\n      },\r\n      detailDetailDialogVisible: false,\r\n      detailDetailRow: {},\r\n\r\n      // 跟单明细相关\r\n      historyLoading: false,\r\n      historyList: [],\r\n      historyTotal: 0,\r\n      historyDateRange: [],\r\n      historyQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        followerUsername: '',\r\n        followerEmail: '',\r\n        leaderNickname: '',\r\n        isReturned: '',\r\n        resultStatus: ''\r\n      },\r\n      historyDetailDialogVisible: false,\r\n      historyDetailRow: {},\r\n\r\n      // 选中数组\r\n      ids: [],\r\n      single: true,\r\n      multiple: true,\r\n      uploadHeaders: {\r\n        Authorization: 'Bearer ' + localStorage.getItem('token')\r\n      },\r\n      pairNameList: [],\r\n    }\r\n  },\r\n  created() {\r\n    this.getLeaderList()\r\n  },\r\n  mounted() {\r\n    this.getLeaderInfo()\r\n    this.getPairNameList();\r\n  },\r\n  methods: {\r\n    // 标签页切换\r\n    handleTabClick(tab) {\r\n      if (tab.name === 'leader') {\r\n        this.getLeaderInfo()\r\n      } else if (tab.name === 'follow') {\r\n        this.getDetailList()\r\n      } else if (tab.name === 'history') {\r\n        this.getHistoryList()\r\n      }\r\n    },\r\n\r\n    // 带单管理相关方法\r\n    getLeaderList() {\r\n      this.leaderLoading = true\r\n      // TODO: 调用API获取带单管理数据\r\n      this.leaderLoading = false\r\n    },\r\n    handleLeaderQuery() {\r\n      this.leaderQueryParams.pageNum = 1\r\n      this.getLeaderList()\r\n    },\r\n    resetLeaderQuery() {\r\n      this.leaderDateRange = []\r\n      this.resetForm('leaderQueryForm')\r\n      this.handleLeaderQuery()\r\n    },\r\n    handleAddLeader() {\r\n      this.resetLeaderForm()\r\n      this.leaderOpen = true\r\n      this.leaderTitle = '添加带单管理'\r\n    },\r\n    handleUpdateLeader(row) {\r\n      this.resetLeaderForm()\r\n      // TODO: 获取详情数据\r\n      this.leaderOpen = true\r\n      this.leaderTitle = '修改带单管理'\r\n    },\r\n    handleViewLeader(row) {\r\n      // TODO: 实现查看详情\r\n    },\r\n    handleDeleteLeader(row) {\r\n      // TODO: 实现删除功能\r\n    },\r\n    handleLeaderSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    handleLeaderSizeChange(val) {\r\n      this.leaderQueryParams.pageSize = val\r\n      this.getLeaderList()\r\n    },\r\n    handleLeaderCurrentChange(val) {\r\n      this.leaderQueryParams.pageNum = val\r\n      this.getLeaderList()\r\n    },\r\n    resetLeaderForm() {\r\n      this.leaderForm = {\r\n        id: null,\r\n        leaderNickname: null,\r\n        symbol: null,\r\n        periodNo: null,\r\n        marginBalance: 900000,\r\n        remark: null,\r\n        status: 0\r\n      }\r\n    },\r\n    submitLeaderForm() {\r\n      this.$refs['leaderForm'].validate((valid) => {\r\n        if (valid) {\r\n          // TODO: 提交表单\r\n          this.leaderOpen = false\r\n          this.getLeaderList()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 跟单管理明细相关方法\r\n    getDetailList() {\r\n      this.detailLoading = true\r\n      request({\r\n        url: '/api/copyFollowDetail/list',\r\n        method: 'get',\r\n        params: this.detailQueryParams\r\n      }).then(res => {\r\n        this.detailList = (res.data && res.data.records) || res.records || []\r\n        this.detailTotal = (res.data && res.data.total) || res.total || 0\r\n      }).finally(() => {\r\n        this.detailLoading = false\r\n      })\r\n    },\r\n    handleDetailQuery() {\r\n      this.detailQueryParams.pageNum = 1\r\n      this.getDetailList()\r\n    },\r\n    resetDetailQuery() {\r\n      this.detailQueryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        followerUsername: '',\r\n        followerUid: '',\r\n        followerEmail: '',\r\n        status: '',\r\n        isFollowing: '',\r\n        resultStatus: '',\r\n        isReturned: '',\r\n        isSettled: ''\r\n      }\r\n      this.getDetailList()\r\n    },\r\n    handleDetailSizeChange(val) {\r\n      this.detailQueryParams.pageSize = val\r\n      this.getDetailList()\r\n    },\r\n    handleDetailCurrentChange(val) {\r\n      this.detailQueryParams.pageNum = val\r\n      this.getDetailList()\r\n    },\r\n    showDetailDetail(row) {\r\n      this.detailDetailRow = row\r\n      this.detailDetailDialogVisible = true\r\n    },\r\n\r\n    // 跟单明细相关方法\r\n    getHistoryList() {\r\n      this.historyLoading = true\r\n      request({\r\n        url: '/api/copyFollowHistory/list',\r\n        method: 'get',\r\n        params: this.historyQueryParams\r\n      }).then(res => {\r\n        this.historyList = (res.data && res.data.records) || res.records || []\r\n        this.historyTotal = (res.data && res.data.total) || res.total || 0\r\n      }).finally(() => {\r\n        this.historyLoading = false\r\n      })\r\n    },\r\n    handleHistoryQuery() {\r\n      this.historyQueryParams.pageNum = 1\r\n      this.getHistoryList()\r\n    },\r\n    resetHistoryQuery() {\r\n      this.historyQueryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        followerUsername: '',\r\n        followerEmail: '',\r\n        leaderNickname: '',\r\n        isReturned: '',\r\n        resultStatus: ''\r\n      }\r\n      this.getHistoryList()\r\n    },\r\n    handleHistorySizeChange(val) {\r\n      this.historyQueryParams.pageSize = val\r\n      this.getHistoryList()\r\n    },\r\n    handleHistoryCurrentChange(val) {\r\n      this.historyQueryParams.pageNum = val\r\n      this.getHistoryList()\r\n    },\r\n    showHistoryDetail(row) {\r\n      this.historyDetailRow = row\r\n      this.historyDetailDialogVisible = true\r\n    },\r\n\r\n    // 状态相关方法\r\n    getLeaderStatusText(status) {\r\n      const statusMap = {\r\n        0: '未开始',\r\n        1: '准备中',\r\n        2: '已开始',\r\n        3: '结算中',\r\n        4: '已结束'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n    getLeaderStatusType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'warning',\r\n        2: 'success',\r\n        3: 'primary',\r\n        4: 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n    getHistoryResultText(status) {\r\n      const resultMap = {\r\n        0: '未结算',\r\n        1: '盈利',\r\n        2: '亏损'\r\n      }\r\n      return resultMap[status] || '未知'\r\n    },\r\n    getHistoryResultType(status) {\r\n      const typeMap = {\r\n        0: 'info',\r\n        1: 'success',\r\n        2: 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n\r\n    // 通用方法\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return '';\r\n      const date = new Date(dateTime);\r\n      const pad = n => n < 10 ? '0' + n : n;\r\n      return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +\r\n             `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;\r\n    },\r\n    getLeaderInfo() {\r\n      request({\r\n        url: '/api/copyLeader/info',\r\n        method: 'get'\r\n      }).then(res => {\r\n        this.leaderForm = res.data || res\r\n      })\r\n    },\r\n    saveLeaderInfo() {\r\n      request({\r\n        url: '/api/copyLeader/update',\r\n        method: 'post',\r\n        data: this.leaderForm\r\n      }).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.getLeaderInfo()\r\n      })\r\n    },\r\n    beforeAvatarUpload(file) {\r\n      const isJPG = file.type === 'image/jpeg'\r\n      const isPNG = file.type === 'image/png'\r\n      const isLt2M = file.size / 1024 / 1024 < 2\r\n      if (!isJPG && !isPNG) {\r\n        this.$message.error('上传图片只能是 JPG 或 PNG 格式!')\r\n        return false\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传图片大小不能超过 2MB!')\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    handleAvatarSuccess(res) {\r\n      if (res.code === 0) {\r\n        this.leaderForm.leaderAvatar = res.data\r\n        this.$message.success('上传成功')\r\n      } else {\r\n        this.$message.error(res.msg || '上传失败')\r\n      }\r\n    },\r\n    handleAvatarError(err) {\r\n      this.$message.error('上传失败，请重试')\r\n    },\r\n    getPairNameList() {\r\n      request({\r\n        url: '/exchange/pair/enabledList',\r\n        method: 'get'\r\n      }).then(res => {\r\n        this.pairNameList = res.data || [];\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.tab-content {\r\n  padding: 10px 0;\r\n}\r\n.filter-container {\r\n  padding-bottom: 10px;\r\n}\r\n.filter-item {\r\n  width: 100%;\r\n}\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n.pagination-container {\r\n  padding: 10px 0;\r\n}\r\n.text-success {\r\n  color: #67c23a;\r\n}\r\n.text-danger {\r\n  color: #f56c6c;\r\n}\r\n\r\n/* 自定义标签页样式 */\r\n.deal-tabs /deep/ .el-tabs__nav {\r\n  display: flex !important;\r\n  flex-direction: row !important;\r\n  width: 100%;\r\n}\r\n.deal-tabs /deep/ .el-tabs__item {\r\n  color: #fff !important;\r\n  background: transparent !important;\r\n  border-radius: 12px 12px 0 0;\r\n  position: relative;\r\n  transition: background 0.2s, color 0.2s;\r\n  z-index: 1;\r\n  height: 48px;\r\n  line-height: 48px;\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-sizing: border-box;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  padding: 0 24px;\r\n  flex: 1 1 0;\r\n  min-width: 120px;\r\n}\r\n.deal-tabs /deep/ .el-tabs__item.is-active {\r\n  color: #111 !important;\r\n  background: #FFD700 !important;\r\n  font-weight: bold;\r\n  box-shadow: 0 -2px 8px rgba(255,215,0,0.08);\r\n  z-index: 2;\r\n  border-radius: 12px 12px 0 0 !important;\r\n}\r\n.deal-tabs /deep/ .el-tabs__item.is-active::after {\r\n  content: '';\r\n  display: block;\r\n  height: 4px;\r\n  background: #FFD700;\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  border-radius: 2px;\r\n}\r\n.avatar-uploader .el-upload {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.avatar-uploader .el-upload:hover {\r\n  border-color: #409EFF;\r\n}\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 80px;\r\n  height: 80px;\r\n  line-height: 80px;\r\n  text-align: center;\r\n}\r\n.avatar {\r\n  width: 80px;\r\n  height: 80px;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n</style> "], "mappings": ";;;;;;AA0dA,OAAAA,OAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MAEA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,eAAA;MACAC,iBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;QACAC,MAAA;MACA;MACAC,WAAA;QACAF,cAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,MAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,QAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAG,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,iBAAA;QACAb,OAAA;QACAC,QAAA;QACAa,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAb,MAAA;QACAc,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,SAAA;MACA;MACAC,yBAAA;MACAC,eAAA;MAEA;MACAC,cAAA;MACAC,WAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,kBAAA;QACA3B,OAAA;QACAC,QAAA;QACAa,gBAAA;QACAE,aAAA;QACAd,cAAA;QACAiB,UAAA;QACAD,YAAA;MACA;MACAU,0BAAA;MACAC,gBAAA;MAEA;MACAC,GAAA;MACAC,MAAA;MACAC,QAAA;MACAC,aAAA;QACAC,aAAA,cAAAC,YAAA,CAAAC,OAAA;MACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,IAAAA,GAAA,CAAAxD,IAAA;QACA,KAAAoD,aAAA;MACA,WAAAI,GAAA,CAAAxD,IAAA;QACA,KAAAyD,aAAA;MACA,WAAAD,GAAA,CAAAxD,IAAA;QACA,KAAA0D,cAAA;MACA;IACA;IAEA;IACAR,aAAA,WAAAA,cAAA;MACA,KAAA/C,aAAA;MACA;MACA,KAAAA,aAAA;IACA;IACAwD,iBAAA,WAAAA,kBAAA;MACA,KAAAjD,iBAAA,CAAAC,OAAA;MACA,KAAAuC,aAAA;IACA;IACAU,gBAAA,WAAAA,iBAAA;MACA,KAAAnD,eAAA;MACA,KAAAoD,SAAA;MACA,KAAAF,iBAAA;IACA;IACAG,eAAA,WAAAA,gBAAA;MACA,KAAAC,eAAA;MACA,KAAAzD,UAAA;MACA,KAAAC,WAAA;IACA;IACAyD,kBAAA,WAAAA,mBAAAC,GAAA;MACA,KAAAF,eAAA;MACA;MACA,KAAAzD,UAAA;MACA,KAAAC,WAAA;IACA;IACA2D,gBAAA,WAAAA,iBAAAD,GAAA;MACA;IAAA,CACA;IACAE,kBAAA,WAAAA,mBAAAF,GAAA;MACA;IAAA,CACA;IACAG,2BAAA,WAAAA,4BAAAC,SAAA;MACA,KAAA5B,GAAA,GAAA4B,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAA9B,MAAA,GAAA2B,SAAA,CAAAI,MAAA;MACA,KAAA9B,QAAA,IAAA0B,SAAA,CAAAI,MAAA;IACA;IACAC,sBAAA,WAAAA,uBAAAC,GAAA;MACA,KAAAjE,iBAAA,CAAAE,QAAA,GAAA+D,GAAA;MACA,KAAAzB,aAAA;IACA;IACA0B,yBAAA,WAAAA,0BAAAD,GAAA;MACA,KAAAjE,iBAAA,CAAAC,OAAA,GAAAgE,GAAA;MACA,KAAAzB,aAAA;IACA;IACAa,eAAA,WAAAA,gBAAA;MACA,KAAAvD,UAAA;QACAgE,EAAA;QACA3D,cAAA;QACAM,MAAA;QACAC,QAAA;QACAyD,aAAA;QACAC,MAAA;QACAhE,MAAA;MACA;IACA;IACAiE,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,eAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAH,KAAA,CAAA1E,UAAA;UACA0E,KAAA,CAAA9B,aAAA;QACA;MACA;IACA;IAEA;IACAO,aAAA,WAAAA,cAAA;MAAA,IAAA2B,MAAA;MACA,KAAA/D,aAAA;MACAtB,OAAA;QACAsF,GAAA;QACAC,MAAA;QACAC,MAAA,OAAA/D;MACA,GAAAgE,IAAA,WAAAC,GAAA;QACAL,MAAA,CAAA9D,UAAA,GAAAmE,GAAA,CAAAxF,IAAA,IAAAwF,GAAA,CAAAxF,IAAA,CAAAyF,OAAA,IAAAD,GAAA,CAAAC,OAAA;QACAN,MAAA,CAAA7D,WAAA,GAAAkE,GAAA,CAAAxF,IAAA,IAAAwF,GAAA,CAAAxF,IAAA,CAAA0F,KAAA,IAAAF,GAAA,CAAAE,KAAA;MACA;QACAP,MAAA,CAAA/D,aAAA;MACA;IACA;IACAuE,iBAAA,WAAAA,kBAAA;MACA,KAAApE,iBAAA,CAAAb,OAAA;MACA,KAAA8C,aAAA;IACA;IACAoC,gBAAA,WAAAA,iBAAA;MACA,KAAArE,iBAAA;QACAb,OAAA;QACAC,QAAA;QACAa,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAb,MAAA;QACAc,WAAA;QACAC,YAAA;QACAC,UAAA;QACAC,SAAA;MACA;MACA,KAAA0B,aAAA;IACA;IACAqC,sBAAA,WAAAA,uBAAAnB,GAAA;MACA,KAAAnD,iBAAA,CAAAZ,QAAA,GAAA+D,GAAA;MACA,KAAAlB,aAAA;IACA;IACAsC,yBAAA,WAAAA,0BAAApB,GAAA;MACA,KAAAnD,iBAAA,CAAAb,OAAA,GAAAgE,GAAA;MACA,KAAAlB,aAAA;IACA;IACAuC,gBAAA,WAAAA,iBAAA/B,GAAA;MACA,KAAAhC,eAAA,GAAAgC,GAAA;MACA,KAAAjC,yBAAA;IACA;IAEA;IACA0B,cAAA,WAAAA,eAAA;MAAA,IAAAuC,MAAA;MACA,KAAA/D,cAAA;MACAnC,OAAA;QACAsF,GAAA;QACAC,MAAA;QACAC,MAAA,OAAAjD;MACA,GAAAkD,IAAA,WAAAC,GAAA;QACAQ,MAAA,CAAA9D,WAAA,GAAAsD,GAAA,CAAAxF,IAAA,IAAAwF,GAAA,CAAAxF,IAAA,CAAAyF,OAAA,IAAAD,GAAA,CAAAC,OAAA;QACAO,MAAA,CAAA7D,YAAA,GAAAqD,GAAA,CAAAxF,IAAA,IAAAwF,GAAA,CAAAxF,IAAA,CAAA0F,KAAA,IAAAF,GAAA,CAAAE,KAAA;MACA;QACAM,MAAA,CAAA/D,cAAA;MACA;IACA;IACAgE,kBAAA,WAAAA,mBAAA;MACA,KAAA5D,kBAAA,CAAA3B,OAAA;MACA,KAAA+C,cAAA;IACA;IACAyC,iBAAA,WAAAA,kBAAA;MACA,KAAA7D,kBAAA;QACA3B,OAAA;QACAC,QAAA;QACAa,gBAAA;QACAE,aAAA;QACAd,cAAA;QACAiB,UAAA;QACAD,YAAA;MACA;MACA,KAAA6B,cAAA;IACA;IACA0C,uBAAA,WAAAA,wBAAAzB,GAAA;MACA,KAAArC,kBAAA,CAAA1B,QAAA,GAAA+D,GAAA;MACA,KAAAjB,cAAA;IACA;IACA2C,0BAAA,WAAAA,2BAAA1B,GAAA;MACA,KAAArC,kBAAA,CAAA3B,OAAA,GAAAgE,GAAA;MACA,KAAAjB,cAAA;IACA;IACA4C,iBAAA,WAAAA,kBAAArC,GAAA;MACA,KAAAzB,gBAAA,GAAAyB,GAAA;MACA,KAAA1B,0BAAA;IACA;IAEA;IACAgE,mBAAA,WAAAA,oBAAAzF,MAAA;MACA,IAAA0F,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA1F,MAAA;IACA;IACA2F,mBAAA,WAAAA,oBAAA3F,MAAA;MACA,IAAA4F,OAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA5F,MAAA;IACA;IACA6F,oBAAA,WAAAA,qBAAA7F,MAAA;MACA,IAAA8F,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA9F,MAAA;IACA;IACA+F,oBAAA,WAAAA,qBAAA/F,MAAA;MACA,IAAA4F,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA5F,MAAA;IACA;IAEA;IACAgG,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,QAAA;MACA,IAAAG,GAAA,YAAAA,IAAAC,CAAA;QAAA,OAAAA,CAAA,cAAAA,CAAA,GAAAA,CAAA;MAAA;MACA,UAAAC,MAAA,CAAAJ,IAAA,CAAAK,WAAA,SAAAD,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAM,QAAA,cAAAF,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAO,OAAA,eAAAH,MAAA,CACAF,GAAA,CAAAF,IAAA,CAAAQ,QAAA,UAAAJ,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAS,UAAA,UAAAL,MAAA,CAAAF,GAAA,CAAAF,IAAA,CAAAU,UAAA;IACA;IACAtE,aAAA,WAAAA,cAAA;MAAA,IAAAuE,MAAA;MACA5H,OAAA;QACAsF,GAAA;QACAC,MAAA;MACA,GAAAE,IAAA,WAAAC,GAAA;QACAkC,MAAA,CAAAnH,UAAA,GAAAiF,GAAA,CAAAxF,IAAA,IAAAwF,GAAA;MACA;IACA;IACAmC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA9H,OAAA;QACAsF,GAAA;QACAC,MAAA;QACArF,IAAA,OAAAO;MACA,GAAAgF,IAAA;QACAqC,MAAA,CAAAC,QAAA,CAAAC,OAAA;QACAF,MAAA,CAAAzE,aAAA;MACA;IACA;IACA4E,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,KAAA,GAAAD,IAAA,CAAAE,IAAA;MACA,IAAAC,KAAA,GAAAH,IAAA,CAAAE,IAAA;MACA,IAAAE,MAAA,GAAAJ,IAAA,CAAAK,IAAA;MACA,KAAAJ,KAAA,KAAAE,KAAA;QACA,KAAAN,QAAA,CAAAS,KAAA;QACA;MACA;MACA,KAAAF,MAAA;QACA,KAAAP,QAAA,CAAAS,KAAA;QACA;MACA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAA/C,GAAA;MACA,IAAAA,GAAA,CAAAgD,IAAA;QACA,KAAAjI,UAAA,CAAAkI,YAAA,GAAAjD,GAAA,CAAAxF,IAAA;QACA,KAAA6H,QAAA,CAAAC,OAAA;MACA;QACA,KAAAD,QAAA,CAAAS,KAAA,CAAA9C,GAAA,CAAAkD,GAAA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAAf,QAAA,CAAAS,KAAA;IACA;IACAlF,eAAA,WAAAA,gBAAA;MAAA,IAAAyF,MAAA;MACA/I,OAAA;QACAsF,GAAA;QACAC,MAAA;MACA,GAAAE,IAAA,WAAAC,GAAA;QACAqD,MAAA,CAAA9F,YAAA,GAAAyC,GAAA,CAAAxF,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}