{"ast": null, "code": "import OverloadYield from \"./OverloadYield.js\";\nfunction _awaitAsyncGenerator(e) {\n  return new OverloadYield(e, 0);\n}\nexport { _awaitAsyncGenerator as default };", "map": {"version": 3, "names": ["OverloadYield", "_awaitAsyncGenerator", "e", "default"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/@babel/runtime/helpers/esm/awaitAsyncGenerator.js"], "sourcesContent": ["import OverloadYield from \"./OverloadYield.js\";\nfunction _awaitAsyncGenerator(e) {\n  return new OverloadYield(e, 0);\n}\nexport { _awaitAsyncGenerator as default };"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,SAASC,oBAAoBA,CAACC,CAAC,EAAE;EAC/B,OAAO,IAAIF,aAAa,CAACE,CAAC,EAAE,CAAC,CAAC;AAChC;AACA,SAASD,oBAAoB,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}