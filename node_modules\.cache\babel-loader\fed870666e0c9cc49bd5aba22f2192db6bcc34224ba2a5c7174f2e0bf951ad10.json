{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport request from '@/utils/request';\n\n// 获取轮播图列表\nexport function getBannerList(params) {\n  return request({\n    url: '/banner/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 新增轮播图\nexport function addBanner(data) {\n  return request({\n    url: '/banner/add',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改轮播图\nexport function updateBanner(data) {\n  return request({\n    url: '/banner/update',\n    method: 'put',\n    data: data\n  });\n}\n\n// 修改状态\nexport function toggleBannerStatus(id, status) {\n  return request({\n    url: \"/banner/status/\".concat(id, \"/\").concat(status),\n    method: 'put'\n  });\n}\n\n// 删除轮播图\nexport function deleteBanner(id) {\n  return request({\n    url: \"/banner/delete/\".concat(id),\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["request", "getBannerList", "params", "url", "method", "addBanner", "data", "updateBanner", "toggleBannerStatus", "id", "status", "concat", "deleteBanner"], "sources": ["E:/新项目/整理6/adminweb/src/api/carousel/banner.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取轮播图列表\r\nexport function getBannerList(params) {\r\n  return request({\r\n    url: '/banner/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 新增轮播图\r\nexport function addBanner(data) {\r\n  return request({\r\n    url: '/banner/add',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 修改轮播图\r\nexport function updateBanner(data) {\r\n  return request({\r\n    url: '/banner/update',\r\n    method: 'put',\r\n    data\r\n  })\r\n}\r\n\r\n// 修改状态\r\nexport function toggleBannerStatus(id, status) {\r\n  return request({\r\n    url: `/banner/status/${id}/${status}`,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 删除轮播图\r\nexport function deleteBanner(id) {\r\n  return request({\r\n    url: `/banner/delete/${id}`,\r\n    method: 'delete'\r\n  })\r\n} "], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbE,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,kBAAkBA,CAACC,EAAE,EAAEC,MAAM,EAAE;EAC7C,OAAOV,OAAO,CAAC;IACbG,GAAG,oBAAAQ,MAAA,CAAoBF,EAAE,OAAAE,MAAA,CAAID,MAAM,CAAE;IACrCN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASQ,YAAYA,CAACH,EAAE,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,oBAAAQ,MAAA,CAAoBF,EAAE,CAAE;IAC3BL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}